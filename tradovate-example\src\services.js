// services.js
// Contains helper functions for interacting with Tradovate API

import { URLs } from '../tutorialsURLs'
import { getAccessToken as getStoredToken } from './storage'

const { DEMO_URL } = URLs

// Create a global tradovate object for console access
if (typeof window !== 'undefined') {
    window.tradovate = {}
}

/**
 * Helper function for GET requests to Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {boolean} auth - Whether to include auth header (default: true)
 * @returns {Promise<Object>} JSON response
 */
export const tvGet = async (endpoint, params = {}, auth = true) => {
    // Build query string from params
    const queryString = Object.keys(params).length
        ? '?' + Object.entries(params)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&')
        : ''

    // Build request options
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    }

    // Add auth header if needed
    if (auth) {
        const { token } = getStoredToken()
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`
        }
    }

    // Make the request
    const response = await fetch(`${DEMO_URL}${endpoint}${queryString}`, options)

    // Parse and return JSON response
    return await response.json()
}

/**
 * Helper function for POST requests to Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {boolean} auth - Whether to include auth header (default: true)
 * @returns {Promise<Object>} JSON response
 */
export const tvPost = async (endpoint, data = {}, auth = true) => {
    // Build request options
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    }

    // Add auth header if needed
    if (auth) {
        const { token } = getStoredToken()
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`
        }
    }

    // Make the request
    const response = await fetch(`${DEMO_URL}${endpoint}`, options)

    // Parse and return JSON response
    return await response.json()
}

// Add the functions to the global tradovate object for console access
if (typeof window !== 'undefined') {
    window.tradovate.get = tvGet
    window.tradovate.post = tvPost
}
