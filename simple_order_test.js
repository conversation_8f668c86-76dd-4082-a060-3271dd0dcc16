/**
 * Simple Order Test for Tradovate API
 *
 * This script focuses solely on placing an order with the correct parameters.
 */

const axios = require('axios');
const logger = require('./data_logger');

// Configuration
const config = {
    baseUrl: 'https://demo.tradovateapi.com/v1',
    name: 'bravesbeat<PERSON>s',
    password: 'Braves12$',
    appId: 'Trading Bot',
    appVersion: '0.0.1',
    cid: '6186',
    sec: '69311dad-75a7-49c6-9958-00ab2c4f1ab6',
    deviceId: '25e3f568-2890-5442-1e40-b9e8983af7e7',
    accountId: '4690440' // Demo account ID without DEMO prefix
};

// Create axios instance
const api = axios.create({
    baseURL: config.baseUrl,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

// Authentication state
let accessToken = null;

/**
 * Authenticate with Tradovate API
 * @returns {Promise<Object>} - Authentication result
 */
async function authenticate() {
    try {
        console.log('Authenticating with Tradovate API...');

        // Prepare authentication payload
        const payload = {
            name: config.name,
            password: config.password,
            appId: config.appId,
            appVersion: config.appVersion,
            deviceId: config.deviceId,
            cid: config.cid,
            sec: config.sec
        };

        // Send authentication request
        const response = await api.post('/auth/accesstokenrequest', payload);

        if (response.data && response.data.accessToken) {
            accessToken = response.data.accessToken;

            console.log('Authentication successful!');
            console.log(`Access Token: ${accessToken.substring(0, 10)}...`);

            return {
                success: true,
                accessToken
            };
        } else {
            throw new Error('Authentication response did not contain an access token');
        }
    } catch (error) {
        console.error('Authentication failed:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Place a test order
 * @returns {Promise<Object>} - Order result
 */
async function placeTestOrder() {
    try {
        // Authenticate first
        const authResult = await authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }

        console.log('Checking account information...');
        try {
            const accountResponse = await api.get('/account/list', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });

            if (accountResponse.data && Array.isArray(accountResponse.data)) {
                console.log(`Found ${accountResponse.data.length} accounts`);
                accountResponse.data.forEach(account => {
                    console.log(`- Account: ${account.name} (ID: ${account.id})`);
                });
            }
        } catch (accountError) {
            console.error('Error getting account information:', accountError.message);
        }

        // Order parameters - using the exact format from Tradovate documentation
        const orderParams = {
            accountSpec: 'DEMO4690440', // Try with DEMO prefix
            symbol: 'MNQM5',
            action: 'Buy',
            orderQty: 1,
            orderType: 'Market',
            isAutomated: true
        };

        console.log('Placing test order with parameters:');
        console.log(JSON.stringify(orderParams, null, 2));

        // Send order request with detailed error handling
        console.log('Sending order request to:', `${config.baseUrl}/order/placeorder`);

        // Send order request
        const response = await api.post('/order/placeorder', orderParams, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Order placed successfully!');
        console.log('Response:', response.data);

        return {
            success: true,
            orderData: response.data
        };
    } catch (error) {
        console.error('Failed to place order:', error.message);

        if (error.response) {
            console.error('Error response status:', error.response.status);
            console.error('Error response data:', error.response.data);

            // Try to get more detailed error information
            if (typeof error.response.data === 'string') {
                console.error('Error details:', error.response.data);

                // Parse the error message for more information
                if (error.response.data.includes('missing required field')) {
                    const match = error.response.data.match(/missing required field "([^"]+)"/);
                    if (match) {
                        console.error(`Missing required field: ${match[1]}`);
                    }
                }
            }

            return {
                success: false,
                error: error.message,
                status: error.response.status,
                data: error.response.data
            };
        } else if (error.request) {
            console.error('No response received from server');
            console.error('Request details:', error.request);

            return {
                success: false,
                error: 'No response received from server',
                request: error.request
            };
        } else {
            console.error('Error details:', error);

            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Run the test
placeTestOrder()
    .then(result => {
        if (result.success) {
            console.log('Test completed successfully');
        } else {
            console.error('Test failed');
            process.exit(1);
        }
    })
    .catch(error => {
        console.error('Unhandled error during test:', error);
        process.exit(1);
    });
