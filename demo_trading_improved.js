/**
 * Improved Demo Trading Bot
 *
 * This module provides a more robust implementation of the demo trading bot
 * with improved error handling, stability, and circuit breakers.
 */

const tradovateApi = require('./tradovate_api_improved');
const logger = require('./data_logger');
const fs = require('fs');
const path = require('path');

// Configuration
const mnqConfig = require('./paper_trading_config');
const mgcConfig = require('./mgc_paper_trading_config');

// Create MES config based on MNQ config
const mesConfig = {
    ...mnqConfig,
    symbol: 'MES',
    pointValue: 5.00,
    slFactors: 3.0,
    tpFactors: 3.0,
    trailFactors: 0.01,
    fixedTpPoints: 0
};

// State
let isConnected = false;
let isInitialized = false;
let accountId = null;
let marketData = {};
let activeOrders = {};
let activePositions = {};
let lastCandleTimestamps = {};
let circuitBreakers = {
    maxDailyLoss: 0.05, // 5% of account
    maxConsecutiveLosses: 3,
    consecutiveLossCount: 0,
    dailyLoss: 0,
    isTripped: false,
    lastResetTime: null
};

// Constants
const SYMBOLS = ['MNQ', 'MES', 'MGC'];
const RECONNECT_INTERVAL = 60000; // 1 minute
const HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
const MAX_RETRY_ATTEMPTS = 3;

// Intervals
let reconnectInterval = null;
let healthCheckInterval = null;

/**
 * Initialize the demo trading bot
 * @returns {Promise<boolean>} - True if initialization was successful
 */
async function initialize() {
    try {
        logger.logSystem('Initializing demo trading bot...', 'info');

        // Authenticate with Tradovate API
        const authResult = await tradovateApi.authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }
        logger.logSystem('Authentication successful', 'info');

        // Use the account ID from the config
        accountId = '4690440'; // Hardcoded for demo purposes
        logger.logSystem(`Using account ID: ${accountId}`, 'info');

        // Update configs with account ID
        mnqConfig.accountId = accountId;
        mesConfig.accountId = accountId;
        mgcConfig.accountId = accountId;

        // Initialize market data for each symbol
        await initializeMarketData();

        // Connect to WebSockets
        await connectWebSockets();

        // Set up WebSocket message handler
        tradovateApi.onWebSocketMessage = handleWebSocketMessage;

        // Start health check
        startHealthCheck();

        // Reset circuit breakers
        resetCircuitBreakers();

        isInitialized = true;
        isConnected = true;
        logger.logSystem('Demo trading bot initialized successfully', 'info');
        console.log('Demo trading bot initialized successfully');

        return true;
    } catch (error) {
        logger.logSystem(`Initialization failed: ${error.message}`, 'error');
        console.error(`Initialization failed: ${error.message}`);
        return false;
    }
}

/**
 * Initialize market data for all symbols
 * @returns {Promise<void>}
 */
async function initializeMarketData() {
    for (const symbol of SYMBOLS) {
        marketData[symbol] = {
            candles: [],
            indicators: {},
            positions: [],
            orders: [],
            config: symbol === 'MNQ' ? mnqConfig : symbol === 'MES' ? mesConfig : mgcConfig
        };

        logger.logSystem(`Initialized market data for ${symbol}`, 'info');
    }

    logger.logSystem('Market data initialized for all symbols', 'info');
}

/**
 * Connect to WebSockets
 * @returns {Promise<boolean>} - True if connection was successful
 */
async function connectWebSockets() {
    try {
        // Connect to main WebSocket
        const mainWsResult = await tradovateApi.connectWebSocket();
        if (!mainWsResult.success) {
            throw new Error(`Failed to connect to main WebSocket: ${mainWsResult.error}`);
        }

        // Connect to market data WebSocket
        const mdWsResult = await tradovateApi.connectMarketDataWebSocket();
        if (!mdWsResult.success) {
            throw new Error(`Failed to connect to market data WebSocket: ${mdWsResult.error}`);
        }

        // Subscribe to chart data for each symbol
        for (const symbol of SYMBOLS) {
            await tradovateApi.subscribeToChart(symbol, '1m');
            logger.logSystem(`Subscribed to chart data for ${symbol}`, 'info');
        }

        return true;
    } catch (error) {
        logger.logSystem(`WebSocket connection failed: ${error.message}`, 'error');
        return false;
    }
}

/**
 * Handle WebSocket messages
 * @param {Object} message - WebSocket message
 */
function handleWebSocketMessage(message) {
    try {
        // Process different message types
        if (message.e === 'chart') {
            handleChartData(message.d);
        } else if (message.e === 'order') {
            handleOrderUpdate(message.d);
        } else if (message.e === 'position') {
            handlePositionUpdate(message.d);
        } else if (message.e === 'account') {
            handleAccountUpdate(message.d);
        } else if (message.e === 'fill') {
            handleFillUpdate(message.d);
        }
    } catch (error) {
        logger.logSystem(`Error handling WebSocket message: ${error.message}`, 'error');
    }
}

/**
 * Handle chart data
 * @param {Object} chartData - Chart data
 */
function handleChartData(chartData) {
    try {
        // Extract symbol from chart ID
        const symbolMatch = chartData.id.match(/^([A-Z0-9]+)-/);
        if (!symbolMatch || !symbolMatch[1]) {
            logger.logSystem(`Could not extract symbol from chart ID: ${chartData.id}`, 'warning');
            return;
        }

        const symbol = symbolMatch[1];

        // Check if we have market data for this symbol
        if (!marketData[symbol]) {
            logger.logSystem(`No market data for symbol: ${symbol}`, 'warning');
            return;
        }

        // Process candles
        if (chartData.candles && chartData.candles.length > 0) {
            logger.logSystem(`Received ${chartData.candles.length} candles for ${symbol}`, 'info');

            // Process each candle
            for (const candle of chartData.candles) {
                processCandle(symbol, candle);
            }
        }
    } catch (error) {
        logger.logSystem(`Error handling chart data: ${error.message}`, 'error');
    }
}

/**
 * Process a candle
 * @param {string} symbol - Symbol
 * @param {Object} candle - Candle data
 */
function processCandle(symbol, candle) {
    try {
        // Format candle
        const formattedCandle = {
            timestamp: new Date(candle.timestamp).getTime(),
            open: candle.open,
            high: candle.high,
            low: candle.low,
            close: candle.close,
            volume: candle.volume || 0
        };

        // Check if we've already processed this candle
        if (lastCandleTimestamps[symbol] === formattedCandle.timestamp) {
            return;
        }

        // Update last candle timestamp
        lastCandleTimestamps[symbol] = formattedCandle.timestamp;

        // Add candle to market data
        marketData[symbol].candles.push(formattedCandle);

        // Limit candles array to 1000 candles
        if (marketData[symbol].candles.length > 1000) {
            marketData[symbol].candles.shift();
        }

        // Calculate indicators
        calculateIndicators(symbol);

        // Check for trading signals
        checkForSignals(symbol);

        // Update trailing stops
        updateTrailingStops(symbol);

        logger.logSystem(`Processed candle for ${symbol}: ${JSON.stringify(formattedCandle)}`, 'debug');
    } catch (error) {
        logger.logSystem(`Error processing candle for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Calculate indicators for a symbol
 * @param {string} symbol - Symbol
 */
function calculateIndicators(symbol) {
    try {
        const candles = marketData[symbol].candles;

        // Need at least 50 candles for indicators
        if (candles.length < 50) {
            return;
        }

        // Calculate ATR (Average True Range)
        const atr = calculateATR(candles, 14);
        marketData[symbol].indicators.atr = atr;

        // Calculate RSI (Relative Strength Index)
        const rsi = calculateRSI(candles, 14);
        marketData[symbol].indicators.rsi = rsi;

        // Calculate WMA (Weighted Moving Average)
        const wma50 = calculateWMA(candles, 50);
        marketData[symbol].indicators.wma50 = wma50;

        logger.logSystem(`Calculated indicators for ${symbol}: ATR=${atr.toFixed(2)}, RSI=${rsi.toFixed(2)}, WMA50=${wma50.toFixed(2)}`, 'debug');
    } catch (error) {
        logger.logSystem(`Error calculating indicators for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Calculate ATR (Average True Range)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - ATR value
 */
function calculateATR(candles, period) {
    // Simple ATR calculation
    let trSum = 0;

    for (let i = 1; i < period + 1; i++) {
        const high = candles[candles.length - i].high;
        const low = candles[candles.length - i].low;
        const prevClose = candles[candles.length - i - 1].close;

        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);

        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
    }

    return trSum / period;
}

/**
 * Calculate RSI (Relative Strength Index)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - RSI value
 */
function calculateRSI(candles, period) {
    // Simple RSI calculation
    let gains = 0;
    let losses = 0;

    for (let i = candles.length - period; i < candles.length; i++) {
        const change = candles[i].close - candles[i - 1].close;

        if (change >= 0) {
            gains += change;
        } else {
            losses -= change;
        }
    }

    const avgGain = gains / period;
    const avgLoss = losses / period;

    if (avgLoss === 0) {
        return 100;
    }

    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

/**
 * Calculate WMA (Weighted Moving Average)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - WMA value
 */
function calculateWMA(candles, period) {
    // Simple WMA calculation
    let sum = 0;
    let weightSum = 0;

    for (let i = 0; i < period; i++) {
        const weight = period - i;
        const price = candles[candles.length - 1 - i].close;

        sum += price * weight;
        weightSum += weight;
    }

    return sum / weightSum;
}

/**
 * Check for trading signals
 * @param {string} symbol - Symbol
 */
function checkForSignals(symbol) {
    try {
        // Check if circuit breakers are tripped
        if (circuitBreakers.isTripped) {
            logger.logSystem(`Circuit breakers tripped, not checking for signals for ${symbol}`, 'warning');
            return;
        }

        // Get market data
        const data = marketData[symbol];
        const candles = data.candles;
        const indicators = data.indicators;
        const config = data.config;

        // Need at least 50 candles and indicators
        if (candles.length < 50 || !indicators.rsi || !indicators.atr || !indicators.wma50) {
            return;
        }

        // Get latest candle
        const latestCandle = candles[candles.length - 1];

        // Check for long signal
        if (indicators.rsi < 30 && latestCandle.close > indicators.wma50) {
            // Long signal
            logger.logSystem(`Long signal for ${symbol}: RSI=${indicators.rsi.toFixed(2)}, Close=${latestCandle.close}, WMA50=${indicators.wma50.toFixed(2)}`, 'info');

            // Place long order
            placeLongOrder(symbol);
        }
        // Check for short signal
        else if (indicators.rsi > 70 && latestCandle.close < indicators.wma50) {
            // Short signal
            logger.logSystem(`Short signal for ${symbol}: RSI=${indicators.rsi.toFixed(2)}, Close=${latestCandle.close}, WMA50=${indicators.wma50.toFixed(2)}`, 'info');

            // Place short order
            placeShortOrder(symbol);
        }
    } catch (error) {
        logger.logSystem(`Error checking for signals for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Place a long order
 * @param {string} symbol - Symbol
 */
async function placeLongOrder(symbol) {
    try {
        // Get market data
        const data = marketData[symbol];
        const candles = data.candles;
        const indicators = data.indicators;
        const config = data.config;

        // Calculate position size
        const positionSize = 10; // Fixed position size for demo

        // Calculate stop loss and take profit prices
        const latestCandle = candles[candles.length - 1];
        const stopLossPoints = indicators.atr * config.slFactors;
        const takeProfitPoints = indicators.atr * config.tpFactors;

        const entryPrice = latestCandle.close;
        const stopLossPrice = entryPrice - stopLossPoints;
        const takeProfitPrice = entryPrice + takeProfitPoints;

        // Find contract
        const contract = await tradovateApi.findContract(symbol);
        if (!contract) {
            throw new Error(`Could not find contract for ${symbol}`);
        }

        // Place market order
        const orderParams = {
            accountId: accountId,
            contractId: contract.id,
            action: 'Buy',
            orderQty: positionSize,
            orderType: 'Market',
            isAutomated: true
        };

        const orderResult = await tradovateApi.placeOrder(orderParams);
        if (!orderResult.success) {
            throw new Error(`Failed to place long order for ${symbol}: ${orderResult.error}`);
        }

        logger.logSystem(`Placed long order for ${symbol}: ${JSON.stringify(orderResult)}`, 'info');

        // Store order
        activeOrders[orderResult.orderId] = {
            symbol,
            orderId: orderResult.orderId,
            action: 'Buy',
            quantity: positionSize,
            entryPrice,
            stopLossPrice,
            takeProfitPrice,
            trailFactor: config.trailFactors,
            timestamp: Date.now()
        };
    } catch (error) {
        logger.logSystem(`Error placing long order for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Place a short order
 * @param {string} symbol - Symbol
 */
async function placeShortOrder(symbol) {
    try {
        // Get market data
        const data = marketData[symbol];
        const candles = data.candles;
        const indicators = data.indicators;
        const config = data.config;

        // Calculate position size
        const positionSize = 10; // Fixed position size for demo

        // Calculate stop loss and take profit prices
        const latestCandle = candles[candles.length - 1];
        const stopLossPoints = indicators.atr * config.slFactors;
        const takeProfitPoints = indicators.atr * config.tpFactors;

        const entryPrice = latestCandle.close;
        const stopLossPrice = entryPrice + stopLossPoints;
        const takeProfitPrice = entryPrice - takeProfitPoints;

        // Find contract
        const contract = await tradovateApi.findContract(symbol);
        if (!contract) {
            throw new Error(`Could not find contract for ${symbol}`);
        }

        // Place market order
        const orderParams = {
            accountId: accountId,
            contractId: contract.id,
            action: 'Sell',
            orderQty: positionSize,
            orderType: 'Market',
            isAutomated: true
        };

        const orderResult = await tradovateApi.placeOrder(orderParams);
        if (!orderResult.success) {
            throw new Error(`Failed to place short order for ${symbol}: ${orderResult.error}`);
        }

        logger.logSystem(`Placed short order for ${symbol}: ${JSON.stringify(orderResult)}`, 'info');

        // Store order
        activeOrders[orderResult.orderId] = {
            symbol,
            orderId: orderResult.orderId,
            action: 'Sell',
            quantity: positionSize,
            entryPrice,
            stopLossPrice,
            takeProfitPrice,
            trailFactor: config.trailFactors,
            timestamp: Date.now()
        };
    } catch (error) {
        logger.logSystem(`Error placing short order for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Update trailing stops for all positions
 * @param {string} symbol - Symbol
 */
function updateTrailingStops(symbol) {
    try {
        // Get market data
        const data = marketData[symbol];
        const candles = data.candles;

        // Get latest candle
        const latestCandle = candles[candles.length - 1];

        // Update trailing stops for all positions
        for (const positionId in activePositions) {
            const position = activePositions[positionId];

            // Skip positions for other symbols
            if (position.symbol !== symbol) {
                continue;
            }

            // Update trailing stop for long positions
            if (position.action === 'Buy') {
                // Calculate trailing stop
                const trailAmount = (latestCandle.close - position.entryPrice) * position.trailFactor;
                const newStopPrice = position.entryPrice + trailAmount;

                // Only move stop loss up
                if (newStopPrice > position.stopLossPrice) {
                    position.stopLossPrice = newStopPrice;
                    logger.logSystem(`Updated trailing stop for long position ${positionId} to ${newStopPrice}`, 'info');
                }

                // Check if stop loss is hit
                if (latestCandle.low <= position.stopLossPrice) {
                    logger.logSystem(`Stop loss hit for long position ${positionId}`, 'info');
                    closePosition(position);
                }

                // Check if take profit is hit
                if (latestCandle.high >= position.takeProfitPrice) {
                    logger.logSystem(`Take profit hit for long position ${positionId}`, 'info');
                    closePosition(position);
                }
            }
            // Update trailing stop for short positions
            else if (position.action === 'Sell') {
                // Calculate trailing stop
                const trailAmount = (position.entryPrice - latestCandle.close) * position.trailFactor;
                const newStopPrice = position.entryPrice - trailAmount;

                // Only move stop loss down
                if (newStopPrice < position.stopLossPrice) {
                    position.stopLossPrice = newStopPrice;
                    logger.logSystem(`Updated trailing stop for short position ${positionId} to ${newStopPrice}`, 'info');
                }

                // Check if stop loss is hit
                if (latestCandle.high >= position.stopLossPrice) {
                    logger.logSystem(`Stop loss hit for short position ${positionId}`, 'info');
                    closePosition(position);
                }

                // Check if take profit is hit
                if (latestCandle.low <= position.takeProfitPrice) {
                    logger.logSystem(`Take profit hit for short position ${positionId}`, 'info');
                    closePosition(position);
                }
            }
        }
    } catch (error) {
        logger.logSystem(`Error updating trailing stops for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Close a position
 * @param {Object} position - Position to close
 */
async function closePosition(position) {
    try {
        // Find contract
        const contract = await tradovateApi.findContract(position.symbol);
        if (!contract) {
            throw new Error(`Could not find contract for ${position.symbol}`);
        }

        // Place market order to close position
        const orderParams = {
            accountId: accountId,
            contractId: contract.id,
            action: position.action === 'Buy' ? 'Sell' : 'Buy',
            orderQty: position.quantity,
            orderType: 'Market',
            isAutomated: true
        };

        const orderResult = await tradovateApi.placeOrder(orderParams);
        if (!orderResult.success) {
            throw new Error(`Failed to close position for ${position.symbol}: ${orderResult.error}`);
        }

        logger.logSystem(`Closed position for ${position.symbol}: ${JSON.stringify(orderResult)}`, 'info');

        // Remove position
        delete activePositions[position.id];

        // Update circuit breakers
        updateCircuitBreakers(position, orderResult);
    } catch (error) {
        logger.logSystem(`Error closing position for ${position.symbol}: ${error.message}`, 'error');
    }
}

/**
 * Handle order update
 * @param {Object} orderData - Order data
 */
function handleOrderUpdate(orderData) {
    try {
        logger.logSystem(`Order update: ${JSON.stringify(orderData)}`, 'info');

        // Update active orders
        if (activeOrders[orderData.id]) {
            activeOrders[orderData.id].status = orderData.status;

            // If order is filled, create position
            if (orderData.status === 'Filled') {
                const order = activeOrders[orderData.id];

                // Create position
                activePositions[orderData.id] = {
                    id: orderData.id,
                    symbol: order.symbol,
                    action: order.action,
                    quantity: order.quantity,
                    entryPrice: orderData.avgPrice || order.entryPrice,
                    stopLossPrice: order.stopLossPrice,
                    takeProfitPrice: order.takeProfitPrice,
                    trailFactor: order.trailFactor,
                    timestamp: Date.now()
                };

                logger.logSystem(`Created position for ${order.symbol}: ${JSON.stringify(activePositions[orderData.id])}`, 'info');

                // Remove order
                delete activeOrders[orderData.id];
            }
        }
    } catch (error) {
        logger.logSystem(`Error handling order update: ${error.message}`, 'error');
    }
}

/**
 * Handle position update
 * @param {Object} positionData - Position data
 */
function handlePositionUpdate(positionData) {
    try {
        logger.logSystem(`Position update: ${JSON.stringify(positionData)}`, 'info');

        // Update active positions
        for (const positionId in activePositions) {
            const position = activePositions[positionId];

            if (position.symbol === positionData.contractId) {
                position.quantity = positionData.netPos;
                position.entryPrice = positionData.netPrice;

                logger.logSystem(`Updated position for ${position.symbol}: ${JSON.stringify(position)}`, 'info');

                // If position is closed, remove it
                if (positionData.netPos === 0) {
                    delete activePositions[positionId];
                    logger.logSystem(`Removed position for ${position.symbol}`, 'info');
                }
            }
        }
    } catch (error) {
        logger.logSystem(`Error handling position update: ${error.message}`, 'error');
    }
}

/**
 * Handle account update
 * @param {Object} accountData - Account data
 */
function handleAccountUpdate(accountData) {
    try {
        logger.logSystem(`Account update: ${JSON.stringify(accountData)}`, 'info');

        // Update circuit breakers
        if (accountData.accountId === accountId) {
            // Check if daily loss limit is reached
            const accountBalance = accountData.cashBalance;
            const initialBalance = 10000; // Hardcoded for demo
            const dailyPnL = accountBalance - initialBalance;

            if (dailyPnL < 0 && Math.abs(dailyPnL) / initialBalance > circuitBreakers.maxDailyLoss) {
                logger.logSystem(`Daily loss limit reached: ${dailyPnL}`, 'warning');
                tripCircuitBreakers('Daily loss limit reached');
            }
        }
    } catch (error) {
        logger.logSystem(`Error handling account update: ${error.message}`, 'error');
    }
}

/**
 * Handle fill update
 * @param {Object} fillData - Fill data
 */
function handleFillUpdate(fillData) {
    try {
        logger.logSystem(`Fill update: ${JSON.stringify(fillData)}`, 'info');

        // Update active orders
        if (activeOrders[fillData.orderId]) {
            activeOrders[fillData.orderId].filled = true;
            activeOrders[fillData.orderId].fillPrice = fillData.price;

            logger.logSystem(`Updated order fill for ${activeOrders[fillData.orderId].symbol}: ${JSON.stringify(activeOrders[fillData.orderId])}`, 'info');
        }
    } catch (error) {
        logger.logSystem(`Error handling fill update: ${error.message}`, 'error');
    }
}

/**
 * Update circuit breakers
 * @param {Object} position - Position
 * @param {Object} closeResult - Close result
 */
function updateCircuitBreakers(position, closeResult) {
    try {
        // Calculate P&L
        const entryPrice = position.entryPrice;
        const exitPrice = closeResult.orderData.avgPrice;
        const quantity = position.quantity;

        let pnl = 0;
        if (position.action === 'Buy') {
            pnl = (exitPrice - entryPrice) * quantity;
        } else {
            pnl = (entryPrice - exitPrice) * quantity;
        }

        logger.logSystem(`Position P&L: ${pnl}`, 'info');

        // Update consecutive loss count
        if (pnl < 0) {
            circuitBreakers.consecutiveLossCount++;
            circuitBreakers.dailyLoss += Math.abs(pnl);

            logger.logSystem(`Consecutive loss count: ${circuitBreakers.consecutiveLossCount}`, 'warning');

            // Check if max consecutive losses reached
            if (circuitBreakers.consecutiveLossCount >= circuitBreakers.maxConsecutiveLosses) {
                logger.logSystem(`Max consecutive losses reached: ${circuitBreakers.consecutiveLossCount}`, 'warning');
                tripCircuitBreakers('Max consecutive losses reached');
            }
        } else {
            // Reset consecutive loss count
            circuitBreakers.consecutiveLossCount = 0;
        }
    } catch (error) {
        logger.logSystem(`Error updating circuit breakers: ${error.message}`, 'error');
    }
}

/**
 * Trip circuit breakers
 * @param {string} reason - Reason for tripping
 */
function tripCircuitBreakers(reason) {
    try {
        if (circuitBreakers.isTripped) {
            return;
        }

        circuitBreakers.isTripped = true;
        logger.logSystem(`Circuit breakers tripped: ${reason}`, 'warning');

        // Close all positions
        for (const positionId in activePositions) {
            closePosition(activePositions[positionId]);
        }

        // Cancel all orders
        for (const orderId in activeOrders) {
            cancelOrder(activeOrders[orderId]);
        }

        // Schedule reset for next day
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const timeUntilReset = tomorrow.getTime() - now.getTime();

        setTimeout(() => {
            resetCircuitBreakers();
        }, timeUntilReset);
    } catch (error) {
        logger.logSystem(`Error tripping circuit breakers: ${error.message}`, 'error');
    }
}

/**
 * Reset circuit breakers
 */
function resetCircuitBreakers() {
    try {
        circuitBreakers.isTripped = false;
        circuitBreakers.consecutiveLossCount = 0;
        circuitBreakers.dailyLoss = 0;
        circuitBreakers.lastResetTime = new Date();

        logger.logSystem('Circuit breakers reset', 'info');
    } catch (error) {
        logger.logSystem(`Error resetting circuit breakers: ${error.message}`, 'error');
    }
}

/**
 * Cancel an order
 * @param {Object} order - Order to cancel
 */
async function cancelOrder(order) {
    try {
        // Cancel order
        const cancelResult = await tradovateApi.cancelOrder({
            orderId: order.orderId
        });

        if (!cancelResult.success) {
            throw new Error(`Failed to cancel order for ${order.symbol}: ${cancelResult.error}`);
        }

        logger.logSystem(`Cancelled order for ${order.symbol}: ${JSON.stringify(cancelResult)}`, 'info');

        // Remove order
        delete activeOrders[order.orderId];
    } catch (error) {
        logger.logSystem(`Error cancelling order for ${order.symbol}: ${error.message}`, 'error');
    }
}

/**
 * Start health check
 */
function startHealthCheck() {
    if (healthCheckInterval) {
        clearInterval(healthCheckInterval);
    }

    logger.logSystem('Starting health check', 'info');

    healthCheckInterval = setInterval(() => {
        try {
            // Check WebSocket connection
            if (!isConnected) {
                logger.logSystem('WebSocket connection lost, attempting to reconnect...', 'warning');
                reconnect();
            }

            // Check circuit breakers
            if (circuitBreakers.isTripped) {
                logger.logSystem('Circuit breakers tripped, trading disabled', 'warning');
            }

            // Log active positions and orders
            logger.logSystem(`Active positions: ${Object.keys(activePositions).length}`, 'info');
            logger.logSystem(`Active orders: ${Object.keys(activeOrders).length}`, 'info');
        } catch (error) {
            logger.logSystem(`Health check error: ${error.message}`, 'error');
        }
    }, HEALTH_CHECK_INTERVAL);
}

/**
 * Reconnect to Tradovate API
 */
async function reconnect() {
    try {
        logger.logSystem('Attempting to reconnect...', 'info');

        // Disconnect WebSockets
        tradovateApi.disconnectWebSockets();

        // Authenticate
        const authResult = await tradovateApi.authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }

        // Connect WebSockets
        await connectWebSockets();

        isConnected = true;
        logger.logSystem('Reconnected successfully', 'info');
    } catch (error) {
        logger.logSystem(`Reconnection failed: ${error.message}`, 'error');

        // Schedule another reconnect attempt
        if (!reconnectInterval) {
            reconnectInterval = setInterval(() => {
                if (!isConnected) {
                    reconnect();
                } else {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
            }, RECONNECT_INTERVAL);
        }
    }
}

/**
 * Shutdown the demo trading bot
 * @returns {Promise<boolean>} - True if shutdown was successful
 */
async function shutdown() {
    try {
        logger.logSystem('Shutting down demo trading bot...', 'info');

        // Stop intervals
        if (healthCheckInterval) {
            clearInterval(healthCheckInterval);
            healthCheckInterval = null;
        }

        if (reconnectInterval) {
            clearInterval(reconnectInterval);
            reconnectInterval = null;
        }

        // Close all positions
        for (const positionId in activePositions) {
            await closePosition(activePositions[positionId]);
        }

        // Cancel all orders
        for (const orderId in activeOrders) {
            await cancelOrder(activeOrders[orderId]);
        }

        // Disconnect WebSockets
        tradovateApi.disconnectWebSockets();

        isConnected = false;
        isInitialized = false;

        logger.logSystem('Demo trading bot shutdown complete', 'info');
        console.log('Demo trading bot shutdown complete');

        return true;
    } catch (error) {
        logger.logSystem(`Shutdown failed: ${error.message}`, 'error');
        console.error(`Shutdown failed: ${error.message}`);
        return false;
    }
}

/**
 * Get the status of the demo trading bot
 * @returns {Object} - Status object
 */
function getStatus() {
    return {
        isConnected,
        isInitialized,
        activePositions: Object.keys(activePositions).length,
        activeOrders: Object.keys(activeOrders).length,
        circuitBreakers: {
            isTripped: circuitBreakers.isTripped,
            consecutiveLossCount: circuitBreakers.consecutiveLossCount,
            dailyLoss: circuitBreakers.dailyLoss,
            lastResetTime: circuitBreakers.lastResetTime
        }
    };
}

/**
 * Get active positions
 * @returns {Object} - Active positions
 */
function getPositions() {
    return activePositions;
}

/**
 * Get active orders
 * @returns {Object} - Active orders
 */
function getOrders() {
    return activeOrders;
}

// Export functions
module.exports = {
    initialize,
    shutdown,
    getStatus,
    getPositions,
    getOrders
};
