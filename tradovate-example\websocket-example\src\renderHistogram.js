// renderHistogram.js
// Renders histogram data to HTML

/**
 * Render a histogram item
 * @param {number} offset - Offset from base
 * @param {number} value - Value at the offset
 * @returns {string} HTML representation of the histogram item
 */
const renderHistogramItem = (offset, value) => {
    // Calculate bar width as percentage of maximum value (capped at 100%)
    const maxBarWidth = 100;
    const barWidth = Math.min(value / 10, maxBarWidth);
    
    return `
    <div class="histogram-item">
        <span class="histogram-offset">${offset}</span>
        <div class="histogram-bar-container">
            <div class="histogram-bar" style="width: ${barWidth}%"></div>
        </div>
        <span class="histogram-value">${value}</span>
    </div>
    `;
};

/**
 * Render histogram data to HTML
 * @param {string} symbol - Contract symbol
 * @param {Object} histogramData - Histogram data
 * @param {number} histogramData.contractId - Contract ID
 * @param {string} histogramData.timestamp - Timestamp
 * @param {Object} histogramData.tradeDate - Trade date
 * @param {number} histogramData.base - Base price
 * @param {Object} histogramData.items - Histogram items
 * @param {boolean} histogramData.refresh - Whether to refresh
 * @returns {string} HTML representation of the histogram
 */
export const renderHistogram = (symbol, {
    contractId,
    timestamp,
    tradeDate,
    base,
    items = {},
    refresh
}) => {
    const formattedTime = new Date(timestamp).toLocaleTimeString();
    const formattedDate = tradeDate ? 
        `${tradeDate.year}-${tradeDate.month.toString().padStart(2, '0')}-${tradeDate.day.toString().padStart(2, '0')}` : 
        'N/A';
    
    // Sort items by offset
    const sortedItems = Object.entries(items)
        .sort(([offsetA], [offsetB]) => parseInt(offsetA) - parseInt(offsetB))
        .map(([offset, value]) => renderHistogramItem(offset, value))
        .join('');
    
    return `
    <section class="histogram-container">
        <div class="histogram-header">
            <h2 class="histogram-symbol">${symbol}</h2>
            <div class="histogram-info">
                <span class="histogram-contract-id">Contract ID: ${contractId}</span>
                <span class="histogram-timestamp">Time: ${formattedTime}</span>
                <span class="histogram-date">Date: ${formattedDate}</span>
                <span class="histogram-base">Base: ${base}</span>
                <span class="histogram-refresh">Refresh: ${refresh ? 'Yes' : 'No'}</span>
            </div>
        </div>
        
        <div class="histogram-items">
            ${sortedItems || '<div class="histogram-empty">No histogram data available</div>'}
        </div>
    </section>
    `;
};
