/**
 * Generate CSV Trade Entries using exact backtest.js logic
 * This script uses the same pattern detection and entry logic as backtest.js
 * but only captures trade entry points instead of running full backtest
 */

const fs = require('fs');
const path = require('path');

// Import the exact same config and functions as backtest.js
const config = require('./config');

console.log("=== MNQ Trade Entries CSV Generator (Using Exact Backtest Logic) ===");

// Output settings
const outputDir = 'C:/backtest-bot/output/MNQ_TradeEntries_CSV';
const csvOutputFile = path.join(outputDir, 'MNQ_Trade_Entries_LastYear.csv');

// Create output directory
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`Created output directory: ${outputDir}`);
}

// Trade entries array
let tradeEntries = [];

// Load data using the same logic as backtest.js
console.log("Loading and processing MNQ data...");

// Read the CSV file and parse it exactly like backtest.js does
const csvData = fs.readFileSync(config.inputFile, 'utf8');
const lines = csvData.split('\n');
const headers = lines[0].split(',');

console.log(`Found headers: ${headers.join(', ')}`);

const allCandles = [];

// Parse CSV data (skip header)
for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    const values = line.split(',');
    if (values.length < headers.length) continue;

    const candle = {};
    headers.forEach((header, index) => {
        candle[header.trim()] = values[index];
    });

    // Convert to the format expected by backtest.js
    const processedCandle = {
        timestamp: new Date(candle.ts_event).getTime() / 1000, // Convert to Unix timestamp
        open: parseFloat(candle.open),
        high: parseFloat(candle.high),
        low: parseFloat(candle.low),
        close: parseFloat(candle.close),
        volume: parseInt(candle.volume || 0),
        symbol: candle.symbol
    };

    // Filter for MNQ symbols and last year
    if (processedCandle.symbol && processedCandle.symbol.startsWith('MNQ')) {
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
        const candleDate = new Date(processedCandle.timestamp * 1000);

        if (candleDate >= oneYearAgo && !isNaN(processedCandle.open) && !isNaN(processedCandle.close)) {
            allCandles.push(processedCandle);
        }
    }
}

console.log(`Loaded ${allCandles.length} candles for the last year`);

if (allCandles.length === 0) {
    console.error("No candles loaded. Exiting.");
    process.exit(1);
}

// Calculate indicators for all candles (same as backtest.js)
console.log("Calculating indicators...");
calculateIndicatorsForAllCandles(allCandles);

// Process candles to find trade entries using exact backtest.js logic
console.log("Processing candles to find trade entries...");
findTradeEntries(allCandles);

// Generate CSV file
generateCSV();

// ===== FUNCTIONS (copied from backtest.js) =====

function calculateIndicatorsForAllCandles(candles) {
    for (let i = 0; i < candles.length; i++) {
        const candle = candles[i];

        // Calculate RSI
        candle.rsi = calculateRSI(candles, i, config.rsiPeriod);

        // Calculate RSI MA
        candle.rsiMa = calculateRSIMA(candles, i, config.rsiPeriod, config.rsiMaPeriod);

        // Calculate ATR
        candle.atr = calculateATR(candles, i, config.atrPeriod);

        // Calculate WMA50
        candle.wma50 = calculateWMA(candles, i, config.wma50Period);

        // Calculate SMA200
        candle.sma200 = calculateSMA(candles, i, config.sma200Period);
    }
}

function findTradeEntries(candles) {
    const requiredLookback = 3;
    const startIdx = Math.max(requiredLookback, 50); // Start after indicator warmup

    for (let i = startIdx; i < candles.length; i++) {
        const c0 = candles[i-3];
        const c1 = candles[i-2];
        const c2 = candles[i-1];
        const c3 = candles[i];

        if (!c3) continue;

        // Skip if indicators are invalid (same check as backtest.js)
        if (isNaN(c3.wma50) || isNaN(c3.sma200) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
            continue;
        }

        // Pattern detection (exact same logic as backtest.js)
        const p3 = detect3(c1, c2, c3);
        const p4 = detect4(c0, c1, c2, c3);

        let pat = null, pType = null;
        if (p4) { pat = p4; pType = 'four'; }
        else if (p3) { pat = p3; pType = 'three'; }

        // Check if entry is OK using exact same logic as backtest.js
        if (pat && entryOK(pat, pType, c3, i, candles)) {
            // Convert timestamp to NYC EST
            const nycTime = convertToNYCTime(new Date(c3.timestamp * 1000));

            tradeEntries.push({
                timestamp: nycTime,
                direction: pat,
                price: c3.close,
                pattern: pType,
                rsi: c3.rsi.toFixed(2),
                rsiMa: c3.rsiMa.toFixed(2),
                atr: c3.atr.toFixed(2)
            });

            console.log(`Trade found: ${nycTime} ${pat} (${pType}-candle) at ${c3.close}`);
        }
    }
}

// ===== EXACT FUNCTIONS FROM BACKTEST.JS =====

function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: green-red-green
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }

    // Bearish pattern: red-green-red
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }

    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: green-red-red-green
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }

    // Bearish pattern: red-green-green-red
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }

    return null;
}

function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod) {
    // Basic validation
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
        return false;
    }

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // 200 SMA + 50 WMA confluence filter
    if (config.use200SmaFilter) {
        if (dir === 'bullish') {
            if (c3.wma50 >= c3.sma200 || c3.close <= c3.sma200) {
                return false;
            }
        }
        if (dir === 'bearish') {
            if (c3.wma50 <= c3.sma200 || c3.close >= c3.sma200) {
                return false;
            }
        }
    }

    // RSI filter for all patterns
    if ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa)) {
        return false;
    }

    // Minimum ATR filter
    const minAtrEntry = config.minAtrEntry || 0;
    if (c3.atr < minAtrEntry) {
        return false;
    }

    // RSI-MA separation filter
    const minRsiMaSeparation = config.minRsiMaSeparation || 0;
    if (Math.abs(c3.rsi - c3.rsiMa) < minRsiMaSeparation) {
        return false;
    }

    return true;
}

function candlestickColor(candle) {
    if (!candle || isNaN(candle.open) || isNaN(candle.close)) return 'invalid';

    if (candle.close > candle.open) {
        return 'green';
    } else if (candle.close < candle.open) {
        return 'red';
    } else {
        return 'doji';
    }
}

// Convert timestamp to NYC EST
function convertToNYCTime(utcDate) {
    // Simple EST/EDT conversion
    const month = utcDate.getUTCMonth();
    const isDST = month >= 2 && month <= 10; // Rough DST period
    const offset = isDST ? -4 : -5; // EDT is UTC-4, EST is UTC-5

    const nycTime = new Date(utcDate.getTime() + (offset * 60 * 60 * 1000));
    return nycTime.toISOString().slice(0, 19).replace('T', ' ');
}

// Generate CSV file
function generateCSV() {
    console.log(`\nGenerating CSV file with ${tradeEntries.length} trade entries...`);

    if (tradeEntries.length === 0) {
        console.log("No trade entries found.");
        return;
    }

    // Create CSV content in the exact requested format: YYYY-MM-DD HH:MM:SS long/short
    let csvContent = '';

    tradeEntries.forEach(entry => {
        const direction = entry.direction === 'bullish' ? 'long' : 'short';
        csvContent += `${entry.timestamp} ${direction}\n`;
    });

    // Write CSV file
    fs.writeFileSync(csvOutputFile, csvContent);

    console.log(`\n=== CSV GENERATION COMPLETE ===`);
    console.log(`File saved: ${csvOutputFile}`);
    console.log(`Total trade entries: ${tradeEntries.length}`);
    console.log(`Format: YYYY-MM-DD HH:MM:SS long/short (NYC EST time)`);

    // Show sample entries
    console.log(`\nSample entries:`);
    tradeEntries.slice(0, 10).forEach(entry => {
        const direction = entry.direction === 'bullish' ? 'long' : 'short';
        console.log(`${entry.timestamp} ${direction}`);
    });

    if (tradeEntries.length > 10) {
        console.log(`... and ${tradeEntries.length - 10} more entries`);
    }
}

// ===== INDICATOR CALCULATION FUNCTIONS =====

function calculateRSI(candles, currentIndex, period) {
    if (currentIndex < period) return 50;

    const changes = [];
    for (let i = Math.max(1, currentIndex - period + 1); i <= currentIndex; i++) {
        changes.push(candles[i].close - candles[i - 1].close);
    }

    let avgGain = 0;
    let avgLoss = 0;

    // Initial calculation
    for (let i = 0; i < Math.min(period, changes.length); i++) {
        const change = changes[i];
        if (change >= 0) {
            avgGain += change;
        } else {
            avgLoss += Math.abs(change);
        }
    }

    avgGain /= period;
    avgLoss /= period;

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

function calculateRSIMA(candles, currentIndex, rsiPeriod, smaPeriod) {
    if (currentIndex < rsiPeriod + smaPeriod) return 50;

    const rsiValues = [];

    // Calculate RSI for the last smaPeriod periods
    for (let i = Math.max(rsiPeriod, currentIndex - smaPeriod + 1); i <= currentIndex; i++) {
        rsiValues.push(calculateRSI(candles, i, rsiPeriod));
    }

    // Calculate SMA of RSI values
    const sum = rsiValues.reduce((a, b) => a + b, 0);
    return sum / rsiValues.length;
}

function calculateATR(candles, currentIndex, period) {
    if (currentIndex < period) return 0;

    let trSum = 0;

    for (let i = Math.max(1, currentIndex - period + 1); i <= currentIndex; i++) {
        const current = candles[i];
        const previous = candles[i - 1];

        const tr1 = current.high - current.low;
        const tr2 = Math.abs(current.high - previous.close);
        const tr3 = Math.abs(current.low - previous.close);

        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
    }

    return trSum / period;
}

function calculateWMA(candles, currentIndex, period) {
    if (currentIndex < period - 1) return 0;

    let weightedSum = 0;
    let weightSum = 0;

    for (let i = 0; i < period; i++) {
        const candleIndex = currentIndex - period + 1 + i;
        if (candleIndex >= 0 && candleIndex < candles.length) {
            const weight = i + 1;
            weightedSum += candles[candleIndex].close * weight;
            weightSum += weight;
        }
    }

    return weightSum > 0 ? weightedSum / weightSum : 0;
}

function calculateSMA(candles, currentIndex, period) {
    if (currentIndex < period - 1) return 0;

    let sum = 0;
    for (let i = 0; i < period; i++) {
        const candleIndex = currentIndex - period + 1 + i;
        if (candleIndex >= 0 && candleIndex < candles.length) {
            sum += candles[candleIndex].close;
        }
    }

    return sum / period;
}
