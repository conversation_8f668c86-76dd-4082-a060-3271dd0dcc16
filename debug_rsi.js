const fs = require('fs');
const path = require('path');
const config = require('./config.js');

// --- RSI Calculation Functions ---
function RSI(arr, period, index) { 
    if (!arr || index < period || arr.length <= index) return NaN; 
    let gains = 0, losses = 0; 
    let validDeltas = 0; 
    for (let j = index - period + 1; j <= index; j++) { 
        if (j > 0 && typeof arr[j - 1] === 'number' && !isNaN(arr[j - 1]) && typeof arr[j] === 'number' && !isNaN(arr[j])) { 
            const delta = arr[j] - arr[j - 1]; 
            if (delta > 0) { 
                gains += delta; 
            } else { 
                losses -= delta; 
            } 
            validDeltas++; 
        } else { 
            return NaN; 
        } 
    } 
    if (validDeltas < period) return NaN; 
    if (losses === 0) return 100; 
    if (gains === 0) return 0; 
    const avgGain = gains / period, avgLoss = losses / period, rs = avgGain / avgLoss; 
    return 100 - (100 / (1 + rs)); 
}

function SMA(arr, period, index) { 
    if (!arr || index < period - 1 || arr.length <= index) return NaN; 
    let sum = 0; 
    let validCount = 0; 
    for (let j = index - period + 1; j <= index; j++) { 
        if (typeof arr[j] === 'number' && !isNaN(arr[j])) { 
            sum += arr[j]; 
            validCount++; 
        } else { 
            return NaN; 
        } 
    } 
    return validCount === period ? sum / period : NaN; 
}

// --- Manual RSI Calculation for Verification (Exact Match) ---
function calculateRSIManual(prices, period = 14, targetIndex) {
    if (!prices || targetIndex < period || prices.length <= targetIndex) return null;

    let gains = 0;
    let losses = 0;
    let validDeltas = 0;

    // Calculate gains and losses for the exact same period as our function
    for (let j = targetIndex - period + 1; j <= targetIndex; j++) {
        if (j > 0 && typeof prices[j - 1] === 'number' && !isNaN(prices[j - 1]) && typeof prices[j] === 'number' && !isNaN(prices[j])) {
            const delta = prices[j] - prices[j - 1];
            if (delta > 0) {
                gains += delta;
            } else {
                losses -= delta; // Note: losses is positive
            }
            validDeltas++;
        } else {
            return null;
        }
    }

    if (validDeltas < period) return null;
    if (losses === 0) return { rsi: 100, avgGain: gains / period, avgLoss: 0, rs: Infinity };
    if (gains === 0) return { rsi: 0, avgGain: 0, avgLoss: losses / period, rs: 0 };

    const avgGain = gains / period;
    const avgLoss = losses / period;
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));

    return {
        rsi: rsi,
        avgGain: avgGain,
        avgLoss: avgLoss,
        rs: rs,
        totalGains: gains,
        totalLosses: losses,
        validDeltas: validDeltas
    };
}

// --- Main Debug Function ---
function debugRSI() {
    console.log("🔍 DEBUGGING RSI CALCULATIONS");
    console.log("==============================");
    
    // Load data
    const inputFile = path.join(__dirname, 'input', 'MNQ_lastyear.csv');
    const csvContent = fs.readFileSync(inputFile, 'utf8');
    const lines = csvContent.trim().split('\n');
    const header = lines[0];
    
    console.log(`📊 Loaded ${lines.length - 1} candles`);
    
    // Parse candles
    const candles = [];
    const isDatabentoFormat = header.includes('ts_event');
    
    for (let i = 1; i < Math.min(lines.length, 1000); i++) {
        const parts = lines[i].split(',');
        if (parts.length >= 9) {
            let open, high, low, close, timestampSeconds;
            
            if (isDatabentoFormat) {
                open = parseFloat(parts[4]);
                high = parseFloat(parts[5]);
                low = parseFloat(parts[6]);
                close = parseFloat(parts[7]);
                
                const timeString = parts[0];
                if (timeString) {
                    try {
                        const parsedDate = new Date(timeString);
                        if (!isNaN(parsedDate.getTime())) {
                            timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
            
            if (!isNaN(timestampSeconds) && !isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {
                candles.push({
                    timestamp: timestampSeconds,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: parseFloat(parts[8])
                });
            }
        }
    }
    
    console.log(`✅ Parsed ${candles.length} candles`);
    
    // Extract close prices
    const closes = candles.map(c => c.close);
    
    console.log("\n🧮 RSI CALCULATION VERIFICATION");
    console.log("================================");
    
    // Test RSI calculation at different points
    const testPoints = [50, 100, 200, 300, 500];
    
    for (const testIndex of testPoints) {
        if (testIndex >= candles.length) continue;
        
        console.log(`\n📍 Testing RSI at index ${testIndex}:`);
        console.log(`   Timestamp: ${new Date(candles[testIndex].timestamp * 1000).toISOString()}`);
        console.log(`   Price: ${candles[testIndex].close}`);
        
        // Calculate RSI using our function
        const rsiOur = RSI(closes, config.rsiPeriod, testIndex);
        
        // Calculate RSI manually for verification using exact same logic
        const manualRSI = calculateRSIManual(closes, config.rsiPeriod, testIndex);
        
        console.log(`   RSI (Our Function): ${rsiOur.toFixed(2)}`);
        if (manualRSI) {
            console.log(`   RSI (Manual Calc): ${manualRSI.rsi.toFixed(2)}`);
            console.log(`   Avg Gain: ${manualRSI.avgGain.toFixed(4)}`);
            console.log(`   Avg Loss: ${manualRSI.avgLoss.toFixed(4)}`);
            console.log(`   RS Ratio: ${manualRSI.rs.toFixed(4)}`);
            
            const difference = Math.abs(rsiOur - manualRSI.rsi);
            console.log(`   Difference: ${difference.toFixed(4)} ${difference > 0.01 ? '⚠️ SIGNIFICANT!' : '✅ OK'}`);
        }
        
        // Show recent price changes
        console.log(`   Recent prices (last 5):`);
        for (let i = Math.max(0, testIndex - 4); i <= testIndex; i++) {
            const change = i > 0 ? closes[i] - closes[i-1] : 0;
            console.log(`     [${i}] ${closes[i]} (${change >= 0 ? '+' : ''}${change.toFixed(2)})`);
        }
    }
    
    // Test RSI-MA calculation
    console.log("\n🧮 RSI-MA CALCULATION VERIFICATION");
    console.log("===================================");
    
    // Calculate RSI for all candles first
    for (let i = 0; i < candles.length; i++) {
        candles[i].rsi = RSI(closes, config.rsiPeriod, i);
    }
    
    // Calculate RSI-MA
    const rsiValues = candles.map(c => c.rsi);
    for (let i = 0; i < candles.length; i++) {
        candles[i].rsiMa = SMA(rsiValues, config.rsiMaPeriod, i);
    }
    
    // Test RSI-MA at different points
    for (const testIndex of [100, 200, 300, 500]) {
        if (testIndex >= candles.length) continue;
        
        const candle = candles[testIndex];
        if (!isNaN(candle.rsi) && !isNaN(candle.rsiMa)) {
            console.log(`\n📍 RSI-MA at index ${testIndex}:`);
            console.log(`   RSI: ${candle.rsi.toFixed(2)}`);
            console.log(`   RSI-MA: ${candle.rsiMa.toFixed(2)}`);
            console.log(`   RSI vs RSI-MA: ${candle.rsi > candle.rsiMa ? 'RSI ABOVE' : 'RSI BELOW'} RSI-MA`);
            
            // Show recent RSI values used for MA calculation
            console.log(`   Recent RSI values (last 5):`);
            for (let i = Math.max(0, testIndex - 4); i <= testIndex; i++) {
                if (!isNaN(candles[i].rsi)) {
                    console.log(`     [${i}] RSI: ${candles[i].rsi.toFixed(2)}`);
                }
            }
        }
    }
    
    // Summary statistics
    console.log("\n📊 RSI STATISTICS SUMMARY");
    console.log("==========================");
    
    const validRSI = candles.filter(c => !isNaN(c.rsi)).map(c => c.rsi);
    const validRSIMA = candles.filter(c => !isNaN(c.rsiMa)).map(c => c.rsiMa);
    
    if (validRSI.length > 0) {
        const minRSI = Math.min(...validRSI);
        const maxRSI = Math.max(...validRSI);
        const avgRSI = validRSI.reduce((a, b) => a + b, 0) / validRSI.length;
        
        console.log(`Valid RSI calculations: ${validRSI.length}/${candles.length}`);
        console.log(`RSI Range: ${minRSI.toFixed(2)} - ${maxRSI.toFixed(2)}`);
        console.log(`RSI Average: ${avgRSI.toFixed(2)}`);
    }
    
    if (validRSIMA.length > 0) {
        const minRSIMA = Math.min(...validRSIMA);
        const maxRSIMA = Math.max(...validRSIMA);
        const avgRSIMA = validRSIMA.reduce((a, b) => a + b, 0) / validRSIMA.length;
        
        console.log(`Valid RSI-MA calculations: ${validRSIMA.length}/${candles.length}`);
        console.log(`RSI-MA Range: ${minRSIMA.toFixed(2)} - ${maxRSIMA.toFixed(2)}`);
        console.log(`RSI-MA Average: ${avgRSIMA.toFixed(2)}`);
    }
    
    console.log("\n⚙️ CONFIG SETTINGS:");
    console.log("===================");
    console.log(`RSI Period: ${config.rsiPeriod}`);
    console.log(`RSI MA Period: ${config.rsiMaPeriod}`);
}

// Run the debug
debugRSI();
