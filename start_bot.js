/**
 * Enhanced script to start the trading bot with detailed logging
 *
 * This script starts the trading bot with enhanced logging enabled,
 * ensuring that detailed indicator values and edge criteria are logged.
 */

const demoTrading = require('./demo_trading_optimized');
const logger = require('./data_logger');
const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
    console.log(`Created logs directory at ${logsDir}`);
}

// Configure enhanced logging
logger.setConfig({
    detailedLogging: true
});

// Start the bot
async function start() {
    try {
        // Display startup banner
        console.log('='.repeat(80));
        console.log('STARTING TRADING BOT WITH ENHANCED LOGGING');
        console.log('='.repeat(80));
        console.log('This script starts the trading bot with detailed logging enabled.');
        console.log('You will be able to see WMA, RSI, RSI-MA values and detailed edge criteria logs.');
        console.log('-'.repeat(80));
        console.log('Log files will be saved in the ./logs directory:');
        console.log('- indicators.log: Contains all indicator values');
        console.log('- edge_criteria.log: Contains detailed information about which edge criteria passed/failed');
        console.log('- system.log: Contains general system logs');
        console.log('-'.repeat(80));
        console.log('To check the bot status, run: node check_bot_status.js');
        console.log('='.repeat(80));

        console.log('Starting trading bot...');
        logger.logSystem('Starting trading bot with enhanced logging', 'info');

        // Initialize the demo trading bot
        const result = await demoTrading.initialize();

        if (result) {
            console.log('Trading bot initialized successfully');
            logger.logSystem('Trading bot initialized successfully', 'info');
            logger.setBotRunning(true);

            console.log('Bot is now running with enhanced logging. Press Ctrl+C to stop.');

            // Keep the process running
            process.stdin.resume();

            // Handle process signals
            process.on('SIGINT', async () => {
                console.log('\nReceived SIGINT. Shutting down...');
                logger.logSystem('Received SIGINT signal, shutting down', 'info');
                await shutdown();
            });

            process.on('SIGTERM', async () => {
                console.log('\nReceived SIGTERM. Shutting down...');
                logger.logSystem('Received SIGTERM signal, shutting down', 'info');
                await shutdown();
            });
        } else {
            logger.logSystem('Failed to initialize trading bot', 'error');
            throw new Error('Failed to initialize demo trading bot');
        }
    } catch (error) {
        console.error(`Initialization failed: ${error.message}`);
        logger.logSystem(`Initialization failed: ${error.message}`, 'error');
        process.exit(1);
    }
}

// Shutdown the bot
async function shutdown() {
    try {
        console.log('Shutting down trading bot...');
        logger.logSystem('Shutting down trading bot...', 'info');

        // Set bot running status to false
        logger.setBotRunning(false);

        // Shutdown the demo trading bot
        await demoTrading.shutdown();

        console.log('Trading bot shut down successfully');
        logger.logSystem('Trading bot shut down successfully', 'info');
        process.exit(0);
    } catch (error) {
        console.error(`Shutdown failed: ${error.message}`);
        logger.logSystem(`Shutdown failed: ${error.message}`, 'error');
        process.exit(1);
    }
}

// Start the bot
start();
