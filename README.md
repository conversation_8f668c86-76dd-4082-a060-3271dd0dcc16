# High-Frequency Trading Bot for Tradovate

This trading bot implements a high-frequency trading strategy using pattern recognition and ATR-based trailing stops. It connects to the Tradovate API to execute trades in real-time.

## Features

- **Pattern Recognition**: Uses 3-candle and 4-candle pattern detection for entry signals
- **ATR-Based Trailing Stops**: Adapts to market volatility for optimal exit timing
- **Multiple Instruments**: Trades MNQ, MES, and MGC futures contracts
- **Risk Management**: Implements daily stop loss and other risk controls
- **Comprehensive Logging**: Tracks all trades, performance metrics, and system events
- **Alert System**: Sends notifications for important events

## Backtest Results

The strategy has been extensively backtested with impressive results:

- **MNQ (Micro Nasdaq)**:
  - Win Rate: 76.61%
  - Profit Factor: 27.62
  - Win Day Rate: 100%

- **MES (Micro S&P 500)**:
  - Win Rate: 80.81%
  - Profit Factor: 23.37
  - Win Day Rate: 100%

- **MGC (Micro Gold)**:
  - Win Rate: 79.36%
  - Profit Factor: 21.13
  - Win Day Rate: 100%

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/tradovate-trading-bot.git
   cd tradovate-trading-bot
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file with your Tradovate credentials:
   ```
   TRADOVATE_NAME=your_username
   TRADOVATE_PASSWORD=your_password
   TRADOVATE_APP_ID=Sample App
   TRADOVATE_APP_VERSION=1.0
   TRADOVATE_CID=your_cid
   TRADOVATE_SEC=your_sec
   ```

## Usage

### Running the Bot in Demo Mode

```
npm run start:demo
```

### Running the Bot in Live Mode

```
npm run start:live
```

### Running Backtests

```
npm run run-backtest
```

## Configuration

Each trading instrument has its own configuration file:

- `mnq_config.js` - Configuration for Micro Nasdaq futures
- `mes_config.js` - Configuration for Micro E-mini S&P 500 futures
- `mgc_config.js` - Configuration for Micro Gold futures

You can adjust parameters such as:

- Position size
- Stop loss and take profit factors
- Trailing stop factors
- Risk management settings
- Entry filters

## File Structure

- `main.js` - Main entry point for the trading bot
- `tradovate_api.js` - Handles communication with the Tradovate API
- `trading_logic.js` - Implements the trading strategy and signal generation
- `order_manager.js` - Manages order placement, modification, and cancellation
- `data_logger.js` - Logs trades, performance metrics, and system events
- `alert_system.js` - Sends notifications for important events
- `*_config.js` - Configuration files for each trading instrument
- `backtest_framework.js` - Framework for backtesting the strategy
- `run_backtest.js` - Script for running backtests

## Risk Warning

Trading futures involves substantial risk of loss and is not suitable for all investors. Past performance is not indicative of future results. This trading bot is provided for educational and informational purposes only.

## License

This project is licensed under the ISC License.
