// run_m2k_direct.js - Direct approach to run M2K grid test

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// First, let's check if config_m2k_grid.js exists
if (!fs.existsSync('./config_m2k_grid.js')) {
  console.error('Error: config_m2k_grid.js not found!');
  process.exit(1);
}

// Create a copy of backtest.js with direct config import
const backtestContent = fs.readFileSync('./backtest.js', 'utf8');

// Create a modified version that directly uses config_m2k_grid
const modifiedContent = `
// Modified backtest for M2K grid test
const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Direct import of config_m2k_grid.js
const config = require(${JSON.stringify(path.resolve('./config_m2k_grid.js'))});

${backtestContent.split('const config = require(\'./config\');')[1]}
`;

// Write the modified file
fs.writeFileSync('./m2k_direct_test.js', modifiedContent);

console.log('Created direct test file with absolute path to config.');
console.log('Running M2K grid test...');

try {
  // Run the modified file
  execSync('node m2k_direct_test.js', { stdio: 'inherit' });
  console.log('M2K grid test completed successfully.');
} catch (error) {
  console.error('Error running grid test:', error.message);
} finally {
  // Clean up
  fs.unlinkSync('./m2k_direct_test.js');
  console.log('Cleaned up temporary files.');
}