<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Candlestick Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #333;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin-bottom: 30px;
        }
        .stats {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .stat-box {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-right: 15px;
            margin-bottom: 15px;
            min-width: 150px;
        }
        .stat-box h3 {
            margin-top: 0;
            color: #555;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .symbol-selector {
            margin-bottom: 20px;
        }
        select {
            padding: 8px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .refresh-control {
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        .last-update {
            font-style: italic;
            color: #777;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Trading Bot Candlestick Chart</h1>
        
        <div class="refresh-control">
            <button id="refreshBtn">Refresh Data</button>
            <label>
                <input type="checkbox" id="autoRefresh" checked> Auto-refresh (5s)
            </label>
            <span class="last-update" id="lastUpdate"></span>
        </div>
        
        <div class="symbol-selector">
            <label for="symbolSelect">Select Symbol: </label>
            <select id="symbolSelect"></select>
        </div>
        
        <div class="stats" id="statsContainer">
            <!-- Stats will be populated here -->
        </div>
        
        <h2>Price Chart</h2>
        <div class="chart-container" id="chartContainer"></div>
    </div>

    <script>
        // Global variables
        let chart = null;
        let candleSeries = null;
        let marketData = {};
        let selectedSymbol = null;
        let autoRefreshInterval = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Create chart
            chart = LightweightCharts.createChart(document.getElementById('chartContainer'), {
                width: document.getElementById('chartContainer').clientWidth,
                height: document.getElementById('chartContainer').clientHeight,
                layout: {
                    backgroundColor: '#ffffff',
                    textColor: '#333',
                },
                grid: {
                    vertLines: {
                        color: '#f0f0f0',
                    },
                    horzLines: {
                        color: '#f0f0f0',
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                rightPriceScale: {
                    borderColor: '#d1d4dc',
                },
                timeScale: {
                    borderColor: '#d1d4dc',
                    timeVisible: true,
                    secondsVisible: false,
                },
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                chart.applyOptions({
                    width: document.getElementById('chartContainer').clientWidth,
                    height: document.getElementById('chartContainer').clientHeight,
                });
            });

            // Set up event listeners
            document.getElementById('refreshBtn').addEventListener('click', fetchData);
            document.getElementById('symbolSelect').addEventListener('change', function() {
                selectedSymbol = this.value;
                updateChart();
            });
            document.getElementById('autoRefresh').addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });

            // Initial data fetch
            fetchData();
            
            // Start auto-refresh
            startAutoRefresh();
        });

        // Fetch data from the JSON file
        function fetchData() {
            fetch('market_data.json?' + new Date().getTime())
                .then(response => response.json())
                .then(data => {
                    marketData = data;
                    updateSymbolSelector();
                    updateChart();
                    document.getElementById('lastUpdate').textContent = 'Last updated: ' + new Date().toLocaleTimeString();
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                    document.getElementById('lastUpdate').textContent = 'Error fetching data: ' + error.message;
                });
        }

        // Update the symbol selector dropdown
        function updateSymbolSelector() {
            const symbolSelect = document.getElementById('symbolSelect');
            const currentValue = symbolSelect.value;
            
            // Clear existing options
            symbolSelect.innerHTML = '';
            
            // Add options for each symbol
            Object.keys(marketData).forEach(symbol => {
                const option = document.createElement('option');
                option.value = symbol;
                option.textContent = symbol;
                symbolSelect.appendChild(option);
            });
            
            // Try to restore previous selection or select first symbol
            if (currentValue && symbolSelect.querySelector(`option[value="${currentValue}"]`)) {
                symbolSelect.value = currentValue;
            } else if (Object.keys(marketData).length > 0) {
                symbolSelect.value = Object.keys(marketData)[0];
            }
            
            // Update selected symbol
            selectedSymbol = symbolSelect.value;
        }

        // Update chart with the selected symbol's data
        function updateChart() {
            if (!selectedSymbol || !marketData[selectedSymbol]) return;
            
            const symbolData = marketData[selectedSymbol];
            
            // Update stats
            updateStats(symbolData);
            
            // Update candlestick chart
            updateCandlestickChart(symbolData);
        }

        // Update statistics display
        function updateStats(symbolData) {
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = '';
            
            // Add candle count
            addStatBox(statsContainer, 'Candles', symbolData.candleCount);
            
            // Add last price
            if (symbolData.candles && symbolData.candles.length > 0) {
                const lastCandle = symbolData.candles[symbolData.candles.length - 1];
                addStatBox(statsContainer, 'Last Price', lastCandle.close.toFixed(2));
            }
            
            // Add indicator values if available
            const indicators = symbolData.indicators;
            if (indicators) {
                if (indicators.rsi !== null) addStatBox(statsContainer, 'RSI', indicators.rsi.toFixed(2));
                if (indicators.rsiBasedMA !== null) addStatBox(statsContainer, 'RSI-MA', indicators.rsiBasedMA.toFixed(2));
                if (indicators.atr !== null) addStatBox(statsContainer, 'ATR', indicators.atr.toFixed(5));
                if (indicators.wma50 !== null) addStatBox(statsContainer, 'WMA50', indicators.wma50.toFixed(2));
                if (indicators.atrRegime) addStatBox(statsContainer, 'ATR Regime', indicators.atrRegime);
            }
        }

        // Helper to add a stat box
        function addStatBox(container, label, value) {
            const box = document.createElement('div');
            box.className = 'stat-box';
            box.innerHTML = `<h3>${label}</h3><div class="stat-value">${value}</div>`;
            container.appendChild(box);
        }

        // Update the candlestick chart
        function updateCandlestickChart(symbolData) {
            // Format data for candlestick chart
            const candles = symbolData.candles;
            const formattedData = candles.map(c => ({
                time: new Date(c.time).getTime() / 1000,
                open: c.open,
                high: c.high,
                low: c.low,
                close: c.close
            }));
            
            // Remove existing series if any
            if (candleSeries) {
                chart.removeSeries(candleSeries);
            }
            
            // Create new series
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350'
            });
            
            // Set data
            candleSeries.setData(formattedData);
            
            // Fit content
            chart.timeScale().fitContent();
        }

        // Start auto-refresh
        function startAutoRefresh() {
            if (autoRefreshInterval) clearInterval(autoRefreshInterval);
            autoRefreshInterval = setInterval(fetchData, 5000);
        }

        // Stop auto-refresh
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
    </script>
</body>
</html>
