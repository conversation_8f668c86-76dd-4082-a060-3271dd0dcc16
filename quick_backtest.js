/**
 * Quick 30-Day Backtest
 * 
 * This script runs a quick 30-day backtest to verify the trading logic
 * produces similar results to our previous tests.
 */

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Configuration
const symbols = ['MNQ', 'MES', 'MGC'];
const inputFiles = {
    'MNQ': 'C:\\backtest-bot\\input\\MNQ_2020_2025.csv',
    'MES': 'C:\\backtest-bot\\input\\MES_2020-2025.csv',
    'MGC': 'C:\\backtest-bot\\input\\MGC2020_2025.csv'
};

// Load configurations
const mnqConfig = require('./paper_trading_config');
const mgcConfig = require('./mgc_paper_trading_config');
const mesConfig = {
    ...mnqConfig,
    symbol: 'MES',
    pointValue: 5.00,
    slFactors: 3.0,
    tpFactors: 3.0,
    trailFactors: 0.01,
    fixedTpPoints: 0
};

const configs = {
    'MNQ': mnqConfig,
    'MES': mesConfig,
    'MGC': mgcConfig
};

// ATR Thresholds for adaptive mode
const ATR_THRESHOLDS = {
    MNQ: { low_medium: 4.7601, medium_high: 7.2605 },
    MES: { low_medium: 3.0, medium_high: 5.0 },
    MGC: { low_medium: 1.5, medium_high: 3.0 }
};

// Adaptive parameters for each volatility regime
const ADAPTIVE_PARAMS = {
    MNQ: {
        Low: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
        Medium: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
        High: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 }
    },
    MES: {
        Low: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
        Medium: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
        High: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 }
    },
    MGC: {
        Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
    }
};

// Helper functions
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji';
}

function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;

        if (engulf && wick3 > wick2) return 'bullish';
    }

    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);

        if (engulf && wick3 > wick2) return 'bearish';
    }

    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c2.open)) return 'bullish';
    }

    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c2.open)) return 'bearish';
    }

    return null;
}

// Entry filter
function entryOK(dir, patternType, c3, symbol) {
    // Basic validation
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
        return false;
    }

    // WMA filter
    if ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50)) {
        return false;
    }

    // RSI filter for all patterns
    if ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa)) {
        return false;
    }

    // Minimum ATR filter
    const config = configs[symbol];
    if (config.minAtrEntry && c3.atr < config.minAtrEntry) {
        return false;
    }

    // RSI-MA separation filter
    if (config.minRsiMaSeparation && Math.abs(c3.rsi - c3.rsiMa) < config.minRsiMaSeparation) {
        return false;
    }

    return true;
}

// Calculate indicators
function calculateIndicators(candles, symbol) {
    const config = configs[symbol];
    
    // Calculate WMA50
    for (let i = 0; i < candles.length; i++) {
        if (i >= 50 - 1) {
            let weightedSum = 0;
            let weightSum = 0;
            for (let j = 0; j < 50; j++) {
                const weight = 50 - j;
                weightedSum += candles[i - j].close * weight;
                weightSum += weight;
            }
            candles[i].wma50 = weightedSum / weightSum;
        } else {
            candles[i].wma50 = NaN;
        }
    }
    
    // Calculate RSI
    for (let i = 0; i < candles.length; i++) {
        if (i >= 14) {
            let gains = 0, losses = 0;
            for (let j = i - 13; j <= i; j++) {
                if (j > 0) {
                    const delta = candles[j].close - candles[j - 1].close;
                    if (delta > 0) {
                        gains += delta;
                    } else {
                        losses -= delta;
                    }
                }
            }
            
            const avgGain = gains / 14;
            const avgLoss = losses / 14;
            
            if (avgLoss === 0) {
                candles[i].rsi = 100;
            } else if (avgGain === 0) {
                candles[i].rsi = 0;
            } else {
                const rs = avgGain / avgLoss;
                candles[i].rsi = 100 - (100 / (1 + rs));
            }
        } else {
            candles[i].rsi = NaN;
        }
    }
    
    // Calculate RSI MA
    for (let i = 0; i < candles.length; i++) {
        if (i >= 14 + 8 - 1) {
            let sum = 0;
            for (let j = i - 8 + 1; j <= i; j++) {
                sum += candles[j].rsi;
            }
            candles[i].rsiMa = sum / 8;
        } else {
            candles[i].rsiMa = NaN;
        }
    }
    
    // Calculate ATR
    for (let i = 0; i < candles.length; i++) {
        if (i === 0) {
            candles[i].tr = candles[i].high - candles[i].low;
        } else {
            candles[i].tr = Math.max(
                candles[i].high - candles[i].low,
                Math.abs(candles[i].high - candles[i-1].close),
                Math.abs(candles[i].low - candles[i-1].close)
            );
        }
        
        if (i >= 14 - 1) {
            let sum = 0;
            for (let j = i - 14 + 1; j <= i; j++) {
                sum += candles[j].tr;
            }
            candles[i].atr = sum / 14;
        } else {
            candles[i].atr = NaN;
        }
    }
    
    return candles;
}

// Run backtest for a symbol
async function runBacktest(symbol) {
    return new Promise((resolve, reject) => {
        console.log(`\nRunning 30-day backtest for ${symbol}...`);
        
        const config = configs[symbol];
        const inputFile = inputFiles[symbol];
        
        if (!fs.existsSync(inputFile)) {
            console.error(`Input file not found: ${inputFile}`);
            reject(new Error(`Input file not found: ${inputFile}`));
            return;
        }
        
        const allCandles = [];
        
        fs.createReadStream(inputFile)
            .pipe(csv({ separator: ';', mapHeaders: ({ header }) => header.trim() }))
            .on('data', d => {
                const open = +d['Open'];
                const high = +d['High'];
                const low = +d['Low'];
                const close = +d['Close'];
                const timeString = d['Time'] || d['Date'] || d['Time left'];
                
                let timestampSeconds = NaN;
                if (timeString) {
                    let parsedDate;
                    try {
                        parsedDate = new Date(timeString);
                    } catch (e) {}
                    
                    if (parsedDate && !isNaN(parsedDate.getTime())) {
                        timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                    } else if (timeString && !isNaN(Number(timeString))) {
                        const tsNum = Number(timeString);
                        timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                    }
                }
                
                if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
                    return;
                }
                
                allCandles.push({ timestamp: timestampSeconds, open, high, low, close });
            })
            .on('end', () => {
                if (allCandles.length === 0) {
                    console.error(`No valid candle data parsed from ${inputFile}.`);
                    reject(new Error(`No valid candle data parsed from ${inputFile}.`));
                    return;
                }
                
                console.log(`Parsed ${allCandles.length} candles.`);
                allCandles.sort((a, b) => a.timestamp - b.timestamp);
                
                // Get the last 30 days of data (approximately 30 * 24 * 60 = 43,200 minutes)
                const last30DaysCandles = allCandles.slice(-43200);
                console.log(`Using last ${last30DaysCandles.length} candles for 30-day test.`);
                
                // Calculate indicators
                const candlesWithIndicators = calculateIndicators(last30DaysCandles, symbol);
                
                // Run backtest
                const results = runBacktestLogic(candlesWithIndicators, symbol);
                resolve(results);
            })
            .on('error', (err) => {
                console.error(`Error reading CSV file ${inputFile}:`, err);
                reject(err);
            });
    });
}

// Run backtest logic
function runBacktestLogic(candles, symbol) {
    const config = configs[symbol];
    
    // Initialize variables
    let balance = 10000;
    let peakBalance = balance;
    let maxDrawdown = 0;
    let trades = 0;
    let wins = 0;
    let losses = 0;
    let position = null;
    let tradeLog = [];
    let dailyPnL = new Map();
    let exitCounts = { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, end_of_period: 0 };
    
    // Start from the 4th candle to have enough lookback
    const startIdx = 3;
    
    // Loop through candles
    for (let i = startIdx; i < candles.length; i++) {
        const c0 = candles[i-3];
        const c1 = candles[i-2];
        const c2 = candles[i-1];
        const c3 = candles[i];
        
        // Skip if missing data
        if (!c3) continue;
        
        // Skip if indicators are not valid
        if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
            continue;
        }
        
        // Check for entry patterns if no position
        if (!position) {
            const p3 = detect3(c1, c2, c3);
            const p4 = detect4(c0, c1, c2, c3);
            
            let pattern = null;
            let patternType = null;
            
            if (p4) {
                pattern = p4;
                patternType = 'four';
            } else if (p3) {
                pattern = p3;
                patternType = 'three';
            }
            
            // If pattern found and entry filter passes
            if (pattern && entryOK(pattern, patternType, c3, symbol)) {
                trades++;
                
                // Calculate entry parameters
                const entryPrice = c3.close;
                const atr = c3.atr;
                
                // Determine parameters based on mode (adaptive or fixed)
                let slFactor, tpFactor, trailFactor;
                
                if (config.isAdaptiveRun) {
                    // Determine ATR regime
                    const atrRegime = atr < ATR_THRESHOLDS[symbol].low_medium ? 'Low' :
                                     (atr > ATR_THRESHOLDS[symbol].medium_high ? 'High' : 'Medium');
                    
                    // Get parameters for current regime
                    slFactor = ADAPTIVE_PARAMS[symbol][atrRegime].slFactor;
                    tpFactor = ADAPTIVE_PARAMS[symbol][atrRegime].tpFactor;
                    trailFactor = ADAPTIVE_PARAMS[symbol][atrRegime].trailFactor;
                } else {
                    slFactor = config.slFactors;
                    tpFactor = config.tpFactors;
                    trailFactor = config.trailFactors;
                }
                
                const slDistance = atr * slFactor;
                const tpDistance = Math.max(config.fixedTpPoints || 0, atr * tpFactor);
                const contracts = config.fixedContracts;
                
                // Create position object
                position = {
                    entryTime: new Date(c3.timestamp * 1000),
                    direction: pattern,
                    entry: entryPrice,
                    atr: atr,
                    stopLoss: pattern === 'bullish' ? entryPrice - slDistance : entryPrice + slDistance,
                    takeProfit: pattern === 'bullish' ? entryPrice + tpDistance : entryPrice - tpDistance,
                    trailStop: pattern === 'bullish' ? entryPrice - (atr * trailFactor) : entryPrice + (atr * trailFactor),
                    trailFactor: trailFactor,
                    trailHigh: c3.high,
                    trailLow: c3.low,
                    contracts: contracts,
                    entryBar: i,
                    atrRegime: config.isAdaptiveRun ?
                        (atr < ATR_THRESHOLDS[symbol].low_medium ? 'Low' :
                        (atr > ATR_THRESHOLDS[symbol].medium_high ? 'High' : 'Medium')) :
                        'Fixed'
                };
            }
        }
        
        // Manage existing position
        if (position) {
            // Update trail values
            position.trailHigh = Math.max(position.trailHigh, c3.high);
            position.trailLow = Math.min(position.trailLow, c3.low);
            
            // Update trailing stop
            if (position.direction === 'bullish') {
                position.trailStop = Math.max(position.trailStop, position.trailHigh - (c3.atr * position.trailFactor));
            } else {
                position.trailStop = Math.min(position.trailStop, position.trailLow + (c3.atr * position.trailFactor));
            }
            
            // Check for exit conditions
            let exitReason = null;
            let exitPrice = null;
            
            if (position.direction === 'bullish') {
                // Stop loss hit
                if (c3.low <= position.stopLoss) {
                    exitReason = 'sl';
                    exitPrice = position.stopLoss;
                }
                // Take profit hit
                else if (c3.high >= position.takeProfit) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfit;
                }
                // Trailing stop hit
                else if (c3.low <= position.trailStop) {
                    exitReason = 'trail';
                    exitPrice = position.trailStop;
                }
            } else { // Bearish position
                // Stop loss hit
                if (c3.high >= position.stopLoss) {
                    exitReason = 'sl';
                    exitPrice = position.stopLoss;
                }
                // Take profit hit
                else if (c3.low <= position.takeProfit) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfit;
                }
                // Trailing stop hit
                else if (c3.high >= position.trailStop) {
                    exitReason = 'trail';
                    exitPrice = position.trailStop;
                }
            }
            
            // Process exit if triggered
            if (exitReason && exitPrice !== null) {
                // Calculate P&L
                let pnlPoints = 0;
                if (position.direction === 'bullish') {
                    pnlPoints = exitPrice - position.entry;
                } else {
                    pnlPoints = position.entry - exitPrice;
                }
                
                // Calculate dollar P&L
                const pnl = pnlPoints * config.pointValue * position.contracts;
                
                // Update balance
                balance += pnl;
                
                // Update peak balance and drawdown
                if (balance > peakBalance) {
                    peakBalance = balance;
                } else {
                    const drawdown = (peakBalance - balance) / peakBalance * 100;
                    maxDrawdown = Math.max(maxDrawdown, drawdown);
                }
                
                // Update win/loss count
                if (pnl > 0) {
                    wins++;
                } else {
                    losses++;
                }
                
                // Update exit counts
                exitCounts[exitReason]++;
                
                // Log trade
                const trade = {
                    symbol,
                    direction: position.direction,
                    entry: position.entry,
                    exit: exitPrice,
                    entryTime: position.entryTime,
                    exitTime: new Date(c3.timestamp * 1000),
                    pnl,
                    exitReason,
                    contracts: position.contracts,
                    atrRegime: position.atrRegime
                };
                
                tradeLog.push(trade);
                
                // Reset position
                position = null;
            }
        }
    }
    
    // Close any open position at the end
    if (position) {
        const lastCandle = candles[candles.length - 1];
        const exitPrice = lastCandle.close;
        
        // Calculate P&L
        let pnlPoints = 0;
        if (position.direction === 'bullish') {
            pnlPoints = exitPrice - position.entry;
        } else {
            pnlPoints = position.entry - exitPrice;
        }
        
        // Calculate dollar P&L
        const pnl = pnlPoints * config.pointValue * position.contracts;
        
        // Update balance
        balance += pnl;
        
        // Update win/loss count
        if (pnl > 0) {
            wins++;
        } else {
            losses++;
        }
        
        // Update exit counts
        exitCounts.end_of_period++;
        
        // Log trade
        const trade = {
            symbol,
            direction: position.direction,
            entry: position.entry,
            exit: exitPrice,
            entryTime: position.entryTime,
            exitTime: new Date(lastCandle.timestamp * 1000),
            pnl,
            exitReason: 'end_of_period',
            contracts: position.contracts,
            atrRegime: position.atrRegime
        };
        
        tradeLog.push(trade);
    }
    
    // Calculate statistics
    const winRate = trades > 0 ? (wins / trades * 100) : 0;
    const profitFactor = losses > 0 ? (wins / losses) : 0;
    const totalPnL = balance - 10000;
    const totalPnLPercent = totalPnL / 10000 * 100;
    
    // Calculate average win and loss
    let totalWinAmount = 0;
    let totalLossAmount = 0;
    
    for (const trade of tradeLog) {
        if (trade.pnl > 0) {
            totalWinAmount += trade.pnl;
        } else {
            totalLossAmount += Math.abs(trade.pnl);
        }
    }
    
    const avgWin = wins > 0 ? (totalWinAmount / wins) : 0;
    const avgLoss = losses > 0 ? (totalLossAmount / losses) : 0;
    
    // Return results
    return {
        symbol,
        totalPnL,
        totalPnLPercent,
        balance,
        trades,
        wins,
        losses,
        winRate,
        profitFactor,
        maxDrawdown,
        avgWin,
        avgLoss,
        exitCounts,
        tradeLog
    };
}

// Main function
async function main() {
    try {
        console.log('Running 30-day backtest to verify trading logic...');
        
        const results = {};
        
        for (const symbol of symbols) {
            results[symbol] = await runBacktest(symbol);
        }
        
        // Print results
        console.log('\n=== 30-Day Backtest Results ===');
        
        for (const symbol of symbols) {
            const result = results[symbol];
            
            console.log(`\n${symbol} Results:`);
            console.log(`Total P&L: $${result.totalPnL.toFixed(2)} (${result.totalPnLPercent.toFixed(2)}%)`);
            console.log(`Final Balance: $${result.balance.toFixed(2)}`);
            console.log(`Total Trades: ${result.trades}`);
            console.log(`Wins: ${result.wins} (${result.winRate.toFixed(2)}%)`);
            console.log(`Losses: ${result.losses}`);
            console.log(`Profit Factor: ${result.profitFactor.toFixed(2)}`);
            console.log(`Max Drawdown: ${result.maxDrawdown.toFixed(2)}%`);
            console.log(`Average Win: $${result.avgWin.toFixed(2)}`);
            console.log(`Average Loss: $${result.avgLoss.toFixed(2)}`);
            console.log(`Exit Types: SL=${result.exitCounts.sl}, TP=${result.exitCounts.tp}, Trail=${result.exitCounts.trail}, End=${result.exitCounts.end_of_period}`);
        }
        
        console.log('\nBacktest completed successfully!');
    } catch (error) {
        console.error('Error running backtest:', error);
    }
}

// Run the main function
main();
