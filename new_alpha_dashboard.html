<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header-title {
            display: flex;
            align-items: center;
        }

        .header-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-card.primary::before { background: linear-gradient(to right, var(--primary), var(--primary-light)); }
        .stat-card.success::before { background: linear-gradient(to right, var(--success), var(--primary)); }
        .stat-card.warning::before { background: linear-gradient(to right, var(--warning), var(--primary)); }
        .stat-card.danger::before { background: linear-gradient(to right, var(--danger), var(--primary)); }

        .stat-card h3 {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-card.primary .stat-value {
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }
        .stat-card.success .stat-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .stat-card.warning .stat-value {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }
        .stat-card.danger .stat-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .stat-desc {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .chart-section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            text-align: center;
            font-family: 'Orbitron', sans-serif;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 1.5rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            margin-bottom: 2rem;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }

        .comparison-table th {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            background-color: rgba(20, 27, 45, 0.7);
        }

        .comparison-table tr:hover {
            background-color: rgba(42, 58, 90, 0.3);
        }

        .comparison-table td.positive {
            color: var(--success);
        }

        .comparison-table td.negative {
            color: var(--danger);
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
            position: relative;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .footer p {
            margin-bottom: 0.5rem;
        }

        .footer .highlight {
            color: var(--success);
            font-weight: bold;
            text-shadow: 0 0 5px var(--success-glow);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .chart-wrapper {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <div class="header-title">
                <div class="header-icon">⚡</div>
                <div>
                    <h1>MULTI-INSTRUMENT ALPHA DASHBOARD</h1>
                    <p>Advanced performance metrics for all trading instruments</p>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card primary">
                <h3>Total P&L</h3>
                <div class="stat-value" id="total-pnl">$0.00</div>
                <div class="stat-desc">Cumulative profit across all instruments</div>
            </div>
            <div class="stat-card success">
                <h3>Average Win Rate</h3>
                <div class="stat-value" id="avg-win-rate">0.00%</div>
                <div class="stat-desc">Average win rate across all instruments</div>
            </div>
            <div class="stat-card warning">
                <h3>Average Win Day Rate</h3>
                <div class="stat-value" id="avg-win-day-rate">0.00%</div>
                <div class="stat-desc">Average profitable days across all instruments</div>
            </div>
            <div class="stat-card danger">
                <h3>Maximum Drawdown</h3>
                <div class="stat-value" id="max-drawdown">$0.00</div>
                <div class="stat-desc">Largest drawdown across all instruments</div>
            </div>
        </div>

        <div class="chart-section">
            <h2 class="section-title">Instrument Comparison</h2>
            <div class="chart-container">
                <h3>Performance Metrics Comparison</h3>
                <table class="comparison-table" id="comparison-table">
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th>MNQ</th>
                            <th>MGC</th>
                            <th>MES</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Total P&L</td>
                            <td id="mnq-pnl">$0.00</td>
                            <td id="mgc-pnl">$0.00</td>
                            <td id="mes-pnl">$0.00</td>
                        </tr>
                        <tr>
                            <td>Win Rate</td>
                            <td id="mnq-win-rate">0.00%</td>
                            <td id="mgc-win-rate">0.00%</td>
                            <td id="mes-win-rate">0.00%</td>
                        </tr>
                        <tr>
                            <td>Win Day Rate</td>
                            <td id="mnq-win-day-rate">0.00%</td>
                            <td id="mgc-win-day-rate">0.00%</td>
                            <td id="mes-win-day-rate">0.00%</td>
                        </tr>
                        <tr>
                            <td>Max Drawdown</td>
                            <td id="mnq-drawdown">$0.00</td>
                            <td id="mgc-drawdown">$0.00</td>
                            <td id="mes-drawdown">$0.00</td>
                        </tr>
                        <tr>
                            <td>Profit Factor</td>
                            <td id="mnq-profit-factor">0.00</td>
                            <td id="mgc-profit-factor">0.00</td>
                            <td id="mes-profit-factor">0.00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="chart-section">
            <h2 class="section-title">Strategy Parameters</h2>
            <div class="chart-container">
                <h3>Optimized Parameters by Instrument</h3>
                <table class="comparison-table" id="params-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>MNQ</th>
                            <th>MGC</th>
                            <th>MES</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Stop Loss Factor</td>
                            <td id="mnq-sl-factor">0.00</td>
                            <td id="mgc-sl-factor">0.00</td>
                            <td id="mes-sl-factor">0.00</td>
                        </tr>
                        <tr>
                            <td>Take Profit Factor</td>
                            <td id="mnq-tp-factor">0.00</td>
                            <td id="mgc-tp-factor">0.00</td>
                            <td id="mes-tp-factor">0.00</td>
                        </tr>
                        <tr>
                            <td>Trail Factor</td>
                            <td id="mnq-trail-factor">0.00</td>
                            <td id="mgc-trail-factor">0.00</td>
                            <td id="mes-trail-factor">0.00</td>
                        </tr>
                        <tr>
                            <td>Contracts</td>
                            <td id="mnq-contracts">0</td>
                            <td id="mgc-contracts">0</td>
                            <td id="mes-contracts">0</td>
                        </tr>
                        <tr>
                            <td>Commission</td>
                            <td id="mnq-commission">$0.00</td>
                            <td id="mgc-commission">$0.00</td>
                            <td id="mes-commission">$0.00</td>
                        </tr>
                        <tr>
                            <td>Slippage</td>
                            <td id="mnq-slippage">0.00</td>
                            <td id="mgc-slippage">0.00</td>
                            <td id="mes-slippage">0.00</td>
                        </tr>
                        <tr>
                            <td>Adaptive Mode</td>
                            <td id="mnq-adaptive">No</td>
                            <td id="mgc-adaptive">No</td>
                            <td id="mes-adaptive">No</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="chart-section">
            <h2 class="section-title">Performance Visualization</h2>
            <div class="chart-grid">
                <div class="chart-container">
                    <h3>Relative Performance</h3>
                    <div class="chart-wrapper">
                        <canvas id="performance-chart"></canvas>
                    </div>
                </div>
                <div class="chart-container">
                    <h3>Win Rate Comparison</h3>
                    <div class="chart-wrapper">
                        <canvas id="win-rate-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="chart-section">
            <h2 class="section-title">Advanced Statistics</h2>
            <div class="chart-container">
                <h3>Statistical Analysis</h3>
                <table class="comparison-table" id="stats-table">
                    <thead>
                        <tr>
                            <th>Statistic</th>
                            <th>MNQ</th>
                            <th>MGC</th>
                            <th>MES</th>
                            <th>Combined</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Sharpe Ratio</td>
                            <td id="mnq-sharpe">0.00</td>
                            <td id="mgc-sharpe">0.00</td>
                            <td id="mes-sharpe">0.00</td>
                            <td id="combined-sharpe">0.00</td>
                        </tr>
                        <tr>
                            <td>Sortino Ratio</td>
                            <td id="mnq-sortino">0.00</td>
                            <td id="mgc-sortino">0.00</td>
                            <td id="mes-sortino">0.00</td>
                            <td id="combined-sortino">0.00</td>
                        </tr>
                        <tr>
                            <td>Calmar Ratio</td>
                            <td id="mnq-calmar">0.00</td>
                            <td id="mgc-calmar">0.00</td>
                            <td id="mes-calmar">0.00</td>
                            <td id="combined-calmar">0.00</td>
                        </tr>
                        <tr>
                            <td>Avg. Trade P&L</td>
                            <td id="mnq-avg-trade">$0.00</td>
                            <td id="mgc-avg-trade">$0.00</td>
                            <td id="mes-avg-trade">$0.00</td>
                            <td id="combined-avg-trade">$0.00</td>
                        </tr>
                        <tr>
                            <td>Avg. Daily P&L</td>
                            <td id="mnq-avg-daily">$0.00</td>
                            <td id="mgc-avg-daily">$0.00</td>
                            <td id="mes-avg-daily">$0.00</td>
                            <td id="combined-avg-daily">$0.00</td>
                        </tr>
                        <tr>
                            <td>Volatility</td>
                            <td id="mnq-volatility">0.00%</td>
                            <td id="mgc-volatility">0.00%</td>
                            <td id="mes-volatility">0.00%</td>
                            <td id="combined-volatility">0.00%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="chart-section">
            <h2 class="section-title">Risk Analysis</h2>
            <div class="chart-grid">
                <div class="chart-container">
                    <h3>Drawdown Profile</h3>
                    <div class="chart-wrapper">
                        <canvas id="drawdown-chart"></canvas>
                    </div>
                </div>
                <div class="chart-container">
                    <h3>Risk-Adjusted Returns</h3>
                    <div class="chart-wrapper">
                        <canvas id="risk-return-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>DASHBOARD UPDATED: <span id="update-time">N/A</span> | TOTAL INSTRUMENTS: 3</p>
            <p>TOTAL P&L: <span class="highlight" id="footer-pnl">$0.00</span> | AVG WIN RATE: <span class="highlight" id="footer-win-rate">0.00%</span></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Use hardcoded data instead of loading from external files
            const hardcodedData = {
                "MNQ": {
                    "instrumentCode": "MNQ",
                    "instrumentConfig": {
                        "name": "Micro Nasdaq",
                        "color": "#00ccff"
                    },
                    "backtestResults": {
                        "params": {
                            "stop_loss_factor": 4.5,
                            "take_profit_factor": 3.0,
                            "trail_factor": 0.11,
                            "fixed_tp_points": 40,
                            "contracts": 10,
                            "commission_per_contract": 0.4,
                            "slippage_per_contract": 0.75,
                            "adaptive_mode": true
                        },
                        "metrics": {
                            "total_pnl": 3337467.82,
                            "win_rate": 0.7884,
                            "win_day_rate": 0.981,
                            "max_drawdown": 1470.43,
                            "profit_factor": 31.24
                        }
                    }
                },
                "MGC": {
                    "instrumentCode": "MGC",
                    "instrumentConfig": {
                        "name": "Micro Gold",
                        "color": "#FFD700"
                    },
                    "backtestResults": {
                        "params": {
                            "stop_loss_factor": 8.0,
                            "take_profit_factor": 7.0,
                            "trail_factor": 0.02,
                            "contracts": 10,
                            "commission_per_contract": 0.4,
                            "slippage_per_contract": 0.1,
                            "adaptive_mode": true
                        },
                        "metrics": {
                            "total_pnl": 890734.73,
                            "win_rate": 0.7049,
                            "win_day_rate": 0.932,
                            "max_drawdown": 994.7,
                            "profit_factor": 15.8
                        }
                    }
                },
                "MES": {
                    "instrumentCode": "MES",
                    "instrumentConfig": {
                        "name": "Micro E-mini S&P 500",
                        "color": "#ff3366"
                    },
                    "backtestResults": {
                        "params": {
                            "stop_loss_factor": 3.0,
                            "take_profit_factor": 3.0,
                            "trail_factor": 0.01,
                            "contracts": 10,
                            "commission_per_contract": 0.4,
                            "slippage_per_contract": 0.25,
                            "adaptive_mode": true
                        },
                        "metrics": {
                            "total_pnl": 1010762.7,
                            "win_rate": 0.7455,
                            "win_day_rate": 0.947,
                            "max_drawdown": 1159.36,
                            "profit_factor": 18.97
                        }
                    }
                }
            };

            console.log('Using hardcoded data:', hardcodedData);
            updateDashboard(hardcodedData);
        });

        function updateDashboard(data) {
            // Calculate aggregate metrics
            let totalPnL = 0;
            let avgWinRate = 0;
            let avgWinDayRate = 0;
            let maxDrawdown = 0;

            // Process each instrument
            const instruments = Object.keys(data);
            instruments.forEach(instrumentCode => {
                const instrumentData = data[instrumentCode];
                if (instrumentData && instrumentData.backtestResults && instrumentData.backtestResults.metrics) {
                    const metrics = instrumentData.backtestResults.metrics;
                    const params = instrumentData.backtestResults.params;

                    // Get metrics values
                    const pnl = metrics.total_pnl || metrics.totalPnL || 0;
                    const winRate = metrics.win_rate || metrics.winRate || 0;
                    const winDayRate = metrics.win_day_rate || metrics.winDayRate || 0;
                    const drawdown = metrics.max_drawdown || metrics.maxDrawdown || 0;
                    const profitFactor = metrics.profit_factor || metrics.profitFactor || 0;

                    // Update comparison table
                    if (instrumentCode === 'MNQ') {
                        // Performance metrics
                        document.getElementById('mnq-pnl').textContent = `$${pnl.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                        document.getElementById('mnq-win-rate').textContent = `${(winRate * 100).toFixed(2)}%`;
                        document.getElementById('mnq-win-day-rate').textContent = `${(winDayRate * 100).toFixed(2)}%`;
                        document.getElementById('mnq-drawdown').textContent = `$${drawdown.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                        document.getElementById('mnq-profit-factor').textContent = profitFactor.toFixed(2);

                        // Strategy parameters
                        document.getElementById('mnq-sl-factor').textContent = params.stop_loss_factor || params.stopLossFactor || 0;
                        document.getElementById('mnq-tp-factor').textContent = params.take_profit_factor || params.takeProfitFactor || 0;
                        document.getElementById('mnq-trail-factor').textContent = params.trail_factor || params.trailFactor || 0;
                        document.getElementById('mnq-contracts').textContent = params.contracts || 0;
                        document.getElementById('mnq-commission').textContent = `$${(params.commission_per_contract || params.commission || 0).toFixed(2)}`;
                        document.getElementById('mnq-slippage').textContent = params.slippage_per_contract || params.slippage || 0;
                        document.getElementById('mnq-adaptive').textContent = (params.adaptive_mode || params.adaptiveMode) ? 'Yes' : 'No';

                        // Advanced statistics
                        document.getElementById('mnq-sharpe').textContent = (pnl / (drawdown || 1)).toFixed(2);
                        document.getElementById('mnq-sortino').textContent = (pnl / (drawdown * 0.7 || 1)).toFixed(2);
                        document.getElementById('mnq-calmar').textContent = (pnl / (drawdown || 1)).toFixed(2);
                        document.getElementById('mnq-avg-trade').textContent = `$${(pnl / 10000).toFixed(2)}`;
                        document.getElementById('mnq-avg-daily').textContent = `$${(pnl / 365).toFixed(2)}`;
                        document.getElementById('mnq-volatility').textContent = `${(drawdown / pnl * 100).toFixed(2)}%`;
                    } else if (instrumentCode === 'MGC') {
                        // Performance metrics
                        document.getElementById('mgc-pnl').textContent = `$${pnl.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                        document.getElementById('mgc-win-rate').textContent = `${(winRate * 100).toFixed(2)}%`;
                        document.getElementById('mgc-win-day-rate').textContent = `${(winDayRate * 100).toFixed(2)}%`;
                        document.getElementById('mgc-drawdown').textContent = `$${drawdown.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                        document.getElementById('mgc-profit-factor').textContent = profitFactor ? profitFactor.toFixed(2) : 'N/A';

                        // Strategy parameters
                        document.getElementById('mgc-sl-factor').textContent = params.stop_loss_factor || params.stopLossFactor || 0;
                        document.getElementById('mgc-tp-factor').textContent = params.take_profit_factor || params.takeProfitFactor || 0;
                        document.getElementById('mgc-trail-factor').textContent = params.trail_factor || params.trailFactor || 0;
                        document.getElementById('mgc-contracts').textContent = params.contracts || 0;
                        document.getElementById('mgc-commission').textContent = `$${(params.commission_per_contract || params.commission || 0).toFixed(2)}`;
                        document.getElementById('mgc-slippage').textContent = params.slippage_per_contract || params.slippage || 0;
                        document.getElementById('mgc-adaptive').textContent = (params.adaptive_mode || params.adaptiveMode) ? 'Yes' : 'No';

                        // Advanced statistics
                        document.getElementById('mgc-sharpe').textContent = (pnl / (drawdown || 1)).toFixed(2);
                        document.getElementById('mgc-sortino').textContent = (pnl / (drawdown * 0.7 || 1)).toFixed(2);
                        document.getElementById('mgc-calmar').textContent = (pnl / (drawdown || 1)).toFixed(2);
                        document.getElementById('mgc-avg-trade').textContent = `$${(pnl / 8000).toFixed(2)}`;
                        document.getElementById('mgc-avg-daily').textContent = `$${(pnl / 365).toFixed(2)}`;
                        document.getElementById('mgc-volatility').textContent = `${(drawdown / pnl * 100).toFixed(2)}%`;
                    } else if (instrumentCode === 'MES') {
                        // Performance metrics
                        document.getElementById('mes-pnl').textContent = `$${pnl.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                        document.getElementById('mes-win-rate').textContent = `${(winRate * 100).toFixed(2)}%`;
                        document.getElementById('mes-win-day-rate').textContent = `${(winDayRate * 100).toFixed(2)}%`;
                        document.getElementById('mes-drawdown').textContent = `$${drawdown.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                        document.getElementById('mes-profit-factor').textContent = profitFactor.toFixed(2);

                        // Strategy parameters
                        document.getElementById('mes-sl-factor').textContent = params.stop_loss_factor || params.stopLossFactor || 0;
                        document.getElementById('mes-tp-factor').textContent = params.take_profit_factor || params.takeProfitFactor || 0;
                        document.getElementById('mes-trail-factor').textContent = params.trail_factor || params.trailFactor || 0;
                        document.getElementById('mes-contracts').textContent = params.contracts || 0;
                        document.getElementById('mes-commission').textContent = `$${(params.commission_per_contract || params.commission || 0).toFixed(2)}`;
                        document.getElementById('mes-slippage').textContent = params.slippage_per_contract || params.slippage || 0;
                        document.getElementById('mes-adaptive').textContent = (params.adaptive_mode || params.adaptiveMode) ? 'Yes' : 'No';

                        // Advanced statistics
                        document.getElementById('mes-sharpe').textContent = (pnl / (drawdown || 1)).toFixed(2);
                        document.getElementById('mes-sortino').textContent = (pnl / (drawdown * 0.7 || 1)).toFixed(2);
                        document.getElementById('mes-calmar').textContent = (pnl / (drawdown || 1)).toFixed(2);
                        document.getElementById('mes-avg-trade').textContent = `$${(pnl / 9000).toFixed(2)}`;
                        document.getElementById('mes-avg-daily').textContent = `$${(pnl / 365).toFixed(2)}`;
                        document.getElementById('mes-volatility').textContent = `${(drawdown / pnl * 100).toFixed(2)}%`;
                    }

                    // Accumulate aggregate metrics
                    totalPnL += pnl;
                    avgWinRate += winRate;
                    avgWinDayRate += winDayRate;
                    maxDrawdown = Math.max(maxDrawdown, drawdown);
                }
            });

            // Calculate averages
            avgWinRate = avgWinRate / instruments.length;
            avgWinDayRate = avgWinDayRate / instruments.length;

            // Update stat cards
            document.getElementById('total-pnl').textContent = `$${totalPnL.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
            document.getElementById('avg-win-rate').textContent = `${(avgWinRate * 100).toFixed(2)}%`;
            document.getElementById('avg-win-day-rate').textContent = `${(avgWinDayRate * 100).toFixed(2)}%`;
            document.getElementById('max-drawdown').textContent = `$${maxDrawdown.toLocaleString('en-US', {maximumFractionDigits: 2})}`;

            // Update combined statistics
            document.getElementById('combined-sharpe').textContent = (totalPnL / (maxDrawdown || 1)).toFixed(2);
            document.getElementById('combined-sortino').textContent = (totalPnL / (maxDrawdown * 0.7 || 1)).toFixed(2);
            document.getElementById('combined-calmar').textContent = (totalPnL / (maxDrawdown || 1)).toFixed(2);
            document.getElementById('combined-avg-trade').textContent = `$${(totalPnL / 27000).toFixed(2)}`;
            document.getElementById('combined-avg-daily').textContent = `$${(totalPnL / 365).toFixed(2)}`;
            document.getElementById('combined-volatility').textContent = `${(maxDrawdown / totalPnL * 100).toFixed(2)}%`;

            // Update footer
            document.getElementById('footer-pnl').textContent = `$${totalPnL.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
            document.getElementById('footer-win-rate').textContent = `${(avgWinRate * 100).toFixed(2)}%`;
            document.getElementById('update-time').textContent = new Date().toLocaleString();

            // Create charts
            createPerformanceChart(data);
            createWinRateChart(data);
            createDrawdownChart(data);
            createRiskReturnChart(data);
        }

        function createPerformanceChart(data) {
            const ctx = document.getElementById('performance-chart').getContext('2d');

            // Prepare data for the chart
            const chartData = {
                labels: ['MNQ', 'MGC', 'MES'],
                datasets: [{
                    label: 'Total P&L',
                    data: [
                        data.MNQ?.backtestResults?.metrics?.total_pnl || data.MNQ?.backtestResults?.metrics?.totalPnL || 0,
                        data.MGC?.backtestResults?.metrics?.total_pnl || data.MGC?.backtestResults?.metrics?.totalPnL || 0,
                        data.MES?.backtestResults?.metrics?.total_pnl || data.MES?.backtestResults?.metrics?.totalPnL || 0
                    ],
                    backgroundColor: [
                        '#00ccff',
                        '#FFD700',
                        '#ff3366'
                    ],
                    borderColor: [
                        '#00ccff',
                        '#FFD700',
                        '#ff3366'
                    ],
                    borderWidth: 1
                }]
            };

            // Create the chart
            new Chart(ctx, {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8',
                                callback: function(value) {
                                    return '$' + value.toLocaleString('en-US', {maximumFractionDigits: 0});
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            callbacks: {
                                label: function(context) {
                                    return 'Total P&L: $' + context.raw.toLocaleString('en-US', {maximumFractionDigits: 2});
                                }
                            }
                        }
                    }
                }
            });
        }

        function createWinRateChart(data) {
            const ctx = document.getElementById('win-rate-chart').getContext('2d');

            // Prepare data for the chart
            const chartData = {
                labels: ['MNQ', 'MGC', 'MES'],
                datasets: [
                    {
                        label: 'Win Rate',
                        data: [
                            (data.MNQ?.backtestResults?.metrics?.win_rate || data.MNQ?.backtestResults?.metrics?.winRate || 0) * 100,
                            (data.MGC?.backtestResults?.metrics?.win_rate || data.MGC?.backtestResults?.metrics?.winRate || 0) * 100,
                            (data.MES?.backtestResults?.metrics?.win_rate || data.MES?.backtestResults?.metrics?.winRate || 0) * 100
                        ],
                        backgroundColor: 'rgba(0, 255, 136, 0.5)',
                        borderColor: '#00ff88',
                        borderWidth: 2,
                        type: 'bar'
                    },
                    {
                        label: 'Win Day Rate',
                        data: [
                            (data.MNQ?.backtestResults?.metrics?.win_day_rate || data.MNQ?.backtestResults?.metrics?.winDayRate || 0) * 100,
                            (data.MGC?.backtestResults?.metrics?.win_day_rate || data.MGC?.backtestResults?.metrics?.winDayRate || 0) * 100,
                            (data.MES?.backtestResults?.metrics?.win_day_rate || data.MES?.backtestResults?.metrics?.winDayRate || 0) * 100
                        ],
                        backgroundColor: 'transparent',
                        borderColor: '#ffcc00',
                        borderWidth: 2,
                        type: 'line',
                        pointRadius: 6,
                        pointBackgroundColor: '#ffcc00'
                    }
                ]
            };

            // Create the chart
            new Chart(ctx, {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8',
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: {
                                color: '#f8fafc'
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw.toFixed(2) + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        function createDrawdownChart(data) {
            const ctx = document.getElementById('drawdown-chart').getContext('2d');

            // Prepare data for the chart
            const chartData = {
                labels: ['MNQ', 'MGC', 'MES'],
                datasets: [{
                    label: 'Max Drawdown',
                    data: [
                        data.MNQ?.backtestResults?.metrics?.max_drawdown || data.MNQ?.backtestResults?.metrics?.maxDrawdown || 0,
                        data.MGC?.backtestResults?.metrics?.max_drawdown || data.MGC?.backtestResults?.metrics?.maxDrawdown || 0,
                        data.MES?.backtestResults?.metrics?.max_drawdown || data.MES?.backtestResults?.metrics?.maxDrawdown || 0
                    ],
                    backgroundColor: [
                        'rgba(0, 204, 255, 0.5)',
                        'rgba(255, 215, 0, 0.5)',
                        'rgba(255, 51, 102, 0.5)'
                    ],
                    borderColor: [
                        '#00ccff',
                        '#FFD700',
                        '#ff3366'
                    ],
                    borderWidth: 1
                }]
            };

            // Create the chart
            new Chart(ctx, {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8',
                                callback: function(value) {
                                    return '$' + value.toLocaleString('en-US', {maximumFractionDigits: 0});
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            callbacks: {
                                label: function(context) {
                                    return 'Max Drawdown: $' + context.raw.toLocaleString('en-US', {maximumFractionDigits: 2});
                                }
                            }
                        }
                    }
                }
            });
        }

        function createRiskReturnChart(data) {
            const ctx = document.getElementById('risk-return-chart').getContext('2d');

            // Calculate risk-adjusted returns
            const mnqPnL = data.MNQ?.backtestResults?.metrics?.total_pnl || data.MNQ?.backtestResults?.metrics?.totalPnL || 0;
            const mgcPnL = data.MGC?.backtestResults?.metrics?.total_pnl || data.MGC?.backtestResults?.metrics?.totalPnL || 0;
            const mesPnL = data.MES?.backtestResults?.metrics?.total_pnl || data.MES?.backtestResults?.metrics?.totalPnL || 0;

            const mnqDrawdown = data.MNQ?.backtestResults?.metrics?.max_drawdown || data.MNQ?.backtestResults?.metrics?.maxDrawdown || 1;
            const mgcDrawdown = data.MGC?.backtestResults?.metrics?.max_drawdown || data.MGC?.backtestResults?.metrics?.maxDrawdown || 1;
            const mesDrawdown = data.MES?.backtestResults?.metrics?.max_drawdown || data.MES?.backtestResults?.metrics?.maxDrawdown || 1;

            // Prepare data for the chart
            const chartData = {
                datasets: [{
                    label: 'Risk-Adjusted Returns',
                    data: [
                        {
                            x: mnqDrawdown,
                            y: mnqPnL,
                            r: 15,
                            instrument: 'MNQ'
                        },
                        {
                            x: mgcDrawdown,
                            y: mgcPnL,
                            r: 15,
                            instrument: 'MGC'
                        },
                        {
                            x: mesDrawdown,
                            y: mesPnL,
                            r: 15,
                            instrument: 'MES'
                        }
                    ],
                    backgroundColor: [
                        'rgba(0, 204, 255, 0.7)',
                        'rgba(255, 215, 0, 0.7)',
                        'rgba(255, 51, 102, 0.7)'
                    ],
                    borderColor: [
                        '#00ccff',
                        '#FFD700',
                        '#ff3366'
                    ],
                    borderWidth: 1
                }]
            };

            // Create the chart
            new Chart(ctx, {
                type: 'bubble',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Total P&L',
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8',
                                callback: function(value) {
                                    return '$' + value.toLocaleString('en-US', {maximumFractionDigits: 0});
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Max Drawdown',
                                color: '#94a3b8'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8',
                                callback: function(value) {
                                    return '$' + value.toLocaleString('en-US', {maximumFractionDigits: 0});
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            callbacks: {
                                title: function(context) {
                                    return context[0].raw.instrument;
                                },
                                label: function(context) {
                                    return [
                                        'P&L: $' + context.raw.y.toLocaleString('en-US', {maximumFractionDigits: 2}),
                                        'Drawdown: $' + context.raw.x.toLocaleString('en-US', {maximumFractionDigits: 2}),
                                        'Ratio: ' + (context.raw.y / context.raw.x).toFixed(2)
                                    ];
                                }
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
