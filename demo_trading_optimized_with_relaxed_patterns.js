// This is a backup of the trading bot with relaxed pattern detection logic
// Created on: 2025-05-14
// This version includes:
// 1. Relaxed 3-candle pattern detection (both engulfing and simple patterns)
// 2. Strict 4-candle pattern detection
// 3. Process candles on every update to check for signals

// Pattern detection functions from this version:

function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Log the pattern we're checking
    logger.logSystem(`Checking 3-candle pattern: ${col1}-${col2}-${col3}`, 'info');

    // Bullish pattern: green-red-green
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        // Check for engulfing pattern with wick (favorite pattern)
        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;
        
        // Check for simple pattern where c3 just closes above c2's OPEN (not close)
        const simpleClose = c3.close > c2.open;
        
        // Accept either condition
        if (engulf && wick3 > wick2) {
            logger.logSystem(`Detected bullish 3-candle ENGULFING pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} > c2.open=${c2.open} && c3.open=${c3.open} < c2.close=${c2.close})`, 'info');
            return 'bullish';
        } else if (simpleClose) {
            logger.logSystem(`Detected bullish 3-candle SIMPLE pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} > c2.open=${c2.open})`, 'info');
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-red
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        // Check for engulfing pattern with wick (favorite pattern)
        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);
        
        // Check for simple pattern where c3 just closes below c2's OPEN (not close)
        const simpleClose = c3.close < c2.open;
        
        // Accept either condition
        if (engulf && wick3 > wick2) {
            logger.logSystem(`Detected bearish 3-candle ENGULFING pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} < c2.open=${c2.open} && c3.open=${c3.open} > c2.close=${c2.close})`, 'info');
            return 'bearish';
        } else if (simpleClose) {
            logger.logSystem(`Detected bearish 3-candle SIMPLE pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} < c2.open=${c2.open})`, 'info');
            return 'bearish';
        }
    }

    // Also check for doji patterns as continuation patterns
    if (col2 === 'doji') {
        if (col1 === 'green' && col3 === 'green') {
            logger.logSystem(`Detected bullish continuation with doji: ${col1}-${col2}-${col3}`, 'info');
            return 'bullish';
        }
        if (col1 === 'red' && col3 === 'red') {
            logger.logSystem(`Detected bearish continuation with doji: ${col1}-${col2}-${col3}`, 'info');
            return 'bearish';
        }
    }

    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Log the pattern we're checking
    logger.logSystem(`Checking 4-candle pattern: ${col0}-${col1}-${col2}-${col3}`, 'info');

    // Bullish pattern: green-red-red-green (STRICT version - exactly as in backtest)
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c2.open)) {
            logger.logSystem(`Detected bullish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close} > max(c1.open=${c1.open}, c2.open=${c2.open}))`, 'info');
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-green-red (STRICT version - exactly as in backtest)
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c2.open)) {
            logger.logSystem(`Detected bearish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close} < min(c1.open=${c1.open}, c2.open=${c2.open}))`, 'info');
            return 'bearish';
        }
    }

    return null;
}

// Key change in handleMarketData function:
// Process the candle on every update to check for signals
// This ensures we don't miss any trading opportunities
// processCandle(symbol, candle);
