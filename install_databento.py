#!/usr/bin/env python3
"""
Script to install and verify the Databento Python package.
"""

import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible."""
    logger.info(f"Python version: {sys.version}")
    if sys.version_info < (3, 7):
        logger.error("Python 3.7 or higher is required")
        return False
    return True

def install_databento():
    """Install the Databento package."""
    logger.info("Installing databento package...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "databento"])
        logger.info("databento package installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install databento package: {str(e)}")
        return False

def verify_installation():
    """Verify the Databento package installation."""
    logger.info("Verifying databento package installation...")
    try:
        import databento
        logger.info(f"databento package version: {databento.__version__}")
        return True
    except ImportError:
        logger.error("Failed to import databento package")
        return False

def main():
    """Run the installation and verification."""
    logger.info("Starting Databento package installation and verification...")
    
    if not check_python_version():
        return
    
    if not install_databento():
        return
    
    if not verify_installation():
        return
    
    logger.info("Databento package installation and verification completed successfully")

if __name__ == "__main__":
    main()
