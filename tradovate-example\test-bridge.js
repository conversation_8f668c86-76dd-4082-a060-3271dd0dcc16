/**
 * test-bridge.js
 * Test script for the Tradovate bridge module
 */

// Use dynamic import for ES modules
async function main() {
    try {
        // Import dependencies
        const { default: TradovateBridge } = await import('./src/tradovate-bridge.js');
        const { credentials } = await import('./tutorialsCredentials.js');
        const { URLs } = await import('./tutorialsURLs.js');
        const { commonConfig } = await import('../multi_symbol_config.js');
        const patternDetection = await import('../pattern_detection.js');

        // Set up global config for pattern detection
        global.config = commonConfig;

        // Create a bridge instance
        const bridge = new TradovateBridge(commonConfig, credentials, URLs);

        // Set pattern detection functions
        bridge.detect3 = patternDetection.detect3Pattern;
        bridge.detect4 = patternDetection.detect4Pattern;
        bridge.entryOK = patternDetection.validateEntry;

        // Set up event handlers
        bridge.onConnected = () => {
            console.log('Connected to Tradovate API');

            // Subscribe to market data for MNQ
            bridge.subscribeToMarketData('MNQ')
                .then(() => {
                    console.log('Subscribed to MNQ market data');
                })
                .catch(error => {
                    console.error('Error subscribing to MNQ market data:', error);
                });
        };

        bridge.onDisconnected = () => {
            console.log('Disconnected from Tradovate API');
        };

        bridge.onError = (error) => {
            console.error('Tradovate API error:', error);
        };

        bridge.onTrade = (trade) => {
            console.log('Trade executed:', trade);
        };

        bridge.onPositionUpdate = (symbol, position) => {
            console.log(`Position updated: ${symbol} ${position.netPos} @ ${position.netPrice} (P&L: ${position.currentPL?.toFixed(2) || 'N/A'})`);
        };

        bridge.onMarketData = (type, symbol, data) => {
            if (type === 'chart') {
                console.log(`Received chart data for ${symbol}: ${data.bars?.length || 0} bars`);
            }
        };

        // Connect to Tradovate API
        console.log('Connecting to Tradovate API...');
        const connected = await bridge.connect();

        if (connected) {
            console.log('Successfully connected to Tradovate API');

            // Keep the script running
            console.log('Press Ctrl+C to exit');
        } else {
            console.error('Failed to connect to Tradovate API');
            process.exit(1);
        }

        // Handle process termination
        process.on('SIGINT', () => {
            console.log('Disconnecting from Tradovate API...');
            bridge.disconnect();
            process.exit(0);
        });

        // Return the bridge instance for testing
        return bridge;
    } catch (error) {
        console.error('Error in main function:', error);
        process.exit(1);
    }
}

// Run the main function
main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
});
