import os
import re
import glob
from bs4 import BeautifulSoup
import shutil

# Directories
DASHBOARD_DIR = "C:/backtest-bot"
DATA_DIR = "C:/backtest-bot/market_data"

def find_dashboard_files():
    """Find all dashboard HTML files"""
    dashboard_files = []
    
    # Find all HTML files in the dashboard directory
    html_files = glob.glob(os.path.join(DASHBOARD_DIR, "*.html"))
    
    # Filter for dashboard files
    for file_path in html_files:
        file_name = os.path.basename(file_path)
        if "dashboard" in file_name.lower() or file_name in [
            "hedge_dashboard.html", "mnq_dashboard.html", "mgc_dashboard.html", "mes_dashboard.html",
            "performance_comparison_dashboard.html", "market_intelligence.html", "correlation_analysis_dashboard.html",
            "monte_carlo_dashboard.html", "drawdown_analysis_dashboard.html", "portfolio_manager.html",
            "portfolio_overview.html", "performance_forecast.html", "market_volatility.html",
            "daily_pnl_dashboard.html", "premium_dashboard.html", "custom_alerts_dashboard.html",
            "investor_reporting.html"
        ]:
            dashboard_files.append(file_path)
    
    return dashboard_files

def update_dashboard_file(file_path):
    """Update a dashboard file to use the centralized data repository"""
    file_name = os.path.basename(file_path)
    print(f"Updating {file_name}...")
    
    # Create a backup of the original file
    backup_file = f"{file_path}.bak"
    shutil.copy2(file_path, backup_file)
    print(f"Created backup of {file_path} to {backup_file}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update script references
        soup = BeautifulSoup(content, 'html.parser')
        
        # Ensure dashboard_config.js is included
        config_script = soup.find('script', src='dashboard_config.js')
        if not config_script:
            # Add dashboard_config.js reference
            head = soup.find('head')
            if head:
                new_script = soup.new_tag('script', src='dashboard_config.js')
                head.append(new_script)
                print(f"Added dashboard_config.js reference to {file_name}")
        
        # Ensure dashboard_data_loader.js is included
        data_loader_script = soup.find('script', src='dashboard_data_loader.js')
        if not data_loader_script:
            # Add dashboard_data_loader.js reference
            head = soup.find('head')
            if head:
                new_script = soup.new_tag('script', src='dashboard_data_loader.js')
                head.append(new_script)
                print(f"Added dashboard_data_loader.js reference to {file_name}")
        
        # Ensure dashboard_chart_utils.js is included
        chart_utils_script = soup.find('script', src='dashboard_chart_utils.js')
        if not chart_utils_script:
            # Add dashboard_chart_utils.js reference
            head = soup.find('head')
            if head:
                new_script = soup.new_tag('script', src='dashboard_chart_utils.js')
                head.append(new_script)
                print(f"Added dashboard_chart_utils.js reference to {file_name}")
        
        # Add data loading script
        data_loading_script = """
<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
"""
        
        # Check if the data loading script already exists
        if "initializeDashboard" not in content:
            # Add the data loading script before the closing body tag
            body = soup.find('body')
            if body:
                new_script = BeautifulSoup(data_loading_script, 'html.parser')
                body.append(new_script)
                print(f"Added data loading script to {file_name}")
        
        # Write the updated content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        print(f"Updated {file_name} successfully")
        return True
    
    except Exception as e:
        print(f"Error updating {file_name}: {e}")
        
        # Restore the backup
        shutil.copy2(backup_file, file_path)
        print(f"Restored {file_path} from backup due to error")
        
        return False

def update_all_dashboards():
    """Update all dashboard files to use the centralized data repository"""
    # Find all dashboard files
    dashboard_files = find_dashboard_files()
    
    if not dashboard_files:
        print("No dashboard files found.")
        return
    
    print(f"Found {len(dashboard_files)} dashboard files:")
    for file_path in dashboard_files:
        print(f"  - {os.path.basename(file_path)}")
    
    # Update each dashboard file
    success_count = 0
    for file_path in dashboard_files:
        if update_dashboard_file(file_path):
            success_count += 1
    
    print(f"Updated {success_count} out of {len(dashboard_files)} dashboard files successfully")

def main():
    """Main function"""
    print("Updating all dashboard references...")
    update_all_dashboards()
    print("Dashboard reference update complete!")

if __name__ == "__main__":
    main()
