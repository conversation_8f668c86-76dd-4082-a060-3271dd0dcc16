// renderQuote.js
// Renders real-time quote data to HTML

/**
 * Render price and size information
 * @param {Object} data - Price and size data
 * @param {number} data.price - Price value
 * @param {number} data.size - Size value
 * @returns {string} HTML representation of price and size
 */
const renderPriceSize = ({ price, size }) => `
    ${price !== undefined ? '<li>price: ' + price + '</li>' : ''}
    ${size !== undefined ? '<li>size: ' + size + '</li>' : ''}
`;

/**
 * Render quote data to HTML
 * @param {string} symbol - Contract symbol
 * @param {Object} quoteData - Quote data with various entries
 * @returns {string} HTML representation of the quote
 */
export const renderQuote = (symbol, {
    Bid,
    HighPrice,
    LowPrice,
    Offer,
    OpenInterest,
    OpeningPrice,
    SettlementPrice,
    TotalTradeVolume,
    Trade,
    EmptyBook
}) => `
    <section class="quote-container">
        <h1 class="quote-symbol">${symbol}</h1>
        <span class="quote-grid">
            ${Bid ? `
            <div class="quote-card">
                <h3>Bid</h3>
                <ul>
                    ${renderPriceSize(Bid)}
                </ul>
            </div>
            ` : ''}
            
            ${Offer ? `
            <div class="quote-card">
                <h3>Offer</h3>
                <ul>
                    ${renderPriceSize(Offer)}
                </ul>
            </div>
            ` : ''}
            
            ${Trade ? `
            <div class="quote-card">
                <h3>Trade</h3>
                <ul>
                    ${renderPriceSize(Trade)}
                </ul>
            </div>
            ` : ''}
            
            ${HighPrice ? `
            <div class="quote-card">
                <h3>High Price</h3>
                <ul>
                    ${renderPriceSize(HighPrice)}
                </ul>
            </div>
            ` : ''}
            
            ${LowPrice ? `
            <div class="quote-card">
                <h3>Low Price</h3>
                <ul>
                    ${renderPriceSize(LowPrice)}
                </ul>
            </div>
            ` : ''}
            
            ${OpenInterest ? `
            <div class="quote-card">
                <h3>Open Interest</h3>
                <ul>
                    ${renderPriceSize(OpenInterest)}
                </ul>
            </div>
            ` : ''}
            
            ${OpeningPrice ? `
            <div class="quote-card">
                <h3>Opening Price</h3>
                <ul>
                    ${renderPriceSize(OpeningPrice)}
                </ul>
            </div>
            ` : ''}
            
            ${SettlementPrice ? `
            <div class="quote-card">
                <h3>Settlement Price</h3>
                <ul>
                    ${renderPriceSize(SettlementPrice)}
                </ul>
            </div>
            ` : ''}
            
            ${TotalTradeVolume ? `
            <div class="quote-card">
                <h3>Total Trade Volume</h3>
                <ul>
                    ${renderPriceSize(TotalTradeVolume)}
                </ul>
            </div>
            ` : ''}
            
            ${EmptyBook ? `
            <div class="quote-card">
                <h3>Empty Book</h3>
                <ul>
                    ${renderPriceSize(EmptyBook)}
                </ul>
            </div>
            ` : ''}
        </span>
    </section>
`;
