// test_rsi.js - Test RSI calculation
const fs = require('fs');
const path = require('path');

// Import the indicator functions
const { calculateRSI, calculateSMA, calculateWMA } = require('./indicators');

// Create sample data
const generateSampleData = () => {
    const candles = [];
    let price = 2100.00;

    // Generate 100 candles with some price movement
    for (let i = 0; i < 100; i++) {
        const change = (Math.random() - 0.5) * 10;
        const open = price;
        const close = price + change;
        price = close;

        const high = Math.max(open, close) + Math.random() * 5;
        const low = Math.min(open, close) - Math.random() * 5;

        const timestamp = new Date(2025, 4, 14, 5, i, 0);

        candles.push({
            timestamp,
            open,
            high,
            low,
            close
        });
    }

    return candles;
};

// Calculate RSI for each candle
const calculateRSIValues = (candles, period) => {
    const rsiValues = [];

    for (let i = period; i < candles.length; i++) {
        const slice = candles.slice(i - period, i + 1);
        const rsi = calculateRSI(slice, period);
        rsiValues.push(rsi);
    }

    return rsiValues;
};

// Calculate SMA for each value
const calculateSMAValues = (values, period) => {
    const smaValues = [];

    for (let i = period - 1; i < values.length; i++) {
        const slice = values.slice(i - period + 1, i + 1);
        const sum = slice.reduce((acc, val) => acc + val, 0);
        const sma = sum / period;
        smaValues.push(sma);
    }

    return smaValues;
};

// Test RSI calculation
const testRSI = () => {
    console.log("Testing RSI calculation...");

    // Generate sample data
    const candles = generateSampleData();

    // Calculate RSI with period 14
    const rsiPeriod = 14;
    const rsiValues = calculateRSIValues(candles, rsiPeriod);

    // Calculate SMA of RSI with period 8
    const smaPeriod = 8;
    const rsiSmaValues = calculateSMAValues(rsiValues, smaPeriod);

    // Print the results
    console.log(`\nRSI(${rsiPeriod}) and RSI-MA(${smaPeriod}) values:`);
    console.log("Candle #\tClose\t\tRSI\t\tRSI-MA");
    console.log("-".repeat(50));

    for (let i = rsiPeriod; i < candles.length; i++) {
        const rsiIndex = i - rsiPeriod;
        const rsiValue = rsiValues[rsiIndex];

        let rsiMaValue = "N/A";
        if (rsiIndex >= smaPeriod - 1) {
            const rsiMaIndex = rsiIndex - (smaPeriod - 1);
            rsiMaValue = rsiSmaValues[rsiMaIndex].toFixed(2);
        }

        console.log(`${i+1}\t\t${candles[i].close.toFixed(2)}\t\t${rsiValue.toFixed(2)}\t\t${rsiMaValue}`);
    }

    // Test with real data from MNQM5
    console.log("\nTesting with real data from MNQM5...");
    try {
        // Try to load real data if available
        const dataPath = path.join(__dirname, 'input', 'MNQM5_1min.csv');
        if (fs.existsSync(dataPath)) {
            const data = fs.readFileSync(dataPath, 'utf8');
            const lines = data.split('\n');

            // Parse CSV data
            const realCandles = [];
            for (let i = 1; i < lines.length; i++) {  // Skip header
                const line = lines[i].trim();
                if (line) {
                    const [dateStr, timeStr, open, high, low, close] = line.split(',');

                    const timestamp = new Date(`${dateStr} ${timeStr}`);
                    realCandles.push({
                        timestamp,
                        open: parseFloat(open),
                        high: parseFloat(high),
                        low: parseFloat(low),
                        close: parseFloat(close)
                    });
                }
            }

            // Calculate RSI with period 14
            const realRsiValues = calculateRSIValues(realCandles, rsiPeriod);

            // Calculate SMA of RSI with period 8
            const realRsiSmaValues = calculateSMAValues(realRsiValues, smaPeriod);

            // Print the results for the last 20 candles
            console.log(`\nLast 20 RSI(${rsiPeriod}) and RSI-MA(${smaPeriod}) values for MNQM5:`);
            console.log("Timestamp\t\tClose\t\tRSI\t\tRSI-MA");
            console.log("-".repeat(70));

            const startIndex = Math.max(rsiPeriod, realCandles.length - 20);
            for (let i = startIndex; i < realCandles.length; i++) {
                const rsiIndex = i - rsiPeriod;
                const rsiValue = realRsiValues[rsiIndex];

                let rsiMaValue = "N/A";
                if (rsiIndex >= smaPeriod - 1) {
                    const rsiMaIndex = rsiIndex - (smaPeriod - 1);
                    rsiMaValue = realRsiSmaValues[rsiMaIndex].toFixed(2);
                }

                const timestamp = realCandles[i].timestamp.toISOString().replace('T', ' ').substring(0, 19);
                console.log(`${timestamp}\t${realCandles[i].close.toFixed(2)}\t\t${rsiValue.toFixed(2)}\t\t${rsiMaValue}`);
            }
        } else {
            console.log("Real data file not found. Skipping real data test.");
        }
    } catch (error) {
        console.error("Error loading real data:", error.message);
    }
};

// Run the test
testRSI();
