// placeOrder.js
// <PERSON>les placing orders with the Tradovate API

import { tvPost } from './services'
import { getAccessToken } from './storage'

// Order type enum
export const ORDER_TYPE = {
    Limit: 'Limit',
    MIT: 'MIT',
    Market: 'Market',
    QTS: 'QTS',
    Stop: 'Stop',
    StopLimit: 'StopLimit',
    TrailingStop: 'TrailingStop',
    TrailingStopLimit: 'TrailingStopLimit'
}

// Order action enum
export const ORDER_ACTION = {
    Buy: 'Buy',
    Sell: 'Sell'
}

/**
 * Get the first available account
 * @returns {Object} First available account
 */
export const getAvailableAccounts = async () => {
    try {
        const accounts = await tvGet('/account/list')
        return accounts && accounts.length > 0 ? accounts : []
    } catch (error) {
        console.error('Error fetching accounts:', error)
        return []
    }
}

/**
 * Place an order with the Tradovate API
 * @param {Object} orderParams - Order parameters
 * @returns {Promise<Object>} Order response
 */
export const placeOrder = async ({
    action, 
    symbol,
    orderQty,
    orderType, 
    accountSpec, 
    accountId, 
    clOrdId, 
    price, 
    stopPrice, 
    maxShow, 
    pegDifference,
    timeInForce, 
    expireTime, 
    text, 
    activationTime, 
    customTag50, 
    isAutomated
}) => {
    try {
        // Get the first available account if not provided
        let accountDetails = { id: accountId, name: accountSpec }
        
        if (!accountId || !accountSpec) {
            const accounts = await getAvailableAccounts()
            if (accounts.length === 0) {
                throw new Error('No accounts available')
            }
            accountDetails = { id: accounts[0].id, name: accounts[0].name }
        }

        // Check if we have a valid token
        const { token } = getAccessToken()
        if (!token) {
            throw new Error('No access token found. Please acquire a token and try again.')
        }

        // Build the order request body
        const normalized_body = {
            action, 
            symbol, 
            orderQty, 
            orderType,
            isAutomated: isAutomated || false,
            accountId: accountDetails.id,
            accountSpec: accountDetails.name
        }

        // Add optional parameters if provided
        if (clOrdId) normalized_body.clOrdId = clOrdId
        if (price) normalized_body.price = price
        if (stopPrice) normalized_body.stopPrice = stopPrice
        if (maxShow) normalized_body.maxShow = maxShow
        if (pegDifference) normalized_body.pegDifference = pegDifference
        if (timeInForce) normalized_body.timeInForce = timeInForce
        if (expireTime) normalized_body.expireTime = expireTime
        if (text) normalized_body.text = text
        if (activationTime) normalized_body.activationTime = activationTime
        if (customTag50) normalized_body.customTag50 = customTag50

        // Place the order
        console.log('Placing order with parameters:', normalized_body)
        const response = await tvPost('/order/placeOrder', normalized_body)
        
        return response
    } catch (error) {
        console.error('Error placing order:', error)
        return { error: error.message }
    }
}

// Import tvGet for account fetching
import { tvGet } from './services'
