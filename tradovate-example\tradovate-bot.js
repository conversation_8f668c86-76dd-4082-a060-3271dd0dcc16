/**
 * tradovate-bot.js
 *
 * Main trading bot implementation for Tradovate
 * Uses exact pattern detection and position management from original backtest
 */

const WebSocket = require('ws');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Import exact implementations from original backtest
const exactPD = require('../exact_pattern_detection');
const exactPM = require('../exact_position_management');

// Import configuration
const { mnqConfig, mesConfig, mgcConfig, getConfigBySymbol } = require('../multi_symbol_config');

// Global variables
let accessToken = null;
let expirationTime = null;
let ws = null;
let mdWs = null;
let accountId = null;
let activePosition = null;
let tradeIdCounter = 0;
let subscriptions = new Map();
let contractMap = new Map();

// Initialize candles storage
global.candles = {
    MNQ: [],
    MES: [],
    MGC: []
};

// Initialize daily stats
let dailyStats = {
    date: new Date().toISOString().slice(0, 10),
    trades: 0,
    wins: 0,
    losses: 0,
    pnl: 0,
    startBalance: 10000,
    currentBalance: 10000,
    maxDrawdown: 0,
    peakBalance: 10000
};

// Track daily P&L for each symbol
const dailyPnL = {
    MNQ: new Map(),
    MES: new Map(),
    MGC: new Map()
};

// Helper function to get day identifier
function getDayIdentifier(timestamp) {
    const date = new Date(timestamp);
    return date.toISOString().slice(0, 10);
}

// Check if it's a new day and reset daily stats if needed
function checkAndResetDailyStats() {
    const today = new Date().toISOString().slice(0, 10);

    if (today !== dailyStats.date) {
        console.log(`New trading day: ${today}`);

        // Save previous day's stats
        const statsFile = path.join(__dirname, `stats_${dailyStats.date}.json`);
        fs.writeFileSync(statsFile, JSON.stringify(dailyStats, null, 2));

        // Reset daily stats
        dailyStats = {
            date: today,
            trades: 0,
            wins: 0,
            losses: 0,
            pnl: 0,
            startBalance: dailyStats.currentBalance,
            currentBalance: dailyStats.currentBalance,
            maxDrawdown: 0,
            peakBalance: dailyStats.currentBalance
        };

        // Reset daily P&L
        Object.keys(dailyPnL).forEach(symbol => {
            dailyPnL[symbol] = new Map();
        });

        console.log(`Daily stats reset. Starting balance: $${dailyStats.startBalance.toFixed(2)}`);
    }
}

// Calculate indicators for a symbol
function calculateIndicators(symbol) {
    const candles = global.candles[symbol];

    if (!candles || candles.length < 50) {
        return;
    }

    // Get config for symbol
    const config = getConfigBySymbol(symbol);

    // Calculate SMA200
    if (config.sma200Period > 0 && candles.length >= config.sma200Period) {
        for (let i = config.sma200Period - 1; i < candles.length; i++) {
            let sum = 0;
            for (let j = i - config.sma200Period + 1; j <= i; j++) {
                sum += candles[j].close;
            }
            candles[i].sma200 = sum / config.sma200Period;
        }
    }

    // Calculate WMA50
    if (config.wma50Period > 0 && candles.length >= config.wma50Period) {
        for (let i = config.wma50Period - 1; i < candles.length; i++) {
            let weightedSum = 0;
            let weightSum = 0;
            for (let j = 0; j < config.wma50Period; j++) {
                const weight = config.wma50Period - j;
                weightedSum += candles[i - j].close * weight;
                weightSum += weight;
            }
            candles[i].wma50 = weightedSum / weightSum;
        }
    }

    // Calculate RSI
    if (config.rsiPeriod > 0 && candles.length >= config.rsiPeriod + 1) {
        for (let i = config.rsiPeriod; i < candles.length; i++) {
            let gains = 0, losses = 0;
            for (let j = i - config.rsiPeriod + 1; j <= i; j++) {
                if (j > 0) {
                    const delta = candles[j].close - candles[j - 1].close;
                    if (delta > 0) {
                        gains += delta;
                    } else {
                        losses -= delta;
                    }
                }
            }

            const avgGain = gains / config.rsiPeriod;
            const avgLoss = losses / config.rsiPeriod;

            if (avgLoss === 0) {
                candles[i].rsi = 100;
            } else if (avgGain === 0) {
                candles[i].rsi = 0;
            } else {
                const rs = avgGain / avgLoss;
                candles[i].rsi = 100 - (100 / (1 + rs));
            }
        }
    }

    // Calculate RSI MA
    if (config.rsiMaPeriod > 0 && config.rsiPeriod > 0 && candles.length >= config.rsiPeriod + config.rsiMaPeriod) {
        for (let i = config.rsiPeriod + config.rsiMaPeriod - 1; i < candles.length; i++) {
            let sum = 0;
            for (let j = i - config.rsiMaPeriod + 1; j <= i; j++) {
                sum += candles[j].rsi;
            }
            candles[i].rsiMa = sum / config.rsiMaPeriod;
        }
    }

    // Calculate ATR
    if (config.atrPeriod > 0 && candles.length >= config.atrPeriod + 1) {
        const trs = [];

        for (let i = 0; i < candles.length; i++) {
            if (i === 0) {
                trs.push(candles[i].high - candles[i].low);
            } else {
                const tr = Math.max(
                    candles[i].high - candles[i].low,
                    Math.abs(candles[i].high - candles[i-1].close),
                    Math.abs(candles[i].low - candles[i-1].close)
                );
                trs.push(tr);
            }
        }

        for (let i = config.atrPeriod - 1; i < candles.length; i++) {
            let sum = 0;
            for (let j = i - config.atrPeriod + 1; j <= i; j++) {
                sum += trs[j];
            }
            candles[i].atr = sum / config.atrPeriod;
        }
    }
}

// Entry validation function (exact implementation from original backtest)
function validateEntry(pattern, patternType, candle, symbol, currentIndex, config) {
    // Use the exact implementation from the original backtest
    return exactPD.entryOK(pattern, patternType, candle, currentIndex, global.candles[symbol], config);
}

// Process new candle function
function processCandle(symbol, candle) {
    // Check if it's a new day
    checkAndResetDailyStats();

    // Get config for symbol
    const config = getConfigBySymbol(symbol);

    // Skip if we don't have enough candles for pattern detection
    if (!global.candles[symbol] || global.candles[symbol].length < 4) {
        return;
    }

    const candles = global.candles[symbol];
    const c0 = candles[candles.length - 4];
    const c1 = candles[candles.length - 3];
    const c2 = candles[candles.length - 2];
    const c3 = candle; // Current candle

    // Manage existing position if any
    if (activePosition && activePosition.symbol === symbol) {
        const exitInfo = exactPM.managePosition(
            activePosition,
            candle,
            candles.length - 1,
            candles,
            [], // completedTrades (not used in live trading)
            config,
            {} // exitCounts (not used in live trading)
        );

        if (exitInfo) {
            // Update statistics
            dailyStats.trades++;
            if (exitInfo.pnlNetTotal > 0) {
                dailyStats.wins++;
            } else {
                dailyStats.losses++;
            }

            dailyStats.pnl += exitInfo.pnlNetTotal;
            dailyStats.currentBalance += exitInfo.pnlNetTotal;
            dailyStats.peakBalance = Math.max(dailyStats.peakBalance, dailyStats.currentBalance);
            dailyStats.maxDrawdown = Math.max(dailyStats.maxDrawdown, dailyStats.peakBalance - dailyStats.currentBalance);

            // Update daily P&L
            const day = getDayIdentifier(exitInfo.exitTimestamp);
            const symbolPnL = dailyPnL[symbol];
            symbolPnL.set(day, (symbolPnL.get(day) || 0) + exitInfo.pnlNetTotal);

            // Log exit
            console.log(`[${exitInfo.exitTimestamp.toISOString()}] ${symbol} ${activePosition.dir.toUpperCase()} EXIT: P&L: $${exitInfo.pnlNetTotal.toFixed(2)}`);

            // Position closed
            activePosition = null;
        }
    }

    // Check for new entry if no active position
    if (!activePosition) {
        // Detect patterns
        const pattern3 = exactPD.detect3(c1, c2, c3);
        const pattern4 = exactPD.detect4(c0, c1, c2, c3);

        let pattern = null;
        let patternType = null;

        if (pattern4) {
            pattern = pattern4;
            patternType = 'four';
        } else if (pattern3) {
            pattern = pattern3;
            patternType = 'three';
        }

        // Validate entry
        if (pattern && validateEntry(pattern, patternType, c3, symbol, candles.length - 1, config)) {
            // Create new position
            tradeIdCounter++;

            // Determine ATR regime
            const atrRegime = c3.atr < config.atrThresholds.low_medium ? 'Low' :
                             (c3.atr > config.atrThresholds.medium_high ? 'High' : 'Medium');

            // Get parameters based on ATR regime
            let slFactor, tpFactor, trailFactor;

            if (config.isAdaptiveRun) {
                slFactor = config.adaptiveParams[atrRegime].slFactor;
                tpFactor = config.adaptiveParams[atrRegime].tpFactor;
                trailFactor = config.adaptiveParams[atrRegime].trailFactor;
            } else {
                slFactor = config.slFactors;
                tpFactor = config.tpFactors;
                trailFactor = config.trailFactors;
            }

            // Calculate stop loss and take profit distances
            const slDistance = c3.atr * slFactor;
            const atrTpDistance = c3.atr * tpFactor;
            const fixTpDistance = config.fixedTpPoints || 0;
            const tpDistance = Math.max(fixTpDistance, atrTpDistance);

            // Create position exactly as in the original backtest
            activePosition = {
                tradeId: tradeIdCounter,
                symbol: symbol,
                dir: pattern,
                entry: c3.close,
                atr: c3.atr,
                stopLossPrice: pattern === 'bullish' ? c3.close - slDistance : c3.close + slDistance,
                tpDistance: tpDistance,
                trailStopPrice: pattern === 'bullish' ? c3.close - (c3.atr * trailFactor) : c3.close + (c3.atr * trailFactor),
                trailFactor: trailFactor,
                trailHigh: c3.high,
                trailLow: c3.low,
                contracts: config.fixedContracts,
                entryTimestamp: new Date(c3.timestamp),
                entryBarIndex: candles.length - 1,
                currentBarIndex: candles.length - 1,
                entryAtrRegime: atrRegime,
                tpType: fixTpDistance > atrTpDistance ? 'Fixed' : 'ATR'
            };

            // Log entry
            console.log(`[${new Date(c3.timestamp).toISOString()}] ${symbol} ${pattern.toUpperCase()} ENTRY at ${c3.close.toFixed(config.pricePrecision)}, SL: ${activePosition.stopLossPrice.toFixed(config.pricePrecision)}, TP: ${(pattern === 'bullish' ? c3.close + tpDistance : c3.close - tpDistance).toFixed(config.pricePrecision)}`);

            // Place order with Tradovate API
            placeOrder(symbol, pattern === 'bullish' ? 'Buy' : 'Sell', 'Market', config.fixedContracts);
        }
    }
}

// Place order with Tradovate API
async function placeOrder(symbol, action, orderType, quantity) {
    try {
        if (!accessToken || !accountId) {
            console.error('Not authenticated or no account selected');
            return false;
        }

        // Get contract ID for symbol
        const contractId = contractMap.get(symbol);

        if (!contractId) {
            console.error(`Contract not found for symbol: ${symbol}`);
            return false;
        }

        // Prepare order request
        const orderRequest = {
            accountId: accountId,
            contractId: contractId,
            action: action, // 'Buy' or 'Sell'
            orderType: orderType, // 'Market', 'Limit', 'Stop', 'StopLimit'
            orderQty: quantity,
            isAutomated: true // Important for algorithmic trading
        };

        // Place order
        const response = await axios.post('https://demo.tradovateapi.com/v1/order/placeOrder', orderRequest, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.data && response.data.orderId) {
            console.log(`Order placed: ${symbol} ${action} ${quantity} @ Market, Order ID: ${response.data.orderId}`);
            return response.data.orderId;
        } else {
            console.error('Order placement failed:', response.data);
            return false;
        }
    } catch (error) {
        console.error('Error placing order:', error.response ? error.response.data : error.message);
        return false;
    }
}

// Export functions
module.exports = {
    processCandle,
    calculateIndicators,
    getDailyStats: () => dailyStats,
    getDailyPnL: () => dailyPnL,
    getActivePosition: () => activePosition
};
