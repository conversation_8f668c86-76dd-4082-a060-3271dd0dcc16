// place-order-example.js
// Example of placing an order with the Tradovate API

const fetch = require('node-fetch');
const readline = require('readline');

// API URLs
const URLS = {
    DEMO_URL: 'https://demo.tradovateapi.com/v1',
    LIVE_URL: 'https://live.tradovateapi.com/v1'
};

// API credentials
const credentials = {
    name: "bravesbeatmets",
    password: "Braves12$",
    appId: "Trading Bot",
    appVersion: "1.0",
    cid: "6186",
    sec: "69311dad-75a7-49c6-9958-00ab2c4f1ab6",
    deviceId: "example-" + Math.random().toString(36).substring(2, 15) + 
              Math.random().toString(36).substring(2, 15)
};

// Order type enum
const ORDER_TYPE = {
    Limit: 'Limit',
    MIT: 'MIT',
    Market: 'Market',
    QTS: 'QTS',
    Stop: 'Stop',
    StopLimit: 'StopLimit',
    TrailingStop: 'TrailingStop',
    TrailingStopLimit: 'TrailingStopLimit'
};

// Order action enum
const ORDER_ACTION = {
    Buy: 'Buy',
    Sell: 'Sell'
};

// In-memory storage for tokens
let accessToken = null;
let tokenExpiration = null;

/**
 * Make a POST request to the Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {boolean} auth - Whether to include auth header
 * @returns {Promise<Object>} JSON response
 */
const tvPost = async (endpoint, data = {}, auth = true) => {
    // Build request options
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    };

    // Add auth header if needed
    if (auth && accessToken) {
        options.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Make a GET request to the Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {boolean} auth - Whether to include auth header
 * @returns {Promise<Object>} JSON response
 */
const tvGet = async (endpoint, params = {}, auth = true) => {
    // Build query string from params
    const queryString = Object.keys(params).length
        ? '?' + Object.entries(params)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&')
        : '';

    // Build request options
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    };

    // Add auth header if needed
    if (auth && accessToken) {
        options.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}${queryString}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Authenticate with the Tradovate API
 * @returns {Promise<boolean>} Whether authentication was successful
 */
const authenticate = async () => {
    try {
        console.log('Authenticating with Tradovate API...');
        
        // Request access token
        const response = await tvPost('/auth/accesstokenrequest', credentials, false);
        
        if (response.accessToken) {
            console.log('Authentication successful!');
            accessToken = response.accessToken;
            tokenExpiration = response.expirationTime;
            return true;
        } else {
            console.error('Authentication failed:', response.errorText || 'Unknown error');
            return false;
        }
    } catch (error) {
        console.error('Error authenticating:', error);
        return false;
    }
};

/**
 * Get available accounts
 * @returns {Promise<Array>} List of accounts
 */
const getAvailableAccounts = async () => {
    try {
        const accounts = await tvGet('/account/list');
        return accounts && accounts.length > 0 ? accounts : [];
    } catch (error) {
        console.error('Error fetching accounts:', error);
        return [];
    }
};

/**
 * Place an order with the Tradovate API
 * @param {Object} orderParams - Order parameters
 * @returns {Promise<Object>} Order response
 */
const placeOrder = async (orderParams) => {
    try {
        // Get the first available account if not provided
        if (!orderParams.accountId || !orderParams.accountSpec) {
            const accounts = await getAvailableAccounts();
            if (accounts.length === 0) {
                throw new Error('No accounts available');
            }
            orderParams.accountId = accounts[0].id;
            orderParams.accountSpec = accounts[0].name;
        }

        // Place the order
        console.log('Placing order with parameters:', orderParams);
        const response = await tvPost('/order/placeOrder', orderParams);
        
        return response;
    } catch (error) {
        console.error('Error placing order:', error);
        return { error: error.message };
    }
};

/**
 * Create a readline interface for user input
 */
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Prompt the user for input
 * @param {string} question - Question to ask
 * @returns {Promise<string>} User input
 */
const prompt = (question) => {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
};

/**
 * Run the order placement example
 */
const runOrderExample = async () => {
    try {
        // First authenticate
        const authenticated = await authenticate();
        
        if (!authenticated) {
            console.error('Cannot place order without authentication');
            rl.close();
            return;
        }
        
        // Get accounts
        const accounts = await getAvailableAccounts();
        if (accounts.length === 0) {
            console.error('No accounts available');
            rl.close();
            return;
        }
        
        console.log('\n=== AVAILABLE ACCOUNTS ===');
        accounts.forEach((account, index) => {
            console.log(`${index + 1}. ${account.name} (ID: ${account.id})`);
        });
        
        // Get order details from user
        console.log('\n=== PLACE ORDER ===');
        const symbol = await prompt('Enter symbol (e.g., MNQ): ');
        const quantity = parseInt(await prompt('Enter quantity: '));
        
        console.log('\nOrder Types:');
        Object.keys(ORDER_TYPE).forEach((type, index) => {
            console.log(`${index + 1}. ${type}`);
        });
        const orderTypeIndex = parseInt(await prompt('Select order type (number): ')) - 1;
        const orderType = Object.keys(ORDER_TYPE)[orderTypeIndex];
        
        // Get price if needed
        let price = null;
        if (orderType === 'Limit' || orderType === 'StopLimit') {
            price = parseFloat(await prompt('Enter price: '));
        }
        
        // Get stop price if needed
        let stopPrice = null;
        if (orderType === 'Stop' || orderType === 'StopLimit') {
            stopPrice = parseFloat(await prompt('Enter stop price: '));
        }
        
        // Get action
        console.log('\nActions:');
        Object.keys(ORDER_ACTION).forEach((action, index) => {
            console.log(`${index + 1}. ${action}`);
        });
        const actionIndex = parseInt(await prompt('Select action (number): ')) - 1;
        const action = Object.keys(ORDER_ACTION)[actionIndex];
        
        // Get isAutomated
        const isAutomatedInput = await prompt('Is this an automated order? (y/n): ');
        const isAutomated = isAutomatedInput.toLowerCase() === 'y';
        
        // Prepare order parameters
        const orderParams = {
            action: ORDER_ACTION[action],
            symbol,
            orderQty: quantity,
            orderType: ORDER_TYPE[orderType],
            accountId: accounts[0].id,
            accountSpec: accounts[0].name,
            isAutomated
        };
        
        // Add price if needed
        if (price !== null) {
            orderParams.price = price;
        }
        
        // Add stop price if needed
        if (stopPrice !== null) {
            orderParams.stopPrice = stopPrice;
        }
        
        // Confirm order
        console.log('\n=== ORDER SUMMARY ===');
        console.log(JSON.stringify(orderParams, null, 2));
        const confirm = await prompt('\nConfirm order? (y/n): ');
        
        if (confirm.toLowerCase() === 'y') {
            // Place the order
            const response = await placeOrder(orderParams);
            
            console.log('\n=== ORDER RESPONSE ===');
            console.log(JSON.stringify(response, null, 2));
            
            if (response.error || response.errorText) {
                console.error('Error placing order:', response.error || response.errorText);
            } else {
                console.log('Order placed successfully!');
                console.log(`Order ID: ${response.orderId || 'N/A'}`);
            }
        } else {
            console.log('Order cancelled');
        }
    } catch (error) {
        console.error('Error running order example:', error);
    } finally {
        rl.close();
    }
};

// Run the example
runOrderExample();
