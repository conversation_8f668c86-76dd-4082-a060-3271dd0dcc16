// Golf Course Tracker App
class GolfTracker {
    constructor() {
        this.rounds = this.loadRounds();
        this.init();
    }

    init() {
        this.updateStats();
        this.renderRounds();
        this.setupEventListeners();
        this.setTodayDate();
    }

    setupEventListeners() {
        document.getElementById('addRoundForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addRound();
        });

        // Close modal when clicking outside
        document.getElementById('addRoundModal').addEventListener('click', (e) => {
            if (e.target.id === 'addRoundModal') {
                this.closeAddRoundModal();
            }
        });
    }

    setTodayDate() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('playDate').value = today;
    }

    loadRounds() {
        const stored = localStorage.getItem('golfRounds');
        return stored ? JSON.parse(stored) : [];
    }

    saveRounds() {
        localStorage.setItem('golfRounds', JSON.stringify(this.rounds));
    }

    addRound() {
        const formData = {
            id: Date.now(),
            courseName: document.getElementById('courseName').value,
            courseLocation: document.getElementById('courseLocation').value,
            playDate: document.getElementById('playDate').value,
            score: parseInt(document.getElementById('score').value),
            par: parseInt(document.getElementById('par').value) || null,
            tees: document.getElementById('tees').value,
            notes: document.getElementById('notes').value,
            dateAdded: new Date().toISOString()
        };

        this.rounds.unshift(formData); // Add to beginning of array
        this.saveRounds();
        this.updateStats();
        this.renderRounds();
        this.closeAddRoundModal();
        this.clearForm();
    }

    deleteRound(id) {
        if (confirm('Are you sure you want to delete this round?')) {
            this.rounds = this.rounds.filter(round => round.id !== id);
            this.saveRounds();
            this.updateStats();
            this.renderRounds();
        }
    }

    updateStats() {
        const totalRounds = this.rounds.length;
        const uniqueCourses = new Set(this.rounds.map(r => r.courseName.toLowerCase())).size;
        const scores = this.rounds.map(r => r.score).filter(s => s);
        const avgScore = scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : '--';
        const bestScore = scores.length > 0 ? Math.min(...scores) : '--';

        document.getElementById('totalRounds').textContent = totalRounds;
        document.getElementById('uniqueCourses').textContent = uniqueCourses;
        document.getElementById('avgScore').textContent = avgScore;
        document.getElementById('bestScore').textContent = bestScore;
    }

    renderRounds() {
        const container = document.getElementById('roundsList');
        
        if (this.rounds.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h3>No rounds recorded yet</h3>
                    <p>Add your first round to get started!</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.rounds.map(round => `
            <div class="round-item">
                <div class="round-info">
                    <h3>${round.courseName}</h3>
                    <div class="round-details">
                        📍 ${round.courseLocation} • 📅 ${this.formatDate(round.playDate)}
                        ${round.par ? ` • Par ${round.par}` : ''}
                        ${round.tees ? ` • ${round.tees} Tees` : ''}
                        ${round.notes ? `<br>💭 ${round.notes}` : ''}
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div class="round-score">${round.score}</div>
                    <button onclick="golfTracker.deleteRound(${round.id})" 
                            style="background: #ff4444; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer;">
                        🗑️
                    </button>
                </div>
            </div>
        `).join('');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }

    clearForm() {
        document.getElementById('addRoundForm').reset();
        this.setTodayDate();
    }

    openAddRoundModal() {
        document.getElementById('addRoundModal').style.display = 'block';
        document.getElementById('courseName').focus();
    }

    closeAddRoundModal() {
        document.getElementById('addRoundModal').style.display = 'none';
    }
}

// Global functions for HTML onclick events
function openAddRoundModal() {
    golfTracker.openAddRoundModal();
}

function closeAddRoundModal() {
    golfTracker.closeAddRoundModal();
}

// Initialize the app
const golfTracker = new GolfTracker();

// Service Worker Registration for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Add to home screen prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    
    // Show install button or banner
    const installBanner = document.createElement('div');
    installBanner.innerHTML = `
        <div style="background: #2e7d32; color: white; padding: 15px; text-align: center; position: fixed; top: 0; left: 0; right: 0; z-index: 1001;">
            <p>Install Golf Tracker on your phone for easy access!</p>
            <button onclick="installApp()" style="background: white; color: #2e7d32; border: none; padding: 8px 16px; border-radius: 4px; margin-left: 10px; cursor: pointer;">
                Install App
            </button>
            <button onclick="this.parentElement.parentElement.remove()" style="background: transparent; color: white; border: 1px solid white; padding: 8px 16px; border-radius: 4px; margin-left: 10px; cursor: pointer;">
                Maybe Later
            </button>
        </div>
    `;
    document.body.appendChild(installBanner);
});

function installApp() {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            }
            deferredPrompt = null;
        });
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeAddRoundModal();
    }
    if (e.key === 'n' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        openAddRoundModal();
    }
});
