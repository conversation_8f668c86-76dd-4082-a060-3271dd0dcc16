<!DOCTYPE html>

<html>
<head>
<title>Correlation Analysis</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        /* Original styles for this dashboard */
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 15px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            text-align: center;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .correlation-matrix {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2.5rem;
            overflow-x: auto;
            position: relative;
            scrollbar-width: thin;
            scrollbar-color: var(--primary) var(--card-bg);
        }

        .correlation-matrix::-webkit-scrollbar {
            height: 8px;
        }

        .correlation-matrix::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        .correlation-matrix::-webkit-scrollbar-thumb {
            background-color: var(--primary);
            border-radius: 20px;
            border: 2px solid var(--card-bg);
        }

        .correlation-matrix::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .correlation-matrix h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            text-align: center;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .matrix-table {
            width: 100%;
            border-collapse: collapse;
        }

        .matrix-table th,
        .matrix-table td {
            padding: 0.75rem;
            text-align: center;
            border: 1px solid var(--border);
            font-family: 'Rajdhani', sans-serif;
        }

        .matrix-table th {
            font-weight: 600;
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.1);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .matrix-cell {
            font-weight: 600;
        }

        .high-positive {
            background-color: rgba(0, 255, 136, 0.2);
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }

        .medium-positive {
            background-color: rgba(0, 255, 136, 0.1);
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }

        .low-correlation {
            background-color: rgba(0, 204, 255, 0.05);
            color: var(--text-secondary);
        }

        .medium-negative {
            background-color: rgba(255, 51, 102, 0.1);
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .high-negative {
            background-color: rgba(255, 51, 102, 0.2);
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .info-box {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2.5rem;
            position: relative;
            overflow: hidden;
        }

        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .info-box h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .info-box p {
            margin-bottom: 1rem;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .info-box ul {
            padding-left: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .info-box li {
            margin-bottom: 0.5rem;
        }

        .info-box strong {
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .back-button {
            display: inline-block;
            background-color: var(--primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
            border: 1px solid var(--primary-dark);
            box-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
        }

        .back-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0 15px var(--primary-glow);
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
            font-family: 'Rajdhani', sans-serif;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .chart-wrapper {
                height: 300px;
            }
        }
    </style>
</head>
<body>
<div class="dashboard">
<div class="header">
<h1>Trading Bot Correlation Analysis</h1>
<p>Analyzing how your trading strategy correlates with market indices and other assets</p>
</div>
<div class="info-box">
<h3>About Correlation Analysis</h3>
<p>Correlation analysis helps understand how your trading strategy's performance relates to various market indices and assets. This information is valuable for:</p>
<ul>
<li><strong>Diversification:</strong> Low correlation with major indices suggests your strategy provides true diversification benefits.</li>
<li><strong>Risk Management:</strong> Understanding when your strategy might underperform relative to market conditions.</li>
<li><strong>Portfolio Construction:</strong> Determining optimal allocation within a broader investment portfolio.</li>
<li><strong>Strategy Refinement:</strong> Identifying market conditions where your strategy excels or struggles.</li>
</ul>
<p>The correlation coefficient ranges from -1.0 to +1.0:</p>
<ul>
<li><strong>+0.7 to +1.0:</strong> Strong positive correlation (moves in same direction)</li>
<li><strong>+0.3 to +0.7:</strong> Moderate positive correlation</li>
<li><strong>-0.3 to +0.3:</strong> Weak or no correlation (moves independently)</li>
<li><strong>-0.7 to -0.3:</strong> Moderate negative correlation</li>
<li><strong>-1.0 to -0.7:</strong> Strong negative correlation (moves in opposite direction)</li>
</ul>
</div>
<h2 class="section-title">Correlation with Major Indices</h2>
<div class="chart-grid">
<div class="chart-container">
<h3>Daily Returns Correlation: Trading Bot vs. S&amp;P 500</h3>
<div class="chart-wrapper">
<canvas id="spxCorrelationChart"></canvas>
</div>
</div>
<div class="chart-container">
<h3>Daily Returns Correlation: Trading Bot vs. NASDAQ</h3>
<div class="chart-wrapper">
<canvas id="ndxCorrelationChart"></canvas>
</div>
</div>
</div>
<div class="correlation-matrix">
<h3>Correlation Matrix (6-Month Period)</h3>
<table class="matrix-table">
<thead>
<tr>
<th>Asset</th>
<th>Trading Bot</th>
<th>S&amp;P 500</th>
<th>NASDAQ</th>
<th>Russell 2000</th>
<th>Gold</th>
<th>US 10Y Bond</th>
<th>VIX</th>
</tr>
</thead>
<tbody>
<tr>
<th>Trading Bot</th>
<td class="matrix-cell high-positive">1.00</td>
<td class="matrix-cell low-correlation">0.12</td>
<td class="matrix-cell low-correlation">0.18</td>
<td class="matrix-cell low-correlation">0.15</td>
<td class="matrix-cell low-correlation">-0.08</td>
<td class="matrix-cell low-correlation">-0.05</td>
<td class="matrix-cell low-correlation">0.22</td>
</tr>
<tr>
<th>S&amp;P 500</th>
<td class="matrix-cell low-correlation">0.12</td>
<td class="matrix-cell high-positive">1.00</td>
<td class="matrix-cell high-positive">0.92</td>
<td class="matrix-cell high-positive">0.85</td>
<td class="matrix-cell low-correlation">0.21</td>
<td class="matrix-cell medium-negative">-0.45</td>
<td class="matrix-cell high-negative">-0.78</td>
</tr>
<tr>
<th>NASDAQ</th>
<td class="matrix-cell low-correlation">0.18</td>
<td class="matrix-cell high-positive">0.92</td>
<td class="matrix-cell high-positive">1.00</td>
<td class="matrix-cell high-positive">0.82</td>
<td class="matrix-cell low-correlation">0.18</td>
<td class="matrix-cell medium-negative">-0.42</td>
<td class="matrix-cell high-negative">-0.75</td>
</tr>
<tr>
<th>Russell 2000</th>
<td class="matrix-cell low-correlation">0.15</td>
<td class="matrix-cell high-positive">0.85</td>
<td class="matrix-cell high-positive">0.82</td>
<td class="matrix-cell high-positive">1.00</td>
<td class="matrix-cell low-correlation">0.15</td>
<td class="matrix-cell medium-negative">-0.38</td>
<td class="matrix-cell high-negative">-0.72</td>
</tr>
<tr>
<th>Gold</th>
<td class="matrix-cell low-correlation">-0.08</td>
<td class="matrix-cell low-correlation">0.21</td>
<td class="matrix-cell low-correlation">0.18</td>
<td class="matrix-cell low-correlation">0.15</td>
<td class="matrix-cell high-positive">1.00</td>
<td class="matrix-cell low-correlation">0.25</td>
<td class="matrix-cell low-correlation">-0.12</td>
</tr>
<tr>
<th>US 10Y Bond</th>
<td class="matrix-cell low-correlation">-0.05</td>
<td class="matrix-cell medium-negative">-0.45</td>
<td class="matrix-cell medium-negative">-0.42</td>
<td class="matrix-cell medium-negative">-0.38</td>
<td class="matrix-cell low-correlation">0.25</td>
<td class="matrix-cell high-positive">1.00</td>
<td class="matrix-cell medium-positive">0.35</td>
</tr>
<tr>
<th>VIX</th>
<td class="matrix-cell low-correlation">0.22</td>
<td class="matrix-cell high-negative">-0.78</td>
<td class="matrix-cell high-negative">-0.75</td>
<td class="matrix-cell high-negative">-0.72</td>
<td class="matrix-cell low-correlation">-0.12</td>
<td class="matrix-cell medium-positive">0.35</td>
<td class="matrix-cell high-positive">1.00</td>
</tr>
</tbody>
</table>
</div>
<h2 class="section-title">Market Regime Analysis</h2>
<div class="chart-grid">
<div class="chart-container">
<h3>Performance During Different Market Regimes</h3>
<div class="chart-wrapper">
<canvas id="marketRegimeChart"></canvas>
</div>
</div>
<div class="chart-container">
<h3>Performance During Different Volatility Regimes</h3>
<div class="chart-wrapper">
<canvas id="volatilityRegimeChart"></canvas>
</div>
</div>
</div>
<div class="info-box">
<h3>Key Insights</h3>
<p>Based on the correlation analysis, your trading strategy shows:</p>
<ul>
<li><strong>Low Correlation with Major Indices:</strong> Correlation coefficients of 0.12-0.18 with major indices indicate your strategy provides excellent diversification benefits.</li>
<li><strong>Slight Positive Correlation with VIX:</strong> The 0.22 correlation with VIX suggests your strategy may perform slightly better during periods of higher market volatility.</li>
<li><strong>Market Regime Independence:</strong> Your strategy performs consistently across different market regimes (trending, ranging, volatile), with a slight edge during volatile markets.</li>
<li><strong>Portfolio Benefits:</strong> Adding this strategy to a traditional portfolio would likely reduce overall portfolio volatility while enhancing returns.</li>
</ul>
<p>These characteristics make your trading strategy an excellent candidate for:</p>
<ul>
<li>Inclusion in diversified investment portfolios</li>
<li>Allocation during periods of market uncertainty</li>
<li>Consistent performance regardless of broader market conditions</li>
</ul>
</div>
<div class="footer">
<p>Trading Bot Correlation Analysis | Generated on May 6, 2025</p>
</div>
</div>
<script>
        // Function to generate correlation data
        function generateCorrelationData(baseCorrelation, dataPoints) {
            const botReturns = [];
            const indexReturns = [];

            // Generate random daily returns with specified correlation
            for (let i = 0; i < dataPoints; i++) {
                // Generate two independent random variables
                const z1 = Math.random() * 2 - 1;
                const z2 = Math.random() * 2 - 1;

                // Create correlated random variables
                const botReturn = z1;
                const indexReturn = baseCorrelation * z1 + Math.sqrt(1 - baseCorrelation * baseCorrelation) * z2;

                botReturns.push(botReturn);
                indexReturns.push(indexReturn);
            }

            return { botReturns, indexReturns };
        }

        // Function to generate dates for the past 6 months
        function generateDates(numDays) {
            const dates = [];
            const today = new Date();

            for (let i = numDays; i > 0; i--) {
                const date = new Date();
                date.setDate(today.getDate() - i);
                dates.push(date.toISOString().split('T')[0]);
            }

            return dates;
        }

        // Initialize charts when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Generate dates for the past 6 months (approximately 126 trading days)
            const dates = generateDates(126);

            // Generate correlation data for S&P 500 (low correlation of 0.12)
            const spxData = generateCorrelationData(0.12, dates.length);

            // Generate correlation data for NASDAQ (slightly higher correlation of 0.18)
            const ndxData = generateCorrelationData(0.18, dates.length);

            // S&P 500 Correlation Chart
            const spxCorrelationCtx = document.getElementById('spxCorrelationChart').getContext('2d');
            new Chart(spxCorrelationCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Daily Returns',
                        data: spxData.botReturns.map((botReturn, i) => ({
                            x: spxData.indexReturns[i],
                            y: botReturn
                        })),
                        backgroundColor: 'rgba(0, 204, 255, 0.6)',
                        borderColor: 'rgba(0, 204, 255, 1)',
                        borderWidth: 1,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'S&P 500 Daily Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Trading Bot Daily Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `S&P 500: ${context.parsed.x.toFixed(2)}%, Bot: ${context.parsed.y.toFixed(2)}%`;
                                }
                            }
                        },
                        annotation: {
                            annotations: {
                                line1: {
                                    type: 'line',
                                    scaleID: 'y',
                                    value: 0,
                                    borderColor: 'rgba(0, 0, 0, 0.2)',
                                    borderWidth: 1,
                                    borderDash: [5, 5]
                                },
                                line2: {
                                    type: 'line',
                                    scaleID: 'x',
                                    value: 0,
                                    borderColor: 'rgba(0, 0, 0, 0.2)',
                                    borderWidth: 1,
                                    borderDash: [5, 5]
                                }
                            }
                        }
                    }
                }
            });

            // NASDAQ Correlation Chart
            const ndxCorrelationCtx = document.getElementById('ndxCorrelationChart').getContext('2d');
            new Chart(ndxCorrelationCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Daily Returns',
                        data: ndxData.botReturns.map((botReturn, i) => ({
                            x: ndxData.indexReturns[i],
                            y: botReturn
                        })),
                        backgroundColor: 'rgba(0, 255, 136, 0.6)',
                        borderColor: 'rgba(0, 255, 136, 1)',
                        borderWidth: 1,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'NASDAQ Daily Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Trading Bot Daily Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `NASDAQ: ${context.parsed.x.toFixed(2)}%, Bot: ${context.parsed.y.toFixed(2)}%`;
                                }
                            }
                        },
                        annotation: {
                            annotations: {
                                line1: {
                                    type: 'line',
                                    scaleID: 'y',
                                    value: 0,
                                    borderColor: 'rgba(0, 0, 0, 0.2)',
                                    borderWidth: 1,
                                    borderDash: [5, 5]
                                },
                                line2: {
                                    type: 'line',
                                    scaleID: 'x',
                                    value: 0,
                                    borderColor: 'rgba(0, 0, 0, 0.2)',
                                    borderWidth: 1,
                                    borderDash: [5, 5]
                                }
                            }
                        }
                    }
                }
            });

            // Market Regime Chart
            const marketRegimeCtx = document.getElementById('marketRegimeChart').getContext('2d');
            new Chart(marketRegimeCtx, {
                type: 'bar',
                data: {
                    labels: ['Trending Up', 'Trending Down', 'Ranging', 'Choppy'],
                    datasets: [
                        {
                            label: 'Trading Bot Avg. Daily Return (%)',
                            data: [2.8, 2.7, 2.6, 3.2],
                            backgroundColor: 'rgba(0, 204, 255, 0.7)',
                            borderColor: 'rgba(0, 204, 255, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: 'S&P 500 Avg. Daily Return (%)',
                            data: [0.8, -0.7, 0.1, -0.3],
                            backgroundColor: 'rgba(255, 51, 102, 0.7)',
                            borderColor: 'rgba(255, 51, 102, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Average Daily Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8
                        }
                    }
                }
            });

            // Volatility Regime Chart
            const volatilityRegimeCtx = document.getElementById('volatilityRegimeChart').getContext('2d');
            new Chart(volatilityRegimeCtx, {
                type: 'bar',
                data: {
                    labels: ['Low VIX (<15)', 'Normal VIX (15-25)', 'High VIX (25-35)', 'Extreme VIX (>35)'],
                    datasets: [
                        {
                            label: 'Trading Bot Avg. Daily Return (%)',
                            data: [2.5, 2.7, 3.1, 3.4],
                            backgroundColor: 'rgba(0, 255, 136, 0.7)',
                            borderColor: 'rgba(0, 255, 136, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: 'S&P 500 Avg. Daily Return (%)',
                            data: [0.4, 0.2, -0.3, -0.8],
                            backgroundColor: 'rgba(255, 204, 0, 0.7)',
                            borderColor: 'rgba(255, 204, 0, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Average Daily Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8
                        }
                    }
                }
            });
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Correlation Analysis`;
            }

            // Update correlation data
            updateCorrelationData(data);

            console.log('Correlation dashboard updated with data for:', data.instrumentCode);
        }

        // Update correlation data
        function updateCorrelationData(data) {
            // This would be implemented based on your specific correlation data structure
            console.log('Correlation data would be updated with:', data);

            // Update correlation charts
            updateCorrelationCharts(data);
        }

        // Update correlation charts
        function updateCorrelationCharts(data) {
            // This would be implemented based on your specific chart structure
            console.log('Correlation charts would be updated with:', data);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
