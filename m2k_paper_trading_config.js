/**
 * M2K (<PERSON> Russell 2000) Paper Trading Configuration
 *
 * This file contains the configuration for paper trading M2K futures.
 * Parameters are based on the optimized backtest results.
 */

module.exports = {
    // Symbol information
    symbol: 'M2K',
    pointValue: 5.0,
    tickSize: 0.10,

    // Contract information
    contractMonth: 'M5', // May 2025 contract
    fullSymbol: 'M2KM5', // Full symbol with month code

    // Account information
    accountId: null, // Will be set during initialization

    // Commission and slippage
    commission: 0.0,
    commissionPerContract: 0.0,
    slippage: 0.0,
    slippagePoints: 0.0,

    // Position sizing
    fixedContracts: 3,

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    sma200Period: 0, // Not used
    atrPeriod: 14,

    // Entry/exit parameters - Optimized values from grid testing
    slFactors: 5.0,
    tpFactors: 4.0,
    trailFactors: 0.03,
    fixedTpPoints: 4,
    fixedSlPoints: 5,

    // Risk management
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0.5,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    useTwoBarColorExit: false,
    requireClosedCandles: true, // Only trade on fully closed candles

    // ATR thresholds for adaptive mode
    isAdaptiveRun: false,
    atrThresholds: {
        low_medium: 1.5,
        medium_high: 3.0
    },

    // Formatting
    pricePrecision: 1,

    // Logging
    logLevel: 'info'
};
