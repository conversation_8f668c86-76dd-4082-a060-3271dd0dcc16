/**
 * Explicit Test Order Utility
 * 
 * This script provides a simple way to test order placement through the Tradovate API
 * with explicit logging and error handling.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');

// Default test order parameters
const DEFAULT_SYMBOL = 'MNQM5'; // Using M5 as seen in Tradovate platform (M for May, 5 for 2025)
const DEFAULT_ACTION = 'Buy';
const DEFAULT_QTY = 1;
const DEFAULT_ORDER_TYPE = 'Market';

/**
 * Place a test order with explicit logging
 */
async function placeExplicitTestOrder() {
    try {
        console.log('\n========== EXPLICIT TEST ORDER ==========');
        console.log('Starting test order placement with explicit logging...');
        
        // Configure API with demo mode
        tradovateApi.setConfig({
            baseUrl: 'https://demo.tradovateapi.com/v1',
            wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
            mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
        });
        
        // Step 1: Authenticate with Tradovate API
        console.log('\nStep 1: Authenticating with Tradovate API...');
        const authResult = await tradovateApi.authenticate();
        
        if (!authResult.success) {
            console.error(`Authentication failed: ${authResult.error}`);
            return false;
        }
        
        console.log('Authentication successful!');
        console.log(`Access Token: ${authResult.accessToken.substring(0, 10)}...`);
        console.log(`User ID: ${authResult.userId}`);
        console.log(`Token expires at: ${new Date(authResult.expirationTime).toLocaleString()}`);
        
        // Step 2: Get account information
        console.log('\nStep 2: Getting account information...');
        const accounts = await tradovateApi.makeApiRequest('GET', 'account/list');
        
        if (!accounts || !Array.isArray(accounts) || accounts.length === 0) {
            console.error('No accounts found');
            return false;
        }
        
        console.log(`Found ${accounts.length} accounts:`);
        accounts.forEach(account => {
            console.log(`- ${account.name} (ID: ${account.id})`);
        });
        
        // Find demo account
        const demoAccount = accounts.find(acc => acc.name.startsWith('DEMO'));
        if (!demoAccount) {
            console.error('No demo account found');
            return false;
        }
        
        console.log(`\nUsing demo account: ${demoAccount.name} (ID: ${demoAccount.id})`);
        
        // Step 3: Prepare order parameters
        console.log('\nStep 3: Preparing order parameters...');
        const orderParams = {
            accountSpec: demoAccount.name,
            accountId: demoAccount.id,
            symbol: DEFAULT_SYMBOL,
            action: DEFAULT_ACTION,
            orderQty: DEFAULT_QTY,
            orderType: DEFAULT_ORDER_TYPE,
            isAutomated: true,
            clOrdId: `test-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            text: 'Explicit test order'
        };
        
        console.log('Order parameters:');
        console.log(JSON.stringify(orderParams, null, 2));
        
        // Step 4: Place the order
        console.log('\nStep 4: Placing order...');
        try {
            const result = await tradovateApi.makeApiRequest('POST', 'order/placeorder', orderParams);
            
            console.log('\nOrder placed successfully!');
            console.log('Order result:');
            console.log(JSON.stringify(result, null, 2));
            
            return true;
        } catch (error) {
            console.error('\nError placing order:');
            
            if (error.response) {
                console.error(`Status: ${error.response.status}`);
                console.error(`Data: ${JSON.stringify(error.response.data, null, 2)}`);
                
                // Check for market closed error
                if (error.response.data && typeof error.response.data === 'string') {
                    if (error.response.data.includes('market') && error.response.data.includes('closed')) {
                        console.error('\nMarket appears to be closed. Orders cannot be placed at this time.');
                        console.error('This is normal behavior. Please try again when the market is open.');
                    }
                }
            } else {
                console.error(`Error: ${error.message}`);
            }
            
            return false;
        }
    } catch (error) {
        console.error(`\nUnhandled error: ${error.message}`);
        return false;
    } finally {
        console.log('\n========== TEST COMPLETE ==========');
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    placeExplicitTestOrder();
}

module.exports = {
    placeExplicitTestOrder
};
