// config.js - MES Configuration (Mirrored from MNQ, needs optimization)

module.exports = {
    // --- General Settings ---
    inputFile: 'input/MES_2020-2025.csv', // Your MES data file
    initialBalance: 10000,

    // --- Instrument Specifics (MES Specs) ---
    pointValue: 5.00,       // MES point value
    tickSize: 0.25,         // MES tick size
    pricePrecision: 2,      // MES price precision

    // --- Costs & Slippage ---
    commissionPerContract: 0.4, // Matching MNQ for now, adjust if needed
    slippagePoints: 0.25,       // 1 Tick Slippage ($1.25) + $0.40 Comm = $1.65 RT Cost. ADJUST AS NEEDED

    // --- Indicator Periods ---
    atrPeriod: 14,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    sma200Period: 0, // Keep disabled like MNQ
    wma50Period: 0,  // Keep disabled like MNQ

    // --- Strategy Parameters ---
    // ** These will likely need significant tuning for MES **
    fixedTpPoints: 20, // STARTING GUESS: 20 points = $100 on MES. Adjust based on tests.
    useWmaFilter: false,
    useTwoBarColorExit: false,
    minAtrEntry: 0,
    minRsiMaSeparation: 0,

    // --- RSI Bands ---
    rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

    // --- Run Mode ---
    // Start with Adaptive TRUE like MNQ, but can test Fixed later
    isAdaptiveRun: true,

    // --- Core Factors (STARTING POINTS based on MNQ - **EXPECT TO CHANGE SIGNIFICANTLY**) ---
    slFactors: 3.0,     // Starting point - Grid Test for MES
    tpFactors: 3.0,     // Starting point - Grid Test for MES
    trailFactors: 0.01,   // Starting point - Grid Test for MES
    // **********************************************************************************

    // --- Latency ---
    latencyDelayBars: 0, // Keep 0 latency for now

    // --- Grid Test Placeholders (Keep null for single/adaptive runs) ---
    costGrid: null,
    riskPercentGrid: null,
    fixedContractsGrid: null,
    fixedTpPointsGrid: null,

    // --- Position Sizing ---
    riskPercent: 0,
    fixedContracts: 10, // Matching MNQ contract size for now
    maxContracts: 10,   // Matching MNQ

    // --- Backtest Compatibility --- (These might be redundant if sizing logic is robust)
    currentRiskPercent: 0,
    currentFixedContracts: 10,

    // --- Time Filter Settings ---
    timeFilterEnabled: false, // Keep disabled like MNQ
    timeFilter: { start: 8, end: 15 },
    cstOffset: -5,
};