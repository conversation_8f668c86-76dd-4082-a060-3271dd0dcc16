/**
 * Test Demo Trading Bot
 * 
 * This script tests the demo trading bot to ensure it's ready for trading.
 */

const demoTrading = require('./demo_trading_optimized');
const logger = require('./data_logger');

// Test the demo trading bot
async function testDemoTrading() {
    console.log('Testing demo trading bot...');
    
    try {
        // Initialize the bot
        console.log('Initializing demo trading bot...');
        const initialized = await demoTrading.initialize();
        
        if (!initialized) {
            console.error('Failed to initialize demo trading bot');
            return false;
        }
        
        console.log('Demo trading bot initialized successfully');
        
        // Get status
        console.log('\nGetting bot status...');
        const status = demoTrading.getStatus();
        console.log('Bot status:', JSON.stringify(status, null, 2));
        
        // Wait for a moment to see if any trades are generated
        console.log('\nWaiting for 30 seconds to see if any trades are generated...');
        await new Promise(resolve => setTimeout(resolve, 30000));
        
        // Get positions and orders
        console.log('\nGetting positions and orders...');
        const positions = demoTrading.getPositions();
        const orders = demoTrading.getOrders();
        
        console.log('Active positions:', Object.keys(positions).length);
        console.log('Active orders:', Object.keys(orders).length);
        
        // Shutdown the bot
        console.log('\nShutting down demo trading bot...');
        const shutdown = await demoTrading.shutdown();
        
        if (!shutdown) {
            console.error('Failed to shutdown demo trading bot');
            return false;
        }
        
        console.log('Demo trading bot shutdown successfully');
        console.log('\nDemo trading bot test completed successfully!');
        return true;
    } catch (error) {
        console.error(`Demo trading bot test failed: ${error.message}`);
        return false;
    }
}

// Run the test
testDemoTrading()
    .then(success => {
        if (success) {
            console.log('All demo trading bot tests passed! Ready for demo trading tomorrow.');
        } else {
            console.error('Demo trading bot tests failed. Please check the errors above.');
        }
        
        // Exit after a short delay to allow any pending operations to complete
        setTimeout(() => process.exit(success ? 0 : 1), 1000);
    })
    .catch(error => {
        console.error(`Unexpected error: ${error.message}`);
        process.exit(1);
    });
