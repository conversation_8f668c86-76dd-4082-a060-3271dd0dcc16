/**
 * Run Demo Trading
 *
 * This script starts the demo trading bot and provides a simple command-line interface
 * to control it.
 */

const readline = require('readline');
const demoTrading = require('./demo_trading_optimized');
const logger = require('./data_logger');

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Bot state
let isRunning = false;

// Command handlers
const commands = {
    help: () => {
        console.log('\n=== Demo Trading Bot Commands ===');
        console.log('start - Start the demo trading bot');
        console.log('stop - Stop the demo trading bot');
        console.log('status - Check the status of the demo trading bot');
        console.log('orders - List all active orders');
        console.log('positions - List all open positions');
        console.log('setconfig [key] [value] - Set configuration value');
        console.log('clear - Clear the console');
        console.log('exit - Exit the program');
        console.log('help - Show this help message');
        return Promise.resolve();
    },

    start: async () => {
        if (isRunning) {
            console.log('Demo trading bot is already running.');
            return;
        }

        console.log('Starting demo trading bot...');
        const initialized = await demoTrading.initialize();

        if (initialized) {
            isRunning = true;
            console.log('Demo trading bot started successfully.');
        } else {
            console.error('Failed to start demo trading bot.');
        }
    },

    stop: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running.');
            return;
        }

        console.log('Stopping demo trading bot...');
        const shutdown = await demoTrading.shutdown();

        if (shutdown) {
            isRunning = false;
            console.log('Demo trading bot stopped successfully.');
        } else {
            console.error('Failed to stop demo trading bot.');
        }
    },

    status: () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running.');
            return Promise.resolve();
        }

        const status = demoTrading.getStatus();
        console.log('\n=== Demo Trading Bot Status ===');
        console.log(`Connected: ${status.isConnected}`);
        console.log(`Initialized: ${status.isInitialized}`);
        console.log(`Active Positions: ${status.activePositions}`);
        console.log(`Active Orders: ${status.activeOrders}`);
        console.log(`Circuit Breakers Tripped: ${status.circuitBreakers.isTripped}`);
        console.log(`Daily Loss: $${status.circuitBreakers.dailyLoss.toFixed(2)}`);

        if (status.circuitBreakers.lastResetTime) {
            console.log(`Last Reset: ${new Date(status.circuitBreakers.lastResetTime).toLocaleString()}`);
        }

        return Promise.resolve();
    },

    orders: () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running.');
            return Promise.resolve();
        }

        const orders = demoTrading.getOrders();
        console.log('\n=== Active Orders ===');

        if (Object.keys(orders).length === 0) {
            console.log('No active orders.');
        } else {
            for (const orderId in orders) {
                const order = orders[orderId];
                console.log(`Order ID: ${orderId}`);
                console.log(`  Symbol: ${order.symbol}`);
                console.log(`  Action: ${order.action}`);
                console.log(`  Quantity: ${order.quantity}`);
                console.log(`  Entry Price: ${order.entryPrice}`);
                console.log(`  Stop Loss: ${order.stopLossPrice}`);
                console.log(`  Take Profit: ${order.takeProfitPrice}`);
                console.log(`  Trail Factor: ${order.trailFactor}`);
                console.log(`  ATR Regime: ${order.atrRegime}`);
                console.log(`  Timestamp: ${new Date(order.timestamp).toLocaleString()}`);
                console.log('---');
            }
        }

        return Promise.resolve();
    },

    positions: () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running.');
            return Promise.resolve();
        }

        const positions = demoTrading.getPositions();
        console.log('\n=== Open Positions ===');

        if (Object.keys(positions).length === 0) {
            console.log('No open positions.');
        } else {
            for (const positionId in positions) {
                const position = positions[positionId];
                console.log(`Position ID: ${positionId}`);
                console.log(`  Symbol: ${position.symbol}`);
                console.log(`  Action: ${position.action}`);
                console.log(`  Quantity: ${position.quantity}`);
                console.log(`  Entry Price: ${position.entryPrice}`);
                console.log(`  Stop Loss: ${position.stopLossPrice}`);
                console.log(`  Take Profit: ${position.takeProfitPrice}`);
                console.log(`  Trail Factor: ${position.trailFactor}`);
                console.log(`  Timestamp: ${new Date(position.timestamp).toLocaleString()}`);
                console.log('---');
            }
        }

        return Promise.resolve();
    },

    setconfig: (key, value) => {
        if (!key) {
            console.log('Please specify a configuration key.');
            return Promise.resolve();
        }

        if (!value) {
            console.log('Please specify a configuration value.');
            return Promise.resolve();
        }

        // Convert value to appropriate type
        let parsedValue = value;
        if (value === 'true') parsedValue = true;
        else if (value === 'false') parsedValue = false;
        else if (!isNaN(Number(value))) parsedValue = Number(value);

        console.log(`Setting ${key} to ${parsedValue}`);

        // Set configuration value
        // This is a placeholder - actual implementation would depend on how
        // the demo trading bot handles configuration changes
        console.log(`Configuration updated: ${key} = ${parsedValue}`);
        return Promise.resolve();
    },

    clear: () => {
        console.clear();
        return Promise.resolve();
    },

    exit: async () => {
        console.log('Shutting down...');
        if (isRunning) {
            await demoTrading.shutdown();
        }
        rl.close();
        process.exit(0);
    }
};

// Process user input
function processCommand(input) {
    const args = input.trim().split(' ');
    const command = args[0].toLowerCase();

    if (commands[command]) {
        return commands[command](...args.slice(1));
    } else {
        console.log(`Unknown command: ${command}`);
        console.log('Type "help" for a list of available commands');
        return Promise.resolve();
    }
}

// Main function
async function main() {
    console.log('='.repeat(80));
    console.log(' '.repeat(20) + 'DEMO TRADING BOT CONSOLE');
    console.log('='.repeat(80));
    console.log('Type "help" for a list of commands.');

    // Set up prompt
    rl.setPrompt('> ');
    rl.prompt();

    rl.on('line', async (line) => {
        try {
            await processCommand(line);
        } catch (error) {
            console.error('Error processing command:', error.message);
        }
        rl.prompt();
    });

    rl.on('close', () => {
        console.log('Exiting...');
        process.exit(0);
    });

    // Handle process termination
    process.on('SIGINT', async () => {
        console.log('\nReceived SIGINT. Shutting down...');
        if (isRunning) {
            await demoTrading.shutdown();
        }
        process.exit(0);
    });
}

// Run the main function
if (require.main === module) {
    main().catch(error => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}
