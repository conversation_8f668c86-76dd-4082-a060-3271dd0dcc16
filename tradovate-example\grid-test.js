/**
 * Grid testing script to find optimal parameters
 * This script will test different combinations of parameters and report the results
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');

// Define parameter ranges to test
const paramRanges = {
  MNQ: {
    slFactors: [2.5, 3.0, 3.4, 3.8, 4.2],
    tpFactors: [1.5, 1.8, 2.0, 2.2, 2.5],
    trailFactors: [0.08, 0.10, 0.11, 0.12, 0.14]
  },
  MES: {
    slFactors: [4.5, 5.0, 5.5, 6.0, 6.5],
    tpFactors: [3.0, 3.5, 4.0, 4.5, 5.0],
    trailFactors: [0.06, 0.07, 0.08, 0.09, 0.10]
  },
  MGC: {
    slFactors: [7.5, 8.0, 8.5, 9.0, 9.5],
    tpFactors: [6.0, 6.5, 7.0, 7.5, 8.0],
    trailFactors: [0.01, 0.015, 0.02, 0.025, 0.03]
  }
};

// Define the symbols to test
const symbols = ['MNQ', 'MES', 'MGC'];

// Function to run grid test for a symbol
async function runGridTest(symbol) {
  const results = [];
  const ranges = paramRanges[symbol];

  console.log(`Starting grid test for ${symbol}...`);
  console.log(`Testing ${ranges.slFactors.length} SL factors, ${ranges.tpFactors.length} TP factors, and ${ranges.trailFactors.length} trail factors`);
  console.log(`Total combinations: ${ranges.slFactors.length * ranges.tpFactors.length * ranges.trailFactors.length}`);

  let bestResult = null;
  let bestWinRate = 0;
  let bestPnL = 0;
  let bestDailyWinRate = 0;

  let count = 0;
  const totalCombinations = ranges.slFactors.length * ranges.tpFactors.length * ranges.trailFactors.length;

  for (const slFactor of ranges.slFactors) {
    for (const tpFactor of ranges.tpFactors) {
      for (const trailFactor of ranges.trailFactors) {
        count++;
        console.log(`Testing combination ${count}/${totalCombinations}: SL=${slFactor}, TP=${tpFactor}, Trail=${trailFactor}`);

        // Create config with current parameters
        const config = {
          symbol,
          slFactors: slFactor,
          tpFactors: tpFactor,
          trailFactors: trailFactor,
          fixedContracts: 10,
          useSma200Filter: false,
          useRsiFilter: true
        };

        // Run backtest with current parameters
        const result = await runBacktest(config);

        // Calculate daily win rate
        const dailyStats = result.dailyStats || {};
        const profitableDays = Object.values(dailyStats).filter(day => day.netPnL > 0).length;
        const totalDays = Object.keys(dailyStats).length;
        const dailyWinRate = totalDays > 0 ? (profitableDays / totalDays) * 100 : 0;

        // Add result to results array
        results.push({
          slFactor,
          tpFactor,
          trailFactor,
          winRate: result.winRate,
          pnl: result.pnl,
          trades: result.trades,
          maxDrawdown: result.maxDrawdown,
          dailyWinRate
        });

        // Update best result if this is better
        if (dailyWinRate > bestDailyWinRate ||
            (dailyWinRate === bestDailyWinRate && result.pnl > bestPnL)) {
          bestResult = {
            slFactor,
            tpFactor,
            trailFactor,
            winRate: result.winRate,
            pnl: result.pnl,
            trades: result.trades,
            maxDrawdown: result.maxDrawdown,
            dailyWinRate
          };
          bestWinRate = result.winRate;
          bestPnL = result.pnl;
          bestDailyWinRate = dailyWinRate;
        }

        // Log progress
        console.log(`Win Rate: ${result.winRate.toFixed(2)}%, PnL: $${result.pnl.toFixed(2)}, Daily Win Rate: ${dailyWinRate.toFixed(2)}%`);
      }
    }
  }

  // Sort results by daily win rate (primary) and PnL (secondary)
  results.sort((a, b) => {
    if (b.dailyWinRate !== a.dailyWinRate) {
      return b.dailyWinRate - a.dailyWinRate;
    }
    return b.pnl - a.pnl;
  });

  // Save results to file
  const resultsFile = path.join(__dirname, `grid_test_results_${symbol}.json`);
  fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));

  console.log(`\nGrid test for ${symbol} completed.`);
  console.log(`Best parameters: SL=${bestResult.slFactor}, TP=${bestResult.tpFactor}, Trail=${bestResult.trailFactor}`);
  console.log(`Best win rate: ${bestResult.winRate.toFixed(2)}%`);
  console.log(`Best PnL: $${bestResult.pnL.toFixed(2)}`);
  console.log(`Best daily win rate: ${bestResult.dailyWinRate.toFixed(2)}%`);
  console.log(`Results saved to ${resultsFile}`);

  return bestResult;
}

// Main function to run grid tests for all symbols
async function runAllGridTests() {
  const bestResults = {};

  for (const symbol of symbols) {
    bestResults[symbol] = await runGridTest(symbol);
  }

  console.log('\nGrid testing completed for all symbols.');
  console.log('Best parameters for each symbol:');

  for (const symbol of symbols) {
    const best = bestResults[symbol];
    console.log(`\n${symbol}:`);
    console.log(`SL=${best.slFactor}, TP=${best.tpFactor}, Trail=${best.trailFactor}`);
    console.log(`Win Rate: ${best.winRate.toFixed(2)}%`);
    console.log(`PnL: $${best.pnl.toFixed(2)}`);
    console.log(`Daily Win Rate: ${best.dailyWinRate.toFixed(2)}%`);
  }
}

// Run all grid tests
runAllGridTests().catch(console.error);
