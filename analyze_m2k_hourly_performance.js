// analyze_m2k_hourly_performance.js - Analyze M2K performance by hour of day

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

console.log("Starting M2K Hourly Performance Analysis...");

// --- Configuration ---
const config = {
  inputFile: 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv',
  outputDir: './output/M2K_HourlyAnalysis',
  initialBalance: 10000,
  pointValue: 5.00,
  commissionPerContract: 0.40,
  slippagePoints: 0.20,
  fixedContracts: 10,
  atrPeriod: 14,
  
  // Best parameters from grid test
  slFactor: 9.0,
  tpFactor: 9.0,
  trailFactor: 0.01
};

// --- Setup ---
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
  console.log(`Created output directory: ${config.outputDir}`);
} else {
  console.log(`Output directory exists: ${config.outputDir}`);
}

// --- Data Loading ---
console.log(`Loading data from: ${config.inputFile}`);
const allCandles = [];

// Check if file exists
if (!fs.existsSync(config.inputFile)) {
  console.error(`Error: Input file not found at path: ${config.inputFile}`);
  process.exit(1);
}

// Load data
fs.createReadStream(config.inputFile)
  .pipe(csv({ separator: ';' }))
  .on('data', (data) => {
    const open = parseFloat(data.Open);
    const high = parseFloat(data.High);
    const low = parseFloat(data.Low);
    const close = parseFloat(data.Close);
    const timeString = data.Time || data.Date || data['Time left'];
    
    let timestamp;
    try {
      timestamp = new Date(timeString).getTime() / 1000;
    } catch (e) {
      return; // Skip invalid dates
    }
    
    if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close) && !isNaN(timestamp)) {
      allCandles.push({ timestamp, open, high, low, close });
    }
  })
  .on('end', () => {
    console.log(`Loaded ${allCandles.length} candles`);
    
    if (allCandles.length === 0) {
      console.error("No valid data loaded. Check your CSV format.");
      process.exit(1);
    }
    
    // Sort candles by timestamp
    allCandles.sort((a, b) => a.timestamp - b.timestamp);
    
    // Calculate ATR
    calculateATR(allCandles, config.atrPeriod);
    
    // Run backtest with hourly tracking
    runHourlyAnalysis(allCandles, config);
  })
  .on('error', (err) => {
    console.error("Error reading CSV:", err);
    process.exit(1);
  });

// --- Calculate ATR ---
function calculateATR(candles, period) {
  // First calculate True Range for each candle
  for (let i = 0; i < candles.length; i++) {
    if (i === 0) {
      candles[i].tr = candles[i].high - candles[i].low;
    } else {
      const prev = candles[i-1];
      candles[i].tr = Math.max(
        candles[i].high - candles[i].low,
        Math.abs(candles[i].high - prev.close),
        Math.abs(candles[i].low - prev.close)
      );
    }
  }
  
  // Then calculate ATR as simple moving average of TR
  for (let i = 0; i < candles.length; i++) {
    if (i < period - 1) {
      candles[i].atr = candles[i].tr;
    } else {
      let sum = 0;
      for (let j = i - period + 1; j <= i; j++) {
        sum += candles[j].tr;
      }
      candles[i].atr = sum / period;
    }
  }
}

// --- Run Hourly Analysis ---
function runHourlyAnalysis(candles, config) {
  console.log("Running hourly performance analysis...");
  
  // Initialize hourly stats
  const hourlyStats = {};
  for (let hour = 0; hour < 24; hour++) {
    hourlyStats[hour] = {
      totalTrades: 0,
      wins: 0,
      losses: 0,
      totalPnL: 0,
      grossProfit: 0,
      grossLoss: 0,
      winRate: 0,
      profitFactor: 0,
      avgWin: 0,
      avgLoss: 0,
      avgPnL: 0,
      largestWin: 0,
      largestLoss: 0
    };
  }
  
  // Run backtest with hourly tracking
  let balance = config.initialBalance;
  let position = null;
  let trades = [];
  
  // Need at least 3 candles for pattern detection
  for (let i = 3; i < candles.length; i++) {
    const c0 = candles[i-3];
    const c1 = candles[i-2];
    const c2 = candles[i-1];
    const c3 = candles[i];
    
    // Skip if ATR is not available
    if (!c3.atr || isNaN(c3.atr) || c3.atr <= 0) continue;
    
    // Manage existing position
    if (position) {
      // Update trail stop
      if (position.direction === 'long') {
        position.trailStop = Math.max(position.trailStop, 
          c3.high - (c3.atr * config.trailFactor));
      } else {
        position.trailStop = Math.min(position.trailStop, 
          c3.low + (c3.atr * config.trailFactor));
      }
      
      // Check for exit conditions
      let exitPrice = null;
      let exitReason = null;
      
      if (position.direction === 'long') {
        // Stop loss hit
        if (c3.low <= position.stopLoss) {
          exitPrice = position.stopLoss;
          exitReason = 'stop_loss';
        }
        // Take profit hit
        else if (c3.high >= position.takeProfit) {
          exitPrice = position.takeProfit;
          exitReason = 'take_profit';
        }
        // Trail stop hit
        else if (c3.low <= position.trailStop) {
          exitPrice = position.trailStop;
          exitReason = 'trail_stop';
        }
      } else { // Short position
        // Stop loss hit
        if (c3.high >= position.stopLoss) {
          exitPrice = position.stopLoss;
          exitReason = 'stop_loss';
        }
        // Take profit hit
        else if (c3.low <= position.takeProfit) {
          exitPrice = position.takeProfit;
          exitReason = 'take_profit';
        }
        // Trail stop hit
        else if (c3.high >= position.trailStop) {
          exitPrice = position.trailStop;
          exitReason = 'trail_stop';
        }
      }
      
      // Process exit if conditions met
      if (exitPrice !== null) {
        const pnlPoints = position.direction === 'long' ? 
          exitPrice - position.entry : position.entry - exitPrice;
        const pnlDollars = pnlPoints * config.pointValue * config.fixedContracts;
        const commission = config.commissionPerContract * config.fixedContracts;
        const slippage = config.slippagePoints * config.pointValue * config.fixedContracts;
        const netPnl = pnlDollars - commission - slippage;
        
        balance += netPnl;
        
        // Get exit hour (UTC)
        const exitDate = new Date(c3.timestamp * 1000);
        const exitHour = exitDate.getUTCHours();
        
        // Record trade with hour information
        const trade = {
          entry: position.entry,
          exit: exitPrice,
          direction: position.direction,
          pnl: netPnl,
          reason: exitReason,
          entryTime: position.entryTime,
          exitTime: c3.timestamp,
          entryHour: position.entryHour,
          exitHour: exitHour
        };
        
        trades.push(trade);
        
        // Update hourly stats based on exit hour
        hourlyStats[exitHour].totalTrades++;
        hourlyStats[exitHour].totalPnL += netPnl;
        
        if (netPnl > 0) {
          hourlyStats[exitHour].wins++;
          hourlyStats[exitHour].grossProfit += netPnl;
          hourlyStats[exitHour].largestWin = Math.max(hourlyStats[exitHour].largestWin, netPnl);
        } else {
          hourlyStats[exitHour].losses++;
          hourlyStats[exitHour].grossLoss += Math.abs(netPnl);
          hourlyStats[exitHour].largestLoss = Math.max(hourlyStats[exitHour].largestLoss, Math.abs(netPnl));
        }
        
        position = null;
      }
    }
    
    // Look for new entry if no position
    if (!position) {
      // Simple pattern: bullish if previous candle is green and current is green
      const isBullish = c2.close > c2.open && c3.open < c3.close;
      // Simple pattern: bearish if previous candle is red and current is red
      const isBearish = c2.close < c2.open && c3.open > c3.close;
      
      // Get entry hour (UTC)
      const entryDate = new Date(c3.timestamp * 1000);
      const entryHour = entryDate.getUTCHours();
      
      if (isBullish) {
        position = {
          direction: 'long',
          entry: c3.close,
          stopLoss: c3.close - (c3.atr * config.slFactor),
          takeProfit: c3.close + (c3.atr * config.tpFactor),
          trailStop: c3.close - (c3.atr * config.trailFactor),
          entryTime: c3.timestamp,
          entryHour: entryHour
        };
      } else if (isBearish) {
        position = {
          direction: 'short',
          entry: c3.close,
          stopLoss: c3.close + (c3.atr * config.slFactor),
          takeProfit: c3.close - (c3.atr * config.tpFactor),
          trailStop: c3.close + (c3.atr * config.trailFactor),
          entryTime: c3.timestamp,
          entryHour: entryHour
        };
      }
    }
  }
  
  // Close any open position at the end
  if (position) {
    const lastCandle = candles[candles.length - 1];
    const pnlPoints = position.direction === 'long' ? 
      lastCandle.close - position.entry : position.entry - lastCandle.close;
    const pnlDollars = pnlPoints * config.pointValue * config.fixedContracts;
    const commission = config.commissionPerContract * config.fixedContracts;
    const netPnl = pnlDollars - commission;
    
    balance += netPnl;
    
    // Get exit hour (UTC)
    const exitDate = new Date(lastCandle.timestamp * 1000);
    const exitHour = exitDate.getUTCHours();
    
    // Record trade with hour information
    trades.push({
      entry: position.entry,
      exit: lastCandle.close,
      direction: position.direction,
      pnl: netPnl,
      reason: 'end_of_data',
      entryTime: position.entryTime,
      exitTime: lastCandle.timestamp,
      entryHour: position.entryHour,
      exitHour: exitHour
    });
    
    // Update hourly stats
    hourlyStats[exitHour].totalTrades++;
    hourlyStats[exitHour].totalPnL += netPnl;
    
    if (netPnl > 0) {
      hourlyStats[exitHour].wins++;
      hourlyStats[exitHour].grossProfit += netPnl;
      hourlyStats[exitHour].largestWin = Math.max(hourlyStats[exitHour].largestWin, netPnl);
    } else {
      hourlyStats[exitHour].losses++;
      hourlyStats[exitHour].grossLoss += Math.abs(netPnl);
      hourlyStats[exitHour].largestLoss = Math.max(hourlyStats[exitHour].largestLoss, Math.abs(netPnl));
    }
  }
  
  // Calculate final hourly metrics
  for (let hour = 0; hour < 24; hour++) {
    const stats = hourlyStats[hour];
    
    if (stats.totalTrades > 0) {
      stats.winRate = (stats.wins / stats.totalTrades) * 100;
      stats.profitFactor = stats.grossLoss > 0 ? stats.grossProfit / stats.grossLoss : stats.grossProfit > 0 ? 999 : 0;
      stats.avgPnL = stats.totalPnL / stats.totalTrades;
      stats.avgWin = stats.wins > 0 ? stats.grossProfit / stats.wins : 0;
      stats.avgLoss = stats.losses > 0 ? stats.grossLoss / stats.losses : 0;
    }
  }
  
  // Sort hours by profit factor
  const sortedByProfitFactor = Object.entries(hourlyStats)
    .filter(([hour, stats]) => stats.totalTrades >= 100) // Minimum 100 trades for statistical significance
    .sort((a, b) => b[1].profitFactor - a[1].profitFactor);
  
  // Sort hours by win rate
  const sortedByWinRate = Object.entries(hourlyStats)
    .filter(([hour, stats]) => stats.totalTrades >= 100)
    .sort((a, b) => b[1].winRate - a[1].winRate);
  
  // Sort hours by total P&L
  const sortedByPnL = Object.entries(hourlyStats)
    .filter(([hour, stats]) => stats.totalTrades >= 100)
    .sort((a, b) => b[1].totalPnL - a[1].totalPnL);
  
  // Display results
  console.log('\n=== Hourly Performance Analysis ===');
  console.log('Hour | Trades | Win Rate | Profit Factor | Total P&L | Avg P&L');
  console.log('-----|--------|----------|---------------|-----------|--------');
  
  for (let hour = 0; hour < 24; hour++) {
    const stats = hourlyStats[hour];
    if (stats.totalTrades > 0) {
      console.log(
        `${hour.toString().padStart(4)} | ${stats.totalTrades.toString().padStart(6)} | ${stats.winRate.toFixed(1).padStart(8)}% | ${stats.profitFactor.toFixed(2).padStart(13)} | ${stats.totalPnL.toFixed(0).padStart(9)} | ${stats.avgPnL.toFixed(2).padStart(6)}`
      );
    }
  }
  
  console.log('\n=== Best Hours by Profit Factor (min 100 trades) ===');
  console.log('Rank | Hour | Profit Factor | Win Rate | Trades | Total PnL');
  console.log('-----|------|---------------|----------|--------|----------');
  
  sortedByProfitFactor.slice(0, 10).forEach((entry, index) => {
    const [hour, stats] = entry;
    console.log(
      `${(index + 1).toString().padStart(4)} | ${hour.toString().padStart(4)} | ${stats.profitFactor.toFixed(2).padStart(13)} | ${stats.winRate.toFixed(2).padStart(8)}% | ${stats.totalTrades.toString().padStart(6)} | ${stats.totalPnL.toFixed(2).padStart(9)}`
    );
  });
  
  console.log('\n=== Best Hours by Win Rate (min 100 trades) ===');
  console.log('Rank | Hour | Win Rate | Profit Factor | Trades | Total PnL');
  console.log('-----|------|----------|---------------|--------|----------');
  
  sortedByWinRate.slice(0, 10).forEach((entry, index) => {
    const [hour, stats] = entry;
    console.log(
      `${(index + 1).toString().padStart(4)} | ${hour.toString().padStart(4)} | ${stats.winRate.toFixed(2).padStart(8)}% | ${stats.profitFactor.toFixed(2).padStart(13)} | ${stats.totalTrades.toString().padStart(6)} | ${stats.totalPnL.toFixed(2).padStart(9)}`
    );
  });
  
  console.log('\n=== Best Hours by Total P&L (min 100 trades) ===');
  console.log('Rank | Hour | Total PnL | Profit Factor | Win Rate | Trades');
  console.log('-----|------|----------|---------------|----------|--------');
  
  sortedByPnL.slice(0, 10).forEach((entry, index) => {
    const [hour, stats] = entry;
    console.log(
      `${(index + 1).toString().padStart(4)} | ${hour.toString().padStart(4)} | ${stats.totalPnL.toFixed(2).padStart(9)} | ${stats.profitFactor.toFixed(2).padStart(13)} | ${stats.winRate.toFixed(2).padStart(8)}% | ${stats.totalTrades.toString().padStart(6)}`
    );
  });
  
  // Identify optimal trading hours
  const optimalHours = sortedByProfitFactor
    .filter(([hour, stats]) => stats.profitFactor > 1.1)
    .map(([hour]) => parseInt(hour));
  
  // Identify hours to avoid
  const avoidHours = Object.entries(hourlyStats)
    .filter(([hour, stats]) => stats.totalTrades > 100 && stats.profitFactor < 1.0)
    .map(([hour]) => parseInt(hour));
  
  console.log('\n=== Recommended Trading Hours ===');
  console.log('Based on the analysis, the following hours (UTC) are recommended for trading:');
  console.log('Optimal Hours:', optimalHours.join(', '));
  console.log('Hours to Avoid:', avoidHours.join(', '));
  
  // Create a time filter configuration
  const timeFilterConfig = {
    enabled: true,
    optimalHours,
    avoidHours
  };
  
 