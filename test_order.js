/**
 * Test Order Utility for Trading Bot
 *
 * This script provides a simple way to test order placement through the Tradovate API.
 * It can be used to verify that the API connection is working correctly.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');

// Default test order parameters
const DEFAULT_SYMBOL = 'MNQM5'; // Using M5 as seen in Tradovate platform (M for May, 5 for 2025)
const DEFAULT_ACTION = 'Buy';
const DEFAULT_QTY = 1;
const DEFAULT_ORDER_TYPE = 'Market';
const DEFAULT_CONTRACT_ID = 1878810; // MNQ product ID from the API response
const DEFAULT_ACCOUNT_ID = '4690440'; // Demo account ID

// Order type enum (based on Tradovate API documentation)
const ORDER_TYPE = {
    Limit: 'Limit',
    MIT: 'MIT',
    Market: 'Market',
    QTS: 'QTS',
    Stop: 'Stop',
    StopLimit: 'StopLimit',
    TrailingStop: 'TrailingStop',
    TrailingStopLimit: 'TrailingStopLimit'
};

// Order action enum (based on Tradovate API documentation)
const ORDER_ACTION = {
    Buy: 'Buy',
    Sell: 'Sell'
};

// Time in force enum (based on Tradovate API documentation)
const TIME_IN_FORCE = {
    Day: 'Day',
    GTC: 'GTC', // Good Till Cancel
    IOC: 'IOC', // Immediate or Cancel
    FOK: 'FOK'  // Fill or Kill
};

/**
 * Place a test order
 * @param {Object} options - Order options
 * @param {string} options.symbol - Symbol to trade
 * @param {string} options.action - Order action (Buy/Sell)
 * @param {number} options.orderQty - Order quantity
 * @param {string} options.orderType - Order type (Market, Limit, Stop, etc.)
 * @param {number} [options.price] - Price for Limit orders
 * @param {number} [options.stopPrice] - Stop price for Stop/StopLimit orders
 * @param {string} [options.timeInForce] - Time in force (Day, GTC, IOC, FOK)
 * @param {string} [options.text] - Custom text for order identification
 * @param {string} [options.clOrdId] - Client order ID for tracking
 * @param {number} [options.maxShow] - Maximum quantity to show (for iceberg orders)
 * @param {string} [options.activationTime] - When the order should become active
 * @returns {Promise<Object>} - Order result
 */
async function placeTestOrder(options = {}) {
    try {
        const symbol = options.symbol || DEFAULT_SYMBOL;
        const action = options.action || DEFAULT_ACTION;
        const orderQty = options.orderQty || DEFAULT_QTY;
        const orderType = options.orderType || DEFAULT_ORDER_TYPE;

        logger.logSystem('Placing test order...', 'info');
        console.log('Placing test order...');
        console.log(`Symbol: ${symbol}, Action: ${action}, Quantity: ${orderQty}, Type: ${orderType}`);

        // Log additional parameters if provided
        if (options.price) console.log(`Price: ${options.price}`);
        if (options.stopPrice) console.log(`Stop Price: ${options.stopPrice}`);
        if (options.timeInForce) console.log(`Time in Force: ${options.timeInForce}`);

        // Always authenticate with Tradovate API to be safe
        console.log('Authenticating with Tradovate API...');
        const authResult = await tradovateApi.authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }
        console.log('Authentication successful');

        // Check account information
        console.log('Checking account information...');
        let accountSpec = 'DEMO4690440'; // Default account spec
        let accountId = 4690440; // Default account ID

        try {
            const accounts = await tradovateApi.makeApiRequest('GET', 'account/list');
            console.log(`Found ${accounts.length} accounts`);

            if (accounts.length > 0) {
                // Look for the demo account
                const demoAccount = accounts.find(acc => acc.name.startsWith('DEMO'));

                if (demoAccount) {
                    console.log(`Found demo account: ${demoAccount.name} (ID: ${demoAccount.id})`);
                    accountSpec = demoAccount.name;
                    accountId = demoAccount.id;
                } else {
                    // If no demo account, use the first account
                    const account = accounts[0];
                    console.log(`No demo account found. Using first account: ${account.name} (ID: ${account.id})`);
                    accountSpec = account.name;
                    accountId = account.id;
                }

                console.log(`Using account: ${accountSpec} (ID: ${accountId})`);
            }
        } catch (accountError) {
            console.warn(`Error getting account information: ${accountError.message}`);
        }

        // Generate a client order ID if not provided
        const clOrdId = options.clOrdId || `bot-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // Prepare order parameters - using the exact format from Tradovate documentation
        const orderParams = {
            accountSpec: accountSpec,
            accountId: Number(accountId), // Ensure accountId is a number
            symbol: symbol,
            action: action,
            orderQty: Number(orderQty),
            orderType: orderType,
            clOrdId: clOrdId,
            isAutomated: true // Important for compliance
        };

        // Log the parameters being used
        logger.logSystem(`Placing order with parameters: ${JSON.stringify(orderParams)}`, 'info');

        // Log the account ID being used
        console.log(`Order using accountId: ${orderParams.accountId} (${typeof orderParams.accountId})`);

        // Add optional parameters if provided
        if (options.price !== undefined) orderParams.price = Number(options.price);
        if (options.stopPrice !== undefined) orderParams.stopPrice = Number(options.stopPrice);
        if (options.timeInForce) orderParams.timeInForce = options.timeInForce;
        if (options.text) orderParams.text = options.text;
        if (options.maxShow !== undefined) orderParams.maxShow = Number(options.maxShow);
        if (options.activationTime) orderParams.activationTime = options.activationTime;

        console.log('Placing order with parameters:');
        console.log(JSON.stringify(orderParams, null, 2));

        // Place the order using direct API request to ensure correct format
        console.log('Sending order request to Tradovate API...');
        const result = await tradovateApi.makeApiRequest('POST', 'order/placeorder', orderParams);

        logger.logSystem(`Test order placed successfully: ${JSON.stringify(result)}`, 'info');
        console.log('Test order placed successfully!');
        console.log('Order ID:', result.orderId || result.id);
        console.log('Client Order ID:', clOrdId);
        console.log(result);

        return {
            success: true,
            orderData: result,
            clOrdId: clOrdId
        };
    } catch (error) {
        logger.logSystem(`Error placing test order: ${error.message}`, 'error');
        console.error('Error placing test order:', error);

        // Log more detailed error information
        if (error.response) {
            logger.logSystem(`Error response data: ${JSON.stringify(error.response.data)}`, 'error');
            logger.logSystem(`Error response status: ${error.response.status}`, 'error');

            console.error('Error response data:', error.response.data);
            console.error('Error response status:', error.response.status);
        }

        return {
            success: false,
            error: error.message,
            details: error.response ? error.response.data : null
        };
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    console.log('Running test order placement...');

    // Configure API with demo mode
    tradovateApi.setConfig({
        baseUrl: 'https://demo.tradovateapi.com/v1',
        wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
        mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
    });

    // Get command line arguments
    const args = process.argv.slice(2);
    const orderType = args[0] || DEFAULT_ORDER_TYPE;

    // Place a test order directly
    async function runTest() {
        try {
            console.log('Placing test order...');

            // Prepare order parameters based on order type
            const orderParams = {
                symbol: DEFAULT_SYMBOL,
                action: DEFAULT_ACTION,
                orderQty: DEFAULT_QTY,
                orderType: orderType,
                text: 'Test order from trading bot'
            };

            // Add specific parameters based on order type
            if (orderType === ORDER_TYPE.Limit) {
                // For Limit orders, we need a price
                console.log('Preparing Limit order...');
                orderParams.price = 19000; // Example price for MNQ
                orderParams.timeInForce = TIME_IN_FORCE.Day;
            } else if (orderType === ORDER_TYPE.Stop) {
                // For Stop orders, we need a stop price
                console.log('Preparing Stop order...');
                orderParams.stopPrice = 19500; // Example stop price for MNQ
                orderParams.timeInForce = TIME_IN_FORCE.GTC;
            } else if (orderType === ORDER_TYPE.StopLimit) {
                // For StopLimit orders, we need both a price and a stop price
                console.log('Preparing Stop Limit order...');
                orderParams.price = 19000;
                orderParams.stopPrice = 19500;
                orderParams.timeInForce = TIME_IN_FORCE.GTC;
            } else {
                // For Market orders, no additional parameters needed
                console.log('Preparing Market order...');
            }

            // Place the order
            const orderResult = await placeTestOrder(orderParams);

            if (orderResult && orderResult.success) {
                console.log('Test completed successfully');
                console.log('Order details:');
                console.log(`- Order ID: ${orderResult.orderData.orderId || orderResult.orderData.id}`);
                console.log(`- Client Order ID: ${orderResult.clOrdId}`);
                console.log(`- Symbol: ${DEFAULT_SYMBOL}`);
                console.log(`- Action: ${DEFAULT_ACTION}`);
                console.log(`- Quantity: ${DEFAULT_QTY}`);
                console.log(`- Order Type: ${orderType}`);

                if (orderParams.price) {
                    console.log(`- Price: ${orderParams.price}`);
                }

                if (orderParams.stopPrice) {
                    console.log(`- Stop Price: ${orderParams.stopPrice}`);
                }

                if (orderParams.timeInForce) {
                    console.log(`- Time in Force: ${orderParams.timeInForce}`);
                }
            } else if (orderResult && orderResult.marketClosed) {
                console.warn('Market is currently closed. Orders cannot be placed at this time.');
                console.warn('This is normal behavior. Please try again when the market is open.');
                console.warn('Futures markets have specific trading hours.');
                // Exit with success code since this is expected behavior
                process.exit(0);
            } else if (orderResult && orderResult.rateLimited) {
                console.warn('Rate limit exceeded. Please try again later.');
                console.warn('Tradovate has variable rate limits to protect their servers.');
                // Exit with success code since this is expected behavior
                process.exit(0);
            } else {
                console.error('Test failed:', orderResult ? orderResult.error : 'Unknown error');
                if (orderResult && orderResult.details) {
                    console.error('Error details:', orderResult.details);
                }
                if (orderResult && orderResult.data) {
                    console.error('Error data:', orderResult.data);
                }
                process.exit(1);
            }
        } catch (error) {
            console.error('Unhandled error during test:', error);
            process.exit(1);
        }
    }

    runTest();
}

module.exports = {
    placeTestOrder,
    ORDER_TYPE,
    ORDER_ACTION,
    TIME_IN_FORCE
};
