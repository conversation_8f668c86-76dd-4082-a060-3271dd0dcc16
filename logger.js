/**
 * Simple logger module for the trading bot
 */

const fs = require('fs');
const path = require('path');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Log levels
const LOG_LEVELS = {
    debug: 0,
    info: 1,
    warning: 2,
    error: 3
};

// Current log level (can be set via environment variable)
const currentLogLevel = process.env.LOG_LEVEL ? LOG_LEVELS[process.env.LOG_LEVEL.toLowerCase()] : LOG_LEVELS.debug;

/**
 * Log a system message
 * @param {string} message - Message to log
 * @param {string} level - Log level ('debug', 'info', 'warning', 'error')
 */
function logSystem(message, level = 'info') {
    // Check if we should log this message based on the current log level
    if (LOG_LEVELS[level.toLowerCase()] < currentLogLevel) {
        return;
    }

    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

    // Log to console
    if (level === 'error') {
        console.error(formattedMessage);
    } else if (level === 'warning') {
        console.warn(formattedMessage);
    } else {
        console.log(formattedMessage);
    }

    // Log to file
    const logFile = path.join(logsDir, `system_${new Date().toISOString().split('T')[0]}.log`);
    fs.appendFileSync(logFile, formattedMessage + '\n');
}

/**
 * Log a trade
 * @param {string} symbol - Symbol
 * @param {string} action - Action ('BUY', 'SELL')
 * @param {number} price - Price
 * @param {number} quantity - Quantity
 * @param {string} reason - Reason for the trade
 */
function logTrade(symbol, action, price, quantity, reason) {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [TRADE] ${symbol} ${action} ${quantity} @ ${price} - ${reason}`;

    // Log to console
    console.log(formattedMessage);

    // Log to file
    const logFile = path.join(logsDir, `trades_${new Date().toISOString().split('T')[0]}.log`);
    fs.appendFileSync(logFile, formattedMessage + '\n');
}

/**
 * Log an error
 * @param {Error} error - Error object
 * @param {string} context - Context where the error occurred
 */
function logError(error, context = '') {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [ERROR] ${context ? context + ': ' : ''}${error.message}\n${error.stack}`;

    // Log to console
    console.error(formattedMessage);

    // Log to file
    const logFile = path.join(logsDir, `errors_${new Date().toISOString().split('T')[0]}.log`);
    fs.appendFileSync(logFile, formattedMessage + '\n');
}

module.exports = {
    logSystem,
    logTrade,
    logError
};
