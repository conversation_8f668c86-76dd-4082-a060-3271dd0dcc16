<!DOCTYPE html>
<html>
<head>
    <title>Portfolio Manager</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Load dashboard integration scripts -->
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <script src="dashboard_integration.js"></script>
    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 2rem;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .time-filter {
            display: flex;
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            overflow: hidden;
            width: fit-content;
            margin-bottom: 2rem;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        }

        .time-filter button {
            padding: 0.5rem 1rem;
            border: none;
            background: none;
            font-family: 'Rajdhani', sans-serif;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            border-right: 1px solid var(--border);
        }

        .time-filter button:last-child {
            border-right: none;
        }

        .time-filter button:hover {
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.1);
        }

        .time-filter button.active {
            background-color: var(--primary);
            color: var(--dark);
            box-shadow: 0 0 10px var(--primary-glow);
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.15);
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .summary-card.primary::before { background: linear-gradient(to right, var(--primary), var(--primary-light)); }
        .summary-card.success::before { background: linear-gradient(to right, var(--success), var(--primary)); }
        .summary-card.warning::before { background: linear-gradient(to right, var(--warning), var(--primary)); }
        .summary-card.danger::before { background: linear-gradient(to right, var(--danger), var(--primary)); }

        .summary-card h3 {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .summary-value {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-family: 'Orbitron', sans-serif;
        }

        .summary-card.primary .summary-value {
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }
        .summary-card.success .summary-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .summary-card.warning .summary-value {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }
        .summary-card.danger .summary-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .summary-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            font-family: 'Rajdhani', sans-serif;
            color: var(--text-secondary);
        }

        .summary-change.positive {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        .summary-change.negative {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        .benchmark-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .benchmark-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .benchmark-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.15);
        }

        .benchmark-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .benchmark-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: rgba(0, 204, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            box-shadow: 0 0 15px var(--primary-glow);
        }

        .benchmark-info {
            flex: 1;
        }

        .benchmark-name {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .benchmark-value {
            font-size: 1.25rem;
            font-weight: 700;
            font-family: 'Rajdhani', sans-serif;
        }

        .benchmark-value.outperform {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        .benchmark-value.underperform {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .benchmark-change {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-family: 'Rajdhani', sans-serif;
        }

        .table-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2rem;
            overflow-x: auto;
            position: relative;
        }

        .table-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .table-container h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border: 1px solid var(--border);
            font-family: 'Rajdhani', sans-serif;
        }

        .data-table th {
            font-weight: 600;
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.1);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .data-table tr:hover {
            background-color: rgba(0, 204, 255, 0.05);
        }

        .positive {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        .negative {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Portfolio Manager View</h1>
            <p>Executive summary of trading strategy performance</p>
        </div>

        <div class="time-filter">
            <button class="active">YTD</button>
            <button>QTD</button>
            <button>MTD</button>
            <button>1W</button>
            <button>1D</button>
            <button>All Time</button>
        </div>

        <div class="summary-grid">
            <div class="summary-card primary">
                <h3>Total Return</h3>
                <div class="summary-value">+448.51%</div>
                <div class="summary-change positive">+10.8x initial capital</div>
            </div>

            <div class="summary-card success">
                <h3>Win Rate</h3>
                <div class="summary-value">78.35%</div>
                <div class="summary-change positive">+33.53% vs previous</div>
            </div>

            <div class="summary-card success">
                <h3>Profitable Days</h3>
                <div class="summary-value">99.4%</div>
                <div class="summary-change positive">156 of 157 days</div>
            </div>

            <div class="summary-card primary">
                <h3>Sharpe Ratio</h3>
                <div class="summary-value">4.87</div>
                <div class="summary-change positive">Excellent risk-adjusted return</div>
            </div>

            <div class="summary-card danger">
                <h3>Max Drawdown</h3>
                <div class="summary-value">$1,484</div>
                <div class="summary-change positive">Only 0.33% of equity</div>
            </div>

            <div class="summary-card warning">
                <h3>Profit Factor</h3>
                <div class="summary-value">22.28</div>
                <div class="summary-change positive">Exceptional risk/reward</div>
            </div>
        </div>

        <div class="chart-grid">
            <div class="chart-container">
                <h3>Equity Curve vs Benchmarks</h3>
                <div class="chart-wrapper">
                    <canvas id="equityCurveChart"></canvas>
                </div>
            </div>

            <div class="chart-container">
                <h3>Monthly Returns</h3>
                <div class="chart-wrapper">
                    <canvas id="monthlyReturnsChart"></canvas>
                </div>
            </div>
        </div>

        <div class="benchmark-grid">
            <div class="benchmark-card">
                <div class="benchmark-icon">📈</div>
                <div class="benchmark-info">
                    <div class="benchmark-name">S&P 500</div>
                    <div class="benchmark-value outperform">+448.51% vs +8.2%</div>
                    <div class="benchmark-change">Outperforming by 440.31%</div>
                </div>
            </div>

            <div class="benchmark-card">
                <div class="benchmark-icon">📊</div>
                <div class="benchmark-info">
                    <div class="benchmark-name">NASDAQ</div>
                    <div class="benchmark-value outperform">+448.51% vs +12.5%</div>
                    <div class="benchmark-change">Outperforming by 436.01%</div>
                </div>
            </div>

            <div class="benchmark-card">
                <div class="benchmark-icon">💰</div>
                <div class="benchmark-info">
                    <div class="benchmark-name">Hedge Fund Index</div>
                    <div class="benchmark-value outperform">+448.51% vs +5.7%</div>
                    <div class="benchmark-change">Outperforming by 442.81%</div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <h3>Monthly Performance Summary</h3>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>Month</th>
                        <th>Return</th>
                        <th>Win Rate</th>
                        <th>Trades</th>
                        <th>Profit Factor</th>
                        <th>Max Drawdown</th>
                        <th>Sharpe</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>May 2025</td>
                        <td class="positive">+42.3%</td>
                        <td>79.1%</td>
                        <td>412</td>
                        <td>24.5</td>
                        <td>$982</td>
                        <td>5.12</td>
                    </tr>
                    <tr>
                        <td>April 2025</td>
                        <td class="positive">+68.7%</td>
                        <td>81.2%</td>
                        <td>845</td>
                        <td>26.8</td>
                        <td>$1,484</td>
                        <td>5.87</td>
                    </tr>
                    <tr>
                        <td>March 2025</td>
                        <td class="positive">+55.4%</td>
                        <td>77.8%</td>
                        <td>912</td>
                        <td>21.3</td>
                        <td>$876</td>
                        <td>4.92</td>
                    </tr>
                    <tr>
                        <td>February 2025</td>
                        <td class="positive">+61.2%</td>
                        <td>78.5%</td>
                        <td>784</td>
                        <td>22.7</td>
                        <td>$598</td>
                        <td>5.21</td>
                    </tr>
                    <tr>
                        <td>January 2025</td>
                        <td class="positive">+59.8%</td>
                        <td>76.9%</td>
                        <td>825</td>
                        <td>20.4</td>
                        <td>$743</td>
                        <td>4.76</td>
                    </tr>
                    <tr>
                        <td>December 2024</td>
                        <td class="positive">+48.3%</td>
                        <td>75.8%</td>
                        <td>775</td>
                        <td>18.9</td>
                        <td>$621</td>
                        <td>4.45</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Equity Curve Chart
            const equityCurveCtx = document.getElementById('equityCurveChart').getContext('2d');

            // Generate dates for the past 6 months
            const dates = [];
            const today = new Date();
            for (let i = 180; i >= 0; i--) {
                const date = new Date();
                date.setDate(today.getDate() - i);
                dates.push(date.toISOString().split('T')[0]);
            }

            // Generate equity curve data
            const equityCurveData = [];
            const spxData = [];
            const nasdaqData = [];

            let equity = 10000;
            let spx = 10000;
            let nasdaq = 10000;

            dates.forEach((date, i) => {
                // Simulate daily returns
                const dailyReturn = (Math.random() * 0.03) + 0.01; // 1-4% daily return
                equity *= (1 + dailyReturn);

                // Simulate benchmark returns
                const spxReturn = (Math.random() * 0.01) - 0.002; // -0.2% to 0.8% daily return
                const nasdaqReturn = (Math.random() * 0.015) - 0.003; // -0.3% to 1.2% daily return

                spx *= (1 + spxReturn);
                nasdaq *= (1 + nasdaqReturn);

                equityCurveData.push(equity);
                spxData.push(spx);
                nasdaqData.push(nasdaq);
            });

            new Chart(equityCurveCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'Trading Strategy',
                            data: equityCurveData,
                            borderColor: 'rgba(0, 204, 255, 1)',
                            backgroundColor: 'rgba(0, 204, 255, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'S&P 500',
                            data: spxData,
                            borderColor: 'rgba(255, 51, 102, 1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: 'NASDAQ',
                            data: nasdaqData,
                            borderColor: 'rgba(255, 204, 0, 1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'month'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Value ($)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // Monthly Returns Chart
            const monthlyReturnsCtx = document.getElementById('monthlyReturnsChart').getContext('2d');

            const months = ['Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May'];
            const monthlyReturns = [48.3, 59.8, 61.2, 55.4, 68.7, 42.3];
            const spxMonthlyReturns = [1.2, 2.3, -0.8, 1.5, 3.2, 0.9];
            const nasdaqMonthlyReturns = [1.8, 3.1, -1.2, 2.2, 4.1, 1.3];

            new Chart(monthlyReturnsCtx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: 'Trading Strategy',
                            data: monthlyReturns,
                            backgroundColor: 'rgba(0, 204, 255, 0.7)',
                            borderColor: 'rgba(0, 204, 255, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: 'S&P 500',
                            data: spxMonthlyReturns,
                            backgroundColor: 'rgba(255, 51, 102, 0.7)',
                            borderColor: 'rgba(255, 51, 102, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: 'NASDAQ',
                            data: nasdaqMonthlyReturns,
                            backgroundColor: 'rgba(255, 204, 0, 0.7)',
                            borderColor: 'rgba(255, 204, 0, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.parsed.y}%`;
                                }
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // Time filter buttons
            const timeFilterButtons = document.querySelectorAll('.time-filter button');

            timeFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    timeFilterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // In a real implementation, this would update the data
                    console.log('Switched to time period: ' + this.textContent);
                });
            });
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Portfolio Manager`;
            }

            // Update portfolio data
            updatePortfolioData(data);

            console.log('Portfolio manager dashboard updated with data for:', data.instrumentCode);
        }

        // Update portfolio data
        function updatePortfolioData(data) {
            // This would be implemented to update the portfolio data with actual data
            // For now, we'll just log that we would update the portfolio data
            console.log('Portfolio data would be updated with:', data);
        }
    </script>
</body>
</html>
