{"name": "tradovate-api-example", "version": "1.0.0", "description": "Example application for Tradovate API", "main": "index.js", "scripts": {"start": "webpack serve --open", "build": "webpack --mode production", "test-bridge": "node test-bridge.js", "run-bot": "node run-bot.js", "verify": "node verify-backtest.js", "grid-test": "node grid-test.js"}, "dependencies": {"csv-parser": "^3.2.0", "node-fetch": "^2.6.7"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "babel-loader": "^10.0.0", "html-webpack-plugin": "^5.6.3", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}