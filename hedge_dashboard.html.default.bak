<!DOCTYPE html>

<html class="game-mode">
<head>
<title>Quantum Capital Dashboard</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Press+Start+2P&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        /* Base styles will be applied by dashboard_integration.js */
        /* Additional custom styles for this dashboard */
        :root {
            --sidebar-text: rgba(255, 255, 255, 0.7);
            --sidebar-active: var(--primary);
            --sidebar-hover: rgba(0, 204, 255, 0.15);
            --sidebar-width: 280px;
            --header-height: 60px;
            --shadow-sm: 0 0 5px rgba(0, 204, 255, 0.2);
            --shadow: 0 0 15px rgba(0, 204, 255, 0.3);
            --shadow-lg: 0 0 25px rgba(0, 204, 255, 0.4);
            --grid-lines: rgba(42, 58, 90, 0.5);
        }

        .game-mode {
            /* Game Mode styles will be applied from dashboard_config.js */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            overflow: hidden;
            height: 100vh;
            transition: background-color 0.3s ease, color 0.3s ease;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
        }

        /* Game-specific elements */
        .xp-bar {
            height: 4px;
            background: linear-gradient(to right, var(--success), var(--primary));
            position: fixed;
            top: 0;
            left: 0;
            width: 85%;
            z-index: 1000;
            box-shadow: 0 0 10px var(--primary-glow);
        }

        .level-indicator {
            position: fixed;
            top: 10px;
            right: 20px;
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Press Start 2P', cursive;
            font-size: 0.7rem;
            color: var(--primary);
            box-shadow: 0 0 10px var(--primary-glow);
            z-index: 1000;
        }

        .dashboard-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--bg-sidebar);
            color: white;
            height: 100%;
            overflow-y: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            z-index: 10;
            position: relative;
            border-right: 1px solid var(--border);
            background-image: linear-gradient(to bottom,
                rgba(20, 27, 45, 0.95),
                rgba(20, 27, 45, 0.95)
            );
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .sidebar-header h1 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            transition: opacity 0.3s ease;
            font-family: 'Orbitron', sans-serif;
            letter-spacing: 1px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--primary);
            cursor: pointer;
            font-size: 1.25rem;
            transition: transform 0.3s ease, text-shadow 0.3s ease;
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .sidebar-toggle:hover {
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-category {
            padding: 0.5rem 1.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: var(--secondary);
            margin-top: 1rem;
            transition: opacity 0.3s ease;
            text-shadow: 0 0 5px var(--secondary-glow);
            font-family: 'Press Start 2P', cursive;
            font-size: 0.6rem;
            position: relative;
            display: flex;
            align-items: center;
        }

        .menu-category::before,
        .menu-category::after {
            content: '';
            height: 1px;
            background-color: var(--secondary);
            opacity: 0.3;
            flex: 1;
        }

        .menu-category::before {
            margin-right: 0.5rem;
        }

        .menu-category::after {
            margin-left: 0.5rem;
        }

        .menu-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: var(--sidebar-text);
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .menu-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 100%;
            bottom: 0;
            background: linear-gradient(to right, transparent, var(--primary-glow));
            opacity: 0;
            transition: all 0.3s ease;
            z-index: -1;
        }

        .menu-item:hover {
            background-color: var(--sidebar-hover);
            color: white;
        }

        .menu-item:hover::after {
            right: 0;
            opacity: 0.1;
        }

        .menu-item.active {
            background-color: rgba(0, 204, 255, 0.1);
            color: var(--primary);
            border-left: 3px solid var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .menu-item.active::before {
            content: '▶';
            position: absolute;
            right: 15px;
            font-size: 0.7rem;
            color: var(--primary);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }

        .menu-item-icon {
            margin-right: 0.75rem;
            font-size: 1.25rem;
            width: 24px;
            height: 24px;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            border: 1px solid var(--border);
        }

        .menu-item:hover .menu-item-icon {
            transform: scale(1.1);
        }

        .menu-item.active .menu-item-icon {
            color: var(--primary);
            border-color: var(--primary);
            box-shadow: 0 0 5px var(--primary-glow);
        }

        .menu-item-text {
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }

        .main-content {
            flex: 1;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 75% 25%, rgba(0, 204, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 25% 75%, rgba(255, 0, 170, 0.03) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        .header {
            height: var(--header-height);
            background-color: var(--card-bg);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            box-shadow: var(--shadow-sm);
            z-index: 5;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.5;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(to right, rgba(0, 204, 255, 0.03), transparent),
                linear-gradient(to left, rgba(255, 0, 170, 0.03), transparent);
            pointer-events: none;
        }

        .header-left {
            display: flex;
            align-items: center;
            position: relative;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            transition: all 0.3s ease;
            text-shadow: 0 0 10px var(--primary-glow);
            letter-spacing: 1px;
            font-family: 'Orbitron', sans-serif;
            position: relative;
            padding-left: 15px;
        }

        .header-title::before {
            content: '>';
            position: absolute;
            left: 0;
            color: var(--primary);
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--primary);
            cursor: pointer;
            font-size: 1.25rem;
            margin-right: 1rem;
            transition: all 0.3s ease;
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .theme-toggle:hover {
            text-shadow: 0 0 10px var(--primary-glow);
            transform: scale(1.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            margin-right: 1rem;
            background-color: rgba(0, 0, 0, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            border: 1px solid var(--success);
            box-shadow: 0 0 10px var(--success-glow);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-dot.active {
            background-color: var(--success);
            box-shadow: 0 0 5px var(--success);
            animation: pulse 1.5s infinite;
        }

        .status-text {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }

        .header-button {
            background-color: var(--card-bg);
            color: var(--primary);
            border: 1px solid var(--primary);
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 0.75rem;
            position: relative;
            overflow: hidden;
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
            box-shadow: 0 0 10px rgba(0, 204, 255, 0.2);
        }

        .header-button::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(to right, transparent, rgba(0, 204, 255, 0.1), transparent);
            transform: rotate(30deg);
            transition: all 0.5s ease;
        }

        .header-button:hover {
            background-color: rgba(0, 204, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 204, 255, 0.3);
        }

        .header-button:hover::after {
            transform: rotate(30deg) translate(50%, 50%);
        }

        .header-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 204, 255, 0.2);
        }

        .dashboard-view {
            flex: 1;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
            background-color: var(--bg-main);
        }

        .dashboard-view::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 75% 25%, rgba(0, 204, 255, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 25% 75%, rgba(255, 0, 170, 0.02) 0%, transparent 50%);
            pointer-events: none;
            z-index: 2;
        }

        .dashboard-view::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            opacity: 0.1;
            pointer-events: none;
            z-index: 2;
        }

        .dashboard-iframe {
            width: 100%;
            height: 100%;
            border: none;
            opacity: 0;
            transition: opacity 0.5s ease;
            position: relative;
            z-index: 3;
        }

        .dashboard-iframe.loaded {
            opacity: 1;
        }

        .sidebar-collapsed {
            width: 70px;
        }

        .sidebar-collapsed .menu-item-text,
        .sidebar-collapsed .sidebar-header h1,
        .sidebar-collapsed .menu-category {
            opacity: 0;
            visibility: hidden;
        }

        .sidebar-collapsed .menu-item {
            justify-content: center;
            padding: 0.75rem;
        }

        .sidebar-collapsed .menu-item-icon {
            margin-right: 0;
        }

        .sidebar-collapsed .sidebar-toggle {
            transform: rotate(180deg);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(10, 14, 23, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 60px;
            height: 60px;
            position: relative;
        }

        .spinner::before,
        .spinner::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            animation: spin 1.5s linear infinite;
        }

        .spinner::before {
            border-top-color: var(--primary);
            border-bottom-color: var(--primary-dark);
            animation-delay: 0.15s;
        }

        .spinner::after {
            border-left-color: var(--secondary);
            border-right-color: var(--secondary);
            animation-delay: 0s;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .module-tabs {
            display: flex;
            background-color: var(--card-bg);
            border-bottom: 1px solid var(--border);
            overflow-x: auto;
            transition: all 0.3s ease;
            position: relative;
        }

        .module-tabs::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.3;
        }

        .module-tab {
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            position: relative;
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
            overflow: hidden;
        }

        .module-tab::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .module-tab:hover {
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.05);
        }

        .module-tab:hover::after {
            width: 80%;
        }

        .module-tab.active {
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            background-color: rgba(0, 204, 255, 0.1);
        }

        .module-tab.active::after {
            width: 100%;
            box-shadow: 0 0 10px var(--primary-glow);
        }

        .module-tab.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(0, 204, 255, 0.1), transparent);
            pointer-events: none;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .header-left {
                width: 100%;
                justify-content: space-between;
            }

            .mobile-toggle {
                display: block;
                background: none;
                border: none;
                color: var(--text-primary);
                font-size: 1.5rem;
                cursor: pointer;
            }
        }

        /* Icons */
        .icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            stroke-width: 0;
            stroke: currentColor;
            fill: currentColor;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<!-- Game UI Elements -->
<div class="xp-bar" title="Performance: 85%"></div>
<div class="level-indicator" title="Alpha Score">A+</div>
<div class="dashboard-container">
<div class="sidebar" id="sidebar">
<div class="sidebar-header">
<h1>QUANTUM CAPITAL</h1>
<button class="sidebar-toggle" id="sidebar-toggle">
<span class="icon">◀</span>
</button>
</div>
<div class="sidebar-menu">
<div class="menu-category">PORTFOLIO OVERVIEW</div>
<a class="menu-item active" data-dashboard="premium_dashboard.html" data-title="Alpha Dashboard">
<span class="menu-item-icon">📊</span>
<span class="menu-item-text">Alpha Dashboard</span>
</a>
<a class="menu-item" data-dashboard="daily_pnl_dashboard.html" data-title="Daily P&amp;L">
<span class="menu-item-icon">📈</span>
<span class="menu-item-text">Daily P&amp;L</span>
</a>
<a class="menu-item" data-dashboard="performance_comparison_dashboard.html" data-title="Performance Metrics">
<span class="menu-item-icon">🏆</span>
<span class="menu-item-text">Performance Metrics</span>
</a>
<div class="menu-category">MARKET OPERATIONS</div>
<a class="menu-item" data-dashboard="portfolio_overview.html" data-title="Portfolio Overview">
<span class="menu-item-icon">💼</span>
<span class="menu-item-text">Portfolio Overview</span>
</a>
<a class="menu-item" data-dashboard="realtime_dashboard.html" data-title="Live Trading">
<span class="menu-item-icon">⚡</span>
<span class="menu-item-text">Live Trading</span>
</a>
<a class="menu-item" data-dashboard="custom_alerts_dashboard.html" data-title="Signal Alerts">
<span class="menu-item-icon">🔔</span>
<span class="menu-item-text">Signal Alerts</span>
</a>
<a class="menu-item" data-dashboard="mgc_dashboard.html" data-title="MGC Gold">
<span class="menu-item-icon">🥇</span>
<span class="menu-item-text">MGC Gold</span>
</a>
<a class="menu-item" data-dashboard="mes_dashboard.html" data-title="MES S&amp;P 500">
<span class="menu-item-icon">📈</span>
<span class="menu-item-text">MES S&amp;P 500</span>
</a>
<a class="menu-item" data-dashboard="mnq_dashboard.html" data-title="MNQ Nasdaq-100">
<span class="menu-item-icon">🚀</span>
<span class="menu-item-text">MNQ Nasdaq-100</span>
</a>
<div class="menu-category">RISK MANAGEMENT</div>
<a class="menu-item" data-dashboard="performance_forecast.html" data-title="Performance Forecasting">
<span class="menu-item-icon">🔮</span>
<span class="menu-item-text">Performance Forecasting</span>
</a>
<a class="menu-item" data-dashboard="drawdown_dashboard.html" data-title="Drawdown Analysis">
<span class="menu-item-icon">🛡️</span>
<span class="menu-item-text">Drawdown Analysis</span>
</a>
<a class="menu-item" data-dashboard="monte_carlo_dashboard.html" data-title="Monte Carlo">
<span class="menu-item-icon">🎲</span>
<span class="menu-item-text">Monte Carlo</span>
</a>
<a class="menu-item" data-dashboard="correlation_analysis_dashboard.html" data-title="Correlation Matrix">
<span class="menu-item-icon">🔗</span>
<span class="menu-item-text">Correlation Matrix</span>
</a>
<a class="menu-item" data-dashboard="market_volatility.html" data-title="Market Volatility">
<span class="menu-item-icon">📊</span>
<span class="menu-item-text">Market Volatility</span>
</a>
<div class="menu-category">ASSET ALLOCATION</div>
<a class="menu-item" data-dashboard="portfolio_manager.html" data-title="Portfolio Manager">
<span class="menu-item-icon">💼</span>
<span class="menu-item-text">Portfolio Manager</span>
</a>
<a class="menu-item" data-dashboard="investor_reporting.html" data-title="Investor Reports">
<span class="menu-item-icon">📋</span>
<span class="menu-item-text">Investor Reports</span>
</a>
<a class="menu-item" data-dashboard="market_intelligence.html" data-title="Market Intelligence">
<span class="menu-item-icon">🌐</span>
<span class="menu-item-text">Market Intelligence</span>
</a>
<div class="menu-category">CONFIGURATION</div>
<a class="menu-item" data-title="Strategy Settings" onclick="createConfigViewer()">
<span class="menu-item-icon">⚙️</span>
<span class="menu-item-text">Strategy Settings</span>
</a>
</div>
</div>
<div class="main-content">
<div class="header">
<div class="header-left">
<h2 class="header-title" id="current-dashboard-title">Alpha Dashboard</h2>
</div>
<div class="header-right">
<button class="theme-toggle" id="theme-toggle">
<span class="icon">🌓</span>
</button>
<div class="status-indicator">
<div class="status-dot active"></div>
<div class="status-text">TRADING ACTIVE</div>
</div>
<button class="header-button" onclick="refreshDashboard()">
<span class="icon">🔄</span> REFRESH
                    </button>
<button class="header-button" onclick="toggleFullscreen()">
<span class="icon">⛶</span> FULLSCREEN
                    </button>
</div>
</div>
<div class="module-tabs" id="module-tabs">
<div class="module-tab active" data-tab="main">OVERVIEW</div>
<div class="module-tab" data-tab="performance">PERFORMANCE</div>
<div class="module-tab" data-tab="risk">RISK METRICS</div>
<div class="module-tab" data-tab="allocation">ALLOCATION</div>
<div class="module-tab" data-tab="analytics">ANALYTICS</div>
</div>
<div class="dashboard-view">
<div class="loading-overlay" id="loading-overlay">
<div class="spinner"></div>
</div>
<iframe class="dashboard-iframe" id="dashboard-iframe" onload="iframeLoaded()" src="premium_dashboard.html"></iframe>
</div>
</div>
</div>
<script>
        // Toggle sidebar
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            playSound('click');
            document.getElementById('sidebar').classList.toggle('sidebar-collapsed');
        });

        // Toggle theme
        document.getElementById('theme-toggle').addEventListener('click', function() {
            playSound('click');
            document.documentElement.classList.toggle('dark-mode');
        });

        // Switch dashboards
        const menuItems = document.querySelectorAll('.menu-item');
        const dashboardIframe = document.getElementById('dashboard-iframe');
        const dashboardTitle = document.getElementById('current-dashboard-title');
        const loadingOverlay = document.getElementById('loading-overlay');

        menuItems.forEach(item => {
            item.addEventListener('click', function() {
                // Play click sound
                playSound('click');

                // Remove active class from all menu items
                menuItems.forEach(i => i.classList.remove('active'));

                // Add active class to clicked menu item
                this.classList.add('active');

                // Update dashboard title
                const title = this.getAttribute('data-title');
                if (title) {
                    dashboardTitle.textContent = title;
                }

                // Show loading overlay
                loadingOverlay.classList.add('active');

                // Load dashboard in iframe
                const dashboardUrl = this.getAttribute('data-dashboard');
                if (dashboardUrl) {
                    dashboardIframe.classList.remove('loaded');
                    setTimeout(() => {
                        dashboardIframe.src = dashboardUrl;
                    }, 300);
                }
            });
        });

        // Handle iframe loaded event
        function iframeLoaded() {
            setTimeout(() => {
                loadingOverlay.classList.remove('active');
                dashboardIframe.classList.add('loaded');

                // Inject game styling into the iframe
                injectGameStylingIntoIframe();
            }, 500);
        }

        // Inject game styling into the iframe content
        function injectGameStylingIntoIframe() {
            try {
                const iframe = document.getElementById('dashboard-iframe');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                // First, let's try to directly modify the body background
                if (iframeDoc.body) {
                    iframeDoc.body.style.backgroundColor = "#0a0e17";
                    iframeDoc.body.style.color = "#f8fafc";
                    iframeDoc.body.style.backgroundImage = `
                        radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                        radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                        linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                        url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>')
                    `;
                    iframeDoc.body.style.backgroundAttachment = "fixed";
                }

                // Create a style element with !important rules
                const gameStyle = iframeDoc.createElement('style');
                gameStyle.textContent = `
                    /* Game-inspired styling for iframe content */
                    html, body {
                        background-color: #0a0e17 !important;
                        color: #f8fafc !important;
                        font-family: 'Rajdhani', sans-serif !important;
                        background-image:
                            radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                            radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                            linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                            url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>') !important;
                        background-attachment: fixed !important;
                    }

                    /* EXTREMELY aggressive background override */
                    body, .dashboard, main, .main, #main, #root, #app, .app, #content, .content,
                    .container, .wrapper, .page, .section, .card, .panel, .box, .grid, .row, .col,
                    div, span, header, footer, nav, aside, article, section {
                        background-color: #0a0e17 !important;
                    }

                    /* Specific container backgrounds */
                    .dashboard, .container, .card, .chart-container, div[class*="card"], div[class*="container"],
                    .stat-card, div[class*="stat"], div[class*="card"], .header, .footer, section, article, aside, nav,
                    .chart-wrapper, .notable-days, .day-card, .insights-container, .section-title, .tab-content, .tabcontent,
                    .stats-grid, .chart-grid, .notable-days, .day-card, .insights-container {
                        background-color: #141b2d !important;
                        border-color: #2a3a5a !important;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1) !important;
                        color: #f8fafc !important;
                    }

                    /* Force white backgrounds to be dark - more aggressive selectors */
                    [style*="background"], [style*="background-color"], [style*="background:"], [style*="background-color:"],
                    [style*="background-image"], [style*="background-image:"] {
                        background-color: #141b2d !important;
                        background-image: none !important;
                    }

                    /* Target common white background elements by tag */
                    body, html, div, main, section, article, aside, header, footer, nav, form,
                    .container, .wrapper, .content, .main, .page, .section, .row, .col, .card, .panel, .box {
                        background-color: #0a0e17 !important;
                    }

                    /* Target by common class name patterns */
                    [class*="container"], [class*="wrapper"], [class*="content"], [class*="main"],
                    [class*="page"], [class*="section"], [class*="row"], [class*="col"],
                    [class*="card"], [class*="panel"], [class*="box"], [class*="bg-white"],
                    [class*="bg-light"], [class*="background-white"], [class*="background-light"] {
                        background-color: #141b2d !important;
                    }

                    /* Typography */
                    h1, h2, h3, h4, h5, h6 {
                        font-family: 'Orbitron', sans-serif !important;
                        color: #00ccff !important;
                        text-shadow: 0 0 10px rgba(0, 204, 255, 0.5) !important;
                        letter-spacing: 1px !important;
                    }

                    p, span, div, li, a, label, input, select, textarea {
                        color: #f8fafc !important;
                    }

                    .stat-value, div[class*="value"], strong, b {
                        color: #00ccff !important;
                        text-shadow: 0 0 10px rgba(0, 204, 255, 0.5) !important;
                        font-family: 'Orbitron', monospace !important;
                    }

                    .success, .positive, [class*="success"], [class*="positive"],
                    [style*="color: green"], [style*="color:green"],
                    [style*="color: #0"], [style*="color:#0"] {
                        color: #00ff88 !important;
                        text-shadow: 0 0 10px rgba(0, 255, 136, 0.5) !important;
                    }

                    .danger, .negative, [class*="danger"], [class*="negative"],
                    [style*="color: red"], [style*="color:red"],
                    [style*="color: #f"], [style*="color:#f"] {
                        color: #ff3366 !important;
                        text-shadow: 0 0 10px rgba(255, 51, 102, 0.5) !important;
                    }

                    button, .button, [class*="button"], input[type="button"], input[type="submit"] {
                        background-color: #141b2d !important;
                        color: #00ccff !important;
                        border: 1px solid #00ccff !important;
                        text-shadow: 0 0 5px rgba(0, 204, 255, 0.5) !important;
                        box-shadow: 0 0 10px rgba(0, 204, 255, 0.2) !important;
                        font-family: 'Rajdhani', sans-serif !important;
                    }

                    button:hover, .button:hover, [class*="button"]:hover {
                        background-color: rgba(0, 204, 255, 0.1) !important;
                        box-shadow: 0 5px 15px rgba(0, 204, 255, 0.3) !important;
                    }

                    table {
                        border-color: #2a3a5a !important;
                        color: #f8fafc !important;
                        background-color: transparent !important;
                    }

                    th {
                        background-color: #1a2234 !important;
                        color: #00ccff !important;
                        font-family: 'Orbitron', sans-serif !important;
                        letter-spacing: 1px !important;
                        border-color: #2a3a5a !important;
                    }

                    td {
                        border-color: #2a3a5a !important;
                        background-color: #141b2d !important;
                        color: #f8fafc !important;
                    }

                    tr:nth-child(even) td {
                        background-color: #1a2234 !important;
                    }

                    canvas {
                        background-color: #141b2d !important;
                        border: 1px solid #2a3a5a !important;
                    }

                    /* Chart styling */
                    .chart-container, .chart-wrapper, [class*="chart"] {
                        background-color: #141b2d !important;
                        border: 1px solid #2a3a5a !important;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3), inset 0 0 15px rgba(0, 0, 0, 0.2) !important;
                    }

                    /* Force all text to be visible */
                    * {
                        color: #f8fafc !important;
                    }

                    /* Override any !important inline styles with even more specific selectors */
                    html body,
                    html body div,
                    html body main,
                    html body .container,
                    html body .wrapper,
                    html body .content,
                    html body .dashboard {
                        background-color: #0a0e17 !important;
                        background-image:
                            radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                            radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                            linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                            url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>') !important;
                    }
                `;

                // Add the style element to the iframe document head
                iframeDoc.head.appendChild(gameStyle);

                // Add the required fonts to the iframe
                const fontLink = iframeDoc.createElement('link');
                fontLink.href = "https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap";
                fontLink.rel = "stylesheet";
                iframeDoc.head.appendChild(fontLink);

                // Add a script to ensure the styling is applied after any dynamic content loads
                const enforcerScript = iframeDoc.createElement('script');
                enforcerScript.textContent = `
                    // Function to apply dark theme to all elements
                    function applyDarkTheme() {
                        // Set body background
                        document.body.style.backgroundColor = "#0a0e17";
                        document.body.style.color = "#f8fafc";
                        document.body.style.backgroundImage = \`
                            radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                            radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                            linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                            url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>')
                        \`;
                        document.body.style.backgroundAttachment = "fixed";

                        // Force all elements with white backgrounds to be dark
                        const allElements = document.querySelectorAll('*');
                        allElements.forEach(el => {
                            // Skip certain elements
                            if (el.tagName === 'SCRIPT' || el.tagName === 'STYLE' || el.tagName === 'META' || el.tagName === 'LINK') {
                                return;
                            }

                            // Get computed style
                            const style = window.getComputedStyle(el);
                            const bgColor = style.backgroundColor;

                            // Check for white or light backgrounds
                            if (bgColor === 'rgb(255, 255, 255)' ||
                                bgColor.startsWith('rgba(255, 255, 255') ||
                                bgColor === 'rgb(248, 249, 250)' ||
                                bgColor === 'rgb(241, 245, 249)' ||
                                bgColor.startsWith('rgb(2') ||
                                bgColor.includes('255')) {
                                el.style.backgroundColor = "#141b2d";
                            }

                            // Ensure text is visible
                            const color = style.color;
                            if (color === 'rgb(0, 0, 0)' ||
                                color.startsWith('rgba(0, 0, 0') ||
                                color === 'rgb(33, 37, 41)' ||
                                color.startsWith('rgb(1')) {
                                el.style.color = "#f8fafc";
                            }

                            // Remove background images
                            if (style.backgroundImage !== 'none' && !style.backgroundImage.includes('svg')) {
                                el.style.backgroundImage = 'none';
                            }
                        });

                        // Apply to main containers specifically
                        ['body', 'main', '.main', '#main', '#root', '#app', '.app', '#content', '.content',
                         '.container', '.wrapper', '.page', '.dashboard'].forEach(selector => {
                            try {
                                const elements = document.querySelectorAll(selector);
                                elements.forEach(el => {
                                    el.style.backgroundColor = "#0a0e17";
                                });
                            } catch (e) {}
                        });
                    }

                    // Apply immediately
                    applyDarkTheme();

                    // Apply again after a delay to catch dynamically loaded content
                    setTimeout(applyDarkTheme, 500);
                    setTimeout(applyDarkTheme, 1000);
                    setTimeout(applyDarkTheme, 2000);

                    // Apply on any DOM changes
                    const observer = new MutationObserver(function(mutations) {
                        applyDarkTheme();
                    });

                    observer.observe(document.body, {
                        childList: true,
                        subtree: true,
                        attributes: true,
                        attributeFilter: ['style', 'class']
                    });
                `;
                iframeDoc.body.appendChild(enforcerScript);

                // Create a completely opaque overlay as a last resort
                const overlayDiv = iframeDoc.createElement('div');
                overlayDiv.style.position = 'fixed';
                overlayDiv.style.top = '0';
                overlayDiv.style.left = '0';
                overlayDiv.style.width = '100%';
                overlayDiv.style.height = '100%';
                overlayDiv.style.backgroundColor = '#0a0e17'; // Completely opaque
                overlayDiv.style.backgroundImage = `
                    radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                    radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                    url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>')
                `;
                overlayDiv.style.backgroundAttachment = 'fixed';
                overlayDiv.style.zIndex = '999999999'; // Extremely high z-index

                // Add the overlay to the body immediately
                iframeDoc.body.appendChild(overlayDiv);

                // Create a message to explain that this page is under construction
                const messageDiv = iframeDoc.createElement('div');
                messageDiv.style.position = 'fixed';
                messageDiv.style.top = '50%';
                messageDiv.style.left = '50%';
                messageDiv.style.transform = 'translate(-50%, -50%)';
                messageDiv.style.textAlign = 'center';
                messageDiv.style.zIndex = '9999999999';
                messageDiv.style.width = '80%';
                messageDiv.style.maxWidth = '600px';
                messageDiv.style.padding = '2rem';
                messageDiv.style.backgroundColor = '#141b2d';
                messageDiv.style.border = '1px solid #2a3a5a';
                messageDiv.style.borderRadius = '0.5rem';
                messageDiv.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1)';

                // Add content to the message
                messageDiv.innerHTML = `
                    <h2 style="font-family: 'Orbitron', sans-serif; color: #00ccff; text-shadow: 0 0 10px rgba(0, 204, 255, 0.5); margin-bottom: 1rem; letter-spacing: 1px;">MODULE IN DEVELOPMENT</h2>
                    <p style="font-family: 'Rajdhani', sans-serif; color: #f8fafc; margin-bottom: 1rem;">This analytics module is currently being updated with new market data.</p>
                    <p style="font-family: 'Rajdhani', sans-serif; color: #f8fafc; margin-bottom: 1.5rem;">Enhanced features will be available in the next release.</p>
                    <div style="font-family: 'Press Start 2P', cursive; color: #00ff88; font-size: 0.7rem; text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);">PROCESSING DATA... <span class="blink">█</span></div>
                `;

                // Add a style for the blinking cursor
                const blinkStyle = iframeDoc.createElement('style');
                blinkStyle.textContent = `
                    @keyframes blink {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0; }
                    }
                    .blink {
                        animation: blink 1s infinite;
                    }
                `;
                iframeDoc.head.appendChild(blinkStyle);

                // Add the message to the body
                iframeDoc.body.appendChild(messageDiv);

            } catch (error) {
                console.error("Error injecting game styling into iframe:", error);
            }
        }

        // Refresh dashboard
        function refreshDashboard() {
            loadingOverlay.classList.add('active');
            dashboardIframe.classList.remove('loaded');
            setTimeout(() => {
                // Store the current src
                const currentSrc = dashboardIframe.src;

                // Set to about:blank first to force a complete reload
                dashboardIframe.src = 'about:blank';

                // Then set back to the original src after a short delay
                setTimeout(() => {
                    dashboardIframe.src = currentSrc;
                    // Play a refresh sound effect
                    playSound('refresh');
                }, 100);
            }, 300);
        }

        // Simple sound effect system
        function playSound(type) {
            // Only implement if browser supports AudioContext
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                let oscillator = audioContext.createOscillator();
                let gainNode = audioContext.createGain();

                // Connect the nodes
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // Set parameters based on sound type
                switch(type) {
                    case 'refresh':
                        oscillator.type = 'sine';
                        oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // A5
                        oscillator.frequency.exponentialRampToValueAtTime(440, audioContext.currentTime + 0.2); // A4
                        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                        oscillator.start();
                        oscillator.stop(audioContext.currentTime + 0.2);
                        break;
                    case 'click':
                        oscillator.type = 'square';
                        oscillator.frequency.setValueAtTime(660, audioContext.currentTime);
                        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                        oscillator.start();
                        oscillator.stop(audioContext.currentTime + 0.1);
                        break;
                }
            } catch (e) {
                console.log('Web Audio API not supported or user interaction required');
            }
        }

        // Toggle fullscreen
        function toggleFullscreen() {
            playSound('click');
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }

        // Module tabs
        const moduleTabs = document.querySelectorAll('.module-tab');

        moduleTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Play click sound
                playSound('click');

                // Remove active class from all tabs
                moduleTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // In a real implementation, this would switch between different views
                // For now, we'll just show a message
                console.log('Switched to tab: ' + this.getAttribute('data-tab'));
            });
        });

        // Create config viewer
        function createConfigViewer() {
            // Play click sound
            playSound('click');

            // Remove active class from all menu items
            menuItems.forEach(i => i.classList.remove('active'));

            // Add active class to config menu item
            document.querySelector('.menu-item[onclick="createConfigViewer()"]').classList.add('active');

            // Update dashboard title
            dashboardTitle.textContent = "Bot Upgrades";

            // Show loading overlay
            loadingOverlay.classList.add('active');
            dashboardIframe.classList.remove('loaded');

            // Create a game-themed config viewer HTML
            const configHTML = `
            <!DOCTYPE html>
            <html class="game-mode">
            <head>
                <title>Trading Bot Upgrades</title>
                <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <style>
                    :root {
                        --primary: #00ccff;
                        --primary-glow: rgba(0, 204, 255, 0.5);
                        --secondary: #ff00aa;
                        --secondary-glow: rgba(255, 0, 170, 0.5);
                        --success: #00ff88;
                        --success-glow: rgba(0, 255, 136, 0.5);
                        --warning: #ffcc00;
                        --warning-glow: rgba(255, 204, 0, 0.5);
                        --danger: #ff3366;
                        --danger-glow: rgba(255, 51, 102, 0.5);
                        --dark: #f8fafc;
                        --light: #0a0e17;
                        --gray: #94a3b8;
                        --card-bg: #141b2d;
                        --border: #2a3a5a;
                        --text-primary: #f8fafc;
                        --text-secondary: #94a3b8;
                        --bg-main: #0a0e17;
                    }

                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Rajdhani', sans-serif;
                        background-color: var(--bg-main);
                        color: var(--text-primary);
                        line-height: 1.6;
                        padding: 2rem;
                        background-image:
                            radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                            radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                            linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                            url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
                        background-attachment: fixed;
                    }

                    h1, h2, h3, h4, h5, h6 {
                        font-family: 'Orbitron', sans-serif;
                        letter-spacing: 1px;
                    }

                    .container {
                        max-width: 1000px;
                        margin: 0 auto;
                        background: var(--card-bg);
                        border-radius: 0.5rem;
                        padding: 2rem;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
                        border: 1px solid var(--border);
                        position: relative;
                        overflow: hidden;
                    }

                    .container::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 2px;
                        background: linear-gradient(to right, transparent, var(--primary), transparent);
                        opacity: 0.7;
                    }

                    h1 {
                        font-size: 2rem;
                        font-weight: 700;
                        color: var(--primary);
                        margin-bottom: 1.5rem;
                        text-align: center;
                        text-shadow: 0 0 10px var(--primary-glow);
                    }

                    pre {
                        background-color: rgba(10, 14, 23, 0.5);
                        color: var(--text-primary);
                        padding: 1.5rem;
                        border-radius: 0.5rem;
                        overflow-x: auto;
                        font-family: 'Rajdhani', monospace;
                        line-height: 1.5;
                        border: 1px solid var(--border);
                        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
                    }

                    pre .comment {
                        color: var(--secondary);
                        text-shadow: 0 0 5px var(--secondary-glow);
                    }

                    pre .keyword {
                        color: var(--primary);
                        text-shadow: 0 0 5px var(--primary-glow);
                    }

                    pre .string {
                        color: var(--success);
                        text-shadow: 0 0 5px var(--success-glow);
                    }

                    pre .number {
                        color: var(--warning);
                        text-shadow: 0 0 5px var(--warning-glow);
                    }

                    pre .boolean {
                        color: var(--danger);
                        text-shadow: 0 0 5px var(--danger-glow);
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>TRADING BOT UPGRADES</h1>
                    <pre><span class="comment">// config.js - GOATED Optimized Configuration with 0 bar latency
// Using the best parameters from the grid test</span>

<span class="keyword">module.exports</span> = {
  <span class="comment">// --- General Settings ---</span>
  inputFile: <span class="string">'./input/MNQ_Last6months.csv'</span>, <span class="comment">// Recent 6-month data</span>
  initialBalance: <span class="number">10000</span>,

  <span class="comment">// --- Instrument Specifics (MNQ Specs) ---</span>
  pointValue: <span class="number">2.00</span>,
  tickSize: <span class="number">0.25</span>,
  pricePrecision: <span class="number">2</span>,

  <span class="comment">// --- Costs & Slippage ---</span>
  commissionPerContract: <span class="number">0.40</span>, <span class="comment">// Realistic commission costs</span>
  slippagePoints: <span class="number">0.75</span>,       <span class="comment">// Realistic slippage</span>

  <span class="comment">// --- Indicator Periods ---</span>
  atrPeriod: <span class="number">14</span>,
  rsiPeriod: <span class="number">14</span>,
  rsiMaPeriod: <span class="number">8</span>,
  sma200Period: <span class="number">0</span>,
  wma50Period: <span class="number">0</span>,

  <span class="comment">// --- Strategy Parameters ---</span>
  fixedTpPoints: <span class="number">40</span>,
  useWmaFilter: <span class="boolean">false</span>,
  useTwoBarColorExit: <span class="boolean">false</span>,
  minAtrEntry: <span class="number">0</span>,
  minRsiMaSeparation: <span class="number">0</span>,

  <span class="comment">// --- RSI Bands ---</span>
  rsiUpperBand: <span class="number">60</span>, rsiLowerBand: <span class="number">40</span>, rsiMiddleBand: <span class="number">50</span>,

  <span class="comment">// --- Run Mode: OPTIMIZED CONFIGURATION ---</span>
  isAdaptiveRun: <span class="boolean">true</span>,

  <span class="comment">// Using optimal parameters from grid test</span>
  slFactors: <span class="number">4.5</span>,
  tpFactors: <span class="number">3.0</span>,
  trailFactors: <span class="number">0.11</span>,

  <span class="comment">// 0 bar latency</span>
  latencyDelayBars: <span class="number">0</span>,

  <span class="comment">// Keep these for compatibility</span>
  costGrid: <span class="keyword">null</span>,
  riskPercentGrid: <span class="keyword">null</span>,
  fixedContractsGrid: <span class="keyword">null</span>,
  fixedTpPointsGrid: <span class="keyword">null</span>,

  <span class="comment">// Fixed parameters</span>
  riskPercent: <span class="number">0</span>,
  fixedContracts: <span class="number">10</span>,   <span class="comment">// Using 10 contracts for final version</span>
  maxContracts: <span class="number">10</span>,

  <span class="comment">// For compatibility with backtest.js</span>
  currentRiskPercent: <span class="number">0</span>,
  currentFixedContracts: <span class="number">10</span>,

  <span class="comment">// --- Time Filter Settings ---</span>
  timeFilterEnabled: <span class="boolean">false</span>,

  <span class="comment">// --- Enhanced Features Configuration ---</span>
  <span class="comment">// All enhanced features disabled</span>
  useAdaptiveSlippage: <span class="boolean">false</span>,
  minSpreadPoints: <span class="number">0.25</span>,
  defaultSpreadPoints: <span class="number">0.5</span>,
  useVolatilityPositionSizing: <span class="boolean">false</span>,
  useDynamicPositionSizing: <span class="boolean">false</span>,
  basePositionSize: <span class="number">10</span>,
  maxPositionSize: <span class="number">10</span>,
  minPositionSize: <span class="number">10</span>,
  useMLEntryFilter: <span class="boolean">false</span>,
  useAdvancedMLFilter: <span class="boolean">false</span>,
  mlEntryThreshold: <span class="number">0.5</span>,
  useTimeAnalysis: <span class="boolean">false</span>,
  timeScoreThreshold: <span class="number">0.5</span>,
  useCircuitBreakers: <span class="boolean">false</span>,
  maxSlippageMultiplier: <span class="number">5</span>,
  pauseTradingOnExcessiveSlippage: <span class="boolean">false</span>,
  useSteppedTrail: <span class="boolean">false</span>,
  trailStepSizeMultiplier: <span class="number">0.1</span>,
  spreadBufferMultiplier: <span class="number">2.0</span>
};</pre>
                </div>
            </body>
            </html>
            `;

            // Set iframe content
            setTimeout(() => {
                const iframe = document.getElementById('dashboard-iframe');
                iframe.src = 'about:blank';

                setTimeout(() => {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    iframeDoc.open();
                    iframeDoc.write(configHTML);
                    iframeDoc.close();

                    setTimeout(() => {
                        loadingOverlay.classList.remove('active');
                        dashboardIframe.classList.add('loaded');
                    }, 300);
                }, 100);
            }, 300);
        }

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);
                console.log('Show all instruments:', event.detail.showAllInstruments);

                let data;

                if (event.detail.showAllInstruments) {
                    // Load data for all instruments
                    data = await dashboardIntegration.loadAllInstrumentsData();

                    // Update dashboard with all instruments data
                    updateDashboardWithAllData(data);
                } else {
                    // Load data for the active instrument only
                    data = await dashboardIntegration.loadInstrumentData();

                    // Update dashboard with the loaded data
                    updateDashboardWithData(data);
                }

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header title with instrument name
            const headerTitle = document.querySelector('.header-title');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Dashboard`;
            }

            // Update status indicator
            const statusText = document.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = `${data.instrumentConfig.name} Active`;
            }

            // Update charts if they exist
            updateCharts(data);

            console.log('Dashboard updated with data for:', data.instrumentCode);
        }

        // Update charts with data
        function updateCharts(data) {
            // This function will be expanded as we add charts to the dashboard
            console.log('Charts would be updated with:', data);

            // Example: If we have an equity chart, update it
            if (window.equityChart) {
                // Update existing chart with new data
                // This is just a placeholder - actual implementation depends on your charts
            }
        }

        // Update dashboard with data for all instruments
        function updateDashboardWithAllData(allData) {
            // Update header title to show all instruments
            const headerTitle = document.querySelector('.header-title');
            if (headerTitle) {
                headerTitle.textContent = 'All Instruments Dashboard';
            }

            // Update status indicator
            const statusText = document.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = 'ALL INSTRUMENTS ACTIVE';
            }

            // Create a comparison view of all instruments
            createMultiInstrumentView(allData);

            console.log('Dashboard updated with data for all instruments');
        }

        // Create a multi-instrument view
        function createMultiInstrumentView(allData) {
            // Get the iframe
            const iframe = document.getElementById('dashboard-iframe');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

            // Clear the iframe content
            iframeDoc.open();

            // Create HTML for the multi-instrument view
            const instruments = Object.keys(allData);
            const instrumentsData = instruments.map(code => allData[code]);

            // Create HTML content
            const multiInstrumentHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Multi-Instrument Dashboard</title>
                    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                    <style>
                        :root {
                            --primary: #00ccff;
                            --primary-dark: #0099cc;
                            --primary-light: #66e0ff;
                            --primary-glow: rgba(0, 204, 255, 0.5);
                            --success: #00ff88;
                            --success-glow: rgba(0, 255, 136, 0.5);
                            --warning: #ffcc00;
                            --warning-glow: rgba(255, 204, 0, 0.5);
                            --danger: #ff3366;
                            --danger-glow: rgba(255, 51, 102, 0.5);
                            --dark: #f8fafc;
                            --light: #0a0e17;
                            --gray: #94a3b8;
                            --card-bg: #141b2d;
                            --border: #2a3a5a;
                            --text-primary: #f8fafc;
                            --text-secondary: #94a3b8;
                            --bg-main: #0a0e17;
                            --bg-sidebar: #141b2d;
                        }

                        body {
                            font-family: 'Rajdhani', sans-serif;
                            background-color: var(--bg-main);
                            color: var(--text-primary);
                            padding: 20px;
                            background-image:
                                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
                            background-attachment: fixed;
                        }

                        h1, h2, h3 {
                            font-family: 'Orbitron', sans-serif;
                            color: var(--primary);
                            text-shadow: 0 0 10px var(--primary-glow);
                        }

                        .dashboard-title {
                            text-align: center;
                            margin-bottom: 2rem;
                        }

                        .instruments-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                            gap: 1.5rem;
                            margin-bottom: 2rem;
                        }

                        .instrument-card {
                            background-color: var(--card-bg);
                            border: 1px solid var(--border);
                            border-radius: 0.5rem;
                            padding: 1.5rem;
                            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
                        }

                        .instrument-card h2 {
                            font-size: 1.5rem;
                            margin-bottom: 1rem;
                            text-align: center;
                        }

                        .stats-grid {
                            display: grid;
                            grid-template-columns: repeat(2, 1fr);
                            gap: 1rem;
                        }

                        .stat-item {
                            background-color: rgba(0, 0, 0, 0.2);
                            border: 1px solid var(--border);
                            border-radius: 0.25rem;
                            padding: 0.75rem;
                            text-align: center;
                        }

                        .stat-label {
                            font-size: 0.8rem;
                            color: var(--text-secondary);
                            margin-bottom: 0.25rem;
                        }

                        .stat-value {
                            font-family: 'Orbitron', sans-serif;
                            font-size: 1.2rem;
                            font-weight: 600;
                            color: var(--primary);
                            text-shadow: 0 0 5px var(--primary-glow);
                        }

                        .stat-value.positive {
                            color: var(--success);
                            text-shadow: 0 0 5px var(--success-glow);
                        }

                        .stat-value.negative {
                            color: var(--danger);
                            text-shadow: 0 0 5px var(--danger-glow);
                        }

                        .comparison-section {
                            margin-top: 2rem;
                        }

                        .comparison-title {
                            text-align: center;
                            margin-bottom: 1.5rem;
                        }

                        .comparison-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 2rem;
                        }

                        .comparison-table th,
                        .comparison-table td {
                            padding: 0.75rem 1rem;
                            text-align: left;
                            border-bottom: 1px solid var(--border);
                        }

                        .comparison-table th {
                            background-color: rgba(0, 0, 0, 0.2);
                            color: var(--primary);
                            font-family: 'Orbitron', sans-serif;
                            font-weight: 600;
                            text-shadow: 0 0 5px var(--primary-glow);
                        }

                        .comparison-table tr:hover {
                            background-color: rgba(0, 204, 255, 0.05);
                        }
                    </style>
                </head>
                <body>
                    <div class="dashboard-title">
                        <h1>MULTI-INSTRUMENT DASHBOARD</h1>
                        <p>Comparing performance across all trading instruments</p>
                    </div>

                    <div class="instruments-grid">
                        ${instruments.map(code => {
                            const instrument = allData[code];
                            const backtestResults = instrument.backtestResults || {};
                            const trades = instrument.trades || [];
                            const dailyPnL = instrument.dailyPnL || [];

                            // Calculate key metrics
                            const totalPnL = backtestResults.totalPnL || 0;
                            const winRate = backtestResults.winRate || 0;
                            const winDayRate = backtestResults.winDayRate || 0;
                            const maxDrawdown = backtestResults.maxDrawdown || 0;

                            return `
                                <div class="instrument-card">
                                    <h2>${instrument.instrumentConfig.name}</h2>
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <div class="stat-label">Total PnL</div>
                                            <div class="stat-value ${totalPnL >= 0 ? 'positive' : 'negative'}">$${totalPnL.toLocaleString('en-US', {maximumFractionDigits: 2})}</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">Win Rate</div>
                                            <div class="stat-value">${(winRate * 100).toFixed(2)}%</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">Win Day Rate</div>
                                            <div class="stat-value">${(winDayRate * 100).toFixed(2)}%</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">Max Drawdown</div>
                                            <div class="stat-value negative">$${maxDrawdown.toLocaleString('en-US', {maximumFractionDigits: 2})}</div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>

                    <div class="comparison-section">
                        <h2 class="comparison-title">PERFORMANCE COMPARISON</h2>
                        <table class="comparison-table">
                            <thead>
                                <tr>
                                    <th>Instrument</th>
                                    <th>Total PnL</th>
                                    <th>Win Rate</th>
                                    <th>Win Day Rate</th>
                                    <th>Max Drawdown</th>
                                    <th>Sharpe Ratio</th>
                                    <th>Total Trades</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${instruments.map(code => {
                                    const instrument = allData[code];
                                    const backtestResults = instrument.backtestResults || {};
                                    const trades = instrument.trades || [];

                                    // Calculate key metrics
                                    const totalPnL = backtestResults.totalPnL || 0;
                                    const winRate = backtestResults.winRate || 0;
                                    const winDayRate = backtestResults.winDayRate || 0;
                                    const maxDrawdown = backtestResults.maxDrawdown || 0;
                                    const sharpeRatio = backtestResults.sharpeRatio || 0;
                                    const totalTrades = trades.length;

                                    return `
                                        <tr>
                                            <td>${instrument.instrumentConfig.name}</td>
                                            <td class="${totalPnL >= 0 ? 'positive' : 'negative'}">$${totalPnL.toLocaleString('en-US', {maximumFractionDigits: 2})}</td>
                                            <td>${(winRate * 100).toFixed(2)}%</td>
                                            <td>${(winDayRate * 100).toFixed(2)}%</td>
                                            <td class="negative">$${maxDrawdown.toLocaleString('en-US', {maximumFractionDigits: 2})}</td>
                                            <td>${sharpeRatio.toFixed(2)}</td>
                                            <td>${totalTrades.toLocaleString('en-US')}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </body>
                </html>
            `;

            // Write the HTML to the iframe
            iframeDoc.write(multiInstrumentHTML);
            iframeDoc.close();

            // Show the iframe
            setTimeout(() => {
                iframe.classList.add('loaded');
            }, 300);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
