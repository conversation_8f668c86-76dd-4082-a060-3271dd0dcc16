/**
 * Feed Simulated Data
 * 
 * This script feeds the simulated market data to the trading bot.
 */

const fs = require('fs');
const path = require('path');
const WebSocket = require('ws');

// Create a WebSocket server
const wss = new WebSocket.Server({ port: 8080 });

// Load the simulated candle data
const dataDir = path.join(__dirname, 'data');
const SYMBOLS = ['MNQM5', 'MESM5', 'MGCM5', 'M2KM5'];
const candleData = {};

// Load candle data for each symbol
for (const symbol of SYMBOLS) {
    const candleDataPath = path.join(dataDir, `${symbol}_candles.json`);
    if (fs.existsSync(candleDataPath)) {
        candleData[symbol] = JSON.parse(fs.readFileSync(candleDataPath, 'utf8'));
        console.log(`Loaded ${candleData[symbol].length} candles for ${symbol}`);
    } else {
        console.log(`No candle data found for ${symbol}`);
    }
}

// WebSocket connection handler
wss.on('connection', (ws) => {
    console.log('Client connected');
    
    // Send ready message
    ws.send(JSON.stringify({
        type: 'ready',
        data: {
            message: 'Simulated market data ready'
        }
    }));
    
    // Handle messages from client
    ws.on('message', (message) => {
        try {
            const msg = JSON.parse(message);
            console.log('Received message:', msg);
            
            // Handle subscribe message
            if (msg.type === 'subscribe') {
                const symbols = msg.data.symbols;
                console.log(`Client subscribed to symbols: ${symbols.join(', ')}`);
                
                // Send initial candle data for each symbol
                for (const symbol of symbols) {
                    if (candleData[symbol]) {
                        // Send the first 50 candles as historical data
                        const historicalCandles = candleData[symbol].slice(0, 50);
                        ws.send(JSON.stringify({
                            type: 'candles',
                            data: {
                                symbol,
                                candles: historicalCandles
                            }
                        }));
                        console.log(`Sent ${historicalCandles.length} historical candles for ${symbol}`);
                    }
                }
                
                // Start sending real-time candle updates
                startSendingCandles(ws, symbols);
            }
        } catch (error) {
            console.error('Error processing message:', error);
        }
    });
    
    // Handle client disconnect
    ws.on('close', () => {
        console.log('Client disconnected');
    });
});

// Function to start sending candle updates
function startSendingCandles(ws, symbols) {
    // Current candle index for each symbol
    const currentIndex = {};
    for (const symbol of symbols) {
        currentIndex[symbol] = 50; // Start after the historical candles
    }
    
    // Send candle updates every second
    const interval = setInterval(() => {
        let allDone = true;
        
        for (const symbol of symbols) {
            if (candleData[symbol] && currentIndex[symbol] < candleData[symbol].length) {
                allDone = false;
                
                // Send the next candle
                const candle = candleData[symbol][currentIndex[symbol]];
                ws.send(JSON.stringify({
                    type: 'candle_update',
                    data: {
                        symbol,
                        candle
                    }
                }));
                console.log(`Sent candle update for ${symbol} at index ${currentIndex[symbol]}`);
                
                // Increment the index
                currentIndex[symbol]++;
            }
        }
        
        // If all candles have been sent, stop the interval
        if (allDone) {
            clearInterval(interval);
            console.log('All candles have been sent');
        }
    }, 1000);
    
    // Store the interval ID on the WebSocket object so we can clear it if the client disconnects
    ws._candleInterval = interval;
    
    // Clear the interval when the client disconnects
    ws.on('close', () => {
        clearInterval(interval);
    });
}

console.log('WebSocket server started on port 8080');
