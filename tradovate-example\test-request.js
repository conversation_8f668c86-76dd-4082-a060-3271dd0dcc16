// test-request.js
// Example of making a test request to the Tradovate API

const fetch = require('node-fetch');
const { waitForMs } = require('./node-utils');

// API URLs
const URLS = {
    DEMO_URL: 'https://demo.tradovateapi.com/v1',
    LIVE_URL: 'https://live.tradovateapi.com/v1'
};

// API credentials
const credentials = {
    name: "bravesbeatmets",
    password: "Braves12$",
    appId: "Trading Bot",
    appVersion: "1.0",
    cid: "6186",
    sec: "69311dad-75a7-49c6-9958-00ab2c4f1ab6",
    deviceId: "example-" + Math.random().toString(36).substring(2, 15) + 
              Math.random().toString(36).substring(2, 15)
};

// In-memory storage for tokens
let accessToken = null;
let tokenExpiration = null;

/**
 * Make a POST request to the Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {boolean} auth - Whether to include auth header
 * @returns {Promise<Object>} JSON response
 */
const tvPost = async (endpoint, data = {}, auth = true) => {
    // Build request options
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    };

    // Add auth header if needed
    if (auth && accessToken) {
        options.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Make a GET request to the Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {boolean} auth - Whether to include auth header
 * @returns {Promise<Object>} JSON response
 */
const tvGet = async (endpoint, params = {}, auth = true) => {
    // Build query string from params
    const queryString = Object.keys(params).length
        ? '?' + Object.entries(params)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&')
        : '';

    // Build request options
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    };

    // Add auth header if needed
    if (auth && accessToken) {
        options.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}${queryString}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Authenticate with the Tradovate API
 * @returns {Promise<boolean>} Whether authentication was successful
 */
const authenticate = async () => {
    try {
        console.log('Authenticating with Tradovate API...');
        
        // Request access token
        const response = await tvPost('/auth/accesstokenrequest', credentials, false);
        
        if (response.accessToken) {
            console.log('Authentication successful!');
            accessToken = response.accessToken;
            tokenExpiration = response.expirationTime;
            return true;
        } else {
            console.error('Authentication failed:', response.errorText || 'Unknown error');
            return false;
        }
    } catch (error) {
        console.error('Error authenticating:', error);
        return false;
    }
};

/**
 * Make a test request to the Tradovate API
 */
const makeTestRequest = async () => {
    try {
        // First authenticate
        const authenticated = await authenticate();
        
        if (!authenticated) {
            console.error('Cannot make test request without authentication');
            return;
        }
        
        // Make a test request to get user data
        console.log('Making test request to get user data...');
        const userData = await tvGet('/account/list');
        
        console.log('User accounts:', userData);
        
        // Make another test request to get contract items
        console.log('Making test request to get contract items...');
        const contractItems = await tvGet('/contract/find', { name: 'MNQ' });
        
        console.log('Contract items:', contractItems);
    } catch (error) {
        console.error('Error making test request:', error);
    }
};

// Run the test
makeTestRequest();
