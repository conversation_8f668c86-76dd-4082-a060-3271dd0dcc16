/**
 * Simulate Market Data
 * 
 * This script simulates market data for testing the trading bot.
 * It generates candles with patterns that should trigger the bot's pattern detection.
 */

const fs = require('fs');
const path = require('path');

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir);
}

// Symbols to generate data for
const SYMBOLS = ['MNQM5', 'MESM5', 'MGCM5', 'M2KM5'];

// Base prices for each symbol
const BASE_PRICES = {
    'MNQM5': 21000,
    'MESM5': 5900,
    'MGCM5': 3200,
    'M2KM5': 2100
};

// Generate a random price with some volatility
function generatePrice(basePrice, volatility = 0.001) {
    return basePrice * (1 + (Math.random() - 0.5) * volatility);
}

// Generate a candle
function generateCandle(timestamp, basePrice, volatility = 0.001, direction = null) {
    // If direction is not specified, randomly choose up or down
    if (direction === null) {
        direction = Math.random() > 0.5 ? 'up' : 'down';
    }
    
    // Generate prices
    const range = basePrice * volatility;
    let open, close, high, low;
    
    if (direction === 'up') {
        open = basePrice - range * Math.random();
        close = basePrice + range * Math.random();
        high = close + range * Math.random() * 0.5;
        low = open - range * Math.random() * 0.5;
    } else {
        open = basePrice + range * Math.random();
        close = basePrice - range * Math.random();
        high = open + range * Math.random() * 0.5;
        low = close - range * Math.random() * 0.5;
    }
    
    return {
        timestamp,
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 100) + 1
    };
}

// Generate a series of candles with a bullish 3-candle pattern
function generateBullish3CandlePattern(baseTimestamp, basePrice) {
    const candles = [];
    
    // First candle: green
    candles.push(generateCandle(baseTimestamp, basePrice, 0.002, 'up'));
    
    // Second candle: red
    candles.push(generateCandle(baseTimestamp + 60000, basePrice * 0.998, 0.002, 'down'));
    
    // Third candle: green (engulfing)
    const thirdCandle = generateCandle(baseTimestamp + 120000, basePrice * 1.002, 0.002, 'up');
    // Make sure it engulfs the second candle
    thirdCandle.open = Math.min(thirdCandle.open, candles[1].close * 0.999);
    thirdCandle.close = Math.max(thirdCandle.close, candles[1].open * 1.001);
    // Make sure the wick is longer
    thirdCandle.low = Math.min(thirdCandle.low, candles[1].low * 0.998);
    candles.push(thirdCandle);
    
    return candles;
}

// Generate a series of candles with a bearish 3-candle pattern
function generateBearish3CandlePattern(baseTimestamp, basePrice) {
    const candles = [];
    
    // First candle: red
    candles.push(generateCandle(baseTimestamp, basePrice, 0.002, 'down'));
    
    // Second candle: green
    candles.push(generateCandle(baseTimestamp + 60000, basePrice * 1.002, 0.002, 'up'));
    
    // Third candle: red (engulfing)
    const thirdCandle = generateCandle(baseTimestamp + 120000, basePrice * 0.998, 0.002, 'down');
    // Make sure it engulfs the second candle
    thirdCandle.open = Math.max(thirdCandle.open, candles[1].close * 1.001);
    thirdCandle.close = Math.min(thirdCandle.close, candles[1].open * 0.999);
    // Make sure the wick is longer
    thirdCandle.high = Math.max(thirdCandle.high, candles[1].high * 1.002);
    candles.push(thirdCandle);
    
    return candles;
}

// Generate a series of candles with a bullish 4-candle pattern
function generateBullish4CandlePattern(baseTimestamp, basePrice) {
    const candles = [];
    
    // First candle: green
    candles.push(generateCandle(baseTimestamp, basePrice, 0.002, 'up'));
    
    // Second candle: red
    candles.push(generateCandle(baseTimestamp + 60000, basePrice * 0.998, 0.002, 'down'));
    
    // Third candle: red
    candles.push(generateCandle(baseTimestamp + 120000, basePrice * 0.996, 0.002, 'down'));
    
    // Fourth candle: green (closing above second candle's open)
    const fourthCandle = generateCandle(baseTimestamp + 180000, basePrice * 1.003, 0.002, 'up');
    // Make sure it closes above the second candle's open
    fourthCandle.close = Math.max(fourthCandle.close, Math.max(candles[1].open, candles[2].open) * 1.001);
    candles.push(fourthCandle);
    
    return candles;
}

// Generate a series of candles with a bearish 4-candle pattern
function generateBearish4CandlePattern(baseTimestamp, basePrice) {
    const candles = [];
    
    // First candle: red
    candles.push(generateCandle(baseTimestamp, basePrice, 0.002, 'down'));
    
    // Second candle: green
    candles.push(generateCandle(baseTimestamp + 60000, basePrice * 1.002, 0.002, 'up'));
    
    // Third candle: green
    candles.push(generateCandle(baseTimestamp + 120000, basePrice * 1.004, 0.002, 'up'));
    
    // Fourth candle: red (closing below second candle's open)
    const fourthCandle = generateCandle(baseTimestamp + 180000, basePrice * 0.997, 0.002, 'down');
    // Make sure it closes below the second candle's open
    fourthCandle.close = Math.min(fourthCandle.close, Math.min(candles[1].open, candles[2].open) * 0.999);
    candles.push(fourthCandle);
    
    return candles;
}

// Generate simulated data for each symbol
for (const symbol of SYMBOLS) {
    const basePrice = BASE_PRICES[symbol];
    const candles = [];
    
    // Generate 50 random candles
    let timestamp = Date.now() - (60 * 60 * 1000); // Start 1 hour ago
    for (let i = 0; i < 50; i++) {
        candles.push(generateCandle(timestamp, basePrice));
        timestamp += 60000; // Add 1 minute
    }
    
    // Add a bullish 3-candle pattern
    const bullish3Pattern = generateBullish3CandlePattern(timestamp, basePrice);
    candles.push(...bullish3Pattern);
    timestamp += 180000; // Add 3 minutes
    
    // Add a few random candles
    for (let i = 0; i < 5; i++) {
        candles.push(generateCandle(timestamp, basePrice));
        timestamp += 60000; // Add 1 minute
    }
    
    // Add a bearish 3-candle pattern
    const bearish3Pattern = generateBearish3CandlePattern(timestamp, basePrice);
    candles.push(...bearish3Pattern);
    timestamp += 180000; // Add 3 minutes
    
    // Add a few random candles
    for (let i = 0; i < 5; i++) {
        candles.push(generateCandle(timestamp, basePrice));
        timestamp += 60000; // Add 1 minute
    }
    
    // Add a bullish 4-candle pattern
    const bullish4Pattern = generateBullish4CandlePattern(timestamp, basePrice);
    candles.push(...bullish4Pattern);
    timestamp += 240000; // Add 4 minutes
    
    // Add a few random candles
    for (let i = 0; i < 5; i++) {
        candles.push(generateCandle(timestamp, basePrice));
        timestamp += 60000; // Add 1 minute
    }
    
    // Add a bearish 4-candle pattern
    const bearish4Pattern = generateBearish4CandlePattern(timestamp, basePrice);
    candles.push(...bearish4Pattern);
    
    // Save the candles to a file
    const filePath = path.join(dataDir, `${symbol}_candles.json`);
    fs.writeFileSync(filePath, JSON.stringify(candles, null, 2));
    console.log(`Generated ${candles.length} candles for ${symbol}`);
}

console.log('Simulated market data generated successfully');
