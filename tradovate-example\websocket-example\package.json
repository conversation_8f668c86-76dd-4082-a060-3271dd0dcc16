{"name": "tradovate-websocket-example", "version": "1.0.0", "description": "WebSocket example for Tradovate API", "main": "app.js", "type": "module", "scripts": {"start": "webpack serve --open", "build": "webpack --mode production"}, "dependencies": {"node-fetch": "^2.6.7"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "babel-loader": "^8.2.3", "html-webpack-plugin": "^5.5.0", "webpack": "^5.69.1", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}}