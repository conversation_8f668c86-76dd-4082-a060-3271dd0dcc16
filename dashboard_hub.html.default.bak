<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Trading Bot Dashboard Hub</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Press+Start+2P&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
            --gold: #FFD700;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 15px var(--primary-glow);
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .instrument-selector {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 2rem 0;
        }

        .instrument-selector label {
            margin-right: 1rem;
            color: var(--text-secondary);
        }

        .instrument-selector select {
            background-color: var(--card-bg);
            color: var(--text-primary);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-family: 'Rajdhani', sans-serif;
            font-size: 1rem;
            cursor: pointer;
            outline: none;
            transition: all 0.3s ease;
        }

        .instrument-selector select:hover {
            border-color: var(--primary);
            box-shadow: 0 0 5px var(--primary-glow);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.15);
        }

        .dashboard-card h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .dashboard-card p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            flex-grow: 1;
        }

        .dashboard-card a {
            display: inline-block;
            background-color: var(--primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid var(--primary-dark);
            box-shadow: 0 0 10px var(--primary-glow);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
            text-align: center;
        }

        .dashboard-card a:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0 15px var(--primary-glow);
        }

        .dashboard-card.performance a {
            background-color: var(--success);
            border-color: var(--success);
            box-shadow: 0 0 10px var(--success-glow);
            text-shadow: 0 0 5px var(--success-glow);
        }

        .dashboard-card.performance a:hover {
            background-color: rgba(0, 255, 136, 0.8);
            box-shadow: 0 0 15px var(--success-glow);
        }

        .dashboard-card.risk a {
            background-color: var(--danger);
            border-color: var(--danger);
            box-shadow: 0 0 10px var(--danger-glow);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .dashboard-card.risk a:hover {
            background-color: rgba(255, 51, 102, 0.8);
            box-shadow: 0 0 15px var(--danger-glow);
        }

        .dashboard-card.market a {
            background-color: var(--warning);
            border-color: var(--warning);
            box-shadow: 0 0 10px var(--warning-glow);
            text-shadow: 0 0 5px var(--warning-glow);
        }

        .dashboard-card.market a:hover {
            background-color: rgba(255, 204, 0, 0.8);
            box-shadow: 0 0 15px var(--warning-glow);
        }

        .dashboard-card.gold a {
            background-color: var(--gold);
            border-color: var(--gold);
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
            color: #000;
        }

        .dashboard-card.gold a:hover {
            background-color: rgba(255, 215, 0, 0.8);
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            text-shadow: 0 0 10px var(--primary-glow);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<div class="container">
<div class="header">
<h1>TRADING BOT COMMAND CENTER</h1>
<p>Centralized access to all trading dashboards</p>
</div>
<div class="instrument-selector">
<label for="instrument-select">Select Instrument:</label>
<select id="instrument-select">
<option value="ALL" selected>All Instruments</option>
<option value="MNQ">Micro Nasdaq (MNQ)</option>
<option value="MGC">Micro Gold (MGC)</option>
<option value="MES">Micro E-mini S&amp;P 500 (MES)</option>
</select>
</div>
<h2 class="section-title">Main Dashboards</h2>
<div class="dashboard-grid">
<div class="dashboard-card">
<h3>Quantum Capital Dashboard</h3>
<p>Advanced trading performance and analytics with comprehensive metrics and visualizations.</p>
<a href="hedge_dashboard.html" id="hedge-link">Open Dashboard</a>
</div>
<div class="dashboard-card performance">
<h3>Performance Analytics</h3>
<p>Detailed performance metrics and analysis of trading strategy effectiveness.</p>
<a href="performance_comparison_dashboard.html" id="performance-link">Open Dashboard</a>
</div>
<div class="dashboard-card market">
<h3>Market Intelligence</h3>
<p>Market analysis and intelligence with correlation and volatility metrics.</p>
<a href="market_intelligence.html" id="market-link">Open Dashboard</a>
</div>
</div>
<h2 class="section-title">Analysis Dashboards</h2>
<div class="dashboard-grid">
<div class="dashboard-card">
<h3>Correlation Analysis</h3>
<p>Analyze correlations between different markets and trading instruments.</p>
<a href="correlation_analysis_dashboard.html" id="correlation-link">Open Dashboard</a>
</div>
<div class="dashboard-card risk">
<h3>Monte Carlo Simulation</h3>
<p>Risk and performance projections using Monte Carlo simulation techniques.</p>
<a href="monte_carlo_dashboard.html" id="monte-carlo-link">Open Dashboard</a>
</div>
<div class="dashboard-card risk">
<h3>Drawdown Analysis</h3>
<p>Detailed analysis of drawdowns and recovery periods.</p>
<a href="drawdown_analysis_dashboard.html" id="drawdown-link">Open Dashboard</a>
</div>
</div>
<h2 class="section-title">Portfolio Management</h2>
<div class="dashboard-grid">
<div class="dashboard-card">
<h3>Portfolio Manager</h3>
<p>Manage and optimize your trading portfolio across multiple instruments.</p>
<a href="portfolio_manager.html" id="portfolio-manager-link">Open Dashboard</a>
</div>
<div class="dashboard-card">
<h3>Portfolio Overview</h3>
<p>High-level overview of your entire trading portfolio performance.</p>
<a href="portfolio_overview.html" id="portfolio-overview-link">Open Dashboard</a>
</div>
<div class="dashboard-card performance">
<h3>Performance Forecast</h3>
<p>Forecast future performance based on historical trading data.</p>
<a href="performance_forecast.html" id="forecast-link">Open Dashboard</a>
</div>
</div>
<h2 class="section-title">Market &amp; Risk Analysis</h2>
<div class="dashboard-grid">
<div class="dashboard-card market">
<h3>Market Volatility</h3>
<p>Analyze market volatility and its impact on trading performance.</p>
<a href="market_volatility.html" id="volatility-link">Open Dashboard</a>
</div>
<div class="dashboard-card">
<h3>Daily PnL Analysis</h3>
<p>Detailed analysis of daily profit and loss performance.</p>
<a href="daily_pnl_dashboard.html" id="daily-pnl-link">Open Dashboard</a>
</div>
<div class="dashboard-card gold">
<h3>Alpha Dashboard</h3>
<p>Premium dashboard with advanced analytics and insights.</p>
<a href="premium_dashboard.html" id="premium-link">Open Dashboard</a>
</div>
</div>
<h2 class="section-title">Utilities</h2>
<div class="dashboard-grid">
<div class="dashboard-card">
<h3>Custom Alerts</h3>
<p>Set up and manage custom trading alerts and notifications.</p>
<a href="custom_alerts_dashboard.html" id="alerts-link">Open Dashboard</a>
</div>
<div class="dashboard-card">
<h3>Investor Reporting</h3>
<p>Generate and view reports for investors and stakeholders.</p>
<a href="investor_reporting.html" id="investor-link">Open Dashboard</a>
</div>
</div>
<div class="footer">
<p>Trading Bot Dashboard Hub © 2023</p>
</div>
</div>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the instrument selector
            const instrumentSelect = document.getElementById('instrument-select');

            // Get all dashboard links
            const dashboardLinks = document.querySelectorAll('.dashboard-card a');

            // Update links when instrument changes
            instrumentSelect.addEventListener('change', function() {
                const selectedInstrument = this.value;

                // Update all dashboard links with the selected instrument
                dashboardLinks.forEach(link => {
                    const currentHref = link.getAttribute('href');
                    let newHref;

                    if (selectedInstrument === 'ALL') {
                        // For "All Instruments" option, use showAllInstruments parameter
                        newHref = currentHref.includes('?')
                            ? currentHref.split('?')[0] + `?showAllInstruments=true`
                            : currentHref + `?showAllInstruments=true`;
                    } else {
                        // For specific instrument, use instrument parameter
                        newHref = currentHref.includes('?')
                            ? currentHref.split('?')[0] + `?instrument=${selectedInstrument}`
                            : currentHref + `?instrument=${selectedInstrument}`;
                    }

                    link.setAttribute('href', newHref);
                });

                console.log(`Selected instrument: ${selectedInstrument}`);
            });

            // Initialize links with default instrument
            const defaultInstrument = instrumentSelect.value;
            dashboardLinks.forEach(link => {
                const currentHref = link.getAttribute('href');
                let newHref;

                if (defaultInstrument === 'ALL') {
                    newHref = currentHref + `?showAllInstruments=true`;
                } else {
                    newHref = currentHref + `?instrument=${defaultInstrument}`;
                }

                link.setAttribute('href', newHref);
            });
        });
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;

    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });

    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';

        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }

        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);

        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }

    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }

    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });

        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }

        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }

        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
