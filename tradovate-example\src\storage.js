// storage.js
// Handles session storage of tokens, device IDs, and other data

/**
 * Store a value in sessionStorage
 * @param {string} key - Storage key
 * @param {any} value - Value to store
 */
export const setItem = (key, value) => {
    try {
        if (typeof window !== 'undefined' && window.sessionStorage) {
            if (typeof value === 'object') {
                sessionStorage.setItem(key, JSON.stringify(value))
            } else {
                sessionStorage.setItem(key, value)
            }
        }
    } catch (error) {
        console.error('Error storing item in sessionStorage:', error)
    }
}

/**
 * Retrieve a value from sessionStorage
 * @param {string} key - Storage key
 * @param {boolean} parse - Whether to parse the value as JSON
 * @returns {any} The stored value
 */
export const getItem = (key, parse = false) => {
    try {
        if (typeof window !== 'undefined' && window.sessionStorage) {
            const value = sessionStorage.getItem(key)
            if (parse && value) {
                return JSON.parse(value)
            }
            return value
        }
    } catch (error) {
        console.error('Error retrieving item from sessionStorage:', error)
    }
    return null
}

/**
 * Remove a value from sessionStorage
 * @param {string} key - Storage key
 */
export const removeItem = (key) => {
    try {
        if (typeof window !== 'undefined' && window.sessionStorage) {
            sessionStorage.removeItem(key)
        }
    } catch (error) {
        console.error('Error removing item from sessionStorage:', error)
    }
}

// Storage keys
const KEYS = {
    ACCESS_TOKEN: 'tradovate_access_token',
    TOKEN_EXPIRATION: 'tradovate_token_expiration',
    DEVICE_ID: 'tradovate_device_id',
    USER_DATA: 'tradovate_user_data',
    ACCOUNTS: 'tradovate_accounts'
}

/**
 * Store the access token and its expiration
 * @param {string} token - Access token
 * @param {number} expiration - Expiration timestamp
 */
export const setAccessToken = (token, expiration) => {
    setItem(KEYS.ACCESS_TOKEN, token)
    setItem(KEYS.TOKEN_EXPIRATION, expiration)
}

/**
 * Get the stored access token and expiration
 * @returns {Object} Object containing token and expiration
 */
export const getAccessToken = () => {
    const token = getItem(KEYS.ACCESS_TOKEN)
    const expiration = getItem(KEYS.TOKEN_EXPIRATION)
    return {
        token,
        expiration: expiration ? parseInt(expiration) : null
    }
}

/**
 * Check if the token is valid (not expired)
 * @param {number} expiration - Token expiration timestamp
 * @returns {boolean} Whether the token is valid
 */
export const tokenIsValid = (expiration) => {
    if (!expiration) {
        return false
    }

    // Check if token is expired (with 5 minute buffer)
    const now = Date.now()
    return now < (expiration - (5 * 60 * 1000))
}

/**
 * Store the device ID
 * @param {string} deviceId - Device ID
 */
export const setDeviceId = (deviceId) => {
    setItem(KEYS.DEVICE_ID, deviceId)
}

/**
 * Get the stored device ID
 * @returns {string|null} The device ID
 */
export const getDeviceId = () => {
    return getItem(KEYS.DEVICE_ID)
}

/**
 * Store user data
 * @param {Object} userData - User data
 */
export const setUserData = (userData) => {
    setItem(KEYS.USER_DATA, userData)
}

/**
 * Get the stored user data
 * @returns {Object|null} The user data
 */
export const getUserData = () => {
    return getItem(KEYS.USER_DATA, true)
}

/**
 * Store account data
 * @param {Array} accounts - Account data
 */
export const setAccounts = (accounts) => {
    setItem(KEYS.ACCOUNTS, accounts)
}

/**
 * Get the stored accounts
 * @returns {Array|null} The accounts
 */
export const getAccounts = () => {
    return getItem(KEYS.ACCOUNTS, true)
}

/**
 * Get the first available account
 * @returns {Object|null} The first account
 */
export const getAvailableAccounts = () => {
    const accounts = getAccounts()
    return accounts || []
}
