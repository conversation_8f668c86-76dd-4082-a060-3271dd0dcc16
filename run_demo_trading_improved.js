/**
 * Improved Demo Trading Bot Runner
 * 
 * This script provides a command-line interface for controlling the improved demo trading bot.
 */

const readline = require('readline');
const demoTrading = require('./demo_trading_improved');
const logger = require('./data_logger');
const fs = require('fs');
const path = require('path');

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '> '
});

// State
let isRunning = false;
let isShuttingDown = false;

// Command history
const commandHistory = [];
const MAX_HISTORY_SIZE = 50;

// Help text
const helpText = `
=== Demo Trading Bot Commands ===
start - Start the demo trading bot
stop - Stop the demo trading bot
status - Check the status of the demo trading bot
orders - List all active orders
positions - List all open positions
logs - Show recent logs
clear - Clear the console
exit - Exit the program
help - Show this help message
`;

// Welcome message
console.log('INFO: Automated log backup enabled');
console.log('=== Improved Demo Trading Bot CLI ===');
console.log('Type "help" for a list of available commands');
rl.prompt();

// Handle commands
rl.on('line', async (line) => {
    const command = line.trim();
    
    // Add command to history
    if (command && command !== commandHistory[0]) {
        commandHistory.unshift(command);
        if (commandHistory.length > MAX_HISTORY_SIZE) {
            commandHistory.pop();
        }
    }
    
    // Process command
    try {
        if (commands[command]) {
            await commands[command]();
        } else if (command) {
            console.log(`Unknown command: ${command}`);
            console.log('Type "help" for a list of available commands');
        }
    } catch (error) {
        console.error(`Error executing command: ${error.message}`);
    }
    
    rl.prompt();
}).on('close', () => {
    console.log('Shutting down...');
    
    // Shutdown the bot if it's running
    if (isRunning && !isShuttingDown) {
        isShuttingDown = true;
        demoTrading.shutdown()
            .then(() => {
                console.log('Exiting...');
                process.exit(0);
            })
            .catch((error) => {
                console.error(`Error shutting down: ${error.message}`);
                process.exit(1);
            });
    } else {
        console.log('Exiting...');
        process.exit(0);
    }
});

// Command handlers
const commands = {
    start: async () => {
        if (isRunning) {
            console.log('Demo trading bot is already running');
            return;
        }
        
        console.log('Starting demo trading bot...');
        
        try {
            const result = await demoTrading.initialize();
            
            if (result) {
                isRunning = true;
                console.log('Demo trading bot started successfully');
            } else {
                console.error('ERROR: Failed to initialize demo trading bot');
                console.log('Failed to start demo trading bot');
            }
        } catch (error) {
            console.error(`ERROR: ${error.message}`);
            console.log('Failed to start demo trading bot');
        }
    },
    
    stop: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }
        
        console.log('Stopping demo trading bot...');
        
        try {
            isShuttingDown = true;
            const result = await demoTrading.shutdown();
            
            if (result) {
                isRunning = false;
                isShuttingDown = false;
                console.log('Demo trading bot stopped successfully');
            } else {
                isShuttingDown = false;
                console.error('ERROR: Failed to shutdown demo trading bot');
                console.log('Failed to stop demo trading bot');
            }
        } catch (error) {
            isShuttingDown = false;
            console.error(`ERROR: ${error.message}`);
            console.log('Failed to stop demo trading bot');
        }
    },
    
    status: async () => {
        if (!isRunning) {
            console.log('Demo trading bot status: STOPPED');
            return;
        }
        
        try {
            const status = demoTrading.getStatus();
            
            console.log('\n=== Demo Trading Bot Status ===');
            console.log(`Connection: ${status.isConnected ? 'CONNECTED' : 'DISCONNECTED'}`);
            console.log(`Initialization: ${status.isInitialized ? 'INITIALIZED' : 'NOT INITIALIZED'}`);
            console.log(`Active Positions: ${status.activePositions}`);
            console.log(`Active Orders: ${status.activeOrders}`);
            console.log('\nCircuit Breakers:');
            console.log(`  Status: ${status.circuitBreakers.isTripped ? 'TRIPPED' : 'NORMAL'}`);
            console.log(`  Consecutive Losses: ${status.circuitBreakers.consecutiveLossCount}`);
            console.log(`  Daily Loss: $${status.circuitBreakers.dailyLoss.toFixed(2)}`);
            
            if (status.circuitBreakers.lastResetTime) {
                console.log(`  Last Reset: ${status.circuitBreakers.lastResetTime.toLocaleString()}`);
            }
        } catch (error) {
            console.error(`Error getting status: ${error.message}`);
        }
    },
    
    orders: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }
        
        try {
            const orders = demoTrading.getOrders();
            
            console.log('\n=== Active Orders ===');
            
            if (Object.keys(orders).length === 0) {
                console.log('No active orders');
            } else {
                console.log('ID\t\tSymbol\tAction\tQty\tPrice\tStatus');
                console.log('----------------------------------------------------------');
                
                for (const orderId in orders) {
                    const order = orders[orderId];
                    console.log(`${orderId}\t${order.symbol}\t${order.action}\t${order.quantity}\t${order.entryPrice}\t${order.status || 'Pending'}`);
                }
            }
        } catch (error) {
            console.error(`Error getting orders: ${error.message}`);
        }
    },
    
    positions: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }
        
        try {
            const positions = demoTrading.getPositions();
            
            console.log('\n=== Active Positions ===');
            
            if (Object.keys(positions).length === 0) {
                console.log('No active positions');
            } else {
                console.log('ID\t\tSymbol\tAction\tQty\tEntry\tStop\tTarget');
                console.log('----------------------------------------------------------');
                
                for (const positionId in positions) {
                    const position = positions[positionId];
                    console.log(`${positionId}\t${position.symbol}\t${position.action}\t${position.quantity}\t${position.entryPrice}\t${position.stopLossPrice}\t${position.takeProfitPrice}`);
                }
            }
        } catch (error) {
            console.error(`Error getting positions: ${error.message}`);
        }
    },
    
    logs: async () => {
        try {
            const logFile = path.join(__dirname, 'logs', 'system.log');
            
            if (!fs.existsSync(logFile)) {
                console.log('No log file found');
                return;
            }
            
            // Read the last 20 lines of the log file
            const logs = fs.readFileSync(logFile, 'utf8').split('\n').slice(-20);
            
            console.log('\n=== Recent Logs ===');
            logs.forEach(log => {
                if (log.trim()) {
                    console.log(log);
                }
            });
        } catch (error) {
            console.error(`Error reading logs: ${error.message}`);
        }
    },
    
    clear: async () => {
        console.clear();
        console.log('=== Improved Demo Trading Bot CLI ===');
        console.log('Type "help" for a list of available commands');
    },
    
    help: async () => {
        console.log(helpText);
    },
    
    exit: async () => {
        rl.close();
    }
};

// Handle SIGINT (Ctrl+C)
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT. Shutting down...');
    rl.close();
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(`Uncaught Exception: ${error.message}`);
    console.error(error.stack);
    
    // Shutdown the bot if it's running
    if (isRunning && !isShuttingDown) {
        isShuttingDown = true;
        demoTrading.shutdown()
            .then(() => {
                console.log('Exiting due to uncaught exception...');
                process.exit(1);
            })
            .catch((shutdownError) => {
                console.error(`Error shutting down: ${shutdownError.message}`);
                process.exit(1);
            });
    } else {
        console.log('Exiting due to uncaught exception...');
        process.exit(1);
    }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Promise Rejection:');
    console.error(reason);
    
    // Don't exit, just log the error
});
