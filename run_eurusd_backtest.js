/**
 * Run EURUSD Backtest
 * 
 * This script runs a backtest for EURUSD forex pair using optimized parameters.
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');

console.log("Starting EURUSD Backtest...");

// Load configuration
const config = require('./eurusd_optimized_config.js');
const dataPath = 'C:/backtest-bot/input/EURUSD_2020_2025.csv';

// Date range for testing
const dateRange = {
    startDate: '2020-01-01',
    endDate: '2025-01-01'
};

/**
 * Run backtest for EURUSD
 */
async function runBacktest() {
    console.log(`\n===== RUNNING EURUSD BACKTEST =====`);
    console.log(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
    console.log(`Using optimized parameters:`);
    console.log(`  SL Factor: ${config.slFactors}`);
    console.log(`  TP Factor: ${config.tpFactors}`);
    console.log(`  Trail Factor: ${config.trailFactors}`);
    console.log(`  Adaptive Mode: ${config.isAdaptiveRun ? 'Enabled' : 'Disabled'}`);
    
    // Create backtest instance with configuration
    const backtest = new GridBacktest(config);
    
    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { dateRange });
    
    // Run backtest
    console.log(`Running backtest with optimized parameters...`);
    const result = backtest.runBacktest();
    
    if (!result.success) {
        console.error(`Error running backtest: ${result.error}`);
        return;
    }
    
    // Display results
    console.log("\n===== BACKTEST RESULTS =====");
    console.log(`Total PnL: $${result.stats.totalPnL.toFixed(2)}`);
    console.log(`Win Rate: ${(result.stats.winRate * 100).toFixed(2)}%`);
    console.log(`Win Day Rate: ${(result.stats.winDayRate * 100).toFixed(2)}%`);
    console.log(`Profit Factor: ${result.stats.profitFactor.toFixed(2)}`);
    console.log(`Max Drawdown: $${result.stats.maxDrawdown.toFixed(2)}`);
    console.log(`Total Trades: ${result.stats.totalTrades}`);
    console.log(`Average Trade: $${result.stats.averageTrade.toFixed(2)}`);
    console.log(`Average Win: $${result.stats.averageWin.toFixed(2)}`);
    console.log(`Average Loss: $${result.stats.averageLoss.toFixed(2)}`);
    console.log(`Largest Win: $${result.stats.largestWin.toFixed(2)}`);
    console.log(`Largest Loss: $${result.stats.largestLoss.toFixed(2)}`);
    
    // Create output directory if it doesn't exist
    const outputDir = './output/EURUSD_Backtest';
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Save results to file
    const resultsPath = path.join(outputDir, 'backtest_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(result.stats, null, 2));
    console.log(`Results saved to ${resultsPath}`);
    
    // Generate HTML report
    const reportPath = path.join(outputDir, 'backtest_report.html');
    generateHtmlReport(result.stats, reportPath);
    console.log(`HTML report saved to ${reportPath}`);
}

/**
 * Generate HTML report
 * @param {Object} stats - Backtest statistics
 * @param {string} reportPath - Path to save the report
 */
function generateHtmlReport(stats, reportPath) {
    // Create HTML report
    let html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>EURUSD Backtest Results</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #1e1e1e; color: #f0f0f0; }
            h1, h2 { color: #4caf50; }
            .container { max-width: 1200px; margin: 0 auto; }
            .stats-container { display: flex; flex-wrap: wrap; }
            .stat-box { 
                background-color: #2d2d2d; 
                border-radius: 5px; 
                padding: 15px; 
                margin: 10px; 
                flex: 1 0 200px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            }
            .stat-title { font-size: 14px; color: #aaa; margin-bottom: 5px; }
            .stat-value { font-size: 24px; font-weight: bold; }
            .positive { color: #4caf50; }
            .negative { color: #f44336; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #444; padding: 8px; text-align: left; }
            th { background-color: #333; }
            tr:nth-child(even) { background-color: #2a2a2a; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>EURUSD Backtest Results</h1>
            
            <div class="stats-container">
                <div class="stat-box">
                    <div class="stat-title">Total P&L</div>
                    <div class="stat-value ${stats.totalPnL >= 0 ? 'positive' : 'negative'}">$${stats.totalPnL.toFixed(2)}</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Win Rate</div>
                    <div class="stat-value">${(stats.winRate * 100).toFixed(2)}%</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Win Day Rate</div>
                    <div class="stat-value">${(stats.winDayRate * 100).toFixed(2)}%</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Profit Factor</div>
                    <div class="stat-value">${stats.profitFactor.toFixed(2)}</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Max Drawdown</div>
                    <div class="stat-value negative">$${stats.maxDrawdown.toFixed(2)}</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Total Trades</div>
                    <div class="stat-value">${stats.totalTrades}</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Average Trade</div>
                    <div class="stat-value ${stats.averageTrade >= 0 ? 'positive' : 'negative'}">$${stats.averageTrade.toFixed(2)}</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Average Win</div>
                    <div class="stat-value positive">$${stats.averageWin.toFixed(2)}</div>
                </div>
                <div class="stat-box">
                    <div class="stat-title">Average Loss</div>
                    <div class="stat-value negative">$${stats.averageLoss.toFixed(2)}</div>
                </div>
            </div>
            
            <h2>Monthly Performance</h2>
            <table>
                <tr>
                    <th>Month</th>
                    <th>P&L</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                </tr>
                ${generateMonthlyRows(stats)}
            </table>
        </div>
    </body>
    </html>
    `;
    
    // Save HTML report
    fs.writeFileSync(reportPath, html);
}

/**
 * Generate monthly performance rows for HTML report
 * @param {Object} stats - Backtest statistics
 * @returns {string} - HTML table rows
 */
function generateMonthlyRows(stats) {
    if (!stats.monthlyStats) {
        return '<tr><td colspan="4">No monthly data available</td></tr>';
    }
    
    let rows = '';
    for (const [month, data] of Object.entries(stats.monthlyStats)) {
        rows += `
        <tr>
            <td>${month}</td>
            <td class="${data.pnl >= 0 ? 'positive' : 'negative'}">$${data.pnl.toFixed(2)}</td>
            <td>${((data.wins / (data.wins + data.losses)) * 100).toFixed(2)}%</td>
            <td>${data.wins + data.losses}</td>
        </tr>
        `;
    }
    
    return rows;
}

// Run the backtest
runBacktest().catch(err => {
    console.error('Error running backtest:', err);
});
