const fs = require('fs');
const csv = require('csv-parser');

console.log('🔍 DEBUG: Testing CSV data parsing...');

const inputFile = 'C:/backtest-bot/input/MNQ_lastyear.csv';

// First check the file format by reading the header
const headerLine = fs.readFileSync(inputFile, 'utf8').split('\n')[0];
console.log(`CSV Header: ${headerLine}`);

// Determine if this is a Databento format file
const isDatabentoFormat = headerLine.includes('ts_event');
const separator = headerLine.includes(';') ? ';' : ',';

console.log(`Detected format: ${isDatabentoFormat ? 'Databento' : 'Standard'}, separator: '${separator}'`);

let candleCount = 0;
let corruptedCount = 0;

fs.createReadStream(inputFile)
    .pipe(csv({
        separator: separator,
        mapHeaders: ({ header }) => header.trim()
    }))
    .on('data', d => {
        let open, high, low, close, timestampSeconds;

        if (isDatabentoFormat) {
            // Databento format
            open = parseFloat(d['open']);
            high = parseFloat(d['high']);
            low = parseFloat(d['low']);
            close = parseFloat(d['close']);

            // Parse timestamp from ts_event field
            const timeString = d['ts_event'];
            if (timeString) {
                try {
                    const parsedDate = new Date(timeString);
                    if (!isNaN(parsedDate.getTime())) {
                        timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                    }
                } catch (e) {
                    // Skip invalid timestamps
                }
            }

            // Filter by symbol if needed (include all MNQ contracts)
            const symbol = d['symbol'];
            if (symbol && !symbol.startsWith('MNQ')) {
                return; // Skip non-MNQ symbols
            }
        } else {
            // Standard format
            open = +d['Open'];
            high = +d['High'];
            low = +d['Low'];
            close = +d['Close'];
            const timeString = d['Time'] || d['Date'] || d['Time left'];

            if (timeString) {
                let parsedDate;
                try { parsedDate = new Date(timeString); } catch (e) {}
                if (parsedDate && !isNaN(parsedDate.getTime())) {
                    timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                } else if (timeString && !isNaN(Number(timeString))) {
                    const tsNum = Number(timeString);
                    timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                }
            }
        }

        if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            // Skip invalid data
            return;
        }

        candleCount++;
        
        // Check for corrupted prices (should be in 18000-21000 range for MNQ)
        if (close < 1000 || open < 1000 || high < 1000 || low < 1000) {
            corruptedCount++;
            console.log(`🚨 CORRUPTED CANDLE ${candleCount}: open=${open}, high=${high}, low=${low}, close=${close}, timestamp=${timestampSeconds}`);
            console.log(`Raw data: ${JSON.stringify(d)}`);
        }
        
        // Log first few normal candles
        if (candleCount <= 5 && close > 1000) {
            console.log(`✅ Normal candle ${candleCount}: open=${open}, high=${high}, low=${low}, close=${close}, timestamp=${timestampSeconds}`);
        }
        
        // Stop after checking 1000 candles
        if (candleCount >= 1000) {
            console.log(`\n📊 SUMMARY after ${candleCount} candles:`);
            console.log(`   Corrupted candles: ${corruptedCount}`);
            console.log(`   Normal candles: ${candleCount - corruptedCount}`);
            process.exit(0);
        }
    })
    .on('end', () => {
        console.log(`\n📊 FINAL SUMMARY:`);
        console.log(`   Total candles: ${candleCount}`);
        console.log(`   Corrupted candles: ${corruptedCount}`);
        console.log(`   Normal candles: ${candleCount - corruptedCount}`);
    })
    .on('error', (err) => {
        console.error(`Error reading CSV file:`, err);
    });
