const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

console.log('🕐 HOURLY PERFORMANCE ANALYSIS - Finding Worst Performing Hours');

// Find the most recent trade log file
const tradeLogDir = 'C:/backtest-bot/output/MNQ_LastYear_TradeLog';
const files = fs.readdirSync(tradeLogDir)
    .filter(file => file.endsWith('.csv'))
    .map(file => ({
        name: file,
        path: path.join(tradeLogDir, file),
        mtime: fs.statSync(path.join(tradeLogDir, file)).mtime
    }))
    .sort((a, b) => b.mtime - a.mtime);

if (files.length === 0) {
    console.error('❌ No CSV trade log files found in:', tradeLogDir);
    process.exit(1);
}

const tradeLogPath = files[0].path;
console.log('📁 Using most recent trade log:', files[0].name);

const hourlyStats = {};

// Initialize hourly stats (0-23 hours)
for (let hour = 0; hour < 24; hour++) {
    hourlyStats[hour] = {
        hour: hour,
        trades: 0,
        totalPnL: 0,
        wins: 0,
        losses: 0,
        totalWinAmount: 0,
        totalLossAmount: 0,
        avgPnL: 0,
        winRate: 0,
        avgWin: 0,
        avgLoss: 0,
        profitFactor: 0
    };
}

console.log('📊 Processing trade log...');

fs.createReadStream(tradeLogPath)
    .pipe(csv({ separator: ';' }))
    .on('data', (row) => {
        try {
            // Parse entry time to get hour
            const entryTime = new Date(row.EntryTime);
            if (isNaN(entryTime.getTime())) {
                return; // Skip invalid dates
            }
            
            const hour = entryTime.getUTCHours(); // Use UTC hours for consistency
            const pnl = parseFloat(row.PnL_Net);
            
            if (isNaN(pnl) || hour < 0 || hour > 23) {
                return; // Skip invalid data
            }
            
            const stats = hourlyStats[hour];
            stats.trades++;
            stats.totalPnL += pnl;
            
            if (pnl > 0) {
                stats.wins++;
                stats.totalWinAmount += pnl;
            } else {
                stats.losses++;
                stats.totalLossAmount += Math.abs(pnl);
            }
            
        } catch (error) {
            // Skip problematic rows
        }
    })
    .on('end', () => {
        console.log('✅ Trade log processed. Calculating hourly statistics...\n');
        
        // Calculate derived statistics
        for (let hour = 0; hour < 24; hour++) {
            const stats = hourlyStats[hour];
            
            if (stats.trades > 0) {
                stats.avgPnL = stats.totalPnL / stats.trades;
                stats.winRate = (stats.wins / stats.trades) * 100;
                stats.avgWin = stats.wins > 0 ? stats.totalWinAmount / stats.wins : 0;
                stats.avgLoss = stats.losses > 0 ? stats.totalLossAmount / stats.losses : 0;
                stats.profitFactor = stats.totalLossAmount > 0 ? stats.totalWinAmount / stats.totalLossAmount : 0;
            }
        }
        
        // Convert to array and sort by average PnL (worst first)
        const hourlyArray = Object.values(hourlyStats)
            .filter(stats => stats.trades > 0)
            .sort((a, b) => a.avgPnL - b.avgPnL);
        
        // Display results
        console.log('🔴 WORST PERFORMING HOURS (Sorted by Average PnL):');
        console.log('=' .repeat(120));
        console.log('Hour | Trades | Total PnL | Avg PnL | Win Rate | Avg Win | Avg Loss | Profit Factor');
        console.log('-'.repeat(120));
        
        hourlyArray.forEach((stats, index) => {
            const hourStr = stats.hour.toString().padStart(2, '0') + ':00';
            const tradesStr = stats.trades.toString().padStart(6);
            const totalPnLStr = stats.totalPnL.toFixed(2).padStart(10);
            const avgPnLStr = stats.avgPnL.toFixed(2).padStart(8);
            const winRateStr = stats.winRate.toFixed(1).padStart(7) + '%';
            const avgWinStr = stats.avgWin.toFixed(2).padStart(8);
            const avgLossStr = stats.avgLoss.toFixed(2).padStart(9);
            const pfStr = stats.profitFactor.toFixed(3).padStart(12);
            
            const worstIndicator = index < 5 ? '🔴' : index < 10 ? '🟡' : '🟢';
            
            console.log(`${worstIndicator} ${hourStr} | ${tradesStr} | ${totalPnLStr} | ${avgPnLStr} | ${winRateStr} | ${avgWinStr} | ${avgLossStr} | ${pfStr}`);
        });
        
        console.log('\n📈 SUMMARY INSIGHTS:');
        
        // Find worst 5 hours
        const worst5 = hourlyArray.slice(0, 5);
        const best5 = hourlyArray.slice(-5).reverse();
        
        console.log('\n🔴 TOP 5 WORST HOURS:');
        worst5.forEach((stats, index) => {
            const hourStr = stats.hour.toString().padStart(2, '0') + ':00';
            console.log(`${index + 1}. ${hourStr} - Avg PnL: $${stats.avgPnL.toFixed(2)} (${stats.trades} trades, ${stats.winRate.toFixed(1)}% win rate)`);
        });
        
        console.log('\n🟢 TOP 5 BEST HOURS:');
        best5.forEach((stats, index) => {
            const hourStr = stats.hour.toString().padStart(2, '0') + ':00';
            console.log(`${index + 1}. ${hourStr} - Avg PnL: $${stats.avgPnL.toFixed(2)} (${stats.trades} trades, ${stats.winRate.toFixed(1)}% win rate)`);
        });
        
        // Calculate total losses from worst hours
        const totalWorstLosses = worst5.reduce((sum, stats) => sum + (stats.totalPnL < 0 ? Math.abs(stats.totalPnL) : 0), 0);
        const totalWorstTrades = worst5.reduce((sum, stats) => sum + stats.trades, 0);
        
        console.log('\n💡 ACTIONABLE INSIGHTS:');
        console.log(`📊 Worst 5 hours account for ${totalWorstTrades} trades with $${totalWorstLosses.toFixed(2)} in losses`);
        
        // Time zone analysis
        console.log('\n🌍 TIME ZONE ANALYSIS:');
        console.log('Note: Times shown in UTC. Convert to your local timezone:');
        console.log('- EST: UTC - 5 hours (UTC - 4 during DST)');
        console.log('- CST: UTC - 6 hours (UTC - 5 during DST)');
        console.log('- PST: UTC - 8 hours (UTC - 7 during DST)');
        
        // Market session analysis
        console.log('\n📅 MARKET SESSION ANALYSIS:');
        const preMarket = hourlyArray.filter(s => s.hour >= 9 && s.hour <= 13); // 4-8 AM EST
        const regularHours = hourlyArray.filter(s => s.hour >= 14 && s.hour <= 21); // 9 AM - 4 PM EST
        const afterHours = hourlyArray.filter(s => s.hour >= 22 || s.hour <= 8); // 5 PM - 3 AM EST
        
        const calcSessionAvg = (session) => {
            if (session.length === 0) return 0;
            return session.reduce((sum, s) => sum + s.avgPnL, 0) / session.length;
        };
        
        console.log(`🌅 Pre-Market (4-8 AM EST): Avg PnL $${calcSessionAvg(preMarket).toFixed(2)}`);
        console.log(`🏢 Regular Hours (9 AM-4 PM EST): Avg PnL $${calcSessionAvg(regularHours).toFixed(2)}`);
        console.log(`🌙 After Hours (5 PM-3 AM EST): Avg PnL $${calcSessionAvg(afterHours).toFixed(2)}`);
        
        // Save detailed results to CSV
        const csvOutput = 'C:/backtest-bot/output/hourly_performance_analysis.csv';
        const csvHeader = 'Hour,Hour_Display,Trades,Total_PnL,Avg_PnL,Win_Rate,Avg_Win,Avg_Loss,Profit_Factor,Performance_Rank\n';
        const csvData = hourlyArray.map((stats, index) => {
            const hourDisplay = stats.hour.toString().padStart(2, '0') + ':00';
            return `${stats.hour},${hourDisplay},${stats.trades},${stats.totalPnL.toFixed(2)},${stats.avgPnL.toFixed(2)},${stats.winRate.toFixed(2)},${stats.avgWin.toFixed(2)},${stats.avgLoss.toFixed(2)},${stats.profitFactor.toFixed(3)},${index + 1}`;
        }).join('\n');
        
        fs.writeFileSync(csvOutput, csvHeader + csvData);
        console.log(`\n💾 Detailed results saved to: ${csvOutput}`);
        
        console.log('\n🎯 RECOMMENDATIONS:');
        console.log('1. Consider avoiding trading during the worst 5 hours');
        console.log('2. Focus trading during the best performing hours');
        console.log('3. Analyze if worst hours coincide with specific market events');
        console.log('4. Test time-based filters to exclude poor performing hours');
    })
    .on('error', (error) => {
        console.error('❌ Error processing trade log:', error);
    });
