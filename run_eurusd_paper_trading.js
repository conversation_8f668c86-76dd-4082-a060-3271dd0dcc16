/**
 * Run EURUSD Paper Trading
 * 
 * This script runs paper trading for EURUSD forex pair using optimized parameters.
 */

const fs = require('fs');
const path = require('path');
const TradingBot = require('./trading_bot');

console.log("Starting EURUSD Paper Trading...");

// Load configuration
const config = require('./eurusd_paper_trading_config.js');

/**
 * Run paper trading for EURUSD
 */
async function runPaperTrading() {
    console.log(`\n===== RUNNING EURUSD PAPER TRADING =====`);
    console.log(`Using optimized parameters:`);
    console.log(`  SL Factor: ${config.slFactors}`);
    console.log(`  TP Factor: ${config.tpFactors}`);
    console.log(`  Trail Factor: ${config.trailFactors}`);
    console.log(`  Adaptive Mode: ${config.isAdaptiveRun ? 'Enabled' : 'Disabled'}`);
    console.log(`  Position Size: ${config.fixedContracts} mini lots`);
    
    // Create trading bot instance with configuration
    const tradingBot = new TradingBot(config);
    
    // Initialize trading bot
    console.log(`Initializing trading bot...`);
    await tradingBot.initialize();
    
    // Start trading
    console.log(`Starting paper trading...`);
    await tradingBot.start();
    
    // Log trading status
    console.log(`Trading bot is running. Press Ctrl+C to stop.`);
    
    // Handle process termination
    process.on('SIGINT', async () => {
        console.log(`\nStopping trading bot...`);
        await tradingBot.stop();
        console.log(`Trading bot stopped.`);
        process.exit(0);
    });
}

// Run paper trading
runPaperTrading().catch(err => {
    console.error('Error running paper trading:', err);
});
