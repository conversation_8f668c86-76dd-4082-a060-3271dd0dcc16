/**
 * Databento API Client for Node.js
 * Main entry point that exports all client classes
 */

const HistoricalClient = require('./historical');
const LiveClient = require('./live');
const ReferenceClient = require('./reference');

class DabentoClient {
  /**
   * Create a new Databento client
   * @param {string} apiKey - Your Databento API key
   * @param {string} gateway - Site of historical gateway to connect to (default: 'bo1')
   */
  constructor(apiKey, gateway = 'bo1') {
    if (!apiKey) {
      apiKey = process.env.DATABENTO_API_KEY;
    }
    
    if (!apiKey) {
      throw new Error('API key is required. Provide it as a parameter or set DATABENTO_API_KEY environment variable.');
    }
    
    this.apiKey = apiKey;
    this.gateway = gateway;
    this.historical = new HistoricalClient(apiKey, gateway);
    this.live = new LiveClient(apiKey);
    this.reference = new ReferenceClient(apiKey, gateway);
  }
}

module.exports = DabentoClient;
