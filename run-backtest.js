/**
 * Run Backtest
 * 
 * This script runs a backtest of the trading strategy on historical data.
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const Backtester = require('./backtest-framework');

// Parse command line arguments
const args = process.argv.slice(2);
const symbolArg = args.find(arg => arg.startsWith('--symbol='));
const startDateArg = args.find(arg => arg.startsWith('--start='));
const endDateArg = args.find(arg => arg.startsWith('--end='));
const positionSizeArg = args.find(arg => arg.startsWith('--size='));

// Default values
const defaultSymbols = ['MNQ', 'MES', 'MGC', 'M2K'];
const defaultStartDate = new Date('2025-04-30T00:00:00Z');
const defaultEndDate = new Date('2025-05-01T23:59:59Z');
const defaultPositionSize = 2;

// Parse symbols
let symbols = defaultSymbols;
if (symbolArg) {
    const symbolValue = symbolArg.split('=')[1];
    if (symbolValue === 'all') {
        symbols = defaultSymbols;
    } else {
        symbols = symbolValue.split(',');
    }
}

// Parse dates
let startDate = defaultStartDate;
if (startDateArg) {
    const startDateValue = startDateArg.split('=')[1];
    startDate = new Date(startDateValue);
}

let endDate = defaultEndDate;
if (endDateArg) {
    const endDateValue = endDateArg.split('=')[1];
    endDate = new Date(endDateValue);
}

// Parse position size
let positionSize = defaultPositionSize;
if (positionSizeArg) {
    const positionSizeValue = positionSizeArg.split('=')[1];
    positionSize = parseInt(positionSizeValue, 10);
}

// Print configuration
console.log('Backtest Configuration:');
console.log(`Symbols: ${symbols.join(', ')}`);
console.log(`Start Date: ${startDate.toISOString()}`);
console.log(`End Date: ${endDate.toISOString()}`);
console.log(`Position Size: ${positionSize} contracts`);

// Run backtest
async function runBacktest() {
    try {
        // Create backtester
        const backtester = new Backtester({
            startDate,
            endDate,
            symbols,
            positionSize
        });
        
        // Initialize backtester
        console.log('\nInitializing backtester...');
        const initialized = await backtester.initialize();
        
        if (!initialized) {
            console.error('Failed to initialize backtester');
            return;
        }
        
        // Run backtest
        console.log('\nRunning backtest...');
        const result = await backtester.run();
        
        if (!result) {
            console.error('Backtest failed');
            return;
        }
        
        // Save results
        const resultsDir = path.join('C:', 'backtest-bot', 'results');
        
        // Create results directory if it doesn't exist
        if (!fs.existsSync(resultsDir)) {
            fs.mkdirSync(resultsDir, { recursive: true });
        }
        
        // Create results filename
        const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
        const symbolsStr = symbols.join('-');
        const resultsFile = path.join(resultsDir, `backtest-${symbolsStr}-${timestamp}.json`);
        
        // Save results
        const results = {
            config: {
                symbols,
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                positionSize
            },
            stats: {
                totalTrades: backtester.totalTrades,
                totalWins: backtester.totalWins,
                totalLosses: backtester.totalLosses,
                winRate: backtester.totalTrades > 0 ? (backtester.totalWins / backtester.totalTrades) * 100 : 0,
                totalPnL: backtester.totalPnL,
                initialBalance: backtester.options.initialBalance,
                finalBalance: backtester.balance,
                finalEquity: backtester.equity,
                return: (backtester.balance / backtester.options.initialBalance - 1) * 100,
                maxDrawdown: backtester.maxDrawdown
            },
            symbolStats: {},
            dailyStats: backtester.dailyStats,
            trades: backtester.trades
        };
        
        // Add symbol-specific statistics
        for (const symbol of symbols) {
            const trades = backtester.trades[symbol];
            const wins = trades.filter(t => t.pnl > 0).length;
            const losses = trades.filter(t => t.pnl < 0).length;
            const pnl = trades.reduce((sum, t) => sum + t.pnl, 0);
            
            results.symbolStats[symbol] = {
                totalTrades: trades.length,
                wins,
                losses,
                winRate: trades.length > 0 ? (wins / trades.length) * 100 : 0,
                pnl
            };
        }
        
        // Save results to file
        fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
        
        console.log(`\nResults saved to ${resultsFile}`);
        
        // Print summary
        console.log('\nBacktest Summary:');
        console.log(`Total Trades: ${results.stats.totalTrades}`);
        console.log(`Win Rate: ${results.stats.winRate.toFixed(2)}%`);
        console.log(`Total P&L: $${results.stats.totalPnL.toFixed(2)}`);
        console.log(`Return: ${results.stats.return.toFixed(2)}%`);
        console.log(`Max Drawdown: ${results.stats.maxDrawdown.toFixed(2)}%`);
        
        // Print symbol-specific statistics
        for (const symbol of symbols) {
            const stats = results.symbolStats[symbol];
            console.log(`\n${symbol} Statistics:`);
            console.log(`Total Trades: ${stats.totalTrades}`);
            console.log(`Win Rate: ${stats.winRate.toFixed(2)}%`);
            console.log(`P&L: $${stats.pnl.toFixed(2)}`);
        }
    } catch (error) {
        console.error('Error running backtest:', error);
    }
}

// Run the backtest
runBacktest().catch(console.error);
