/**
 * Grid Test Configuration
 * Configuration for grid testing multiple markets
 */

module.exports = {
    // Runtime options
    options: {
        // Set to true to run a quick test with limited data
        quickTest: true,

        // Date range options
        dateRange: {
            // Use custom date range instead of days from now
            useCustomRange: true,
            // Start date (YYYY-MM-DD)
            startDate: '2024-01-01',
            // End date (YYYY-MM-DD) - leave empty for current date
            endDate: '2024-03-31'  // Just Q1 2024 for a quick test
        },

        // Symbols to test (comment out to disable)
        symbols: [
            'MNQ',
            'MES',
            'MGC',
            'EURUSD'
        ],

        // Output directory for reports
        outputDir: './output/grid_test'
    },

    // Grid parameters to test
    gridParams: {
        // SL factors to test
        slFactors: [3.0, 3.5, 4.0, 4.5, 5.0],

        // TP factors to test
        tpFactors: [2.0, 2.5, 3.0, 3.5, 4.0],

        // Trail factors to test
        trailFactors: [0.05, 0.08, 0.11, 0.15, 0.2],

        // Fixed TP points to test
        fixedTpPoints: [0, 20, 40, 60]
    },

    // Focused grid parameters (fewer combinations for faster testing)
    focusedGridParams: {
        // SL factors to test
        slFactors: [4.0, 4.5, 5.0],

        // TP factors to test
        tpFactors: [2.5, 3.0, 3.5],

        // Trail factors to test
        trailFactors: [0.08, 0.11, 0.15],

        // Fixed TP points to test
        fixedTpPoints: [0, 40]
    },

    // Configuration for each instrument
    configs: {
        EURUSD: {
            symbol: 'EURUSD',
            pointValue: 1.0,
            commission: 0.0,
            slippage: 0.0002,
            fixedContracts: 10,
            rsiPeriod: 14,
            rsiMaPeriod: 8,
            wma50Period: 50,
            atrPeriod: 14,
            minAtrEntry: 0.0005,
            minRsiMaSeparation: 1.0,
            useWmaFilter: true,
            pricePrecision: 5,
            initialBalance: 10000
        },
        MNQ: {
            symbol: 'MNQ',
            pointValue: 2.0,
            commission: 0.40,
            slippage: 0.0,
            fixedContracts: 10,
            rsiPeriod: 14,
            rsiMaPeriod: 8,
            wma50Period: 50,
            atrPeriod: 14,
            minAtrEntry: 0.5,
            minRsiMaSeparation: 1.0,
            useWmaFilter: true,
            pricePrecision: 2,
            initialBalance: 10000
        },
        MES: {
            symbol: 'MES',
            pointValue: 5.0,
            commission: 0.40,
            slippage: 0.0,
            fixedContracts: 10,
            rsiPeriod: 14,
            rsiMaPeriod: 8,
            wma50Period: 50,
            atrPeriod: 14,
            minAtrEntry: 0.5,
            minRsiMaSeparation: 1.0,
            useWmaFilter: true,
            pricePrecision: 2,
            initialBalance: 10000
        },
        MGC: {
            symbol: 'MGC',
            pointValue: 10.0,
            commission: 0.40,
            slippage: 0.0,
            fixedContracts: 10,
            rsiPeriod: 14,
            rsiMaPeriod: 8,
            wma50Period: 50,
            atrPeriod: 14,
            minAtrEntry: 0.5,
            minRsiMaSeparation: 1.0,
            useWmaFilter: true,
            pricePrecision: 2,
            initialBalance: 10000
        },
        M2K: {
            symbol: 'M2K',
            pointValue: 5.0,
            commission: 0.40,
            slippage: 0.0,
            fixedContracts: 10,
            rsiPeriod: 14,
            rsiMaPeriod: 8,
            wma50Period: 50,
            atrPeriod: 14,
            minAtrEntry: 0.5,
            minRsiMaSeparation: 1.0,
            useWmaFilter: true,
            pricePrecision: 1,
            initialBalance: 10000,
            slFactors: 5.0,
            tpFactors: 4.0,
            trailFactors: 0.03,
            fixedTpPoints: 0
        }
    },

    // File paths for historical data
    dataPaths: {
        EURUSD: 'C:\\backtest-bot\\input\\EURUSD_2020_2025.csv',
        MNQ: 'C:\\backtest-bot\\input\\MNQ_2020_2025.csv',
        MES: 'C:\\backtest-bot\\input\\MES_2020-2025.csv',
        MGC: 'C:\\backtest-bot\\input\\MGC2020_2025.csv',
        M2K: 'C:\\backtest-bot\\input\\MiniRussell2000_2020_2025.csv'
    },

    // ATR Thresholds for adaptive mode
    atrThresholds: {
        EURUSD: { low_medium: 0.0010, medium_high: 0.0020 },
        MNQ: { low_medium: 4.7601, medium_high: 7.2605 },
        MES: { low_medium: 3.0, medium_high: 5.0 },
        MGC: { low_medium: 1.5, medium_high: 3.0 },
        M2K: { low_medium: 1.5, medium_high: 3.0 }
    },

    // Adaptive parameters for each volatility regime
    adaptiveParams: {
        EURUSD: {
            Low: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 },
            Medium: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 },
            High: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 }
        },
        MNQ: {
            Low: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
            Medium: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
            High: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 }
        },
        MES: {
            Low: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
            Medium: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
            High: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 }
        },
        MGC: {
            Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
            Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
            High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
        },
        M2K: {
            Low: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 },
            Medium: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 },
            High: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 }
        }
    }
};
