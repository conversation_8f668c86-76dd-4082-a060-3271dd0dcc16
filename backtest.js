// ========================
// backtest.js
// V10.40: Advanced ML Filter, Time Analysis, and Dynamic Position Sizing
// ========================

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
const config = require('./config');
const spreadUtils = require('./spread_volatility_utils');
const enhancedPM = require('./enhanced_position_management');

// Import and initialize time analysis
const AdvancedTimeAnalysis = config.useTimeAnalysis ? require('./advanced_time_analysis') : null;
const timeAnalyzer = config.useTimeAnalysis ? new AdvancedTimeAnalysis(config) : null;

// Initialize ML filter and position sizer (set to null if not used)
const mlFilter = null; // ML filter not implemented yet
const positionSizer = null; // Position sizer not implemented yet

// --- Pattern Detection & Entry Filters (moved to top for availability) ---
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : 'red';
}

function detect3(c1, c2, c3) {
    // Basic validation
    if (!c1 || !c2 || !c3) return null;

    // Get candle colors
    const col1 = candlestickColor(c1),
          col2 = candlestickColor(c2),
          col3 = candlestickColor(c3);

    // Validate candle colors
    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: green-red-green
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }

    // Bearish pattern: red-green-red
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }

    return null;
}

function detect4(c0, c1, c2, c3) {
    // Basic validation
    if (!c0 || !c1 || !c2 || !c3) return null;

    // Get candle colors
    const col0 = candlestickColor(c0),
          col1 = candlestickColor(c1),
          col2 = candlestickColor(c2),
          col3 = candlestickColor(c3);

    // Validate candle colors
    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: first candle green, followed by 2 red candles, with 4th candle green
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }

    // Bearish pattern: first candle red, followed by 2 green candles, with 4th candle red
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }

    return null;
}



function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod) {
    // Basic validation - exclude RSI if disabled
    const rsiEnabled = config.rsiPeriod > 0 && config.rsiMaPeriod > 0;
    if (isNaN(c3.wma50)) {
        return false;
    }
    if (rsiEnabled && (isNaN(c3.rsi) || isNaN(c3.rsiMa))) {
        return false;
    }

    // Debug: Track filtering reasons
    let debugReason = null;

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // USER'S EXACT EDGE: Price above/below BOTH MAs for trend direction
    if (config.use200SmaFilter) {
        // For LONG trades: price must be above BOTH 50 WMA AND 200 WMA
        if (dir === 'bullish') {
            if (c3.close <= c3.wma50 || c3.close <= c3.wma200) {
                return false;
            }
        }
        // For SHORT trades: price must be below BOTH 50 WMA AND 200 WMA
        if (dir === 'bearish') {
            if (c3.close >= c3.wma50 || c3.close >= c3.wma200) {
                return false;
            }
        }
    }

    // RSI filter for all patterns (only if RSI is enabled)
    if (rsiEnabled) {
        // UPDATED RSI-MA filter: Allow being "on" the RSI-MA (equal to)
        // For LONGS: RSI must be >= RSI-MA (on or above)
        // For SHORTS: RSI must be <= RSI-MA (on or below)
        if ((dir === 'bullish' && c3.rsi < c3.rsiMa) || (dir === 'bearish' && c3.rsi > c3.rsiMa)) {
            return false;
        }

        // NEW: RSI level conditions
        // Longs only if RSI > 60, Shorts only if RSI < 40
        if (dir === 'bullish' && c3.rsi <= 60) {
            debugReason = `RSI_LEVEL: ${dir} RSI=${c3.rsi?.toFixed(1)} <= 60`;
            return false;
        }
        if (dir === 'bearish' && c3.rsi >= 40) {
            debugReason = `RSI_LEVEL: ${dir} RSI=${c3.rsi?.toFixed(1)} >= 40`;
            return false;
        }
    }

    // ATR filter removed - not needed for entry criteria

    // RSI-MA separation filter (only if RSI is enabled)
    if (rsiEnabled) {
        const minRsiMaSeparation = config.minRsiMaSeparation || 0;
        if (Math.abs(c3.rsi - c3.rsiMa) < minRsiMaSeparation) {
            return false;
        }
    }

    // ML-based entry filter
    if (config.useMLEntryFilter && mlFilter) {
        // Get the timestamp
        const timestamp = new Date(c3.timestamp * 1000);

        // Get the hour of day (UTC)
        const hourOfDay = timestamp.getUTCHours();

        // Get the day of week (0 = Sunday, 6 = Saturday)
        const dayOfWeek = timestamp.getUTCDay();

        // Determine ATR regime
        const atrRegime = c3.atr < config.atrThresholds?.low_medium ? 'Low' :
                         (c3.atr > config.atrThresholds?.medium_high ? 'High' : 'Medium');

        // Get recent candles for pattern analysis if available
        let recentCandles = [];
        if (candlesForPeriod && Array.isArray(candlesForPeriod) && currentIndex !== undefined) {
            const lookbackStart = Math.max(0, currentIndex - 5);
            recentCandles = candlesForPeriod.slice(lookbackStart, currentIndex + 1);
        }

        // Create feature object for ML filter
        const features = {
            direction: dir,
            atrRegime: atrRegime,
            hourOfDay: hourOfDay,
            dayOfWeek: dayOfWeek,
            rsi: c3.rsi,
            rsiMa: c3.rsiMa,
            close: c3.close,
            wma50: c3.wma50,
            patternType: patternType,
            recentCandles: recentCandles,
            timestamp: timestamp
        };

        // Get time score if time analysis is enabled
        let timeScore = 0.5; // Default neutral score
        if (config.useTimeAnalysis && timeAnalyzer) {
            const timeScoreInfo = timeAnalyzer.getTimeScore(timestamp);
            timeScore = timeScoreInfo.combinedScore;

            // Add time score to features
            features.timeScore = timeScore;
        }

        // Use ML filter to decide whether to take the entry
        return mlFilter.shouldEnter(features);
    }

    return true;
}

// --- VERY EARLY LOG ---
console.log("Script started. Attempting to require config...");
// --- END EARLY LOG ---


// --- *** Define Output Directory *** ---
const outputDir = 'C:/backtest-bot/output/MNQ_LastYear_TradeLog'; // << SET FOR TRADE LOG GENERATION
// --- *********************************** ---

// --- 1) Setup & Initialization ---
if (!fs.existsSync(outputDir)) { try { fs.mkdirSync(outputDir, { recursive: true }); console.log(`Created output directory: ${outputDir}`); } catch (err) { console.error(`Error creating output directory '${outputDir}':`, err); process.exit(1); } } else { console.log(`Output directory exists: ${outputDir}`); }
const allCandles = []; let allRunResults = [];
const atrThresholds = { low_medium: 4.7601, medium_high: 7.2605 }; // Might need adjustment for MYM ATR values later
const adaptiveParams = {
    Low: { slFactor: 3.4, tpFactor: 2.0, trailFactor: 0.11 },     // Low volatility - optimized parameters
    Medium: { slFactor: 3.4, tpFactor: 2.0, trailFactor: 0.11 },  // Medium volatility - same parameters
    High: { slFactor: 3.4, tpFactor: 2.0, trailFactor: 0.11 }     // High volatility - same parameters
};

// --- Data Loading & Parsing ---
if (!config) { console.error("FATAL: Config object is undefined after require."); process.exit(1);}
if (!config.inputFile || typeof config.inputFile !== 'string') { console.error("Error: 'inputFile' not defined or invalid in config.js"); process.exit(1); }
if (!fs.existsSync(config.inputFile)) { console.error(`Error: Input file not found at path: ${config.inputFile}`); process.exit(1); }

console.log(`Attempting to read: ${config.inputFile}`);

// First check the file format by reading the header
const headerLine = fs.readFileSync(config.inputFile, 'utf8').split('\n')[0];
console.log(`CSV Header: ${headerLine}`);

// Determine if this is a Databento format file
const isDatabentoFormat = headerLine.includes('ts_event');
const separator = headerLine.includes(';') ? ';' : ',';

console.log(`Detected format: ${isDatabentoFormat ? 'Databento' : 'Standard'}, separator: '${separator}'`);

fs.createReadStream(config.inputFile)
    .pipe(csv({
        separator: separator,
        mapHeaders: ({ header }) => header.trim()
    }))
    .on('data', d => {
        let open, high, low, close, timestampSeconds;

        if (isDatabentoFormat) {
            // Databento format
            open = parseFloat(d['open']);
            high = parseFloat(d['high']);
            low = parseFloat(d['low']);
            close = parseFloat(d['close']);

            // Parse timestamp from ts_event field
            const timeString = d['ts_event'];
            if (timeString) {
                try {
                    const parsedDate = new Date(timeString);
                    if (!isNaN(parsedDate.getTime())) {
                        timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                    }
                } catch (e) {
                    // Skip invalid timestamps
                }
            }

            // Filter by symbol if needed (include all MNQ contracts but exclude spreads)
            const symbol = d['symbol'];
            if (symbol && (!symbol.startsWith('MNQ') || symbol.includes('-'))) {
                return; // Skip non-MNQ symbols and spread data (symbols with hyphens)
            }

            // Additional safety check: reject any prices that look like spread data
            if (open < 1000 || high < 1000 || low < 1000 || close < 1000) {
                console.log(`🚨 REJECTED SPREAD-LIKE DATA: ${symbol} - O=${open}, H=${high}, L=${low}, C=${close}`);
                return; // Skip data that looks like spread prices
            }
        } else {
            // Standard format
            open = +d['Open'];
            high = +d['High'];
            low = +d['Low'];
            close = +d['Close'];
            const timeString = d['Time'] || d['Date'] || d['Time left'];

            if (timeString) {
                let parsedDate;
                try { parsedDate = new Date(timeString); } catch (e) {}
                if (parsedDate && !isNaN(parsedDate.getTime())) {
                    timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                } else if (timeString && !isNaN(Number(timeString))) {
                    const tsNum = Number(timeString);
                    timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                }
            }
        }

        if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            // Skip invalid data
            return;
        }

        allCandles.push({ timestamp: timestampSeconds, open, high, low, close });
    })
    .on('end', () => {
        console.log("CSV parsing finished ('end' event received).");
        if (allCandles.length === 0) {
            console.error(`Error: No valid candle data parsed from ${config.inputFile}. Check headers and format.`);
            process.exit(1);
        }

        console.log(`Parsed ${allCandles.length} initial candles...`);
        allCandles.sort((a, b) => a.timestamp - b.timestamp);
        console.log(`Data time range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);
        console.log("Calling runWeeklyBacktests...");
        runWeeklyBacktests();
        console.log(">>> Finished calling runWeeklyBacktests from 'end' handler.");
    })
    .on('error', (err) => {
        console.error(`Error reading CSV file ${config.inputFile}:`, err);
        process.exit(1);
    });

// --- Indicator Helper Functions ---
function SMA(arr, period, index) { if (!arr || index < period - 1 || arr.length <= index) return NaN; let sum = 0; let validCount = 0; for (let j = index - period + 1; j <= index; j++) { if (typeof arr[j] === 'number' && !isNaN(arr[j])) { sum += arr[j]; validCount++; } else { return NaN; } } return validCount === period ? sum / period : NaN; }
function RSI(arr, period, index) {
    if (!arr || index < period || arr.length <= index) return NaN;

    // Calculate price changes for the period
    let initialGains = 0, initialLosses = 0;
    let validDeltas = 0;

    // Calculate gains and losses for the period
    for (let j = index - period + 1; j <= index; j++) {
        if (j > 0 && typeof arr[j - 1] === 'number' && !isNaN(arr[j - 1]) && typeof arr[j] === 'number' && !isNaN(arr[j])) {
            const delta = arr[j] - arr[j - 1];
            if (delta > 0) {
                initialGains += delta;
            } else {
                initialLosses -= delta;
            }
            validDeltas++;
        } else {
            return NaN;
        }
    }

    if (validDeltas < period) return NaN;
    if (initialLosses === 0) return 100;
    if (initialGains === 0) return 0;

    // Calculate simple averages for the period (standard RSI calculation)
    const avgGain = initialGains / period;
    const avgLoss = initialLosses / period;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}
function WMA(arr, period, index) { if (!arr || index < period - 1 || arr.length <= index) return NaN; let weightedSum = 0, weightSum = 0, validCount = 0; for (let j = 0; j < period; j++) { const idx = index - j, weight = period - j; if (idx < 0) return NaN; if (typeof arr[idx] === 'number' && !isNaN(arr[idx])) { weightedSum += arr[idx] * weight; weightSum += weight; validCount++; } else { return NaN; } } return validCount === period ? weightedSum / weightSum : NaN; }

// --- Other Helper Functions ---
function candlestickColor(candle) { if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) return 'invalid'; return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji'; }
function getWeekIdentifier(timestamp) { const date = new Date(timestamp * 1000); if (isNaN(date.getTime())) { return "InvalidDate"; } const year = date.getUTCFullYear(); const startOfYear = new Date(Date.UTC(year, 0, 1)); if (isNaN(startOfYear.getTime())) { return "InvalidYearStartDate"; } const timeDiff = date.getTime() - startOfYear.getTime(); if (isNaN(timeDiff)) { return "InvalidTimeDiff"; } const daysPassed = Math.floor(timeDiff / (1000 * 60 * 60 * 24)); const firstDayOfYear = startOfYear.getUTCDay(); const weekNo = Math.floor((daysPassed + firstDayOfYear) / 7) + 1; if (isNaN(weekNo) || weekNo < 1 || weekNo > 53) { return "InvalidWeekNo"; } return `${year}-W${weekNo.toString().padStart(2, '0')}`; }
function getDayIdentifier(timestamp) { const date = new Date(timestamp * 1000); return date.toISOString().slice(0, 10); }

// --- Metrics ---
function calculateSharpe(weeklyReturns, riskFreeRate = 0) { const validReturns = weeklyReturns.filter(r => typeof r === 'number' && isFinite(r)); if (!validReturns || validReturns.length < 2) return 'N/A'; const numWeeks = validReturns.length; const meanWeeklyReturn = validReturns.reduce((a, b) => a + b, 0) / numWeeks; if (isNaN(meanWeeklyReturn)) return 'N/A'; const variance = validReturns.map(r => Math.pow(r - meanWeeklyReturn, 2)).reduce((a, b) => a + b, 0) / (numWeeks - 1); const stdDevWeeklyReturn = Math.sqrt(variance); if (isNaN(stdDevWeeklyReturn) || stdDevWeeklyReturn === 0) return 'N/A'; const annualizedMeanReturn = meanWeeklyReturn * 52, annualizedStdDev = stdDevWeeklyReturn * Math.sqrt(52); const sharpeRatio = (annualizedMeanReturn - (riskFreeRate || 0)) / annualizedStdDev; return isNaN(sharpeRatio) ? 'N/A' : sharpeRatio.toFixed(2); }
function calculateDailyStats(dailyPnLMap) { if (!dailyPnLMap || dailyPnLMap.size === 0) { return { avgDailyPnL: '0.00', stdDevDailyPnL: '0.00', winDayRate: 'N/A' }; } const values = Array.from(dailyPnLMap.values()), n = values.length; const totalPnl = values.reduce((s, p) => s + p, 0), avg = totalPnl / n; const winDays = values.filter(p => p > 0).length, winRate = `${(winDays / n * 100).toFixed(1)}%`; const variance = values.map(p => Math.pow(p - avg, 2)).reduce((s, sq) => s + sq, 0) / n; return { avgDailyPnL: avg.toFixed(2), stdDevDailyPnL: Math.sqrt(variance).toFixed(2), winDayRate: winRate }; }
function calculateTradeMetrics(tradeLog) { let winAmt=0, lossAmt=0, winCt=0, lossCt=0, duration=0, tradeCt=0, maxCont=0; if (!tradeLog || tradeLog.length === 0) { return { avgWinDollar: '0.00', avgLossDollar: '0.00', avgDurationBars: 'N/A', maxContractsHit: 0 }; } for (const trade of tradeLog) { const pnl=parseFloat(trade.PnL_Net), dur=parseInt(trade.Duration), cont=parseInt(trade.Contracts)||1; if(!isNaN(pnl)){ if(pnl>0){winAmt+=pnl;winCt++;} else if(pnl<0){lossAmt+=Math.abs(pnl);lossCt++;} } if(!isNaN(dur)&&dur>0){duration+=dur;} tradeCt++; maxCont=Math.max(maxCont,cont); } return { avgWinDollar:winCt>0?(winAmt/winCt).toFixed(2):'0.00', avgLossDollar:lossCt>0?(lossAmt/lossCt).toFixed(2):'0.00', avgDurationBars:tradeCt>0&&duration>0?(duration/tradeCt).toFixed(1):'N/A', maxContractsHit:maxCont }; }

// --- Results Aggregation ---
function aggregateResults(weeklyResults, runLabel) { if (!weeklyResults || weeklyResults.length === 0) return []; const aggData = { ParamsKey: runLabel, SL: weeklyResults[0]?.SL, TP: weeklyResults[0]?.TP, Trail: weeklyResults[0]?.Trail, MinTP_Tested: weeklyResults[0]?.MinTP, RiskPct_Tested: weeklyResults[0]?.RiskPct, FixedContracts_Tested: weeklyResults[0]?.FixedContracts, Latency_Tested: weeklyResults[0]?.LatencyBars, Commission_Tested: weeklyResults[0]?.Commission, Slippage_Tested: weeklyResults[0]?.Slippage, TotalPnL: 0, TotalTrades: 0, TotalWins: 0, TotalLosses: 0, TotalGrossProfit: 0, TotalGrossLoss: 0, MaxWeeklyDD: 0, WeeksRun: 0, ProfitableWeeks: 0, SumAvgATR: 0, AtrValidWeeks: 0, TotalExitCounts: { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, hold_expired: 0, end_of_period: 0 } }; for (const result of weeklyResults) { if (!result) continue; aggData.TotalPnL += result.TotalPnL||0; aggData.TotalTrades += result.Trades||0; aggData.TotalWins += result.Wins||0; aggData.TotalLosses += result.Losses||0; aggData.TotalGrossProfit += (result.AvgWin||0)*(result.Wins||0); aggData.TotalGrossLoss += (result.AvgLoss||0)*(result.Losses||0); aggData.MaxWeeklyDD = Math.max(aggData.MaxWeeklyDD, result.MaxDDWeekly || 0); aggData.WeeksRun++; if (typeof result.AvgATR === 'number' && !isNaN(result.AvgATR)) { aggData.SumAvgATR += result.AvgATR; aggData.AtrValidWeeks++; } if (result.TotalPnL > 0) { aggData.ProfitableWeeks++; } if (result.ExitCounts) { for (const r in result.ExitCounts) { aggData.TotalExitCounts[r] = (aggData.TotalExitCounts[r] || 0) + result.ExitCounts[r]; } } } const totalTradesCalc = aggData.TotalWins + aggData.TotalLosses; const winRate = totalTradesCalc > 0 ? (aggData.TotalWins / totalTradesCalc * 100) : 0; const pf = aggData.TotalGrossLoss > 0 ? (aggData.TotalGrossProfit / aggData.TotalGrossLoss) : (aggData.TotalGrossProfit > 0 ? 99999 : 0); const avgPnlWk = aggData.WeeksRun > 0 ? aggData.TotalPnL / aggData.WeeksRun : 0; const avgAtr = aggData.AtrValidWeeks > 0 ? aggData.SumAvgATR / aggData.AtrValidWeeks : NaN; let exitSum = "N/A"; if (totalTradesCalc > 0) { exitSum = Object.entries(aggData.TotalExitCounts).filter(([_, ct]) => ct > 0).map(([r, ct]) => `${r}: ${(ct / totalTradesCalc * 100).toFixed(1)}%`).join(', '); } return [{ Params: aggData.ParamsKey, TotalPnL: aggData.TotalPnL, AvgPnL_Week: avgPnlWk, WinRate: `${winRate.toFixed(2)}%`, ProfitFactor: pf === 99999 ? 'Infinity' : pf.toFixed(2), Trades: aggData.TotalTrades, Weeks: aggData.WeeksRun, WinWeeks: `${aggData.ProfitableWeeks}/${aggData.WeeksRun} (${aggData.WeeksRun > 0 ? (aggData.ProfitableWeeks/aggData.WeeksRun*100).toFixed(1) : '0.0'}%)`, AvgATR: avgAtr, MaxWeeklyDD: `-${aggData.MaxWeeklyDD.toFixed(config.pricePrecision)}`, Exits: exitSum, SL_Run: aggData.SL, TP_Run: aggData.TP, Trail_Run: aggData.Trail, MinTP_Run: aggData.MinTP_Tested, RiskPct_Run: aggData.RiskPct_Tested, FixedContracts_Tested: aggData.FixedContracts_Tested, Latency_Tested: aggData.Latency_Tested, Commission_Run: aggData.Commission_Tested, Slippage_Run: aggData.Slippage_Tested }]; }

// --- Results Display ---
function processAndDisplayAggregatedResults(resultsWithOverallMetrics) { if (!resultsWithOverallMetrics || resultsWithOverallMetrics.length === 0) { console.log("No aggregated results to display."); return; } console.log("\n--- AGGREGATED RESULTS SUMMARY ---"); const columns = [ "Params", "TotalPnL", "AvgPnL_Week", "WinRate", "ProfitFactor", "Trades", "AvgWinDollar", "AvgLossDollar", "MaxDD_Overall", "Sharpe_Ann", "AvgTradeDur_Bars", "MaxContractsHit", "WinDayRate", "AvgDailyPnL", "StdDevDailyPnL", "Weeks", "WinWeeks", "AvgATR", "Exits", "FixedContracts_Run", "LatencyTested", "Commission_Run", "Slippage_Run" ]; const displayData = resultsWithOverallMetrics.map(row => { const newRow = {}; columns.forEach(col => { let value = row[col]; if (typeof value === 'number') { if (['TotalPnL', 'AvgPnL_Week', 'AvgWinDollar', 'AvgLossDollar', 'AvgDailyPnL', 'StdDevDailyPnL'].includes(col)) { value = value.toFixed(2); } else if (col === 'ProfitFactor' && value !== Infinity && value !== 99999) { value = value.toFixed(2); } else if (col === 'AvgATR') { value = isNaN(value) ? 'N/A' : value.toFixed(4); } else if (col === 'Sharpe_Ann') { value = (isNaN(value) || !isFinite(value)) ? 'N/A' : value.toFixed(2); } } if (col === 'MaxDD_Overall' && typeof value === 'string' && value !== 'N/A' && !value.startsWith('-')) { const num = parseFloat(value); value = isNaN(num) ? 'N/A' : `-${num.toFixed(2)}`; } if (col === 'ProfitFactor' && value === 99999) { value = 'Infinity'; } if (col === 'FixedContracts_Run') value = row['FixedContracts_Tested']; if (col === 'LatencyTested') value = row['Latency_Tested']; newRow[col] = (value !== undefined && value !== null) ? value : 'N/A'; }); return newRow; }); if (displayData.length > 1) { displayData.sort((a, b) => { try { const pnlA=Number(String(a.TotalPnL).replace(/[^0-9.-]+/g,""))||-Infinity; const pnlB=Number(String(b.TotalPnL).replace(/[^0-9.-]+/g,""))||-Infinity; return pnlB - pnlA; } catch (e) { return 0; } }); } console.table(displayData, columns); }

// --- Save Trade Log ---
function saveTradeLog(tradeLog, filename) { if (!tradeLog || tradeLog.length === 0) { console.log(`Skipping save for ${filename}: No trades.`); return; } try { const headers = Object.keys(tradeLog[0]); if (!headers || headers.length === 0) { console.warn(`Cannot save ${filename}: No headers.`); return; } const headerString = headers.join(';'); const rows = tradeLog.map(trade => headers.map(h => String(trade[h] ?? '').replace(/;/g, ',').replace(/\n/g, ' ')).join(';')); fs.writeFileSync(path.join(outputDir, filename), [headerString, ...rows].join('\n')); console.log(`Trade log saved to: ${path.join(outputDir, filename)}`); } catch (error) { console.error(`\nError saving trade log ${filename}:`, error); } }

// --- 2) Global Indicator Calculation ---
function computeGlobalIndicators(candles) { console.log("Computing global indicators..."); if (!candles || !Array.isArray(candles) || candles.length === 0) { throw new Error("computeGlobalIndicators invalid candle data."); } if (!candles[0]?.close) { throw new Error("computeGlobalIndicators invalid candle structure."); }
// FIXED: Use typical price (HLC/3) for both moving averages instead of close only
const typicalPrices = candles.map(c => c && !isNaN(c.high) && !isNaN(c.low) && !isNaN(c.close) ? (c.high + c.low + c.close) / 3 : NaN);
// FIXED: 200 should be WMA (Weighted Moving Average), not SMA
const wma200Arr = config.wma200Period > 0 ? typicalPrices.map((_, i) => WMA(typicalPrices, config.wma200Period, i)) : new Array(candles.length).fill(NaN);
// FIXED: 50 WMA should also use typical price, not close
const wma50Arr = config.wma50Period > 0 ? typicalPrices.map((_, i) => WMA(typicalPrices, config.wma50Period, i)) : new Array(candles.length).fill(NaN);
const closes = candles.map(c => c?.close); const rsiArr = config.rsiPeriod > 0 ? closes.map((_, i) => RSI(closes, config.rsiPeriod, i)) : new Array(candles.length).fill(NaN); const rsiMaArr = config.rsiMaPeriod > 0 && config.rsiPeriod > 0 ? rsiArr.map((_, i) => SMA(rsiArr, config.rsiMaPeriod, i)) : new Array(candles.length).fill(NaN); const trs = []; for (let i = 0; i < candles.length; i++) { const cur = candles[i]; if (!cur) { trs.push(NaN); continue; } if (i === 0) { if (!isNaN(cur.high) && !isNaN(cur.low)) { trs.push(cur.high - cur.low); } else { trs.push(NaN); } continue; } const prev = candles[i - 1]; if (!prev || isNaN(prev.close) || isNaN(cur.high) || isNaN(cur.low)) { trs.push(NaN); continue; } trs.push(Math.max(cur.high - cur.low, Math.abs(cur.high - prev.close), Math.abs(cur.low - prev.close))); } const atrs = config.atrPeriod > 0 ? trs.map((_, i) => SMA(trs, config.atrPeriod, i)) : new Array(trs.length).fill(NaN); candles.forEach((c, i) => { if (c) { c.wma200 = wma200Arr[i]; c.wma50 = wma50Arr[i]; c.rsi = rsiArr[i]; c.rsiMa = rsiMaArr[i]; c.atr = (i >= config.atrPeriod - 1 && i < atrs.length) ? atrs[i] : NaN; } }); console.log("Global indicators computed."); }

// --- 3) Weekly Data Segmentation ---
function segmentDataIntoWeeks(candlesWithIndicators) { console.log("Segmenting data into weeks (using simple Week definition)..."); if (!candlesWithIndicators || !Array.isArray(candlesWithIndicators)) { console.error("Error: segmentDataIntoWeeks received invalid input."); return {}; } const weeklyData = {}; let invalidWeekIdCount = 0; let validWeekIdCount = 0; for (const candle of candlesWithIndicators) { if (!candle || isNaN(candle.timestamp)) { continue; } const weekId = getWeekIdentifier(candle.timestamp); if (!weekId || typeof weekId !== 'string' || weekId.includes("Invalid") || weekId.includes("NaN")) { invalidWeekIdCount++; continue; } else { validWeekIdCount++; if (!weeklyData[weekId]) { weeklyData[weekId] = []; } weeklyData[weekId].push(candle); } } if (invalidWeekIdCount > 0) { console.warn(`Warning: Skipped ${invalidWeekIdCount} candles due to invalid week ID generation.`); } if (validWeekIdCount === 0 && candlesWithIndicators.length > 0) { console.error("Error: No valid week IDs generated for any candles!"); } console.log(`Segmented data into ${Object.keys(weeklyData).length} weeks.`); const sortedWeeks = Object.keys(weeklyData).sort(); const sortedWeeklyData = {}; for(const weekId of sortedWeeks){ if (weeklyData[weekId]?.length > 0) sortedWeeklyData[weekId] = weeklyData[weekId]; } return sortedWeeklyData; }

// --- 4) Main Workflow: Run Backtests Weekly + Overall Metrics ---
function runWeeklyBacktests() {
    console.log(">>> runWeeklyBacktests function entered.");
    try {
        console.log("Starting runWeeklyBacktests try block...");
        computeGlobalIndicators(allCandles);
        console.log("Finished computeGlobalIndicators.");
        const weeklySegmentedData = segmentDataIntoWeeks(allCandles);
        console.log("Finished segmentDataIntoWeeks.");

        if (typeof weeklySegmentedData !== 'object' || weeklySegmentedData === null) { console.error("Error: segmentDataIntoWeeks did not return a valid object."); return; }
        const totalWeeksAllData = Object.keys(weeklySegmentedData).length;
        console.log(`Total weeks identified: ${totalWeeksAllData}`);
        if (totalWeeksAllData === 0) { console.error("Error: No weeks identified for backtesting. Exiting."); return; }

        allRunResults = [];
        let runFullTradeLog = [];

        // Determine Run Type and Parameter Sets
        console.log("Determining run type and parameters...");
        const isAdaptive = config.isAdaptiveRun === true;
        const isCostGridTest = config.costGrid?.length > 0;
        const isRiskGridTest = config.riskPercentGrid?.length > 0;
        const isFixedContractsGridTest = config.fixedContractsGrid?.length > 0;
        const isParameterGridTest = !isAdaptive && ( (Array.isArray(config.slFactors) && config.slFactors.length > 0) || (Array.isArray(config.tpFactors) && config.tpFactors.length > 0) || (Array.isArray(config.trailFactors) && config.trailFactors.length > 0) );
        let parameterSets = [];
         console.log(` - Flags: isAdaptive=${isAdaptive}, isCostGrid=${isCostGridTest}, isRiskGrid=${isRiskGridTest}, isFixedContractsGrid=${isFixedContractsGridTest}, isParameterGrid=${isParameterGridTest}`);

        // Build Parameter Sets
        console.log("Attempting to build parameter sets...");

        if (isParameterGridTest) {
            console.log(" -> Detected Parameter Grid Test condition."); parameterSets = getParameterCombinations(); if (!Array.isArray(parameterSets)) { console.error("FATAL: Param gen failed!"); parameterSets = [];} else if (parameterSets.length === 0) { console.error("PARAM GRID ERROR: No valid combinations generated."); } else { console.log(` -> Running FIXED Parameter Grid Test (${parameterSets.length} combinations)...`); }
        } else if (isFixedContractsGridTest) { console.log(" -> Building Fixed Contracts Grid Sets..."); const baseParamsList = getParameterCombinations(); if (isAdaptive || baseParamsList.length !== 1) { console.error("GRID ERROR: Contracts Grid needs single base params."); return; } const baseParams = baseParamsList[0]; parameterSets = config.fixedContractsGrid.map(c => typeof c==='number'&&c>0 ? {...baseParams, fixedContracts:c, isAdaptive:false, riskPercent:0, commission:config.commissionPerContract, slippage:config.slippagePoints, latencyDelayBars:config.latencyDelayBars, fixedTp:config.fixedTpPoints} : null).filter(p=>p); if (parameterSets.length === 0) { console.error("GRID ERROR: No valid contract sizes."); } }
        else if (isCostGridTest) { console.log(" -> Building Cost Grid Sets..."); const baseParamsList = getParameterCombinations(); if (isAdaptive || baseParamsList.length !== 1) { console.error("GRID ERROR: Cost Grid needs single base params."); return; } const baseParams = baseParamsList[0]; const riskP = config.riskPercent||0, contracts = riskP>0 ? 0 : (config.fixedContracts||1); parameterSets = config.costGrid.map(c => ({...baseParams, commission:c.commission, slippage:c.slippage, isAdaptive:false, riskPercent:riskP, fixedContracts:contracts, latencyDelayBars:config.latencyDelayBars, fixedTp:config.fixedTpPoints })); }
        else if (isRiskGridTest) { console.log(" -> Building Risk Grid Sets..."); let baseParamsList; if (isAdaptive) { baseParamsList = [{ isAdaptive: true, SL: 'Adaptive', TP: 'Adaptive', Trail: 'Adaptive', fixedTp: config.fixedTpPoints || 0 }]; } else { baseParamsList = getParameterCombinations(); if (baseParamsList.length === 0) { console.error("GRID ERROR: No base params for Risk Grid."); return; } if (baseParamsList.length > 1) console.warn("WARN: Multiple base params found for Risk Grid."); baseParamsList = [baseParamsList[0]]; baseParamsList = baseParamsList.map(p => ({...p, fixedTp: config.fixedTpPoints || 0, isAdaptive: false})); } parameterSets = []; baseParamsList.forEach(bp => config.riskPercentGrid.forEach(r => typeof r==='number'&&r>0 ? parameterSets.push({...bp, riskPercent:r, commission:config.commissionPerContract, slippage:config.slippagePoints, fixedContracts:0, latencyDelayBars:config.latencyDelayBars}) : console.warn(`Skip invalid risk %: ${r}`))); if (parameterSets.length === 0) { console.error("GRID ERROR: No valid risk percentages."); } }
         else { // Single Run
            console.log(" -> Building Single Run Set..."); let baseParams; if (isAdaptive) { baseParams = { isAdaptive: true, SL: 'Adaptive', TP: 'Adaptive', Trail: 'Adaptive' }; console.log(" -> Mode: SINGLE ADAPTIVE"); } else { const bpl = getParameterCombinations(); if (!Array.isArray(bpl) || bpl.length === 0) { console.error("RUN ERROR: No valid SL/TP/Trail params found for Single Run."); return; } if (bpl.length > 1) console.warn("WARN: Multiple base params found for Single Run."); baseParams = bpl[0]; console.log(" -> Mode: SINGLE FIXED"); } const riskP = config.riskPercent||0, contracts = riskP>0 ? 0 : (config.fixedContracts||1); parameterSets = [{ ...baseParams, fixedTp: config.fixedTpPoints||0, riskPercent: riskP, fixedContracts: contracts, commission: config.commissionPerContract, slippage: config.slippagePoints, latencyDelayBars: config.latencyDelayBars, isAdaptive: isAdaptive }];
        }

        console.log(` -> Generated ${parameterSets ? parameterSets.length : 'undefined'} parameter sets (final assignment).`);
        parameterSets = Array.isArray(parameterSets) ? parameterSets : [];
        if (parameterSets.length === 0) { console.error("Error: No parameter sets to process after setup."); }


        console.log(`Starting parameter set loop (processing ${parameterSets.length} sets)...`);
        // --- Loop through parameter sets ---
        for (const params of parameterSets) {
             config.currentRunParams = { ...params }; config.fixedTpPoints = config.fixedTpPoints || 0; config.currentRiskPercent = config.riskPercent || 0; config.currentFixedContracts = config.fixedContracts || 1; config.commissionPerContract = config.commissionPerContract; config.slippagePoints = config.slippagePoints; config.latencyDelayBars = config.latencyDelayBars || 0;
             const isCurrentRunAdaptive = params.isAdaptive ?? false;
             const trailStepSizeMultiplier = params.trailStepSizeMultiplier || config.trailStepSizeMultiplier;
             const baseLabel = isCurrentRunAdaptive ? 'Adaptive' : `SL=${params.slFactor},TP=${params.tpFactor},Tr=${params.trailFactor},TrStep=${trailStepSizeMultiplier}`;
             let additions = [];
             if (config.fixedTpPoints > 0) additions.push(`MinTP=${config.fixedTpPoints}`);
             if (config.currentRiskPercent > 0) {
                 additions.push(`Risk=${(config.currentRiskPercent * 100).toFixed(1)}%`);
                 if(config.maxContracts < Infinity) additions.push(`Cap=${config.maxContracts}`);
             } else if (config.currentFixedContracts >= 1) {
                 additions.push(`Contracts=${config.currentFixedContracts}`);
             } else {
                 config.currentFixedContracts = 1;
                 additions.push(`Contracts=1`);
             }
             if (config.latencyDelayBars > 0) additions.push(`Latency=${config.latencyDelayBars}bar`);
             if (isCostGridTest || config.commissionPerContract !== 1.00 || config.slippagePoints !== 0.25) {
                 const rtSlipCost=config.slippagePoints*config.pointValue, totalRT=config.commissionPerContract+rtSlipCost;
                 additions.push(`Costs(C=${config.commissionPerContract.toFixed(2)}/S=${config.slippagePoints.toFixed(2)}pt=$${totalRT.toFixed(2)}RT)`);
             }
             const runLabel = baseLabel + (additions.length > 0 ? ', ' + additions.join(', ') : '');
             console.log(`\nTesting Params: ${runLabel}`);
             let currentEquity = config.initialBalance, overallPeakBalance = config.initialBalance, overallMaxDrawdown = 0; let weeklyReturns = [], weeklyResultsForParamSet = []; runFullTradeLog = []; let dailyPnLMap = new Map(), weekCounter = 0;
             const weeklyDataCopy = JSON.parse(JSON.stringify(weeklySegmentedData));
             const weeksToProcess = Object.entries(weeklyDataCopy); const totalWeeksToRun = weeksToProcess.length; if (totalWeeksToRun === 0) { console.warn(`Warning: No weeks in copied data. Skipping ${runLabel}.`); continue; }

             for (const [weekIdentifier, weekCandles] of weeksToProcess) {
                weekCounter++; process.stdout.write(` Processing Week ${weekCounter}/${totalWeeksToRun}: ${weekIdentifier}\r`);
                const indPeriods = [config.wma200Period,config.wma50Period,config.rsiPeriod,config.rsiMaPeriod,config.atrPeriod]; const validP = indPeriods.map(Number).filter(p=>p>0&&!isNaN(p)); const maxP = validP.length>0?Math.max(...validP):0; const minLookback = Math.max(maxP, 4); if (weekCandles.length < minLookback) { continue; }
                const startBalance = currentEquity; const backtestResult = backtest(weekCandles, {
                    initialBalance: currentEquity,
                    overallPeakBalance: overallPeakBalance,
                    overallMaxDrawdown: overallMaxDrawdown,
                    slFactor: params.slFactor,
                    tpFactor: params.tpFactor,
                    trailFactor: params.trailFactor,
                    isAdaptiveRun: isCurrentRunAdaptive,
                    dailyPnLMap: dailyPnLMap,
                    fixedSlPoints: params.fixedSlPoints,
                    fixedTpPoints: params.fixedTpPoints,
                    useFixedPoints: params.useFixedPoints,
                    immediateCandleExit: params.immediateCandleExit
                });
                currentEquity = backtestResult.finalBalance; overallPeakBalance = backtestResult.peakBalance; overallMaxDrawdown = backtestResult.maxDrawdown; dailyPnLMap = backtestResult.dailyPnLMap; if (startBalance > 0 && !isNaN(currentEquity) && !isNaN(startBalance)) { weeklyReturns.push((currentEquity - startBalance) / startBalance); } else if (startBalance <= 0) { weeklyReturns.push(0); } runFullTradeLog.push(...backtestResult.tradeLog); let atrSum = 0, atrCt = 0; for(const c of weekCandles) if(c && typeof c.atr === 'number' && !isNaN(c.atr) && c.atr > 0) { atrSum += c.atr; atrCt++; } const wkAvgAtr = atrCt > 0 ? (atrSum / atrCt) : NaN; const wkRegime = isNaN(wkAvgAtr) ? 'N/A' : (wkAvgAtr < atrThresholds.low_medium ? 'Low' : (wkAvgAtr > atrThresholds.medium_high ? 'High' : 'Medium')); weeklyResultsForParamSet.push({ Week: weekIdentifier, ParamsSpecific: runLabel, SL: params.slFactor, TP: params.tpFactor, Trail: params.trailFactor, MinTP: config.fixedTpPoints, RiskPct: config.currentRiskPercent, FixedContracts: config.currentFixedContracts, Commission: config.commissionPerContract, Slippage: config.slippagePoints, LatencyBars: config.latencyDelayBars, AvgATR: isNaN(wkAvgAtr)?NaN:parseFloat(wkAvgAtr.toFixed(4)), Regime: wkRegime, Trades: backtestResult.tradesTaken, Wins: backtestResult.wins, Losses: backtestResult.losses, TotalPnL: backtestResult.totalPnL, WinRate: backtestResult.winRate, ProfitFactor: backtestResult.profitFactor, AvgWin: backtestResult.avgWin, AvgLoss: backtestResult.avgLoss, MaxDDWeekly: parseFloat(backtestResult.maxDrawdownWeekly.replace('-','')) || 0, ExitCounts: backtestResult.exitCounts });
             } process.stdout.write('\n');
             if (weeklyResultsForParamSet.length > 0) { const aggRes = aggregateResults(weeklyResultsForParamSet, runLabel)[0]; if (aggRes) { aggRes.MaxDD_Overall = `-${overallMaxDrawdown.toFixed(config.pricePrecision)}`; aggRes.Sharpe_Ann = calculateSharpe(weeklyReturns); const tradeMetrics = calculateTradeMetrics(runFullTradeLog); aggRes.AvgWinDollar = tradeMetrics.avgWinDollar; aggRes.AvgLossDollar = tradeMetrics.avgLossDollar; aggRes.AvgTradeDur_Bars = tradeMetrics.avgDurationBars; aggRes.MaxContractsHit = tradeMetrics.maxContractsHit; const dailyStats = calculateDailyStats(dailyPnLMap); aggRes.AvgDailyPnL = dailyStats.avgDailyPnL; aggRes.StdDevDailyPnL = dailyStats.stdDevDailyPnL; aggRes.WinDayRate = dailyStats.winDayRate; aggRes.FixedContracts_Run = aggRes.FixedContracts_Tested; aggRes.LatencyTested = aggRes.Latency_Tested; allRunResults.push(aggRes); } else { console.warn(`Warning: No aggregated result for ${runLabel}.`); } } else { console.warn(`Warning: No weekly results for ${runLabel}.`); }
        } console.log("Finished parameter set loop.");

        console.log("\n\n================ ALL BACKTESTS COMPLETE ================");
        processAndDisplayAggregatedResults(allRunResults);
        try { const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); const resultsFilename = `aggregated_results_${timestamp}.json`; fs.writeFileSync(path.join(outputDir, resultsFilename), JSON.stringify(allRunResults, null, 2)); console.log(`\nAggregated results saved to: ${path.join(outputDir, resultsFilename)}`); if (allRunResults.length > 0 && runFullTradeLog?.length > 0) { const lastRunLabel = allRunResults[allRunResults.length-1]?.Params || 'UnknownParams'; const safeLastRunLabel = lastRunLabel.replace(/[^a-zA-Z0-9_,-]/g, '_').substring(0, 60); const tradeLogFilename = `trade_log_LastRun_${safeLastRunLabel}_${timestamp}.csv`; saveTradeLog(runFullTradeLog, tradeLogFilename); } else { console.log("Skipping trade log save (no trades/runs)."); } } catch (error) { console.error("\nError saving results files:", error); }

    } catch (error) { console.error("Critical error during backtest run:", error); process.exit(1); }
} // --- End runWeeklyBacktests ---


// --- 8) Backtest Function ---
function backtest(candlesForPeriod, { initialBalance, overallPeakBalance, overallMaxDrawdown, slFactor, tpFactor, trailFactor, isAdaptiveRun, dailyPnLMap, fixedSlPoints, fixedTpPoints, useFixedPoints, immediateCandleExit }) { const riskPercent = config.currentRiskPercent, fixedContracts = config.currentFixedContracts, maxContractsCap = config.maxContracts || Infinity, latencyDelay = config.latencyDelayBars || 0; let balance = initialBalance, wins = 0, losses = 0, trades = 0, pos = null, tradeIdCounter = 0; const completedTrades = []; let peakBalanceWeekly = initialBalance, maxDrawdownWeekly = 0, currentOverallPeak = overallPeakBalance, currentOverallMaxDD = overallMaxDrawdown; let grossProfit = 0, grossLoss = 0; let exitCounts = { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, hold_expired: 0, end_of_period: 0, time_1candle: 0 }; let currentDailyPnLMap = dailyPnLMap || new Map(); const requiredLookback = 3, startIdx = requiredLookback; if (candlesForPeriod.length <= startIdx) { return { tradesTaken: 0, wins: 0, losses: 0, totalPnL: 0.0, winRate: 0.0, maxDrawdownWeekly: '-0.00', profitFactor: 0.0, avgWin: 0.0, avgLoss: 0.0, tradeLog: [], exitCounts, finalBalance: balance, peakBalance: currentOverallPeak, maxDrawdown: currentOverallMaxDD, dailyPnLMap: currentDailyPnLMap }; } for (let i = startIdx; i < candlesForPeriod.length; i++) { const c0=candlesForPeriod[i-3], c1=candlesForPeriod[i-2], c2=candlesForPeriod[i-1], c3=candlesForPeriod[i]; if (!c3) continue; // Time filter: Only trade during profitable hours
if (config.timeFilterEnabled && config.allowedTradingHours) {
    const currentHour = new Date(c3.timestamp * 1000).getUTCHours();
    const isAllowedHour = config.allowedTradingHours.includes(currentHour);

    if (!isAllowedHour) {
        // Outside trading hours - manage existing position but don't enter new trades
        if (pos) {
            // Continue managing existing position
        } else {
            // Skip this candle for new entries
            continue;
        }
    }
}

// Daily profit target: Stop trading if we hit $200 profit for the day
const currentDay = getDayIdentifier(c3.timestamp);
const todaysPnL = currentDailyPnLMap.get(currentDay) || 0;
const dailyProfitTarget = 200; // $200 daily target

if (todaysPnL >= dailyProfitTarget) {
    // Hit daily target - manage existing position but don't enter new trades
    if (pos) {
        // Continue managing existing position
    } else {
        // Skip this candle for new entries - daily target reached
        continue;
    }
} // Skip RSI validation if RSI is disabled
const rsiEnabled = config.rsiPeriod > 0 && config.rsiMaPeriod > 0;
const rsiValid = !rsiEnabled || (!isNaN(c3.rsi) && !isNaN(c3.rsiMa));
if (isNaN(c3.wma50)||isNaN(c3.wma200)||!rsiValid) { if (pos) { /* ... manage pos ... */ } continue; }

// Debug: Track pattern detection and filtering
let debugPatternCount = 0;
let debugRsiFilterCount = 0;
let debugMaFilterCount = 0; let curSL, curTP, curTr, curReg; if (isAdaptiveRun) { const atr=c3.atr; curReg=(atr<atrThresholds.low_medium)?'Low':(atr>atrThresholds.medium_high?'High':'Medium'); curSL=adaptiveParams[curReg].slFactor; curTP=adaptiveParams[curReg].tpFactor; curTr=adaptiveParams[curReg].trailFactor; } else { curSL=slFactor; curTP=tpFactor; curTr=trailFactor; curReg='Fixed'; } const p3=detect3(c1,c2,c3), p4=detect4(c0,c1,c2,c3); let pat=null, pType=null; if(p4){pat=p4;pType='four';debugPatternCount++;} else if(p3){pat=p3;pType='three';debugPatternCount++;}



        if (!pos && pat && entryOK(pat, pType, c3, i, candlesForPeriod)) { tradeIdCounter++; trades++; let entryP, atrE, entryTs, entryIdx = i; atrE = c3.atr; entryP = c3.close; entryTs = new Date(c3.timestamp * 1000); if(latencyDelay>0&&(i+latencyDelay)<candlesForPeriod.length){ const entryExecCandleIndex = i + latencyDelay; const entryExecCandle = candlesForPeriod[entryExecCandleIndex]; if (entryExecCandle && !isNaN(entryExecCandle.open)) { entryP = entryExecCandle.open; entryTs = new Date(entryExecCandle.timestamp * 1000); entryIdx = entryExecCandleIndex; } else {trades--;tradeIdCounter--;continue;} } else if(latencyDelay>0){trades--;tradeIdCounter--;continue;} if (typeof curSL !== 'number' || isNaN(curSL) || typeof curTP !== 'number' || isNaN(curTP) || typeof curTr !== 'number' || isNaN(curTr)) { console.error(`Invalid factors: SL=${curSL}, TP=${curTP}, Trail=${curTr}`); trades--; tradeIdCounter--; continue; }

// Check if we're using fixed points for SL/TP
let slDist, tpDist;
if (useFixedPoints) {
    // Use fixed point values directly
    slDist = fixedSlPoints;
    tpDist = fixedTpPoints;
    console.log(`Using fixed points: SL=${slDist}, TP=${tpDist}`);
} else {
    // Use ATR-based values
    slDist = atrE * curSL;
    const atrTpDist = atrE * curTP;
    const fixTpDist = config.fixedTpPoints || 0;
    tpDist = Math.max(fixTpDist, atrTpDist);
} let cont;
                // Use dynamic position sizing if enabled
                if (config.useDynamicPositionSizing && positionSizer) {
                    // Get the timestamp
                    const timestamp = new Date(c3.timestamp * 1000);

                    // Determine ATR regime
                    const atrRegime = c3.atr < atrThresholds.low_medium ? 'Low' :
                                     (c3.atr > atrThresholds.medium_high ? 'High' : 'Medium');

                    // Get time score if time analysis is enabled
                    let timeScore = 0.5; // Default neutral score
                    if (config.useTimeAnalysis && timeAnalyzer) {
                        const timeScoreInfo = timeAnalyzer.getTimeScore(timestamp);
                        timeScore = timeScoreInfo.combinedScore;
                    }

                    // Get ML entry score if ML filter is enabled
                    let entryScore = 0.7; // Default score
                    if (config.useMLEntryFilter && mlFilter && config.useAdvancedMLFilter) {
                        // Create feature object for ML filter
                        const features = {
                            direction: pat,
                            atrRegime: atrRegime,
                            hourOfDay: timestamp.getUTCHours(),
                            dayOfWeek: timestamp.getUTCDay(),
                            rsi: c3.rsi,
                            rsiMa: c3.rsiMa,
                            close: c3.close,
                            wma50: c3.wma50,
                            patternType: pType
                        };

                        // Calculate entry score (without making the decision)
                        entryScore = mlFilter.calculateEntryScore(features) / mlFilter.entryThreshold;
                    }

                    // Update position sizer with current balance
                    positionSizer.updateBalance(balance);

                    // Calculate position size based on all factors
                    cont = positionSizer.calculatePositionSize({
                        currentATR: c3.atr,
                        regime: atrRegime,
                        timeScore: timeScore,
                        entryScore: entryScore
                    });
                }
                // Use volatility-based position sizing if enabled and dynamic sizing is not used
                else if (config.useVolatilityPositionSizing && riskPercent > 0 && balance > 0) {
                    cont = spreadUtils.calculateVolatilityBasedPositionSize(atrE, balance, riskPercent, config);
                }
                // Use risk-based position sizing if enabled and neither dynamic nor volatility sizing is used
                else if (riskPercent > 0 && balance > 0) {
                    const pv = config.pointValue;
                    const slpC = (config.slippagePoints || 0) * pv;
                    const rpc = (slDist * pv) + config.commissionPerContract + slpC;
                    cont = (rpc > 0) ? Math.min(maxContractsCap, Math.max(1, Math.floor((balance * riskPercent) / rpc))) : 1;
                }
                // Use fixed contracts as a fallback
                else {
                    cont = fixedContracts;
                }
                cont = Math.max(1, cont); const entryC = candlesForPeriod[entryIdx]; if (!entryC) { trades--; tradeIdCounter--; continue; } pos={ tradeId:tradeIdCounter, entryTimestamp:entryTs, dir:pat, entry:entryP, atr:atrE, tpDistance:tpDist, slDistance:slDist, stopLossPrice:(pat==='bullish')?entryP-slDist:entryP+slDist, trailStopPrice:(pat==='bullish')?entryP-(atrE*curTr):entryP+(atrE*curTr), trailFactor:curTr, entryAtrRegime:curReg, trailHigh:entryC.high, trailLow:entryC.low, entryBarIndex:entryIdx, currentBarIndex:entryIdx, tpType:useFixedPoints ? 'FixedPoints' : 'ATR', contracts:cont, immediateCandleExit: immediateCandleExit || null }; } if(pos){ if(i>pos.entryBarIndex){
                // Use enhanced position management if enabled, otherwise use standard
                // Get the current trail step size multiplier from config
                const trailStepSizeMultiplier = config.trailStepSizeMultiplier || 0.1;

                // Create a modified config with the current trail step size multiplier
                const modifiedConfig = {
                    ...config,
                    trailStepSizeMultiplier: trailStepSizeMultiplier
                };

                const exitInfo = config.useSteppedTrail || config.useAdaptiveSlippage ?
                    enhancedPM.enhancedManagePosition(pos, c3, i, candlesForPeriod, completedTrades, modifiedConfig, exitCounts) :
                    managePosition(pos, c3, i, candlesForPeriod, completedTrades, config, exitCounts);

                if(exitInfo){
                    balance+=exitInfo.pnlNetTotal;
                    const day=getDayIdentifier(exitInfo.exitTimestamp.getTime()/1000);
                    currentDailyPnLMap.set(day,(currentDailyPnLMap.get(day)||0)+exitInfo.pnlNetTotal);
                    if(exitInfo.pnlNetTotal>0){wins++;grossProfit+=exitInfo.pnlNetTotal;}else{losses++;grossLoss+=Math.abs(exitInfo.pnlNetTotal);}
                    peakBalanceWeekly=Math.max(peakBalanceWeekly,balance);
                    maxDrawdownWeekly=Math.max(maxDrawdownWeekly, peakBalanceWeekly-balance);
                    currentOverallPeak=Math.max(currentOverallPeak,balance);
                    currentOverallMaxDD=Math.max(currentOverallMaxDD, currentOverallPeak-balance);
                    pos=null;
                }
            } else {
                pos.trailHigh=Math.max(pos.trailHigh, c3.high);
                pos.trailLow=Math.min(pos.trailLow, c3.low);
            }
        } } if(pos){
        const lastC=candlesForPeriod[candlesForPeriod.length-1];
        if (!lastC) {
            console.warn("End of period check: Last candle missing!");
            pos = null;
        } else {
            const exitP=lastC.close, exitTs=new Date(lastC.timestamp*1000);
            pos.currentBarIndex=candlesForPeriod.length-1;

            // Use enhanced exit logic if enabled
            let exitInfo;
            if (config.useSteppedTrail || config.useAdaptiveSlippage) {
                // Get recent candles for spread estimation
                const lookbackStart = Math.max(0, candlesForPeriod.length - 11);
                const recentCandles = candlesForPeriod.slice(lookbackStart);

                // Get the current trail step size multiplier from config
                const trailStepSizeMultiplier = config.trailStepSizeMultiplier || 0.1;

                // Create a modified config with the current trail step size multiplier
                const modifiedConfig = {
                    ...config,
                    trailStepSizeMultiplier: trailStepSizeMultiplier
                };

                // Estimate spread and calculate adaptive slippage
                const estimatedSpread = spreadUtils.estimateSpread(recentCandles, modifiedConfig);
                const adaptiveSlippage = modifiedConfig.useAdaptiveSlippage ?
                    spreadUtils.calculateAdaptiveSlippage(recentCandles, modifiedConfig) :
                    modifiedConfig.slippagePoints || 0.5;

                exitInfo = enhancedPM.enhancedLogAndStoreExit(
                    pos, 'end_of_period', 0, exitP, exitTs, completedTrades, modifiedConfig, exitCounts,
                    adaptiveSlippage, estimatedSpread
                );
            } else {
                exitInfo = logAndStoreExit(pos, 'end_of_period', 0, exitP, exitTs, completedTrades, config, exitCounts);
            }

            balance += exitInfo.pnlNetTotal;
            const day = getDayIdentifier(exitTs.getTime()/1000);
            currentDailyPnLMap.set(day, (currentDailyPnLMap.get(day) || 0) + exitInfo.pnlNetTotal);

            if(exitInfo.pnlNetTotal > 0) {
                wins++;
                grossProfit += exitInfo.pnlNetTotal;
            } else {
                losses++;
                grossLoss += Math.abs(exitInfo.pnlNetTotal);
            }

            peakBalanceWeekly = Math.max(peakBalanceWeekly, balance);
            maxDrawdownWeekly = Math.max(maxDrawdownWeekly, peakBalanceWeekly-balance);
            currentOverallPeak = Math.max(currentOverallPeak, balance);
            currentOverallMaxDD = Math.max(currentOverallMaxDD, currentOverallPeak-balance);
            pos = null;
        }
    } // End of main for loop
    const wkPnl=balance-initialBalance, winRate=(wins+losses)>0?(wins/(wins+losses)*100):0, pf=grossLoss>0?(grossProfit/grossLoss):(grossProfit>0?99999:0); const avgW=wins>0?(grossProfit/wins):0, avgL=losses>0?(grossLoss/losses):0; return { tradesTaken:trades, wins:wins, losses:losses, totalPnL:wkPnl, winRate:winRate, profitFactor:pf, avgWin:avgW, avgLoss:avgL, tradeLog:completedTrades, exitCounts:exitCounts, finalBalance:balance, peakBalance:currentOverallPeak, maxDrawdown:currentOverallMaxDD, maxDrawdownWeekly:`-${maxDrawdownWeekly.toFixed(config.pricePrecision)}`, dailyPnLMap:currentDailyPnLMap }; }

// --- 9) Position Management --- (Uses Exit@Close Latency Model)
function managePosition(currentPos, currentCandle, currentIndex, candlesForPeriod, completedTrades, config, exitCounts) {
    const trailF = currentPos.trailFactor;
    const atrTrail = (typeof currentCandle.atr === 'number' && !isNaN(currentCandle.atr) && currentCandle.atr > 0) ? currentCandle.atr : currentPos.atr;

    // Only update trailing stop if not disabled
    if (!config.disableTrailingStop) {
        if (currentPos.dir === 'bullish') {
            currentPos.trailHigh = Math.max(currentPos.trailHigh, currentCandle.high);
            currentPos.trailStopPrice = Math.max(currentPos.trailStopPrice, currentPos.trailHigh-(atrTrail*trailF));
        } else {
            currentPos.trailLow = Math.min(currentPos.trailLow, currentCandle.low);
            currentPos.trailStopPrice = Math.min(currentPos.trailStopPrice, currentPos.trailLow+(atrTrail*trailF));
        }
    }

    let exitR = null, exitSigP = null;

    // 1-CANDLE EXIT STRATEGY: Skip all SL/TP checks when using 1-candle exits
    const using1CandleExit = config.useImmediateExit && currentPos.immediateCandleExit === 1;

    // Only check TP if NOT using 1-candle exit strategy
    if (!using1CandleExit && currentPos.tpDistance > 0) {
        if (currentPos.dir === 'bullish') {
            if (currentCandle.high >= currentPos.entry + currentPos.tpDistance) {
                exitR = 'tp';
                exitSigP = currentPos.entry + currentPos.tpDistance;
            }
        } else {
            if (currentCandle.low <= currentPos.entry - currentPos.tpDistance) {
                exitR = 'tp';
                exitSigP = currentPos.entry - currentPos.tpDistance;
            }
        }
    }

    // Skip SL checks completely - NO STOP LOSS in this strategy

    // Simple backup exit - time-based only
    if (!exitR) {
        const barsInTrade = currentIndex - currentPos.entryBarIndex;

        // Time-based exit after X candles (if TP not hit)
        if (config.useImmediateExit && currentPos.immediateCandleExit && barsInTrade >= currentPos.immediateCandleExit) {
            exitR = `time_${currentPos.immediateCandleExit}candle`;
            exitSigP = currentCandle.close;
        }
        // Max time backup exit (60 minutes)
        else if (barsInTrade >= (config.maxTimeExitMinutes || 60)) {
            exitR = `max_time_${config.maxTimeExitMinutes || 60}min`;
            exitSigP = currentCandle.close;
        }
    }

    if (!exitR && config.useTwoBarColorExit && currentIndex > currentPos.entryBarIndex + 1) {
        const prevC = candlesForPeriod[currentIndex-1],
              prevCol = candlestickColor(prevC),
              curCol = candlestickColor(currentCandle);

        if (currentPos.dir === 'bullish' && prevCol === 'red' && curCol === 'red') {
            exitR = 'color_flow_2bar';
            exitSigP = currentCandle.close;
        } else if (currentPos.dir === 'bearish' && prevCol === 'green' && curCol === 'green') {
            exitR = 'color_flow_2bar';
            exitSigP = currentCandle.close;
        }
    }

    if (exitR && exitSigP !== null) {
        let finalExitP = exitSigP, exitIdx = currentIndex;
        const lat = config.latencyDelayBars || 0;

        if (lat > 0 && (currentIndex + lat) < candlesForPeriod.length) {
            const idx = currentIndex + lat, c = candlesForPeriod[idx];
            if (c && !isNaN(c.open) && !isNaN(c.close)) {
                finalExitP = c.close;
                exitIdx = idx;
            } else {
                finalExitP = currentCandle.close;
                exitIdx = currentIndex;
            }
        } else if (lat > 0) {
            finalExitP = candlesForPeriod[candlesForPeriod.length-1].close;
            exitIdx = candlesForPeriod.length-1;
        }

        const exitTs = new Date(candlesForPeriod[exitIdx].timestamp * 1000);
        currentPos.currentBarIndex = exitIdx;
        const exitInfo = logAndStoreExit(currentPos, exitR, 0, finalExitP, exitTs, completedTrades, config, exitCounts);
        return exitInfo;
    }

    return null;
}

// --- 10) Trade Logging ---
function logAndStoreExit(posData, reason, pnlPointsTheoretical, exitSignalPrice, exitTimestamp, completedTradesArray, config, exitCounts) { if (!posData || typeof posData.entry !== 'number' || !posData.dir || !exitTimestamp) { console.error("logAndStoreExit: Invalid data."); return { pnlNetTotal: 0, exitTimestamp: exitTimestamp || new Date() }; } const contracts=posData.contracts||1, ptVal=config.pointValue; let adjExitP=exitSignalPrice; const slipPts=config.slippagePoints||0; const applySlip=(reason==='sl'||reason==='trail'||reason==='end_of_period'||reason==='hold_expired'||reason==='color_flow_2bar'); if(applySlip&&slipPts>0){ adjExitP=(posData.dir==='bullish')?exitSignalPrice-slipPts:exitSignalPrice+slipPts; } const pnlPtsAdj=(posData.dir==='bullish')?adjExitP-posData.entry:posData.entry-adjExitP; const pnlGross=pnlPtsAdj*ptVal*contracts, commCost=config.commissionPerContract*contracts, pnlNet=pnlGross-commCost; const durBars=(typeof posData.currentBarIndex==='number'&&typeof posData.entryBarIndex==='number'&&posData.currentBarIndex>=posData.entryBarIndex)?posData.currentBarIndex-posData.entryBarIndex+1:'N/A'; if(exitCounts){ exitCounts[reason]=(exitCounts[reason]||0)+1; } const tradeData={ ID:posData.tradeId||'N/A', EntryTime:posData.entryTimestamp?.toISOString()||'N/A', ExitTime:exitTimestamp.toISOString(), Dir:posData.dir, Entry:posData.entry.toFixed(config.pricePrecision), ExitSignal:exitSignalPrice.toFixed(config.pricePrecision), ExitFill:adjExitP.toFixed(config.pricePrecision), Reason:reason, PnL_Pts_Gross:pnlPtsAdj.toFixed(config.pricePrecision), PnL_Net:pnlNet.toFixed(2), SlippagePts:applySlip?slipPts:0, CommissionCost:commCost.toFixed(2), Contracts:contracts, Duration:durBars, EntryATRRegime:posData.entryAtrRegime||'N/A', TP_Type:posData.tpType||'N/A' }; completedTradesArray.push(tradeData); return { pnlNetTotal:pnlNet, exitTimestamp:exitTimestamp }; }

// --- 11) Param Combinations --- (Corrected Version)
function getParameterCombinations() {
    const combos = [];

    // Check if we're using fixed points for SL/TP
    if (config.useFixedPointsForSLTP && Array.isArray(config.fixedSlPointsGrid) && Array.isArray(config.fixedTpPointsGrid)) {
        console.log(" -> Using fixed points grid for SL/TP");
        const slPointsArray = config.fixedSlPointsGrid;
        const tpPointsArray = config.fixedTpPointsGrid;
        const trailArray = Array.isArray(config.trailFactors) ? config.trailFactors : [config.trailFactors];
        const trailStepArray = Array.isArray(config.trailStepSizeMultiplier) ? config.trailStepSizeMultiplier : [config.trailStepSizeMultiplier];

        let isValid = true;
        if (!slPointsArray || slPointsArray.length === 0) { console.error("Error: Invalid 'fixedSlPointsGrid'."); isValid = false; }
        if (!tpPointsArray || tpPointsArray.length === 0) { console.error("Error: Invalid 'fixedTpPointsGrid'."); isValid = false; }

        if (isValid) {
            // Get immediate candle exit options if enabled
            const immediateCandleArray = config.useImmediateExit && Array.isArray(config.immediateCandleExitGrid)
                ? config.immediateCandleExitGrid
                : [null];

            for (let slPoints of slPointsArray) {
                for (let tpPoints of tpPointsArray) {
                    for (let trail of trailArray) {
                        for (let trailStep of trailStepArray) {
                            for (let immediateCandleExit of immediateCandleArray) {
                                if (typeof slPoints === 'number' && !isNaN(slPoints) &&
                                    typeof tpPoints === 'number' && !isNaN(tpPoints) &&
                                    typeof trail === 'number' && !isNaN(trail) &&
                                    typeof trailStep === 'number' && !isNaN(trailStep)) {
                                    combos.push({
                                        slFactor: 0, // Not using ATR-based SL
                                        tpFactor: 0, // Not using ATR-based TP
                                        trailFactor: trail,
                                        trailStepSizeMultiplier: trailStep,
                                        fixedSlPoints: slPoints,
                                        fixedTpPoints: tpPoints,
                                        useFixedPoints: true,
                                        immediateCandleExit: immediateCandleExit
                                    });
                            }
                        }
                    }
                }
            }
        }
    } else {
        // Original ATR-based parameter combinations
        const slArray = Array.isArray(config.slFactors) ? config.slFactors : [config.slFactors];
        const tpArray = Array.isArray(config.tpFactors) ? config.tpFactors : [config.tpFactors];
        const trailArray = Array.isArray(config.trailFactors) ? config.trailFactors : [config.trailFactors];
        const trailStepArray = Array.isArray(config.trailStepSizeMultiplier) ? config.trailStepSizeMultiplier : [config.trailStepSizeMultiplier];

        let isValid = true;
        if (!slArray || slArray.length === 0 || typeof slArray[0] !== 'number' || isNaN(slArray[0])) { console.error("Error: Invalid 'slFactors'."); isValid = false; }
        if (!tpArray || tpArray.length === 0 || typeof tpArray[0] !== 'number' || isNaN(tpArray[0])) { console.error("Error: Invalid 'tpFactors'."); isValid = false; }
        if (!trailArray || trailArray.length === 0 || typeof trailArray[0] !== 'number' || isNaN(trailArray[0])) { console.error("Error: Invalid 'trailFactors'."); isValid = false; }

        if (isValid) {
            for (let sl of slArray) {
                for (let tp of tpArray) {
                    for (let trail of trailArray) {
                        for (let trailStep of trailStepArray) {
                            if (typeof sl === 'number' && !isNaN(sl) &&
                                typeof tp === 'number' && !isNaN(tp) &&
                                typeof trail === 'number' && !isNaN(trail) &&
                                typeof trailStep === 'number' && !isNaN(trailStep)) {
                                combos.push({
                                    slFactor: sl,
                                    tpFactor: tp,
                                    trailFactor: trail,
                                    trailStepSizeMultiplier: trailStep,
                                    useFixedPoints: false
                                });
                            }
                        }
                    }
                }
            }
        }
    }

    return combos; // Always return the array
}

// --- 12) Metrics --- (moved to top of file)

// --- 13) Results Aggregation --- (moved to top of file)

// --- 14) Results Display --- (moved to top of file)

// --- 15) Save Trade Log --- (moved to top of file)



} // End of main backtest function

// ======================== End of file ========================