/**
 * Simulation Mode Runner
 * 
 * This script provides a command-line interface for controlling the simulation mode.
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');
const simulationMode = require('./simulation_mode');
const logger = require('./data_logger');

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '> '
});

// State
let isRunning = false;
let isShuttingDown = false;
let simulationConfig = {
    slippage: 0.75,
    commission: 0.40,
    latency: 0,
    useHistoricalData: true,
    historicalDataPath: path.join(__dirname, 'historical_data'),
    symbols: ['MNQ', 'MES', 'MGC'],
    accountBalance: 10000,
    maxPositionSize: 10
};

// Command history
const commandHistory = [];
const MAX_HISTORY_SIZE = 50;

// Help text
const helpText = `
=== Simulation Mode Commands ===
start - Start the simulation
stop - Stop the simulation
status - Check the status of the simulation
positions - List all open positions
orders - List all active orders
history - Show trade history
config - Show current configuration
setconfig <param> <value> - Set configuration parameter
loaddata <symbol> - Load historical data for a symbol
savedata <symbol> - Save historical data for a symbol
run <days> - Run simulation for specified number of days
logs - Show recent logs
clear - Clear the console
exit - Exit the program
help - Show this help message
`;

// Welcome message
console.log('=== Trading Bot Simulation Mode ===');
console.log('Type "help" for a list of available commands');
rl.prompt();

// Handle commands
rl.on('line', async (line) => {
    const args = line.trim().split(' ');
    const command = args[0];
    
    // Add command to history
    if (command && command !== commandHistory[0]) {
        commandHistory.unshift(command);
        if (commandHistory.length > MAX_HISTORY_SIZE) {
            commandHistory.pop();
        }
    }
    
    // Process command
    try {
        if (commands[command]) {
            await commands[command](args.slice(1));
        } else if (command) {
            console.log(`Unknown command: ${command}`);
            console.log('Type "help" for a list of available commands');
        }
    } catch (error) {
        console.error(`Error executing command: ${error.message}`);
    }
    
    rl.prompt();
}).on('close', () => {
    console.log('Shutting down...');
    
    // Shutdown the simulation if it's running
    if (isRunning && !isShuttingDown) {
        isShuttingDown = true;
        simulationMode.shutdown()
            .then(() => {
                console.log('Exiting...');
                process.exit(0);
            })
            .catch((error) => {
                console.error(`Error shutting down: ${error.message}`);
                process.exit(1);
            });
    } else {
        console.log('Exiting...');
        process.exit(0);
    }
});

// Command handlers
const commands = {
    start: async () => {
        if (isRunning) {
            console.log('Simulation is already running');
            return;
        }
        
        console.log('Starting simulation...');
        
        try {
            // Create historical data directory if it doesn't exist
            if (!fs.existsSync(simulationConfig.historicalDataPath)) {
                fs.mkdirSync(simulationConfig.historicalDataPath, { recursive: true });
            }
            
            const result = await simulationMode.initialize(simulationConfig);
            
            if (result) {
                isRunning = true;
                console.log('Simulation started successfully');
            } else {
                console.error('ERROR: Failed to initialize simulation');
                console.log('Failed to start simulation');
            }
        } catch (error) {
            console.error(`ERROR: ${error.message}`);
            console.log('Failed to start simulation');
        }
    },
    
    stop: async () => {
        if (!isRunning) {
            console.log('Simulation is not running');
            return;
        }
        
        console.log('Stopping simulation...');
        
        try {
            isShuttingDown = true;
            const result = await simulationMode.shutdown();
            
            if (result) {
                isRunning = false;
                isShuttingDown = false;
                console.log('Simulation stopped successfully');
            } else {
                isShuttingDown = false;
                console.error('ERROR: Failed to shutdown simulation');
                console.log('Failed to stop simulation');
            }
        } catch (error) {
            isShuttingDown = false;
            console.error(`ERROR: ${error.message}`);
            console.log('Failed to stop simulation');
        }
    },
    
    status: async () => {
        if (!isRunning) {
            console.log('Simulation status: STOPPED');
            return;
        }
        
        try {
            const status = simulationMode.getStatus();
            
            console.log('\n=== Simulation Status ===');
            console.log(`Running: ${status.isRunning ? 'YES' : 'NO'}`);
            console.log(`Account Balance: $${status.accountBalance.toFixed(2)}`);
            console.log(`Active Positions: ${status.activePositions}`);
            console.log(`Active Orders: ${status.activeOrders}`);
            console.log(`Filled Orders: ${status.filledOrders}`);
            console.log(`Closed Positions: ${status.closedPositions}`);
            
            console.log('\nConfiguration:');
            console.log(`  Slippage: ${status.config.slippage} points`);
            console.log(`  Commission: $${status.config.commission} per side`);
            console.log(`  Latency: ${status.config.latency} bars`);
            console.log(`  Using Historical Data: ${status.config.useHistoricalData ? 'YES' : 'NO'}`);
            console.log(`  Max Position Size: ${status.config.maxPositionSize} contracts`);
        } catch (error) {
            console.error(`Error getting status: ${error.message}`);
        }
    },
    
    positions: async () => {
        if (!isRunning) {
            console.log('Simulation is not running');
            return;
        }
        
        try {
            const positions = simulationMode.getPositions();
            
            console.log('\n=== Active Positions ===');
            
            if (Object.keys(positions).length === 0) {
                console.log('No active positions');
            } else {
                console.log('ID\t\tSymbol\tAction\tQty\tEntry\tStop\tTarget');
                console.log('----------------------------------------------------------');
                
                for (const positionId in positions) {
                    const position = positions[positionId];
                    console.log(`${positionId.substring(0, 8)}...\t${position.symbol}\t${position.action}\t${position.quantity}\t${position.entryPrice.toFixed(2)}\t${position.stopLossPrice ? position.stopLossPrice.toFixed(2) : 'N/A'}\t${position.takeProfitPrice ? position.takeProfitPrice.toFixed(2) : 'N/A'}`);
                }
            }
        } catch (error) {
            console.error(`Error getting positions: ${error.message}`);
        }
    },
    
    orders: async () => {
        if (!isRunning) {
            console.log('Simulation is not running');
            return;
        }
        
        try {
            const orders = simulationMode.getOrders();
            
            console.log('\n=== Active Orders ===');
            
            if (Object.keys(orders).length === 0) {
                console.log('No active orders');
            } else {
                console.log('ID\t\tSymbol\tAction\tQty\tType\tStatus');
                console.log('----------------------------------------------------------');
                
                for (const orderId in orders) {
                    const order = orders[orderId];
                    console.log(`${orderId.substring(0, 8)}...\t${order.symbol}\t${order.action}\t${order.quantity}\t${order.orderType}\t${order.status}`);
                }
            }
        } catch (error) {
            console.error(`Error getting orders: ${error.message}`);
        }
    },
    
    history: async () => {
        if (!isRunning) {
            console.log('Simulation is not running');
            return;
        }
        
        try {
            const closedPositions = simulationMode.getClosedPositions();
            
            console.log('\n=== Trade History ===');
            
            if (closedPositions.length === 0) {
                console.log('No trade history');
            } else {
                console.log('Symbol\tAction\tQty\tEntry\tExit\tP&L\tReason');
                console.log('----------------------------------------------------------');
                
                let totalPnL = 0;
                
                for (const position of closedPositions) {
                    console.log(`${position.symbol}\t${position.action}\t${position.quantity}\t${position.entryPrice.toFixed(2)}\t${position.exitPrice.toFixed(2)}\t$${position.pnl.toFixed(2)}\t${position.exitReason || 'Manual'}`);
                    totalPnL += position.pnl;
                }
                
                console.log('----------------------------------------------------------');
                console.log(`Total P&L: $${totalPnL.toFixed(2)}`);
            }
        } catch (error) {
            console.error(`Error getting trade history: ${error.message}`);
        }
    },
    
    config: async () => {
        console.log('\n=== Simulation Configuration ===');
        console.log(`Slippage: ${simulationConfig.slippage} points`);
        console.log(`Commission: $${simulationConfig.commission} per side`);
        console.log(`Latency: ${simulationConfig.latency} bars`);
        console.log(`Using Historical Data: ${simulationConfig.useHistoricalData ? 'YES' : 'NO'}`);
        console.log(`Historical Data Path: ${simulationConfig.historicalDataPath}`);
        console.log(`Symbols: ${simulationConfig.symbols.join(', ')}`);
        console.log(`Account Balance: $${simulationConfig.accountBalance}`);
        console.log(`Max Position Size: ${simulationConfig.maxPositionSize} contracts`);
    },
    
    setconfig: async (args) => {
        if (args.length < 2) {
            console.log('Usage: setconfig <param> <value>');
            return;
        }
        
        const param = args[0];
        const value = args[1];
        
        if (param === 'slippage') {
            simulationConfig.slippage = parseFloat(value);
            console.log(`Slippage set to ${simulationConfig.slippage} points`);
        } else if (param === 'commission') {
            simulationConfig.commission = parseFloat(value);
            console.log(`Commission set to $${simulationConfig.commission} per side`);
        } else if (param === 'latency') {
            simulationConfig.latency = parseInt(value);
            console.log(`Latency set to ${simulationConfig.latency} bars`);
        } else if (param === 'useHistoricalData') {
            simulationConfig.useHistoricalData = value.toLowerCase() === 'true';
            console.log(`Using Historical Data set to ${simulationConfig.useHistoricalData ? 'YES' : 'NO'}`);
        } else if (param === 'historicalDataPath') {
            simulationConfig.historicalDataPath = value;
            console.log(`Historical Data Path set to ${simulationConfig.historicalDataPath}`);
        } else if (param === 'symbols') {
            simulationConfig.symbols = value.split(',');
            console.log(`Symbols set to ${simulationConfig.symbols.join(', ')}`);
        } else if (param === 'accountBalance') {
            simulationConfig.accountBalance = parseFloat(value);
            console.log(`Account Balance set to $${simulationConfig.accountBalance}`);
        } else if (param === 'maxPositionSize') {
            simulationConfig.maxPositionSize = parseInt(value);
            console.log(`Max Position Size set to ${simulationConfig.maxPositionSize} contracts`);
        } else {
            console.log(`Unknown parameter: ${param}`);
        }
    },
    
    loaddata: async (args) => {
        if (args.length < 1) {
            console.log('Usage: loaddata <symbol>');
            return;
        }
        
        const symbol = args[0];
        
        console.log(`Loading historical data for ${symbol}...`);
        
        try {
            const filePath = path.join(simulationConfig.historicalDataPath, `${symbol.toLowerCase()}_data.json`);
            
            if (!fs.existsSync(filePath)) {
                console.log(`No historical data file found for ${symbol}`);
                return;
            }
            
            const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            
            if (data && data.candles && data.candles.length > 0) {
                console.log(`Loaded ${data.candles.length} candles for ${symbol}`);
                
                // Process a few candles to show they loaded correctly
                console.log('\nSample candles:');
                for (let i = 0; i < Math.min(5, data.candles.length); i++) {
                    const candle = data.candles[i];
                    console.log(`${new Date(candle.timestamp).toLocaleString()}: O=${candle.open}, H=${candle.high}, L=${candle.low}, C=${candle.close}, V=${candle.volume || 0}`);
                }
            } else {
                console.log(`No candles found in historical data for ${symbol}`);
            }
        } catch (error) {
            console.error(`Error loading historical data: ${error.message}`);
        }
    },
    
    savedata: async (args) => {
        if (args.length < 1) {
            console.log('Usage: savedata <symbol>');
            return;
        }
        
        if (!isRunning) {
            console.log('Simulation is not running');
            return;
        }
        
        const symbol = args[0];
        
        console.log(`Saving historical data for ${symbol}...`);
        
        try {
            const marketData = simulationMode.getMarketData(symbol);
            
            if (!marketData) {
                console.log(`No market data for ${symbol}`);
                return;
            }
            
            const filePath = path.join(simulationConfig.historicalDataPath, `${symbol.toLowerCase()}_data.json`);
            
            fs.writeFileSync(filePath, JSON.stringify({
                symbol,
                candles: marketData.candles
            }, null, 2));
            
            console.log(`Saved ${marketData.candles.length} candles for ${symbol} to ${filePath}`);
        } catch (error) {
            console.error(`Error saving historical data: ${error.message}`);
        }
    },
    
    run: async (args) => {
        if (!isRunning) {
            console.log('Simulation is not running');
            return;
        }
        
        if (args.length < 1) {
            console.log('Usage: run <days>');
            return;
        }
        
        const days = parseInt(args[0]);
        
        if (isNaN(days) || days <= 0) {
            console.log('Days must be a positive number');
            return;
        }
        
        console.log(`Running simulation for ${days} days...`);
        
        try {
            // Generate sample data for the simulation
            const startDate = new Date();
            startDate.setHours(0, 0, 0, 0);
            
            const endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + days);
            
            let currentDate = new Date(startDate);
            
            while (currentDate < endDate) {
                // Skip weekends
                if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
                    currentDate.setDate(currentDate.getDate() + 1);
                    continue;
                }
                
                // Generate candles for each symbol for this day
                for (const symbol of simulationConfig.symbols) {
                    // Generate 24 hourly candles for this day
                    for (let hour = 0; hour < 24; hour++) {
                        const candleTime = new Date(currentDate);
                        candleTime.setHours(hour, 0, 0, 0);
                        
                        // Generate a random candle
                        const basePrice = symbol === 'MNQ' ? 18000 : symbol === 'MES' ? 5000 : 2000;
                        const volatility = symbol === 'MNQ' ? 50 : symbol === 'MES' ? 20 : 10;
                        
                        const open = basePrice + (Math.random() - 0.5) * volatility;
                        const close = open + (Math.random() - 0.5) * volatility;
                        const high = Math.max(open, close) + Math.random() * volatility * 0.5;
                        const low = Math.min(open, close) - Math.random() * volatility * 0.5;
                        const volume = Math.floor(Math.random() * 1000) + 100;
                        
                        const candle = {
                            timestamp: candleTime.getTime(),
                            open,
                            high,
                            low,
                            close,
                            volume
                        };
                        
                        // Process the candle
                        simulationMode.processCandle(symbol, candle);
                    }
                }
                
                // Move to next day
                currentDate.setDate(currentDate.getDate() + 1);
            }
            
            console.log(`Simulation completed for ${days} days`);
            
            // Show final status
            const status = simulationMode.getStatus();
            console.log(`Final Account Balance: $${status.accountBalance.toFixed(2)}`);
            console.log(`Closed Positions: ${status.closedPositions}`);
        } catch (error) {
            console.error(`Error running simulation: ${error.message}`);
        }
    },
    
    logs: async () => {
        try {
            const logFile = path.join(__dirname, 'logs', 'system.log');
            
            if (!fs.existsSync(logFile)) {
                console.log('No log file found');
                return;
            }
            
            // Read the last 20 lines of the log file
            const logs = fs.readFileSync(logFile, 'utf8').split('\n').slice(-20);
            
            console.log('\n=== Recent Logs ===');
            logs.forEach(log => {
                if (log.trim()) {
                    console.log(log);
                }
            });
        } catch (error) {
            console.error(`Error reading logs: ${error.message}`);
        }
    },
    
    clear: async () => {
        console.clear();
        console.log('=== Trading Bot Simulation Mode ===');
        console.log('Type "help" for a list of available commands');
    },
    
    help: async () => {
        console.log(helpText);
    },
    
    exit: async () => {
        rl.close();
    }
};

// Handle SIGINT (Ctrl+C)
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT. Shutting down...');
    rl.close();
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(`Uncaught Exception: ${error.message}`);
    console.error(error.stack);
    
    // Shutdown the simulation if it's running
    if (isRunning && !isShuttingDown) {
        isShuttingDown = true;
        simulationMode.shutdown()
            .then(() => {
                console.log('Exiting due to uncaught exception...');
                process.exit(1);
            })
            .catch((shutdownError) => {
                console.error(`Error shutting down: ${shutdownError.message}`);
                process.exit(1);
            });
    } else {
        console.log('Exiting due to uncaught exception...');
        process.exit(1);
    }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Promise Rejection:');
    console.error(reason);
    
    // Don't exit, just log the error
});
