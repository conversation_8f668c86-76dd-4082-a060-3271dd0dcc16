// TradovateSocket.js
// Node.js version of TradovateSocket for Tradovate API

const WebSocket = require('ws');

// URLs for Tradovate API
const URLS = {
    DEMO_URL: 'https://demo.tradovateapi.com/v1',
    LIVE_URL: 'https://live.tradovateapi.com/v1',
    WS_DEMO_URL: 'wss://demo.tradovateapi.com/v1/websocket',
    WS_LIVE_URL: 'wss://live.tradovateapi.com/v1/websocket',
    MD_DEMO_WS_URL: 'wss://md.tradovateapi.com/v1/websocket',
    MD_LIVE_WS_URL: 'wss://md-live.tradovateapi.com/v1/websocket'
};

/**
 * TradovateSocket constructor
 * @constructor
 * @param {Object} options - Constructor options
 * @param {string} options.debugLabel - Label for debugging
 */
function TradovateSocket(options = {}) {
    let counter = 0

    this.ws = null
    this.listeners = {}
    this.subscriptions = {}
    this.lastHeartbeatTime = new Date()
    this.heartbeatInterval = 2500 // 2.5 seconds
    this.isConnected = false
    this.isAuthorized = false
    this.debugLabel = options.debugLabel || 'TradovateSocket'

    /**
     * Increment and return the request counter
     * @returns {number} The incremented counter
     */
    this.increment = function() {
        return counter++
    }
}

/**
 * Send a request to the WebSocket
 * @param {Object} options - Request options
 * @param {string} options.url - Request URL/operation
 * @param {string} options.query - Query string
 * @param {Object} options.body - Request body
 * @param {Function} options.onResponse - Callback for successful response
 * @param {Function} options.onReject - Callback for failed response
 * @returns {Promise<Object>} Promise that resolves with the response
 */
TradovateSocket.prototype.send = function({ url, query, body, onResponse, onReject }) {
    const self = this

    return new Promise((resolve, reject) => {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            const error = new Error('WebSocket not connected')
            console.error(error)
            if (onReject) onReject(error)
            reject(error)
            return
        }

        const id = this.increment()
        const request = `${url}\n${id}\n${query || ''}\n${body ? JSON.stringify(body) : ''}`

        console.log(`Sending request (ID: ${id}):`, request)

        // Create a one-time message handler for this specific request
        const messageHandler = function(data) {
            const [type, parsedData] = self.prepareMessage(data.toString())

            if (type !== 'a' || !Array.isArray(parsedData)) return

            for (const item of parsedData) {
                // Check if this response is for our request
                if (item.i === id) {
                    // Remove the listener once we've found our response
                    self.ws.removeListener('message', messageHandler)

                    // Handle success response
                    if (item.s === 200) {
                        console.log(`Received successful response for request ID ${id}:`, item)
                        if (onResponse) onResponse(item.d || item)
                        resolve(item.d || item)
                    }
                    // Handle error response
                    else {
                        const error = new Error(`Request failed: ${JSON.stringify(item.d || 'Unknown error')}`)
                        console.error(`Request failed for ID ${id}:`, item)
                        if (onReject) onReject(error)
                        reject(error)
                    }
                    return
                }
            }
        }

        // Add the message handler
        this.ws.on('message', messageHandler)

        // Send the request
        this.ws.send(request)
    })
}

/**
 * Subscribe to real-time updates
 * @param {Object} options - Subscription options
 * @param {string} options.url - Subscription URL/operation
 * @param {string} options.query - Query string
 * @param {Object} options.body - Request body
 * @param {Function} options.subscription - Callback for subscription updates
 * @returns {Promise<Object>} Promise that resolves with the initial response
 */
TradovateSocket.prototype.subscribe = function({ url, query, body, subscription }) {
    const self = this

    return new Promise((resolve, reject) => {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            const error = new Error('WebSocket not connected')
            console.error(error)
            reject(error)
            return
        }

        const id = this.increment()
        const request = `${url}\n${id}\n${query || ''}\n${body ? JSON.stringify(body) : ''}`

        console.log(`Setting up subscription (ID: ${id}):`, request)

        // Create a subscription handler
        const subscriptionHandler = function(data) {
            const [type, parsedData] = self.prepareMessage(data.toString())

            if (type !== 'a' || !Array.isArray(parsedData)) return

            for (const item of parsedData) {
                // Initial response has the same ID as our request
                if (item.i === id && !self.subscriptions[id]) {
                    if (item.s === 200) {
                        console.log(`Subscription established for ID ${id}`)

                        // Store the subscription
                        self.subscriptions[id] = {
                            url,
                            callback: subscription
                        }

                        // Call the subscription callback with the initial data
                        if (subscription) subscription(item.d || item)

                        resolve(item.d || item)
                    } else {
                        // Remove the listener if subscription failed
                        self.ws.removeListener('message', subscriptionHandler)

                        const error = new Error(`Subscription failed: ${JSON.stringify(item.d || 'Unknown error')}`)
                        console.error(`Subscription failed for ID ${id}:`, item)
                        reject(error)
                    }
                    return
                }

                // Subsequent updates for this subscription
                if (self.subscriptions[id] && item.d && subscription) {
                    subscription(item.d)
                }
            }
        }

        // Add the subscription handler
        this.ws.on('message', subscriptionHandler)

        // Send the subscription request
        this.ws.send(request)
    })
}

/**
 * Unsubscribe from a subscription
 * @param {number} id - Subscription ID
 */
TradovateSocket.prototype.unsubscribe = function(id) {
    if (this.subscriptions && this.subscriptions[id]) {
        delete this.subscriptions[id]
        console.log(`Unsubscribed from subscription ID ${id}`)
    }
}

/**
 * Connect to the Tradovate WebSocket
 * @param {string} url - WebSocket URL
 * @param {string} accessToken - Access token for authorization
 * @param {Function} onConnected - Callback when connected
 * @returns {Promise} Promise that resolves when connected and authorized
 */
TradovateSocket.prototype.connect = function(url = URLS.WS_DEMO_URL, accessToken, onConnected = null) {
    const self = this

    return new Promise((resolve, reject) => {
        try {
            console.log('Connecting to WebSocket...')

            // Create WebSocket connection
            this.ws = new WebSocket(url)

            // Set up message handler
            this.ws.on('message', function handleMessage(data) {
                const [type, parsedData] = self.prepareMessage(data.toString())

                // Check if we need to send a heartbeat
                const now = new Date()
                if (now.getTime() - self.lastHeartbeatTime.getTime() >= self.heartbeatInterval) {
                    self.sendHeartbeat()
                    self.lastHeartbeatTime = now
                }

                // Handle different message types
                switch (type) {
                    case 'o':
                        console.log('WebSocket connection opened')
                        self.isConnected = true

                        // Send authorization request
                        if (accessToken) {
                            console.log('Sending authorization request...')
                            self.send({
                                url: 'authorize',
                                body: accessToken
                            })
                        } else {
                            console.error('No access token provided')
                            reject(new Error('No access token provided'))
                        }
                        break

                    case 'h':
                        console.log('Received server heartbeat')
                        break

                    case 'a':
                        console.log('Received data:', parsedData)

                        // Check for authorization response
                        if (!self.isAuthorized && parsedData.length > 0 && parsedData[0].s === 200 && parsedData[0].i === 0) {
                            console.log('Successfully authorized')
                            self.isAuthorized = true

                            if (onConnected) {
                                onConnected(self)
                            }

                            resolve(self)
                        }

                        // Notify listeners
                        self.notifyListeners(parsedData)
                        break

                    case 'c':
                        console.log('WebSocket connection closed by server')
                        self.isConnected = false
                        self.isAuthorized = false
                        break

                    default:
                        console.error('Unexpected message type:', type)
                        break
                }
            })

            // Set up error handler
            this.ws.on('error', function handleError(error) {
                console.error('WebSocket error:', error)
                reject(error)
            })

            // Set up close handler
            this.ws.on('close', function handleClose(code, reason) {
                console.log('WebSocket connection closed:', code, reason)
                self.isConnected = false
                self.isAuthorized = false
            })

            // Set up open handler
            this.ws.on('open', function handleOpen() {
                console.log('WebSocket connection opened')
            })
        } catch (error) {
            console.error('Error connecting to WebSocket:', error)
            reject(error)
        }
    })
}

/**
 * Prepare a WebSocket message for processing
 * @param {string} raw - Raw message data
 * @returns {Array} Array containing message type and parsed data
 */
TradovateSocket.prototype.prepareMessage = function(raw) {
    const type = raw.slice(0, 1)
    let data = []

    if (raw.length > 1) {
        try {
            data = JSON.parse(raw.slice(1))
        } catch (error) {
            console.error('Error parsing message data:', error)
        }
    }

    return [type, data]
}

/**
 * Send a heartbeat to keep the connection alive
 */
TradovateSocket.prototype.sendHeartbeat = function() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log('Sending heartbeat')
        this.ws.send('[]')
    }
}

/**
 * Add a listener for WebSocket responses
 * @param {string} id - Listener ID
 * @param {Function} callback - Callback function
 */
TradovateSocket.prototype.addListener = function(id, callback) {
    this.listeners[id] = callback
}

/**
 * Remove a listener
 * @param {string} id - Listener ID
 */
TradovateSocket.prototype.removeListener = function(id) {
    delete this.listeners[id]
}

/**
 * Notify all listeners of a response
 * @param {Array} data - Response data
 */
TradovateSocket.prototype.notifyListeners = function(data) {
    if (!Array.isArray(data)) return

    for (const response of data) {
        for (const id in this.listeners) {
            this.listeners[id](response)
        }
    }
}

/**
 * Disconnect from the WebSocket
 */
TradovateSocket.prototype.disconnect = function() {
    if (this.ws) {
        this.ws.close()
        this.ws = null
        this.isConnected = false
        this.isAuthorized = false
    }
}

module.exports = TradovateSocket;
