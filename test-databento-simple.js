/**
 * Simple script to test Databento API
 * Based on the Python examples from the documentation
 */

require('dotenv').config();
const axios = require('axios');

// Check if API key is available
if (!process.env.DATABENTO_API_KEY || process.env.DATABENTO_API_KEY === 'your_databento_api_key') {
  console.error('DATABENTO_API_KEY environment variable is required. Please update your .env file with your actual Databento API key.');
  process.exit(1);
}

console.log(`Using Databento API key: ${process.env.DATABENTO_API_KEY.substring(0, 5)}...`);

// Create a client with the API key
const client = axios.create({
  baseURL: 'https://hist.databento.com/v0',
  headers: {
    'Authorization': `Basic ${Buffer.from(`${process.env.DATABENTO_API_KEY}:`).toString('base64')}`,
    'Content-Type': 'application/json'
  }
});

// Test functions
async function testHistorical() {
  try {
    console.log('Testing historical data...');
    
    // Get available datasets
    console.log('Getting available datasets...');
    const datasetsResponse = await client.get('/metadata.list_datasets');
    console.log(`Found ${datasetsResponse.data.length} datasets`);
    datasetsResponse.data.forEach(dataset => {
      console.log(`- ${dataset.dataset}: ${dataset.description}`);
    });
    
    // Get symbols for GLBX.MDP3 dataset
    console.log('\nGetting symbols for GLBX.MDP3 dataset...');
    const symbolsResponse = await client.get('/metadata.list_symbols', {
      params: { dataset: 'GLBX.MDP3' }
    });
    console.log(`Found ${symbolsResponse.data.length} symbols`);
    
    // Find our target symbols
    const targetSymbols = ['MNQ.FUT', 'ES.FUT', 'GC.FUT', 'RTY.FUT'];
    const foundSymbols = symbolsResponse.data.filter(s => targetSymbols.includes(s.symbol));
    console.log('\nFound target symbols:');
    foundSymbols.forEach(s => {
      console.log(`- ${s.symbol}: ${s.description}`);
    });
    
    return true;
  } catch (error) {
    console.error('Error testing historical data:', error.response ? 
      `${error.response.status} ${error.response.statusText} - ${JSON.stringify(error.response.data)}` : 
      error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('Starting Databento simple tests...');
  
  let success = true;
  
  // Test historical data
  if (await testHistorical()) {
    console.log('\n✅ Historical data test passed');
  } else {
    console.log('\n❌ Historical data test failed');
    success = false;
  }
  
  console.log('\nTests completed.');
  if (success) {
    console.log('All tests passed! 🎉');
  } else {
    console.log('Some tests failed. 😢');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
