// TradovateSocket.js
// Encapsulates WebSocket logic for Tradovate API

import { URLs } from '../tutorialsURLs.js'
import { getAccessToken } from '../src/storage.js'

const { WS_DEMO_URL } = URLs

/**
 * TradovateSocket constructor
 * @constructor
 * @param {Object} options - Constructor options
 * @param {string} options.debugLabel - Label for debugging
 */
export function TradovateSocket(options = {}) {
    let counter = 0

    this.ws = null
    this.listeners = {}
    this.subscriptions = {}
    this.lastHeartbeatTime = new Date()
    this.heartbeatInterval = 2500 // 2.5 seconds
    this.isConnected = false
    this.isAuthorized = false
    this.debugLabel = options.debugLabel || 'TradovateSocket'

    /**
     * Increment and return the request counter
     * @returns {number} The incremented counter
     */
    this.increment = function() {
        return counter++
    }
}

/**
 * Send a request to the WebSocket
 * @param {Object} options - Request options
 * @param {string} options.url - Request URL/operation
 * @param {string} options.query - Query string
 * @param {Object} options.body - Request body
 * @param {Function} options.onResponse - Callback for successful response
 * @param {Function} options.onReject - Callback for failed response
 * @returns {Promise<Object>} Promise that resolves with the response
 */
TradovateSocket.prototype.send = function({ url, query, body, onResponse, onReject }) {
    const self = this

    return new Promise((resolve, reject) => {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            const error = new Error('WebSocket not connected')
            console.error(error)
            if (onReject) onReject(error)
            reject(error)
            return
        }

        const id = this.increment()
        const request = `${url}\n${id}\n${query || ''}\n${body ? JSON.stringify(body) : ''}`

        console.log(`[${this.debugLabel}] Sending request (ID: ${id}):`, request)

        // Create a one-time message handler for this specific request
        const messageHandler = function(msg) {
            const [type, data] = self.prepareMessage(msg.data)

            if (type !== 'a' || !Array.isArray(data)) return

            for (const item of data) {
                // Check if this response is for our request
                if (item.i === id) {
                    // Remove the listener once we've found our response
                    self.ws.removeEventListener('message', messageHandler)

                    // Handle success response
                    if (item.s === 200) {
                        console.log(`[${self.debugLabel}] Received successful response for request ID ${id}:`, item)
                        if (onResponse) onResponse(item.d || item)
                        resolve(item.d || item)
                    }
                    // Handle error response
                    else {
                        const error = new Error(`Request failed: ${JSON.stringify(item.d || 'Unknown error')}`)
                        console.error(`[${self.debugLabel}] Request failed for ID ${id}:`, item)
                        if (onReject) onReject(error)
                        reject(error)
                    }
                    return
                }
            }
        }

        // Add the message handler
        this.ws.addEventListener('message', messageHandler)

        // Send the request
        this.ws.send(request)
    })
}

/**
 * Subscribe to real-time updates
 * @param {Object} options - Subscription options
 * @param {string} options.url - Subscription URL/operation
 * @param {string} options.query - Query string
 * @param {Object} options.body - Request body
 * @param {Function} options.subscription - Callback for subscription updates
 * @returns {Promise<Object>} Promise that resolves with the initial response
 */
TradovateSocket.prototype.subscribe = function({ url, query, body, subscription }) {
    const self = this

    return new Promise((resolve, reject) => {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            const error = new Error('WebSocket not connected')
            console.error(error)
            reject(error)
            return
        }

        const id = this.increment()
        const request = `${url}\n${id}\n${query || ''}\n${body ? JSON.stringify(body) : ''}`

        console.log(`[${this.debugLabel}] Setting up subscription (ID: ${id}):`, request)

        // Create a subscription handler
        const subscriptionHandler = function(msg) {
            const [type, data] = self.prepareMessage(msg.data)

            if (type !== 'a' || !Array.isArray(data)) return

            for (const item of data) {
                // Initial response has the same ID as our request
                if (item.i === id && !self.subscriptions[id]) {
                    if (item.s === 200) {
                        console.log(`[${self.debugLabel}] Subscription established for ID ${id}`)

                        // Store the subscription
                        self.subscriptions[id] = {
                            url,
                            callback: subscription
                        }

                        // Call the subscription callback with the initial data
                        if (subscription) subscription(item.d || item)

                        resolve(item.d || item)
                    } else {
                        // Remove the listener if subscription failed
                        self.ws.removeEventListener('message', subscriptionHandler)

                        const error = new Error(`Subscription failed: ${JSON.stringify(item.d || 'Unknown error')}`)
                        console.error(`[${self.debugLabel}] Subscription failed for ID ${id}:`, item)
                        reject(error)
                    }
                    return
                }

                // Subsequent updates for this subscription
                if (self.subscriptions[id] && item.d && subscription) {
                    subscription(item.d)
                }
            }
        }

        // Add the subscription handler
        this.ws.addEventListener('message', subscriptionHandler)

        // Initialize subscriptions object if it doesn't exist
        if (!this.subscriptions) {
            this.subscriptions = {}
        }

        // Send the subscription request
        this.ws.send(request)
    })
}

/**
 * Unsubscribe from a subscription
 * @param {number} id - Subscription ID
 */
TradovateSocket.prototype.unsubscribe = function(id) {
    if (this.subscriptions && this.subscriptions[id]) {
        delete this.subscriptions[id]
        console.log(`[${this.debugLabel}] Unsubscribed from subscription ID ${id}`)
        return true
    }
    return false
}

/**
 * Connect to the Tradovate WebSocket
 * @param {string} url - WebSocket URL
 * @param {Function} onConnected - Callback when connected
 * @returns {Promise} Promise that resolves when connected and authorized
 */
TradovateSocket.prototype.connect = function(url = WS_DEMO_URL, onConnected = null) {
    const self = this

    return new Promise((resolve, reject) => {
        try {
            console.log(`[${this.debugLabel}] Connecting to WebSocket...`)

            // Create WebSocket connection
            this.ws = new WebSocket(url)

            // Set up message handler
            this.ws.addEventListener('message', function handleMessage(msg) {
                const [type, data] = self.prepareMessage(msg.data)

                // Check if we need to send a heartbeat
                const now = new Date()
                if (now.getTime() - self.lastHeartbeatTime.getTime() >= self.heartbeatInterval) {
                    self.sendHeartbeat()
                    self.lastHeartbeatTime = now
                }

                // Handle different message types
                switch (type) {
                    case 'o':
                        console.log(`[${self.debugLabel}] WebSocket connection opened`)
                        self.isConnected = true

                        // Send authorization request
                        const { token } = getAccessToken()
                        if (token) {
                            console.log(`[${self.debugLabel}] Sending authorization request...`)
                            self.send({
                                url: 'authorize',
                                body: token
                            })
                        } else {
                            console.error('No access token found')
                            reject(new Error('No access token found'))
                        }
                        break

                    case 'h':
                        console.log(`[${self.debugLabel}] Received server heartbeat`)
                        break

                    case 'a':
                        console.log(`[${self.debugLabel}] Received data:`, data)

                        // Check for authorization response
                        if (!self.isAuthorized && data.length > 0 && data[0].s === 200 && data[0].i === 0) {
                            console.log(`[${self.debugLabel}] Successfully authorized`)
                            self.isAuthorized = true

                            if (onConnected) {
                                onConnected(self)
                            }

                            resolve(self)
                        }

                        // Notify listeners
                        self.notifyListeners(data)
                        break

                    case 'c':
                        console.log(`[${self.debugLabel}] WebSocket connection closed by server`)
                        self.isConnected = false
                        self.isAuthorized = false
                        break

                    default:
                        console.error(`[${self.debugLabel}] Unexpected message type:`, type)
                        break
                }
            })

            // Set up error handler
            this.ws.addEventListener('error', function handleError(error) {
                console.error(`[${self.debugLabel}] WebSocket error:`, error)
                reject(error)
            })

            // Set up close handler
            this.ws.addEventListener('close', function handleClose(event) {
                console.log(`[${self.debugLabel}] WebSocket connection closed:`, event.code, event.reason)
                self.isConnected = false
                self.isAuthorized = false
            })
        } catch (error) {
            console.error(`[${this.debugLabel}] Error connecting to WebSocket:`, error)
            reject(error)
        }
    })
}

/**
 * Prepare a WebSocket message for processing
 * @param {string} raw - Raw message data
 * @returns {Array} Array containing message type and parsed data
 */
TradovateSocket.prototype.prepareMessage = function(raw) {
    const type = raw.slice(0, 1)
    let data = []

    if (raw.length > 1) {
        try {
            data = JSON.parse(raw.slice(1))
        } catch (error) {
            console.error('Error parsing message data:', error)
        }
    }

    return [type, data]
}

/**
 * Send a heartbeat to keep the connection alive
 */
TradovateSocket.prototype.sendHeartbeat = function() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log(`[${this.debugLabel}] Sending heartbeat`)
        this.ws.send('[]')
    }
}

/**
 * Add a listener for WebSocket responses
 * @param {string} id - Listener ID
 * @param {Function} callback - Callback function
 */
TradovateSocket.prototype.addListener = function(id, callback) {
    this.listeners[id] = callback
}

/**
 * Remove a listener
 * @param {string} id - Listener ID
 */
TradovateSocket.prototype.removeListener = function(id) {
    delete this.listeners[id]
}

/**
 * Notify all listeners of a response
 * @param {Array} data - Response data
 */
TradovateSocket.prototype.notifyListeners = function(data) {
    if (!Array.isArray(data)) return

    for (const response of data) {
        for (const id in this.listeners) {
            this.listeners[id](response)
        }
    }
}

/**
 * Disconnect from the WebSocket
 */
TradovateSocket.prototype.disconnect = function() {
    if (this.ws) {
        this.ws.close()
        this.ws = null
        this.isConnected = false
        this.isAuthorized = false
    }
}
