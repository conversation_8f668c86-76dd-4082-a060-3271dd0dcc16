// websocket-node-example.js
// Node.js WebSocket example for Tradovate API

const fetch = require('node-fetch');
const TradovateSocket = require('./TradovateSocket');

// API URLs
const URLS = {
    DEMO_URL: 'https://demo.tradovateapi.com/v1',
    LIVE_URL: 'https://live.tradovateapi.com/v1',
    WS_DEMO_URL: 'wss://demo.tradovateapi.com/v1/websocket',
    WS_LIVE_URL: 'wss://live.tradovateapi.com/v1/websocket',
    MD_DEMO_WS_URL: 'wss://md.tradovateapi.com/v1/websocket',
    MD_LIVE_WS_URL: 'wss://md-live.tradovateapi.com/v1/websocket'
};

// API credentials
const credentials = {
    name: "bravesbeatmets",
    password: "Braves12$",
    appId: "Trading Bot",
    appVersion: "1.0",
    cid: "6186",
    sec: "69311dad-75a7-49c6-9958-00ab2c4f1ab6",
    deviceId: "example-" + Math.random().toString(36).substring(2, 15) +
              Math.random().toString(36).substring(2, 15)
};

// In-memory storage for tokens
let accessToken = null;
let tokenExpiration = null;

/**
 * Make a POST request to the Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {boolean} auth - Whether to include auth header
 * @returns {Promise<Object>} JSON response
 */
const tvPost = async (endpoint, data = {}, auth = true) => {
    // Build request options
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    };

    // Add auth header if needed
    if (auth && accessToken) {
        options.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Authenticate with the Tradovate API
 * @returns {Promise<boolean>} Whether authentication was successful
 */
const authenticate = async () => {
    try {
        console.log('Authenticating with Tradovate API...');

        // Request access token
        const response = await tvPost('/auth/accesstokenrequest', credentials, false);

        if (response.accessToken) {
            console.log('Authentication successful!');
            accessToken = response.accessToken;
            tokenExpiration = response.expirationTime;
            return true;
        } else {
            console.error('Authentication failed:', response.errorText || 'Unknown error');
            return false;
        }
    } catch (error) {
        console.error('Error authenticating:', error);
        return false;
    }
};

/**
 * Process WebSocket response
 * @param {Object} response - Response object
 */
const processResponse = (response) => {
    // Check for success response (status 200)
    if (response.s === 200) {
        console.log('Successful response for request ID:', response.i);

        // Handle specific response types
        if (response.d) {
            console.log('Response data:', response.d);
        }
    }
    // Check for error response
    else if (response.s >= 400) {
        console.error('Error response:', response);
    }
};

/**
 * Process user sync data
 * @param {Object} data - User sync data
 */
const processUserSyncData = (data) => {
    if (data.users) {
        console.log('\n=== INITIAL USER SYNC DATA ===');
        console.log('Users:', data.users.length);
        console.log('Accounts:', data.accounts ? data.accounts.length : 0);
        console.log('Positions:', data.positions ? data.positions.length : 0);
        console.log('Orders:', data.orders ? data.orders.length : 0);
        console.log('Fills:', data.fills ? data.fills.length : 0);
        console.log('Contracts:', data.contracts ? data.contracts.length : 0);
        console.log('Products:', data.products ? data.products.length : 0);
    } else if (data.entity) {
        console.log('\n=== USER SYNC UPDATE ===');
        console.log('Entity Type:', data.entityType);
        console.log('Event Type:', data.eventType);
        console.log('Entity:', data.entity);
    }
};

/**
 * Format ETH product details for console display
 * @param {Object} product - ETH product data
 */
const displayETHDetails = (product) => {
    console.log('\n=== ETH PRODUCT DETAILS ===');
    console.log(`Name: ${product.name}`);
    console.log(`Description: ${product.description || 'No description available'}`);
    console.log(`Currency ID: ${product.currencyId == 1 ? '$' : product.currencyId}`);
    console.log(`Product Type: ${product.productType}`);
    console.log(`Status: ${product.status}`);
    console.log(`Tick Size: ${product.tickSize}`);
    console.log(`Value Per Point: ${product.valuePerPoint}`);
    console.log(`Is Micro: ${product.isMicro}`);
    console.log(`Exchange ID: ${product.exchangeId}`);
    console.log(`Contract Group ID: ${product.contractGroupId}`);
};

/**
 * Run the WebSocket example
 */
const runWebSocketExample = async () => {
    try {
        // First authenticate to get an access token
        const authenticated = await authenticate();

        if (!authenticated) {
            console.error('Cannot connect to WebSocket without authentication');
            return;
        }

        console.log('Creating TradovateSocket...');
        const tradovateSocket = new TradovateSocket();

        // Add a listener for responses
        tradovateSocket.addListener('main', processResponse);

        // Connect to the WebSocket
        await tradovateSocket.connect(URLS.WS_DEMO_URL, accessToken, (socket) => {
            console.log('WebSocket connected and authorized');
        });

        // Example 1: Get user accounts
        console.log('\nExample 1: Getting user accounts...');
        try {
            const accounts = await tradovateSocket.send({ url: 'user/accounts' });
            console.log('User accounts:', accounts);
        } catch (error) {
            console.error('Error fetching user accounts:', error);
        }

        // Example 2: Get ETH product details
        console.log('\nExample 2: Getting ETH product details...');
        try {
            const ethProduct = await tradovateSocket.send({
                url: 'product/find',
                query: 'name=ETH'
            });
            displayETHDetails(ethProduct);
        } catch (error) {
            console.error('Error fetching ETH product details:', error);
        }

        // Example 3: Subscribe to user sync updates
        console.log('\nExample 3: Setting up user sync subscription...');
        try {
            await tradovateSocket.subscribe({
                url: 'user/syncrequest',
                subscription: processUserSyncData
            });
            console.log('User sync subscription established');

            // Keep the connection open for a while to receive updates
            console.log('\nKeeping connection open for 10 seconds to receive updates...');
            await new Promise(resolve => setTimeout(resolve, 10000));
        } catch (error) {
            console.error('Error setting up user sync subscription:', error);
        }

        // Close the connection
        console.log('\nClosing WebSocket connection...');
        tradovateSocket.disconnect();
        process.exit(0);
    } catch (error) {
        console.error('Error running WebSocket example:', error);
    }
};

/**
 * Run the market data example with quotes
 */
const runQuoteExample = async () => {
    try {
        // First authenticate to get an access token
        const authenticated = await authenticate();

        if (!authenticated) {
            console.error('Cannot connect to WebSocket without authentication');
            return;
        }

        console.log('Creating market data TradovateSocket...');
        const marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' });

        // Connect to the market data WebSocket
        await marketDataSocket.connect(URLS.MD_DEMO_WS_URL, accessToken, (socket) => {
            console.log('Market data WebSocket connected and authorized');
        });

        // Example: Subscribe to quotes for a symbol
        const symbol = 'MNQM3'; // Example symbol - Micro E-mini Nasdaq-100 June 2023
        console.log(`\nSubscribing to quotes for ${symbol}...`);

        try {
            // Set up subscription to quotes
            const unsubscribe = await marketDataSocket.subscribe({
                url: 'md/subscribequote',
                body: { symbol },
                subscription: (data) => {
                    // Process quote data
                    if (data && data.quotes && data.quotes.length > 0) {
                        const quote = data.quotes[0];
                        console.log(`\n=== QUOTE UPDATE FOR ${symbol} ===`);
                        console.log('Timestamp:', quote.timestamp);
                        console.log('Contract ID:', quote.contractId);

                        // Display entries
                        if (quote.entries) {
                            Object.keys(quote.entries).forEach(entryType => {
                                const entry = quote.entries[entryType];
                                console.log(`${entryType}:`, entry);
                            });
                        }
                    }
                }
            });

            console.log(`Successfully subscribed to quotes for ${symbol}`);

            // Keep the connection open for a while to receive updates
            console.log('\nKeeping connection open for 30 seconds to receive quote updates...');
            await new Promise(resolve => setTimeout(resolve, 30000));

            // Unsubscribe
            console.log(`\nUnsubscribing from quotes for ${symbol}...`);
            unsubscribe();
            console.log('Successfully unsubscribed from quotes');

            // Close the connection
            console.log('\nClosing WebSocket connection...');
            marketDataSocket.disconnect();
            process.exit(0);
        } catch (error) {
            console.error('Error in market data subscription:', error);
            marketDataSocket.disconnect();
            process.exit(1);
        }
    } catch (error) {
        console.error('Error running market data example:', error);
    }
};

/**
 * Run the market data example with DOM
 */
const runDOMExample = async () => {
    try {
        // First authenticate to get an access token
        const authenticated = await authenticate();

        if (!authenticated) {
            console.error('Cannot connect to WebSocket without authentication');
            return;
        }

        console.log('Creating market data TradovateSocket...');
        const marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' });

        // Connect to the market data WebSocket
        await marketDataSocket.connect(URLS.MD_DEMO_WS_URL, accessToken, (socket) => {
            console.log('Market data WebSocket connected and authorized');
        });

        // Example: Subscribe to DOM for a symbol
        const symbol = 'MNQM3'; // Example symbol - Micro E-mini Nasdaq-100 June 2023
        console.log(`\nSubscribing to DOM for ${symbol}...`);

        try {
            // Set up subscription to DOM
            const unsubscribe = await marketDataSocket.subscribe({
                url: 'md/subscribedom',
                body: { symbol },
                subscription: (data) => {
                    // Process DOM data
                    if (data && data.doms && data.doms.length > 0) {
                        const dom = data.doms[0];
                        console.log(`\n=== DOM UPDATE FOR ${symbol} ===`);
                        console.log('Timestamp:', dom.timestamp);
                        console.log('Contract ID:', dom.contractId);

                        // Display bids
                        console.log('\nBIDS:');
                        if (dom.bids && dom.bids.length > 0) {
                            dom.bids.forEach((bid, index) => {
                                console.log(`  ${index + 1}. Price: ${bid.price}, Size: ${bid.size}`);
                            });
                        } else {
                            console.log('  No bids available');
                        }

                        // Display offers
                        console.log('\nOFFERS:');
                        if (dom.offers && dom.offers.length > 0) {
                            dom.offers.forEach((offer, index) => {
                                console.log(`  ${index + 1}. Price: ${offer.price}, Size: ${offer.size}`);
                            });
                        } else {
                            console.log('  No offers available');
                        }
                    }
                }
            });

            console.log(`Successfully subscribed to DOM for ${symbol}`);

            // Keep the connection open for a while to receive updates
            console.log('\nKeeping connection open for 30 seconds to receive DOM updates...');
            await new Promise(resolve => setTimeout(resolve, 30000));

            // Unsubscribe
            console.log(`\nUnsubscribing from DOM for ${symbol}...`);
            unsubscribe();
            console.log('Successfully unsubscribed from DOM');

            // Close the connection
            console.log('\nClosing WebSocket connection...');
            marketDataSocket.disconnect();
            process.exit(0);
        } catch (error) {
            console.error('Error in DOM subscription:', error);
            marketDataSocket.disconnect();
            process.exit(1);
        }
    } catch (error) {
        console.error('Error running DOM example:', error);
    }
};

/**
 * Run the market data example with histogram
 */
const runHistogramExample = async () => {
    try {
        // First authenticate to get an access token
        const authenticated = await authenticate();

        if (!authenticated) {
            console.error('Cannot connect to WebSocket without authentication');
            return;
        }

        console.log('Creating market data TradovateSocket...');
        const marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' });

        // Connect to the market data WebSocket
        await marketDataSocket.connect(URLS.MD_DEMO_WS_URL, accessToken, (socket) => {
            console.log('Market data WebSocket connected and authorized');
        });

        // Example: Subscribe to histogram for a symbol
        const symbol = 'MNQM3'; // Example symbol - Micro E-mini Nasdaq-100 June 2023
        console.log(`\nSubscribing to histogram for ${symbol}...`);

        try {
            // Set up subscription to histogram
            const unsubscribe = await marketDataSocket.subscribe({
                url: 'md/subscribehistogram',
                body: { symbol },
                subscription: (data) => {
                    // Process histogram data
                    if (data && data.histograms && data.histograms.length > 0) {
                        const histogram = data.histograms[0];
                        console.log(`\n=== HISTOGRAM UPDATE FOR ${symbol} ===`);
                        console.log('Timestamp:', histogram.timestamp);
                        console.log('Contract ID:', histogram.contractId);
                        console.log('Base:', histogram.base);
                        console.log('Refresh:', histogram.refresh);

                        // Display trade date
                        if (histogram.tradeDate) {
                            console.log('Trade Date:',
                                `${histogram.tradeDate.year}-${histogram.tradeDate.month}-${histogram.tradeDate.day}`);
                        }

                        // Display items
                        console.log('\nITEMS:');
                        if (histogram.items && Object.keys(histogram.items).length > 0) {
                            Object.entries(histogram.items)
                                .sort(([offsetA], [offsetB]) => parseInt(offsetA) - parseInt(offsetB))
                                .forEach(([offset, value]) => {
                                    console.log(`  Offset ${offset}: ${value}`);
                                });
                        } else {
                            console.log('  No histogram items available');
                        }
                    }
                }
            });

            console.log(`Successfully subscribed to histogram for ${symbol}`);

            // Keep the connection open for a while to receive updates
            console.log('\nKeeping connection open for 30 seconds to receive histogram updates...');
            await new Promise(resolve => setTimeout(resolve, 30000));

            // Unsubscribe
            console.log(`\nUnsubscribing from histogram for ${symbol}...`);
            unsubscribe();
            console.log('Successfully unsubscribed from histogram');

            // Close the connection
            console.log('\nClosing WebSocket connection...');
            marketDataSocket.disconnect();
            process.exit(0);
        } catch (error) {
            console.error('Error in histogram subscription:', error);
            marketDataSocket.disconnect();
            process.exit(1);
        }
    } catch (error) {
        console.error('Error running histogram example:', error);
    }
};

/**
 * Run the chart data example with regular charts (OHLC)
 */
const runChartExample = async () => {
    try {
        // First authenticate to get an access token
        const authenticated = await authenticate();

        if (!authenticated) {
            console.error('Cannot connect to WebSocket without authentication');
            return;
        }

        console.log('Creating market data TradovateSocket...');
        const marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' });

        // Connect to the market data WebSocket
        await marketDataSocket.connect(URLS.MD_DEMO_WS_URL, accessToken, (socket) => {
            console.log('Market data WebSocket connected and authorized');
        });

        // Example: Get chart data for a symbol
        const symbol = 'MNQM3'; // Example symbol - Micro E-mini Nasdaq-100 June 2023
        const chartType = 'MinuteBar'; // MinuteBar, Tick, DailyBar
        const elementSize = 30; // 30 minutes
        const numElements = 100; // 100 bars

        console.log(`\nGetting chart data for ${symbol} (${chartType}, ${elementSize})...`);

        try {
            // Array to store all bars
            const allBars = [];

            // Set up subscription to chart data
            const unsubscribe = await marketDataSocket.subscribe({
                url: 'md/getchart',
                body: {
                    symbol: symbol,
                    chartDescription: {
                        underlyingType: chartType,
                        elementSize: elementSize,
                        elementSizeUnit: 'UnderlyingUnits',
                        withHistogram: false
                    },
                    timeRange: {
                        asMuchAsElements: numElements
                    }
                },
                subscription: (chart) => {
                    // Check for end of history
                    if (chart.eoh) {
                        console.log('\nEnd of chart history reached');

                        // Display summary of all bars
                        console.log(`\nReceived ${allBars.length} total bars`);

                        if (allBars.length > 0) {
                            const firstBar = allBars[0];
                            const lastBar = allBars[allBars.length - 1];

                            console.log(`Time range: ${new Date(firstBar.timestamp).toLocaleString()} to ${new Date(lastBar.timestamp).toLocaleString()}`);

                            // Calculate price range
                            const highPrices = allBars.map(bar => bar.high);
                            const lowPrices = allBars.map(bar => bar.low);
                            const highestPrice = Math.max(...highPrices);
                            const lowestPrice = Math.min(...lowPrices);

                            console.log(`Price range: ${lowestPrice} to ${highestPrice}`);
                        }

                        return;
                    }

                    // Process chart data
                    if (chart.bars && chart.bars.length > 0) {
                        console.log(`\n=== CHART DATA FOR ${symbol} ===`);
                        console.log(`Received ${chart.bars.length} bars`);

                        // Add bars to the allBars array
                        allBars.push(...chart.bars);

                        // Display first 5 bars
                        console.log('\nSample bars:');
                        chart.bars.slice(0, 5).forEach((bar, index) => {
                            console.log(`Bar ${index + 1}:`);
                            console.log(`  Timestamp: ${new Date(bar.timestamp).toLocaleString()}`);
                            console.log(`  OHLC: Open=${bar.open}, High=${bar.high}, Low=${bar.low}, Close=${bar.close}`);
                            console.log(`  Volume: Up=${bar.upVolume || 0}, Down=${bar.downVolume || 0}`);
                            console.log(`  Ticks: Up=${bar.upTicks || 0}, Down=${bar.downTicks || 0}`);
                            console.log(`  Bid/Offer Volume: Bid=${bar.bidVolume || 0}, Offer=${bar.offerVolume || 0}`);
                        });
                    }
                }
            });

            console.log(`Successfully requested chart data for ${symbol}`);

            // Keep the connection open for a while to receive all data
            console.log('\nKeeping connection open for 30 seconds to receive chart data...');
            await new Promise(resolve => setTimeout(resolve, 30000));

            // Unsubscribe
            console.log(`\nUnsubscribing from chart data for ${symbol}...`);
            unsubscribe();
            console.log('Successfully unsubscribed from chart data');

            // Close the connection
            console.log('\nClosing WebSocket connection...');
            marketDataSocket.disconnect();
            process.exit(0);
        } catch (error) {
            console.error('Error in chart data request:', error);
            marketDataSocket.disconnect();
            process.exit(1);
        }
    } catch (error) {
        console.error('Error running chart example:', error);
    }
};

/**
 * Run the tick chart data example
 */
const runTickChartExample = async () => {
    try {
        // First authenticate to get an access token
        const authenticated = await authenticate();

        if (!authenticated) {
            console.error('Cannot connect to WebSocket without authentication');
            return;
        }

        console.log('Creating market data TradovateSocket...');
        const marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' });

        // Connect to the market data WebSocket
        await marketDataSocket.connect(URLS.MD_DEMO_WS_URL, accessToken, (socket) => {
            console.log('Market data WebSocket connected and authorized');
        });

        // Example: Get tick chart data for a symbol
        const symbol = 'MNQM3'; // Example symbol - Micro E-mini Nasdaq-100 June 2023
        const chartType = 'Tick'; // Using Tick chart type
        const numElements = 100; // 100 ticks

        console.log(`\nGetting tick chart data for ${symbol}...`);

        try {
            // Array to store all ticks
            const allTicks = [];

            // Set up subscription to chart data
            const unsubscribe = await marketDataSocket.subscribe({
                url: 'md/getchart',
                body: {
                    symbol: symbol,
                    chartDescription: {
                        underlyingType: chartType,
                        elementSize: 1, // Always 1 for Tick charts
                        elementSizeUnit: 'UnderlyingUnits',
                        withHistogram: false
                    },
                    timeRange: {
                        asMuchAsElements: numElements
                    }
                },
                subscription: (chart) => {
                    // Check for end of history
                    if (chart.eoh) {
                        console.log('\nEnd of tick chart history reached');

                        // Display summary of all ticks
                        console.log(`\nReceived ${allTicks.length} total ticks`);

                        if (allTicks.length > 0) {
                            // Sort ticks by timestamp
                            allTicks.sort((a, b) => a.timestamp - b.timestamp);

                            const firstTick = allTicks[0];
                            const lastTick = allTicks[allTicks.length - 1];

                            console.log(`Time range: ${new Date(firstTick.timestamp).toLocaleString()} to ${new Date(lastTick.timestamp).toLocaleString()}`);

                            // Calculate price range
                            const prices = allTicks.map(tick => tick.price);
                            const highestPrice = Math.max(...prices);
                            const lowestPrice = Math.min(...prices);

                            console.log(`Price range: ${lowestPrice} to ${highestPrice}`);
                        }

                        return;
                    }

                    // Process tick chart data
                    if (chart.tks && chart.tks.length > 0) {
                        console.log(`\n=== TICK CHART DATA FOR ${symbol} ===`);
                        console.log(`Received packet with ${chart.tks.length} ticks`);
                        console.log(`Base timestamp: ${new Date(chart.bt).toLocaleString()}`);
                        console.log(`Base price: ${chart.bp}`);
                        console.log(`Tick size: ${chart.ts}`);

                        // Process ticks
                        const processedTicks = chart.tks.map(tick => {
                            const timestamp = chart.bt + tick.t;
                            const price = (chart.bp + tick.p) * chart.ts;

                            return {
                                timestamp,
                                price,
                                size: tick.s,
                                bid: tick.b !== undefined ? (chart.bp + tick.b) * chart.ts : undefined,
                                ask: tick.a !== undefined ? (chart.bp + tick.a) * chart.ts : undefined,
                                bidSize: tick.bs,
                                askSize: tick.as,
                                id: tick.id
                            };
                        });

                        // Add to all ticks
                        allTicks.push(...processedTicks);

                        // Display first 5 ticks
                        console.log('\nSample ticks:');
                        processedTicks.slice(0, 5).forEach((tick, index) => {
                            console.log(`Tick ${index + 1}:`);
                            console.log(`  Timestamp: ${new Date(tick.timestamp).toLocaleString()}`);
                            console.log(`  Price: ${tick.price}`);
                            console.log(`  Size: ${tick.size || 'N/A'}`);

                            if (tick.bid !== undefined) {
                                console.log(`  Bid: ${tick.bid}, Size: ${tick.bidSize || 'N/A'}`);
                            }

                            if (tick.ask !== undefined) {
                                console.log(`  Ask: ${tick.ask}, Size: ${tick.askSize || 'N/A'}`);
                            }

                            if (tick.id) {
                                console.log(`  ID: ${tick.id}`);
                            }
                        });
                    }
                }
            });

            console.log(`Successfully requested tick chart data for ${symbol}`);

            // Keep the connection open for a while to receive all data
            console.log('\nKeeping connection open for 30 seconds to receive tick data...');
            await new Promise(resolve => setTimeout(resolve, 30000));

            // Unsubscribe
            console.log(`\nUnsubscribing from tick chart data for ${symbol}...`);
            unsubscribe();
            console.log('Successfully unsubscribed from tick chart data');

            // Close the connection
            console.log('\nClosing WebSocket connection...');
            marketDataSocket.disconnect();
            process.exit(0);
        } catch (error) {
            console.error('Error in tick chart data request:', error);
            marketDataSocket.disconnect();
            process.exit(1);
        }
    } catch (error) {
        console.error('Error running tick chart example:', error);
    }
};

// Choose which example to run
const exampleToRun = process.argv[2] || 'main';

switch (exampleToRun) {
    case 'quote':
        console.log('Running quote example...');
        runQuoteExample();
        break;
    case 'dom':
        console.log('Running DOM example...');
        runDOMExample();
        break;
    case 'histogram':
        console.log('Running histogram example...');
        runHistogramExample();
        break;
    case 'chart':
        console.log('Running chart example...');
        runChartExample();
        break;
    case 'tick-chart':
        console.log('Running tick chart example...');
        runTickChartExample();
        break;
    default:
        console.log('Running main WebSocket example...');
        runWebSocketExample();
        break;
}
