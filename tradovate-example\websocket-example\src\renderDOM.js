// renderDOM.js
// Renders Depth of Market (DOM) data to HTML

/**
 * Render price and size information
 * @param {Object} data - Price and size data
 * @param {number} data.price - Price value
 * @param {number} data.size - Size value
 * @returns {string} HTML representation of price and size
 */
const renderPriceSize = ({ price, size }) => `
    <li class="dom-item">
        <span class="dom-price">${price}</span>
        <span class="dom-size">${size}</span>
    </li>
`;

/**
 * Render a bid or offer
 * @param {Object} item - Bid or offer data
 * @returns {string} HTML representation of the bid or offer
 */
const renderBidOffer = (item) => `
    <div class="dom-entry">
        ${renderPriceSize(item)}
    </div>
`;

/**
 * Render DOM data to HTML
 * @param {string} symbol - Contract symbol
 * @param {Object} domData - DOM data
 * @param {number} domData.contractId - Contract ID
 * @param {string} domData.timestamp - Timestamp
 * @param {Array} domData.bids - Bid data
 * @param {Array} domData.offers - Offer data
 * @returns {string} HTML representation of the DOM
 */
export const renderDOM = (symbol, {
    contractId,
    timestamp,
    bids = [],
    offers = []
}) => {
    const formattedTime = new Date(timestamp).toLocaleTimeString();
    
    return `
    <section class="dom-container">
        <div class="dom-header">
            <h2 class="dom-symbol">${symbol}</h2>
            <div class="dom-info">
                <span class="dom-contract-id">Contract ID: ${contractId}</span>
                <span class="dom-timestamp">Time: ${formattedTime}</span>
            </div>
        </div>
        
        <div class="dom-columns">
            <div class="dom-column">
                <h3 class="dom-column-header">Bids</h3>
                <div class="dom-column-labels">
                    <span class="dom-label">Price</span>
                    <span class="dom-label">Size</span>
                </div>
                <div class="dom-entries bids">
                    ${bids.length > 0 
                        ? bids.map(renderBidOffer).join('') 
                        : '<div class="dom-empty">No bids available</div>'}
                </div>
            </div>
            
            <div class="dom-column">
                <h3 class="dom-column-header">Offers</h3>
                <div class="dom-column-labels">
                    <span class="dom-label">Price</span>
                    <span class="dom-label">Size</span>
                </div>
                <div class="dom-entries offers">
                    ${offers.length > 0 
                        ? offers.map(renderBidOffer).join('') 
                        : '<div class="dom-empty">No offers available</div>'}
                </div>
            </div>
        </div>
    </section>
    `;
};
