#!/usr/bin/env python3
"""
Test script to check if NQ.FUT has live data from Databento
"""

import databento as db
import logging
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_nq_live():
    """Test live data for NQ.FUT"""
    try:
        api_key = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq"
        logger.info(f"Using API key: {api_key[:8]}...")

        logger.info("Creating Live client...")
        client = db.Live(key=api_key)
        logger.info("Live client created successfully")

        # Add callback to handle incoming data
        def record_callback(record):
            logger.info(f"Received record: {record}")

        logger.info("Adding callback...")
        client.add_callback(record_callback)
        logger.info("Callback added successfully")

        logger.info("Subscribing to NQ.FUT trades...")
        logger.info("Trying to subscribe to NQ.FUT...")
        client.subscribe(dataset='GLBX.MDP3', schema='trades', symbols=['NQ.FUT'], stype_in='parent')
        logger.info("Subscription to NQ.FUT successful")

        logger.info("Starting streaming session...")
        client.start()
        logger.info("Streaming session started")

        logger.info("Waiting for data (30 seconds)...")
        time.sleep(30)

        logger.info("Stopping client...")
        client.stop()
        logger.info("Test completed")

    except Exception as e:
        logger.error(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_nq_live()
