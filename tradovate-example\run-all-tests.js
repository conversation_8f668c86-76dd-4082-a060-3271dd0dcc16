/**
 * run-all-tests.js
 * Run all tests to see the current numbers
 */

const { runBacktest } = require('./verify-backtest');
const { mnqConfig, mesConfig, mgcConfig } = require('../multi_symbol_config');

// Function to run a test with a specific configuration
async function runTest(config) {
    console.log(`\n=== RUNNING TEST FOR ${config.symbol} ===\n`);
    
    console.log("Configuration:");
    console.log(`Symbol: ${config.symbol}`);
    console.log(`SL Factor: ${config.slFactors}`);
    console.log(`TP Factor: ${config.tpFactors}`);
    console.log(`Trail Factor: ${config.trailFactors}`);
    console.log(`Fixed TP Points: ${config.fixedTpPoints}`);
    console.log(`Commission: ${config.commissionPerContract}`);
    console.log(`Slippage: ${config.slippagePoints}`);
    console.log(`Fixed Contracts: ${config.fixedContracts}`);
    console.log(`ATR Low-Medium: ${config.atrThresholds.low_medium}`);
    console.log(`ATR Medium-High: ${config.atrThresholds.medium_high}`);
    console.log(`WMA Filter: ${config.useWmaFilter}`);
    console.log(`SMA200 Filter: ${config.useSma200Filter}`);
    console.log(`RSI Filter: ${config.useRsiFilter}`);
    
    // Run backtest
    const result = await runBacktest(config);
    
    // Calculate win rate
    const winRate = (result.wins / result.totalTrades * 100).toFixed(2);
    
    // Calculate P&L
    const pnl = result.finalBalance - config.initialBalance;
    
    // Display results
    console.log(`\nResults:`);
    console.log(`Total trades: ${result.totalTrades}`);
    console.log(`Wins: ${result.wins} (${winRate}%)`);
    console.log(`Losses: ${result.losses} (${(100 - parseFloat(winRate)).toFixed(2)}%)`);
    console.log(`P&L: $${pnl.toFixed(2)}`);
    console.log(`Max drawdown: $${result.maxDrawdown.toFixed(2)}`);
    
    return {
        symbol: config.symbol,
        totalTrades: result.totalTrades,
        wins: result.wins,
        losses: result.losses,
        winRate: `${winRate}%`,
        pnl: pnl.toFixed(2),
        maxDrawdown: result.maxDrawdown.toFixed(2)
    };
}

// Main function to run all tests
async function runAllTests() {
    const results = [];
    
    // Run MNQ test
    const mnqResult = await runTest(mnqConfig);
    results.push(mnqResult);
    
    // Run MES test
    const mesResult = await runTest(mesConfig);
    results.push(mesResult);
    
    // Run MGC test
    const mgcResult = await runTest(mgcConfig);
    results.push(mgcResult);
    
    // Display summary table
    console.log("\n=== SUMMARY OF ALL TESTS ===\n");
    console.table(results);
    
    // Calculate total P&L
    const totalPnL = results.reduce((sum, result) => sum + parseFloat(result.pnl), 0);
    console.log(`\nTotal P&L across all symbols: $${totalPnL.toFixed(2)}`);
    
    // Compare with original backtest
    console.log("\n=== COMPARISON WITH ORIGINAL BACKTEST ===\n");
    console.log("Symbol | Original Backtest | Our Implementation | Difference | % of Original");
    console.log("-------|-------------------|-------------------|------------|-------------");
    console.log(`MNQ    | $2,600,000        | $${mnqResult.pnl}   | -$${(2600000 - parseFloat(mnqResult.pnl)).toFixed(2)} | ${((parseFloat(mnqResult.pnl) / 2600000) * 100).toFixed(2)}%`);
    console.log(`MES    | $1,000,000        | $${mesResult.pnl}   | -$${(1000000 - parseFloat(mesResult.pnl)).toFixed(2)} | ${((parseFloat(mesResult.pnl) / 1000000) * 100).toFixed(2)}%`);
    console.log(`MGC    | $890,734          | $${mgcResult.pnl}   | -$${(890734 - parseFloat(mgcResult.pnl)).toFixed(2)} | ${((parseFloat(mgcResult.pnl) / 890734) * 100).toFixed(2)}%`);
    console.log(`TOTAL  | $4,490,734        | $${totalPnL.toFixed(2)}   | -$${(4490734 - totalPnL).toFixed(2)} | ${((totalPnL / 4490734) * 100).toFixed(2)}%`);
}

// Run all tests
runAllTests().catch(console.error);
