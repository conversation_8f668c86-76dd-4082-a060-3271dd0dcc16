/**
 * Check Tradovate Connection
 *
 * This script checks the connection to Tradovate API and Market Data.
 */

const fetch = require('node-fetch');

async function checkTradovateConnection() {
    console.log('Checking Tradovate API connection...');

    try {
        // Check Tradovate API
        console.log('Testing Tradovate API endpoint...');
        const apiResponse = await fetch('https://demo.tradovateapi.com/v1/auth/accesstokenrequest', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                name: 'bravesbeatmets',
                password: 'Braves12$',
                appId: 'Trading Bot',
                appVersion: '0.0.1',
                deviceId: '25e3f568-2890-5442-1e40-b9e8983af7e7',
                cid: '6186',
                sec: '69311dad-75a7-49c6-9958-00ab2c4f1ab6'
            })
        });

        console.log(`Tradovate API Status: ${apiResponse.status} ${apiResponse.statusText}`);
        console.log('Tradovate API connection successful!');
    } catch (error) {
        console.error('Error connecting to Tradovate API:', error.message);
    }

    try {
        // Check Market Data
        console.log('\nTesting Market Data endpoint...');
        // For Market Data, we'll just check if the domain is reachable
        const mdResponse = await fetch('https://md.tradovateapi.com', {
            method: 'HEAD'
        });

        console.log(`Market Data Status: ${mdResponse.status} ${mdResponse.statusText}`);
        console.log('Market Data connection successful!');
    } catch (error) {
        console.error('Error connecting to Market Data:', error.message);
    }

    try {
        // Check Internet connection
        console.log('\nTesting Internet connection...');
        const internetResponse = await fetch('https://www.google.com');

        console.log(`Internet Status: ${internetResponse.status} ${internetResponse.statusText}`);
        console.log('Internet connection successful!');
    } catch (error) {
        console.error('Error connecting to Internet:', error.message);
    }
}

checkTradovateConnection().catch(error => {
    console.error('Unexpected error:', error);
});
