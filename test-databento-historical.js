/**
 * Test script for Databento historical data
 */

const { spawn } = require('child_process');
const path = require('path');

// Spawn the Python bridge process
const pythonPath = 'python';
const scriptPath = path.join(__dirname, 'databento_bridge.py');

console.log('Starting Databento bridge...');
const bridge = spawn(pythonPath, [scriptPath]);

// Handle process exit
bridge.on('exit', (code, signal) => {
  console.log(`Databento bridge exited with code ${code} and signal ${signal}`);
});

// Handle process errors
bridge.on('error', (error) => {
  console.error(`Databento bridge error: ${error.message}`);
});

// Handle stdout (responses from the bridge)
bridge.stdout.on('data', (data) => {
  const lines = data.toString().trim().split('\n');

  for (const line of lines) {
    try {
      const response = JSON.parse(line);

      // If bridge is ready, send a command
      if (response.type === 'ready') {
        console.log('Bridge is ready, sending get_historical command...');

        // Get historical data for ES.FUT
        // Use a safer time range (2 hours ago to 1 hour ago)
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 1 * 60 * 60 * 1000);
        const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

        bridge.stdin.write(JSON.stringify({
          type: 'get_historical',
          params: {
            dataset: 'GLBX.MDP3',
            symbols: 'ES.FUT',
            start: twoHoursAgo.toISOString(),
            end: oneHourAgo.toISOString(),
            schema: 'ohlcv-1m',
            stype_in: 'parent'
          },
          request_id: 'test-historical'
        }) + '\n');
      }

      // If we get historical data, print it and exit
      if (response.type === 'historical') {
        console.log('Got historical data:');
        console.log(`Received ${response.data.length} records`);

        if (response.data.length > 0) {
          console.log('First record:');
          console.log(JSON.stringify(response.data[0], null, 2));
        }

        console.log('Test completed successfully');
        bridge.kill();
        process.exit(0);
      }

      // If we get an error, print it and exit
      if (response.type === 'error') {
        console.error('Error:', response.data.message);
        bridge.kill();
        process.exit(1);
      }
    } catch (error) {
      console.error(`Error parsing response: ${error.message}`);
    }
  }
});

// Handle stderr (logs from the bridge)
bridge.stderr.on('data', (data) => {
  console.error(`Databento bridge stderr: ${data.toString().trim()}`);
});

// Set a timeout to exit if we don't get a response
setTimeout(() => {
  console.log('Timeout reached, exiting...');
  bridge.kill();
  process.exit(1);
}, 30000);
