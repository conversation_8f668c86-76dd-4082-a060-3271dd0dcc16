import os
import re
import glob
from bs4 import BeautifulSoup
import shutil

# Directories
DASHBOARD_DIR = "C:/backtest-bot"

def find_dashboard_files():
    """Find all dashboard HTML files"""
    dashboard_files = []
    
    # Find all HTML files in the dashboard directory
    html_files = glob.glob(os.path.join(DASHBOARD_DIR, "*.html"))
    
    # Filter for dashboard files
    for file_path in html_files:
        file_name = os.path.basename(file_path)
        if "dashboard" in file_name.lower() or file_name in [
            "hedge_dashboard.html", "mnq_dashboard.html", "mgc_dashboard.html", "mes_dashboard.html",
            "performance_comparison_dashboard.html", "market_intelligence.html", "correlation_analysis_dashboard.html",
            "monte_carlo_dashboard.html", "drawdown_analysis_dashboard.html", "portfolio_manager.html",
            "portfolio_overview.html", "performance_forecast.html", "market_volatility.html",
            "daily_pnl_dashboard.html", "premium_dashboard.html", "custom_alerts_dashboard.html",
            "investor_reporting.html"
        ]:
            dashboard_files.append(file_path)
    
    return dashboard_files

def update_dashboard_file(file_path):
    """Update a dashboard file to show all instruments by default"""
    file_name = os.path.basename(file_path)
    print(f"Updating {file_name}...")
    
    # Create a backup of the original file
    backup_file = f"{file_path}.default.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(file_path, backup_file)
        print(f"Created backup of {file_path} to {backup_file}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update the data loading script to show all instruments by default
        soup = BeautifulSoup(content, 'html.parser')
        
        # Find all script tags
        script_tags = soup.find_all('script')
        
        for script in script_tags:
            if script.string and 'initializeDashboard' in script.string:
                # Update the script to show all instruments by default
                script_content = script.string
                
                # Replace the instrumentsToDisplay logic
                old_logic = """        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }"""
                
                new_logic = """        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        // Always show all instruments by default
        if (instrumentParam && data[instrumentParam]) {
            // If a specific instrument is requested, show only that one
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to showing all instruments
            instrumentsToDisplay = Object.keys(data);
        }"""
                
                # Replace the logic in the script content
                if old_logic in script_content:
                    script_content = script_content.replace(old_logic, new_logic)
                    script.string = script_content
                    print(f"Updated data loading script in {file_name}")
        
        # Write the updated content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        print(f"Updated {file_name} successfully")
        return True
    
    except Exception as e:
        print(f"Error updating {file_name}: {e}")
        
        # Restore the backup
        shutil.copy2(backup_file, file_path)
        print(f"Restored {file_path} from backup due to error")
        
        return False

def update_all_dashboards():
    """Update all dashboard files to show all instruments by default"""
    # Find all dashboard files
    dashboard_files = find_dashboard_files()
    
    if not dashboard_files:
        print("No dashboard files found.")
        return
    
    print(f"Found {len(dashboard_files)} dashboard files:")
    for file_path in dashboard_files:
        print(f"  - {os.path.basename(file_path)}")
    
    # Update each dashboard file
    success_count = 0
    for file_path in dashboard_files:
        if update_dashboard_file(file_path):
            success_count += 1
    
    print(f"Updated {success_count} out of {len(dashboard_files)} dashboard files successfully")

def main():
    """Main function"""
    print("Updating all dashboards to show all instruments by default...")
    update_all_dashboards()
    print("Dashboard update complete!")

if __name__ == "__main__":
    main()
