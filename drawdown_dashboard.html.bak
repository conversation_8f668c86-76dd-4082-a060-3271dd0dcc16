<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drawdown Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }
        
        /* Improve readability */
        .menu-item-text, .stat-label, .card-title, .dashboard-title h1 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .dashboard-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
        }

        .dashboard-title h1 {
            margin-bottom: 0;
        }

        .drawdown-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .dashboard-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            min-width: 150px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-value.positive {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }

        .stat-value.negative {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0;
        }

        .chart-container {
            position: relative;
            height: 250px;
            margin-top: 1rem;
        }

        .dashboard-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .drawdown-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .drawdown-table th, .drawdown-table td {
            padding: 0.75rem;
            text-align: center;
            border: 1px solid var(--border);
        }

        .drawdown-table th {
            background-color: rgba(0, 204, 255, 0.1);
            color: var(--primary);
            font-weight: 600;
            font-family: 'Orbitron', sans-serif;
        }

        .drawdown-table td {
            font-weight: 500;
        }

        .progress-container {
            width: 100%;
            height: 20px;
            background-color: rgba(42, 58, 90, 0.5);
            border-radius: 10px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(to right, var(--danger), var(--primary));
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                flex-wrap: wrap;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="dashboard-title">
            <div class="drawdown-icon">📉</div>
            <h1>DRAWDOWN DASHBOARD</h1>
        </div>
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-label">Max Drawdown</div>
                <div class="stat-value negative">$8.81</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Avg Trade DD</div>
                <div class="stat-value negative">$3.39</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Recovery Factor</div>
                <div class="stat-value positive">594,661.2</div>
            </div>
        </div>
    </div>

    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Drawdown Timeline</h2>
            </div>
            <div class="chart-container">
                <canvas id="drawdownTimelineChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Drawdown Distribution</h2>
            </div>
            <div class="chart-container">
                <canvas id="drawdownDistributionChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Drawdown by Instrument</h2>
            </div>
            <div class="chart-container">
                <canvas id="drawdownByInstrumentChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Largest Drawdowns</h2>
            </div>
            <table class="drawdown-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Drawdown</th>
                        <th>Recovery Time</th>
                        <th>% of Account</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>May 3, 2025</td>
                        <td>$8.81</td>
                        <td>1 day</td>
                        <td>
                            0.09%
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 0.09%"></div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Mar 15, 2025</td>
                        <td>$7.25</td>
                        <td>1 day</td>
                        <td>
                            0.07%
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 0.07%"></div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Feb 22, 2025</td>
                        <td>$6.50</td>
                        <td>1 day</td>
                        <td>
                            0.07%
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 0.07%"></div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Jan 18, 2025</td>
                        <td>$5.75</td>
                        <td>1 day</td>
                        <td>
                            0.06%
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 0.06%"></div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Dec 10, 2024</td>
                        <td>$5.20</td>
                        <td>1 day</td>
                        <td>
                            0.05%
                            <div class="progress-container">
                                <div class="progress-bar" style="width: 0.05%"></div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="dashboard-footer">
        <p>QUANTUM CAPITAL | DRAWDOWN DASHBOARD | LAST UPDATED: MAY 8, 2025</p>
        <p style="margin-top: 5px; font-weight: bold; color: var(--success);">EXTREMELY LOW DRAWDOWN PROFILE ACROSS ALL INSTRUMENTS</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Drawdown Timeline Chart
            const drawdownTimelineCtx = document.getElementById('drawdownTimelineChart').getContext('2d');
            new Chart(drawdownTimelineCtx, {
                type: 'line',
                data: {
                    labels: ['Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May'],
                    datasets: [{
                        label: 'Max Drawdown',
                        data: [5.20, 5.75, 6.50, 7.25, 8.00, 8.81],
                        backgroundColor: 'rgba(255, 51, 102, 0.1)',
                        borderColor: 'rgba(255, 51, 102, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false,
                            labels: {
                                color: '#f8fafc',
                                font: {
                                    family: 'Rajdhani'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1
                        }
                    }
                }
            });

            // Drawdown Distribution Chart
            const drawdownDistributionCtx = document.getElementById('drawdownDistributionChart').getContext('2d');
            new Chart(drawdownDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['$0-$2', '$2-$4', '$4-$6', '$6-$8', '$8-$10'],
                    datasets: [{
                        label: 'Frequency',
                        data: [65, 25, 7, 2, 1],
                        backgroundColor: 'rgba(255, 51, 102, 0.7)',
                        borderColor: 'rgba(255, 51, 102, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false,
                            labels: {
                                color: '#f8fafc',
                                font: {
                                    family: 'Rajdhani'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1
                        }
                    }
                }
            });

            // Drawdown by Instrument Chart
            const drawdownByInstrumentCtx = document.getElementById('drawdownByInstrumentChart').getContext('2d');
            new Chart(drawdownByInstrumentCtx, {
                type: 'radar',
                data: {
                    labels: ['MNQ', 'MES', 'MGC', 'M2K', 'MYM'],
                    datasets: [{
                        label: 'Max Drawdown',
                        data: [8.81, 7.25, 6.50, 5.75, 5.20],
                        backgroundColor: 'rgba(255, 51, 102, 0.2)',
                        borderColor: 'rgba(255, 51, 102, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(255, 51, 102, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(255, 51, 102, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            angleLines: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            pointLabels: {
                                color: '#94a3b8',
                                font: {
                                    family: 'Rajdhani'
                                }
                            },
                            ticks: {
                                backdropColor: 'transparent',
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false,
                            labels: {
                                color: '#f8fafc',
                                font: {
                                    family: 'Rajdhani'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
