<!DOCTYPE html>

<html>
<head>
<title>Trading Bot Master Dashboard</title>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        :root {
            --primary: #4f46e5;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --card-bg: #ffffff;
            --border: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f1f5f9;
            color: var(--dark);
            line-height: 1.6;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .header p {
            color: var(--gray);
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .stat-card {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: var(--primary);
        }

        .stat-card.success::after { background-color: var(--success); }
        .stat-card.warning::after { background-color: var(--warning); }
        .stat-card.danger::after { background-color: var(--danger); }

        .stat-card h3 {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray);
            margin-bottom: 1rem;
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }

        .stat-card.success .stat-value { color: var(--success); }
        .stat-card.warning .stat-value { color: var(--warning); }
        .stat-card.danger .stat-value { color: var(--danger); }

        .stat-desc {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .dashboard-card {
            background: var(--card-bg);
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dashboard-card-image {
            height: 200px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .dashboard-card-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.7) 100%);
        }

        .dashboard-card-content {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .dashboard-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.75rem;
        }

        .dashboard-card p {
            color: var(--gray);
            margin-bottom: 1.5rem;
            flex-grow: 1;
        }

        .dashboard-card-button {
            display: inline-block;
            background-color: var(--primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
            text-align: center;
        }

        .dashboard-card-button:hover {
            background-color: #4338ca;
        }

        .footer {
            text-align: center;
            padding: 2rem 0;
            color: var(--gray);
            font-size: 0.9rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<div class="dashboard">
<div class="header">
<h1>Trading Bot Master Dashboard</h1>
<p>Comprehensive analysis and monitoring of your optimized trading strategy</p>
</div>
<div class="stats-grid">
<div class="stat-card success">
<h3>Total PnL</h3>
<div class="stat-value">$448,508</div>
<div class="stat-desc">Over 6 months</div>
</div>
<div class="stat-card success">
<h3>Win Rate</h3>
<div class="stat-value">78.35%</div>
<div class="stat-desc">4,194 wins / 5,353 trades</div>
</div>
<div class="stat-card success">
<h3>Win Day Rate</h3>
<div class="stat-value">99.4%</div>
<div class="stat-desc">156 profitable days / 157 total</div>
</div>
<div class="stat-card success">
<h3>Profit Factor</h3>
<div class="stat-value">22.28</div>
<div class="stat-desc">Exceptional risk/reward</div>
</div>
</div>
<h2 class="section-title">Specialized Dashboards</h2>
<div class="dashboard-grid">
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Premium Dashboard</h3>
<p>Comprehensive overview of trading bot performance with key metrics and interactive charts.</p>
<a class="dashboard-card-button" href="premium_dashboard.html">View Dashboard</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1535320903710-d993d3d77d29?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Performance Comparison</h3>
<p>Side-by-side comparison of optimized configuration vs. previous configuration.</p>
<a class="dashboard-card-button" href="performance_comparison_dashboard.html">View Comparison</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1543286386-713bdd548da4?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Drawdown Analysis</h3>
<p>Detailed analysis of drawdowns and recovery periods with visualizations.</p>
<a class="dashboard-card-button" href="drawdown_analysis_dashboard.html">View Analysis</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1415&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Monte Carlo Simulation</h3>
<p>Projecting future performance based on historical data with 1,000 simulations.</p>
<a class="dashboard-card-button" href="monte_carlo_dashboard.html">View Simulation</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Daily PnL Analysis</h3>
<p>Detailed breakdown of daily performance metrics and visualizations.</p>
<a class="dashboard-card-button" href="enhanced_daily_pnl.html">View Daily Analysis</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1526304640581-d334cdbbf45e?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Configuration Settings</h3>
<p>View and edit the optimized configuration settings for your trading bot.</p>
<a class="dashboard-card-button" href="config_viewer.html" onclick="createConfigViewer(); return false;">View Configuration</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Custom Alerts</h3>
<p>Set up and manage alerts for trade entries, exits, drawdowns, and performance metrics.</p>
<a class="dashboard-card-button" href="custom_alerts_dashboard.html">Manage Alerts</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1642790551116-18e150f248e5?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Real-Time Monitoring</h3>
<p>Monitor live trading performance, current positions, and today's completed trades.</p>
<a class="dashboard-card-button" href="realtime_dashboard.html">View Live Data</a>
</div>
</div>
<div class="dashboard-card">
<div class="dashboard-card-image" style="background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1470&amp;q=80');"></div>
<div class="dashboard-card-content">
<h3>Correlation Analysis</h3>
<p>Analyze how your trading strategy correlates with market indices and other assets.</p>
<a class="dashboard-card-button" href="correlation_analysis_dashboard.html">View Correlations</a>
</div>
</div>
</div>
<div class="footer">
<p>Trading Bot Master Dashboard | Generated on May 6, 2025 | Optimized Configuration</p>
</div>
</div>
<script>
        function createConfigViewer() {
            // Create a simple config viewer HTML
            const configHTML = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Trading Bot Configuration</title>
                <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <style>
                    :root {
                        --primary: #4f46e5;
                        --dark: #1e293b;
                        --light: #f8fafc;
                        --gray: #64748b;
                        --card-bg: #ffffff;
                        --border: #e2e8f0;
                    }

                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Poppins', sans-serif;
                        background-color: #f1f5f9;
                        color: var(--dark);
                        line-height: 1.6;
                        padding: 2rem;
                    }

                    .container {
                        max-width: 1000px;
                        margin: 0 auto;
                        background: var(--card-bg);
                        border-radius: 1rem;
                        padding: 2rem;
                        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                    }

                    h1 {
                        font-size: 2rem;
                        font-weight: 700;
                        color: var(--primary);
                        margin-bottom: 1.5rem;
                        text-align: center;
                    }

                    pre {
                        background-color: #f8fafc;
                        padding: 1.5rem;
                        border-radius: 0.5rem;
                        overflow-x: auto;
                        font-family: 'Courier New', monospace;
                        line-height: 1.5;
                    }

                    .back-button {
                        display: inline-block;
                        background-color: var(--primary);
                        color: white;
                        padding: 0.75rem 1.5rem;
                        border-radius: 0.5rem;
                        text-decoration: none;
                        font-weight: 500;
                        margin-top: 1.5rem;
                    }

                    .back-button:hover {
                        background-color: #4338ca;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Trading Bot Configuration</h1>
                    <pre>// config.js - GOATED Optimized Configuration with 0 bar latency
// Using the best parameters from the grid test

module.exports = {
  // --- General Settings ---
  inputFile: './input/MNQ_Last6months.csv', // Recent 6-month data
  initialBalance: 10000,

  // --- Instrument Specifics (MNQ Specs) ---
  pointValue: 2.00,
  tickSize: 0.25,
  pricePrecision: 2,

  // --- Costs & Slippage ---
  commissionPerContract: 0.40, // Realistic commission costs
  slippagePoints: 0.75,       // Realistic slippage

  // --- Indicator Periods ---
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 0,
  wma50Period: 0,

  // --- Strategy Parameters ---
  fixedTpPoints: 40,
  useWmaFilter: false,
  useTwoBarColorExit: false,
  minAtrEntry: 0,
  minRsiMaSeparation: 0,

  // --- RSI Bands ---
  rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

  // --- Run Mode: OPTIMIZED CONFIGURATION ---
  isAdaptiveRun: true,

  // Using optimal parameters from grid test
  slFactors: 4.5,
  tpFactors: 3.0,
  trailFactors: 0.11,

  // 0 bar latency
  latencyDelayBars: 0,

  // Keep these for compatibility
  costGrid: null,
  riskPercentGrid: null,
  fixedContractsGrid: null,
  fixedTpPointsGrid: null,

  // Fixed parameters
  riskPercent: 0,
  fixedContracts: 10,   // Using 10 contracts for final version
  maxContracts: 10,

  // For compatibility with backtest.js
  currentRiskPercent: 0,
  currentFixedContracts: 10,

  // --- Time Filter Settings ---
  timeFilterEnabled: false,

  // --- Enhanced Features Configuration ---
  // All enhanced features disabled
  useAdaptiveSlippage: false,
  minSpreadPoints: 0.25,
  defaultSpreadPoints: 0.5,
  useVolatilityPositionSizing: false,
  useDynamicPositionSizing: false,
  basePositionSize: 10,
  maxPositionSize: 10,
  minPositionSize: 10,
  useMLEntryFilter: false,
  useAdvancedMLFilter: false,
  mlEntryThreshold: 0.5,
  useTimeAnalysis: false,
  timeScoreThreshold: 0.5,
  useCircuitBreakers: false,
  maxSlippageMultiplier: 5,
  pauseTradingOnExcessiveSlippage: false,
  useSteppedTrail: false,
  trailStepSizeMultiplier: 0.1,
  spreadBufferMultiplier: 2.0
};</pre>
                    <a href="main_dashboard.html" class="back-button">Back to Dashboard</a>
                </div>
            </body>
            </html>
            `;

            // Create a blob and open it
            const blob = new Blob([configHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // Save the file
            const a = document.createElement('a');
            a.href = url;
            a.download = 'config_viewer.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // Open the file
            window.open(url, '_blank');
        }

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Master Dashboard`;
            }

            // Update stats with actual data
            updateStats(data);

            console.log('Main dashboard updated with data for:', data.instrumentCode);
        }

        // Update stats with actual data
        function updateStats(data) {
            // This would be implemented to update the stats cards with actual data
            // For now, we'll just log that we would update the stats
            console.log('Stats would be updated with:', data);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
