// run_m2k_grid_test.js - <PERSON><PERSON><PERSON> to run grid test for M2K

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// Create output directory
const outputDir = './output/M2K_GridTest';
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`Created output directory: ${outputDir}`);
}

// Check if the input file exists
const inputFile = 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv';
if (!fs.existsSync(inputFile)) {
  console.error(`Input file not found: ${inputFile}`);
  process.exit(1);
} else {
  console.log(`Found input file: ${inputFile}`);
}

console.log('=== Running M2K Grid Test to Find Optimal Parameters ===');
console.log('Using config file: ./config_m2k_grid.js');
console.log('Output directory:', outputDir);

// Create a modified version of backtest.js for the grid test
const backtestContent = fs.readFileSync('backtest.js', 'utf8');
const modifiedContent = backtestContent
  .replace('./output/MNQ_AdvancedML_2020_2025', outputDir)
  .replace('./config', './config_m2k_grid');

// Write the modified backtest script
const tempBacktestPath = './backtest_m2k_grid.js';
fs.writeFileSync(tempBacktestPath, modifiedContent);
console.log(`Created temporary backtest file: ${tempBacktestPath}`);

// Run the backtest
console.log('Starting grid test...');
try {
  execSync(`node ${tempBacktestPath}`, { stdio: 'inherit' });
  console.log('Grid test completed successfully!');
} catch (error) {
  console.error('Grid test failed:', error.message);
}

console.log('Grid test results are available in:', outputDir);