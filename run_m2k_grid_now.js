// run_m2k_grid_now.js - Ultra simple script to run M2K grid test

const fs = require('fs');
const { execSync } = require('child_process');

console.log('Starting M2K grid test...');

// Make sure output directory exists
const outputDir = './output/M2K_GridTest';
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`Created output directory: ${outputDir}`);
}

// Run the backtest directly
try {
  console.log('Running backtest_m2k_grid.js...');
  execSync('node backtest_m2k_grid.js', { stdio: 'inherit' });
  console.log('M2K grid test completed successfully!');
} catch (error) {
  console.error('Error running grid test:', error.message);
}