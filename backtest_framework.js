/**
 * Backtesting Framework for Trading Bot
 * Tests trading strategy against historical data
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Import trading indicators and logic
const { calculateRSI, calculateSMA, calculateWMA, calculateATR } = require('./indicators');

// Configuration
const DEFAULT_CONFIG = {
    // Instrument settings
    symbol: 'MNQ',
    pointValue: 2.0,
    commission: 0.40, // per contract per side
    slippage: 0.0, // points of slippage per trade

    // Position sizing
    fixedContracts: 10,

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14,

    // Entry/exit parameters
    slFactors: 4.5,
    tpFactors: 3.0,
    trailFactors: 0.11,
    fixedTpPoints: 40,

    // Circuit breakers
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0,
    minRsiMaSeparation: 0,

    // Initial balance
    initialBalance: 10000,
};

// ATR Thresholds for adaptive mode
const ATR_THRESHOLDS = {
    MNQ: { low_medium: 4.7601, medium_high: 7.2605 },
    MES: { low_medium: 3.0, medium_high: 5.0 },
    MGC: { low_medium: 1.5, medium_high: 3.0 }
};

// Adaptive parameters for each volatility regime
const ADAPTIVE_PARAMS = {
    MNQ: {
        Low: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
        Medium: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
        High: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 }
    },
    MES: {
        Low: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
        Medium: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
        High: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 }
    },
    MGC: {
        Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
    }
};

/**
 * Backtest class to run trading strategy on historical data
 */
class Backtest {
    constructor(config = {}, options = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.options = options;
        this.symbol = this.config.symbol;
        this.candles = [];
        this.trades = [];
        this.dailyStats = {};
        this.positions = [];
        this.balance = this.config.initialBalance;
        this.initialBalance = this.config.initialBalance;
        this.highWaterMark = this.config.initialBalance;
        this.maxDrawdown = 0;
        this.circuitBreakers = {
            dailyLoss: 0,
            isTripped: false,
            lastResetDate: null
        };
    }

    /**
     * Load historical data from CSV file
     * @param {string} filePath - Path to CSV file
     * @param {Object} options - Options for loading data
     * @returns {Promise<void>}
     */
    async loadData(filePath, options = {}) {
        console.log(`Loading data from ${filePath}...`);

        // Default options
        const defaultOptions = {
            quickTest: false,
            quickTestDays: 30,
            progressInterval: 10000 // Show progress every 10,000 rows
        };

        // Merge options
        options = { ...defaultOptions, ...options };

        return new Promise((resolve, reject) => {
            // Check if file exists
            if (!fs.existsSync(filePath)) {
                console.error(`File not found: ${filePath}`);
                resolve(); // Resolve with empty candles array
                return;
            }

            const results = [];
            let lineCount = 0;
            let lastProgressTime = Date.now();
            let startTime = Date.now();

            // Try to determine CSV format by reading first few lines
            const sampleLines = fs.readFileSync(filePath, 'utf8')
                .split('\n')
                .slice(0, 5)
                .join('\n');

            console.log(`Sample data from ${filePath}:`);
            console.log(sampleLines);

            // Check if the file is semicolon-separated
            const isSemicolonSeparated = sampleLines.includes(';');

            // Create CSV parser with appropriate options
            const parser = csv({
                separator: isSemicolonSeparated ? ';' : ',',
                mapHeaders: ({ header }) => header.toLowerCase().trim(),
                mapValues: ({ header, value }) => value.trim()
            });

            // Calculate date range for filtering data
            let startTimestamp = 0;
            let endTimestamp = Number.MAX_SAFE_INTEGER;

            if (options.quickTest) {
                if (options.dateRange && options.dateRange.useCustomRange) {
                    // Use custom date range
                    if (options.dateRange.startDate) {
                        const startDate = new Date(options.dateRange.startDate);
                        startTimestamp = startDate.getTime();
                    }

                    if (options.dateRange.endDate) {
                        const endDate = new Date(options.dateRange.endDate);
                        endDate.setHours(23, 59, 59, 999); // End of day
                        endTimestamp = endDate.getTime();
                    }

                    console.log(`Custom date range: ${new Date(startTimestamp).toISOString().split('T')[0]} to ${
                        endTimestamp === Number.MAX_SAFE_INTEGER ? 'present' : new Date(endTimestamp).toISOString().split('T')[0]
                    }`);
                } else {
                    // Use days from now
                    const now = new Date();
                    const cutoffDate = new Date(now);
                    cutoffDate.setDate(cutoffDate.getDate() - options.quickTestDays);
                    startTimestamp = cutoffDate.getTime();
                    console.log(`Quick test mode: Only loading data from ${cutoffDate.toISOString()} onwards`);
                }
            }

            fs.createReadStream(filePath)
                .pipe(parser)
                .on('data', (data) => {
                    lineCount++;

                    // Show progress periodically
                    const now = Date.now();
                    if (now - lastProgressTime > 1000) { // Update every second
                        process.stdout.write(`\rProcessing row ${lineCount.toLocaleString()}...`);
                        lastProgressTime = now;
                    }

                    // Try to find timestamp/date field
                    let timestampField =
                        data.timestamp !== undefined ? 'timestamp' :
                        data.date !== undefined ? 'date' :
                        data.time !== undefined ? 'time' :
                        data.datetime !== undefined ? 'datetime' : null;

                    // Special case for the specific CSV format we're seeing
                    if (!timestampField && data['time left'] !== undefined) {
                        timestampField = 'time left';
                    }

                    if (!timestampField) {
                        if (lineCount <= 5) {
                            console.error('Could not find timestamp field in data:', data);
                        }
                        return;
                    }

                    // Parse timestamp
                    const timestamp = new Date(data[timestampField]).getTime();

                    // Skip data outside the specified date range
                    if ((timestamp < startTimestamp) || (timestamp > endTimestamp)) {
                        return;
                    }

                    // Format the data
                    const candle = {
                        timestamp,
                        open: parseFloat(data.open),
                        high: parseFloat(data.high),
                        low: parseFloat(data.low),
                        close: parseFloat(data.close),
                        volume: parseFloat(data.volume || 0)
                    };

                    // Validate the data
                    if (!isNaN(candle.timestamp) &&
                        !isNaN(candle.open) &&
                        !isNaN(candle.high) &&
                        !isNaN(candle.low) &&
                        !isNaN(candle.close)) {
                        results.push(candle);
                    } else if (lineCount <= 5) {
                        console.error('Invalid data in row:', data);
                        console.error('Parsed candle:', candle);
                    }
                })
                .on('end', () => {
                    // Clear progress line
                    process.stdout.write('\r' + ' '.repeat(50) + '\r');

                    // Sort by timestamp
                    results.sort((a, b) => a.timestamp - b.timestamp);
                    this.candles = results;

                    const elapsedTime = (Date.now() - startTime) / 1000;
                    console.log(`Loaded ${results.length.toLocaleString()} candles from ${lineCount.toLocaleString()} rows in ${elapsedTime.toFixed(1)} seconds`);

                    if (results.length > 0) {
                        const startDate = new Date(results[0].timestamp);
                        const endDate = new Date(results[results.length - 1].timestamp);
                        console.log(`Data period: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);
                    }

                    resolve();
                })
                .on('error', (error) => {
                    console.error(`Error parsing CSV: ${error.message}`);
                    reject(error);
                });
        });
    }

    /**
     * Run the backtest
     */
    run() {
        console.log(`Running backtest for ${this.symbol}...`);
        console.log(`Initial balance: $${this.balance.toFixed(2)}`);
        console.log(`Fixed contracts: ${this.config.fixedContracts}`);

        // Need at least 50 candles for indicators
        if (this.candles.length < 50) {
            console.error('Not enough candles for backtest');
            return;
        }

        // Process each candle
        for (let i = 50; i < this.candles.length; i++) {
            this.processCandle(i);
        }

        // Calculate final statistics
        this.calculateStatistics();
    }

    /**
     * Process a single candle
     * @param {number} index - Index of candle in candles array
     */
    processCandle(index) {
        const candle = this.candles[index];
        const date = new Date(candle.timestamp);
        const dateStr = date.toISOString().split('T')[0];

        // Reset circuit breakers on new day
        if (this.circuitBreakers.lastResetDate !== dateStr) {
            this.resetCircuitBreakers(dateStr);
        }

        // Calculate indicators
        const indicators = this.calculateIndicators(index);

        // Update positions with new candle
        this.updatePositions(candle, indicators);

        // Check for new signals if no open positions and circuit breakers not tripped
        if (this.positions.length === 0 && !this.circuitBreakers.isTripped) {
            this.checkForSignals(candle, indicators);
        }

        // Update daily statistics
        this.updateDailyStats(dateStr);
    }

    /**
     * Calculate indicators for current candle
     * @param {number} index - Index of current candle
     * @returns {Object} - Calculated indicators
     */
    calculateIndicators(index) {
        // Get candles up to current index
        const candles = this.candles.slice(0, index + 1);

        // Calculate ATR
        const atr = calculateATR(candles, this.config.atrPeriod);

        // Calculate RSI values for the last 22 candles
        const rsiValues = [];
        for (let i = 0; i < 22; i++) {
            if (candles.length > i + this.config.rsiPeriod) {
                const rsiValue = calculateRSI(candles.slice(0, candles.length - i), this.config.rsiPeriod);
                rsiValues.unshift(rsiValue);
            }
        }

        // Calculate current RSI
        const rsi = rsiValues.length > 0 ? rsiValues[rsiValues.length - 1] : 50;

        // Calculate SMA of RSI values (RSI-based MA)
        const rsiBasedMA = rsiValues.length >= this.config.rsiMaPeriod ?
            calculateSMA(rsiValues, this.config.rsiMaPeriod) : 50;

        // Calculate WMA (Weighted Moving Average)
        const wma50 = calculateWMA(candles, this.config.wma50Period);

        // Determine ATR regime
        let atrRegime = 'Medium'; // Default regime
        const thresholds = ATR_THRESHOLDS[this.symbol] || { low_medium: 1.5, medium_high: 3.0 };

        if (atr < thresholds.low_medium) {
            atrRegime = 'Low';
        } else if (atr > thresholds.medium_high) {
            atrRegime = 'High';
        }

        return {
            atr,
            rsi,
            rsiBasedMA,
            wma50,
            atrRegime
        };
    }

    /**
     * Determine candlestick color
     * @param {Object} candle - Candle object
     * @returns {string} - 'green', 'red', or 'invalid'
     */
    candlestickColor(candle) {
        if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
            return 'invalid';
        }
        return candle.close >= candle.open ? 'green' : 'red';
    }

    /**
     * Detect 3-candle pattern
     * @param {Object} c1 - First candle
     * @param {Object} c2 - Second candle
     * @param {Object} c3 - Third candle
     * @returns {string|null} - 'bullish', 'bearish', or null
     */
    detect3(c1, c2, c3) {
        if (!c1 || !c2 || !c3) return null;

        const col1 = this.candlestickColor(c1);
        const col2 = this.candlestickColor(c2);
        const col3 = this.candlestickColor(c3);

        if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

        if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
            const engulf = c3.close > c2.open && c3.open < c2.close;
            const wick3 = Math.min(c3.open, c3.close) - c3.low;
            const wick2 = Math.min(c2.open, c2.close) - c2.low;

            if (engulf && wick3 > wick2) return 'bullish';
        }

        if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
            const engulf = c3.close < c2.open && c3.open > c2.close;
            const wick3 = c3.high - Math.max(c3.open, c3.close);
            const wick2 = c2.high - Math.max(c2.open, c2.close);

            if (engulf && wick3 > wick2) return 'bearish';
        }

        return null;
    }

    /**
     * Detect 4-candle pattern
     * @param {Object} c0 - First candle
     * @param {Object} c1 - Second candle
     * @param {Object} c2 - Third candle
     * @param {Object} c3 - Fourth candle
     * @returns {string|null} - 'bullish', 'bearish', or null
     */
    detect4(c0, c1, c2, c3) {
        if (!c0 || !c1 || !c2 || !c3) return null;

        const col0 = this.candlestickColor(c0);
        const col1 = this.candlestickColor(c1);
        const col2 = this.candlestickColor(c2);
        const col3 = this.candlestickColor(c3);

        if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

        if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
            if (c3.close > Math.max(c1.open, c2.open)) return 'bullish';
        }

        if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
            if (c3.close < Math.min(c1.open, c2.open)) return 'bearish';
        }

        return null;
    }

    /**
     * Check if entry conditions are met
     * @param {string} dir - Direction ('bullish' or 'bearish')
     * @param {string} patternType - Pattern type ('three' or 'four')
     * @param {Object} c3 - Current candle
     * @param {number} currentIndex - Current index
     * @param {Array} candlesForPeriod - All candles
     * @returns {boolean} - Whether entry conditions are met
     */
    entryOK(dir, patternType, c3, currentIndex, candlesForPeriod) {
        // Basic validation
        if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
            return false;
        }

        // WMA filter
        if (this.config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
            return false;
        }

        // RSI filter for three-candle patterns
        if (patternType === 'three' && ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa))) {
            return false;
        }

        // Minimum ATR filter
        const minAtrEntry = this.config.minAtrEntry || 0;
        if (c3.atr < minAtrEntry) {
            return false;
        }

        // RSI-MA separation filter
        const minRsiMaSeparation = this.config.minRsiMaSeparation || 0;
        if (Math.abs(c3.rsi - c3.rsiMa) < minRsiMaSeparation) {
            return false;
        }

        return true;
    }

    /**
     * Check for trading signals
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Calculated indicators
     */
    checkForSignals(candle, indicators) {
        // Get recent candles for pattern detection
        const candleIndex = this.candles.indexOf(candle);
        if (candleIndex < 3) return; // Need at least 4 candles for pattern detection

        const c0 = this.candles[candleIndex - 3];
        const c1 = this.candles[candleIndex - 2];
        const c2 = this.candles[candleIndex - 1];
        const c3 = candle;

        // Add indicators to current candle for filtering
        c3.rsi = indicators.rsi;
        c3.rsiMa = indicators.rsiBasedMA;
        c3.wma50 = indicators.wma50;
        c3.atr = indicators.atr;

        // Check for 3-candle pattern
        const pattern3 = this.detect3(c1, c2, c3);
        if (pattern3 && this.entryOK(pattern3, 'three', c3, candleIndex, this.candles)) {
            if (this.options.verbose) {
                console.log(`${pattern3.toUpperCase()} 3-candle pattern detected at ${new Date(candle.timestamp).toISOString()}`);
            }
            if (pattern3 === 'bullish') {
                this.openLongPosition(candle, indicators);
                return;
            } else {
                this.openShortPosition(candle, indicators);
                return;
            }
        }

        // Check for 4-candle pattern
        const pattern4 = this.detect4(c0, c1, c2, c3);
        if (pattern4 && this.entryOK(pattern4, 'four', c3, candleIndex, this.candles)) {
            if (this.options.verbose) {
                console.log(`${pattern4.toUpperCase()} 4-candle pattern detected at ${new Date(candle.timestamp).toISOString()}`);
            }
            if (pattern4 === 'bullish') {
                this.openLongPosition(candle, indicators);
                return;
            } else {
                this.openShortPosition(candle, indicators);
                return;
            }
        }

        // Fallback to RSI/WMA logic if no patterns detected
        // Check conditions for long signal
        const rsiAboveMA = indicators.rsi > indicators.rsiBasedMA;
        const priceAboveWMA = candle.close > indicators.wma50;

        // Check conditions for short signal
        const rsiBelowMA = indicators.rsi < indicators.rsiBasedMA;
        const priceBelowWMA = candle.close < indicators.wma50;

        // Additional filters
        let passesFilters = true;

        // Minimum ATR filter
        if (this.config.minAtrEntry && indicators.atr < this.config.minAtrEntry) {
            passesFilters = false;
        }

        // Minimum RSI-MA separation filter
        if (this.config.minRsiMaSeparation &&
            Math.abs(indicators.rsi - indicators.rsiBasedMA) < this.config.minRsiMaSeparation) {
            passesFilters = false;
        }

        // Check for long signal
        if (rsiAboveMA && priceAboveWMA && passesFilters) {
            if (this.options.verbose) {
                console.log(`RSI/WMA BULLISH signal detected at ${new Date(candle.timestamp).toISOString()}`);
            }
            this.openLongPosition(candle, indicators);
        }
        // Check for short signal
        else if (rsiBelowMA && priceBelowWMA && passesFilters) {
            if (this.options.verbose) {
                console.log(`RSI/WMA BEARISH signal detected at ${new Date(candle.timestamp).toISOString()}`);
            }
            this.openShortPosition(candle, indicators);
        }
    }

    /**
     * Open a long position
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Calculated indicators
     */
    openLongPosition(candle, indicators) {
        // Get adaptive parameters based on ATR regime
        const adaptiveParams = ADAPTIVE_PARAMS[this.symbol] || ADAPTIVE_PARAMS.MNQ;
        const regimeParams = adaptiveParams[indicators.atrRegime];

        // Use adaptive parameters if available, otherwise use config values
        const slFactor = regimeParams ? regimeParams.slFactor : this.config.slFactors;
        const tpFactor = regimeParams ? regimeParams.tpFactor : this.config.tpFactors;
        const trailFactor = regimeParams ? regimeParams.trailFactor : this.config.trailFactors;

        // Calculate stop loss and take profit prices
        const stopLossPoints = indicators.atr * slFactor;
        const atrTakeProfitPoints = indicators.atr * tpFactor;
        const fixedTpPoints = this.config.fixedTpPoints || 0;
        const takeProfitPoints = Math.max(fixedTpPoints, atrTakeProfitPoints);

        const entryPrice = candle.close;
        const stopLossPrice = entryPrice - stopLossPoints;
        const takeProfitPrice = entryPrice + takeProfitPoints;

        // Create position
        const position = {
            type: 'long',
            entryPrice: entryPrice,
            stopLossPrice: stopLossPrice,
            takeProfitPrice: takeProfitPrice,
            trailFactor: trailFactor,
            quantity: this.config.fixedContracts,
            entryTime: candle.timestamp,
            atr: indicators.atr,
            atrRegime: indicators.atrRegime,
            trailHigh: entryPrice, // Initialize trail high with entry price
            tpDistance: takeProfitPoints // Store take profit distance
        };

        this.positions.push(position);
    }

    /**
     * Open a short position
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Calculated indicators
     */
    openShortPosition(candle, indicators) {
        // Get adaptive parameters based on ATR regime
        const adaptiveParams = ADAPTIVE_PARAMS[this.symbol] || ADAPTIVE_PARAMS.MNQ;
        const regimeParams = adaptiveParams[indicators.atrRegime];

        // Use adaptive parameters if available, otherwise use config values
        const slFactor = regimeParams ? regimeParams.slFactor : this.config.slFactors;
        const tpFactor = regimeParams ? regimeParams.tpFactor : this.config.tpFactors;
        const trailFactor = regimeParams ? regimeParams.trailFactor : this.config.trailFactors;

        // Calculate stop loss and take profit prices
        const stopLossPoints = indicators.atr * slFactor;
        const atrTakeProfitPoints = indicators.atr * tpFactor;
        const fixedTpPoints = this.config.fixedTpPoints || 0;
        const takeProfitPoints = Math.max(fixedTpPoints, atrTakeProfitPoints);

        const entryPrice = candle.close;
        const stopLossPrice = entryPrice + stopLossPoints;
        const takeProfitPrice = entryPrice - takeProfitPoints;

        // Create position
        const position = {
            type: 'short',
            entryPrice: entryPrice,
            stopLossPrice: stopLossPrice,
            takeProfitPrice: takeProfitPrice,
            trailFactor: trailFactor,
            quantity: this.config.fixedContracts,
            entryTime: candle.timestamp,
            atr: indicators.atr,
            atrRegime: indicators.atrRegime,
            trailLow: entryPrice, // Initialize trail low with entry price
            tpDistance: takeProfitPoints // Store take profit distance
        };

        this.positions.push(position);
    }

    /**
     * Update positions with new candle data
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Calculated indicators
     */
    updatePositions(candle, indicators) {
        // Process each position
        for (let i = this.positions.length - 1; i >= 0; i--) {
            const position = this.positions[i];

            // Update trailing stops with indicators
            this.updateTrailingStop(position, candle, indicators);

            // Check for exit conditions
            const exitResult = this.checkExitConditions(position, candle);

            if (exitResult.shouldExit) {
                // Close the position
                this.closePosition(position, candle, exitResult.exitPrice, exitResult.exitReason);

                // Remove from positions array
                this.positions.splice(i, 1);
            }
        }
    }

    /**
     * Update trailing stop for a position
     * @param {Object} position - Position to update
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Current indicators
     */
    updateTrailingStop(position, candle, indicators) {
        // Get current ATR or use position's ATR if not available
        const atrTrail = (typeof indicators.atr === 'number' && !isNaN(indicators.atr) && indicators.atr > 0)
            ? indicators.atr
            : position.atr;

        // Initialize trail high/low if not set
        if (position.type === 'long') {
            position.trailHigh = position.trailHigh || position.entryPrice;
            position.trailHigh = Math.max(position.trailHigh, candle.high);

            // Calculate ATR-based trail stop
            const newStopPrice = position.trailHigh - (atrTrail * position.trailFactor);

            // Only move stop loss up (ratchet effect)
            if (newStopPrice > position.stopLossPrice) {
                position.stopLossPrice = newStopPrice;
            }
        } else if (position.type === 'short') {
            position.trailLow = position.trailLow || position.entryPrice;
            position.trailLow = Math.min(position.trailLow, candle.low);

            // Calculate ATR-based trail stop
            const newStopPrice = position.trailLow + (atrTrail * position.trailFactor);

            // Only move stop loss down (ratchet effect)
            if (newStopPrice < position.stopLossPrice) {
                position.stopLossPrice = newStopPrice;
            }
        }
    }

    /**
     * Check exit conditions for a position
     * @param {Object} position - Position to check
     * @param {Object} candle - Current candle
     * @returns {Object} - Exit result
     */
    checkExitConditions(position, candle) {
        const result = {
            shouldExit: false,
            exitPrice: 0,
            exitReason: ''
        };

        if (position.type === 'long') {
            // Check if stop loss is hit
            if (candle.low <= position.stopLossPrice) {
                result.shouldExit = true;
                result.exitPrice = Math.max(position.stopLossPrice, candle.open - this.config.slippage);
                result.exitReason = 'sl';
            }
            // Check if take profit is hit
            else if (candle.high >= position.takeProfitPrice) {
                result.shouldExit = true;
                result.exitPrice = Math.min(position.takeProfitPrice, candle.open + this.config.slippage);
                result.exitReason = 'tp';
            }
            // Check if trailing stop is hit (separate from initial stop loss)
            else if (position.trailHigh > position.entryPrice && candle.low <= position.stopLossPrice) {
                result.shouldExit = true;
                result.exitPrice = Math.max(position.stopLossPrice, candle.open - this.config.slippage);
                result.exitReason = 'trail';
            }
        } else if (position.type === 'short') {
            // Check if stop loss is hit
            if (candle.high >= position.stopLossPrice) {
                result.shouldExit = true;
                result.exitPrice = Math.min(position.stopLossPrice, candle.open + this.config.slippage);
                result.exitReason = 'sl';
            }
            // Check if take profit is hit
            else if (candle.low <= position.takeProfitPrice) {
                result.shouldExit = true;
                result.exitPrice = Math.max(position.takeProfitPrice, candle.open - this.config.slippage);
                result.exitReason = 'tp';
            }
            // Check if trailing stop is hit (separate from initial stop loss)
            else if (position.trailLow < position.entryPrice && candle.high >= position.stopLossPrice) {
                result.shouldExit = true;
                result.exitPrice = Math.min(position.stopLossPrice, candle.open + this.config.slippage);
                result.exitReason = 'trail';
            }
        }

        return result;
    }

    /**
     * Close a position and record the trade
     * @param {Object} position - Position to close
     * @param {Object} candle - Current candle
     * @param {number} exitPrice - Exit price
     * @param {string} exitReason - Exit reason
     */
    closePosition(position, candle, exitPrice, exitReason) {
        // Calculate P&L
        let pnlPoints = 0;
        if (position.type === 'long') {
            pnlPoints = exitPrice - position.entryPrice;
        } else {
            pnlPoints = position.entryPrice - exitPrice;
        }

        // Calculate dollar P&L
        const pnlDollars = pnlPoints * this.config.pointValue * position.quantity;

        // Subtract commission
        const commissionCost = this.config.commission * position.quantity * 2; // Entry and exit
        const netPnl = pnlDollars - commissionCost;

        // Update balance
        this.balance += netPnl;

        // Update high water mark and drawdown
        if (this.balance > this.highWaterMark) {
            this.highWaterMark = this.balance;
        } else {
            const drawdown = (this.highWaterMark - this.balance) / this.highWaterMark;
            if (drawdown > this.maxDrawdown) {
                this.maxDrawdown = drawdown;
            }
        }

        // Update circuit breakers
        if (netPnl < 0) {
            this.circuitBreakers.dailyLoss += Math.abs(netPnl);

            // Check if daily loss limit is reached
            const dailyLossLimit = this.initialBalance * this.config.maxDailyLoss;
            if (this.circuitBreakers.dailyLoss >= dailyLossLimit) {
                this.circuitBreakers.isTripped = true;
            }
        }

        // Record the trade
        const trade = {
            symbol: this.symbol,
            type: position.type,
            entryPrice: position.entryPrice,
            exitPrice: exitPrice,
            quantity: position.quantity,
            entryTime: position.entryTime,
            exitTime: candle.timestamp,
            pnlPoints: pnlPoints,
            pnlDollars: pnlDollars,
            netPnl: netPnl,
            exitReason: exitReason,
            atrRegime: position.atrRegime
        };

        this.trades.push(trade);
    }

    /**
     * Reset circuit breakers for a new day
     * @param {string} dateStr - Date string (YYYY-MM-DD)
     */
    resetCircuitBreakers(dateStr) {
        this.circuitBreakers.isTripped = false;
        this.circuitBreakers.dailyLoss = 0;
        this.circuitBreakers.lastResetDate = dateStr;
    }

    /**
     * Update daily statistics
     * @param {string} dateStr - Date string (YYYY-MM-DD)
     */
    updateDailyStats(dateStr) {
        // Initialize daily stats if not exists
        if (!this.dailyStats[dateStr]) {
            this.dailyStats[dateStr] = {
                trades: 0,
                wins: 0,
                losses: 0,
                pnl: 0,
                balance: this.balance
            };
        }

        // Update with latest balance
        this.dailyStats[dateStr].balance = this.balance;
    }

    /**
     * Calculate final statistics
     */
    calculateStatistics() {
        // Count trades
        const totalTrades = this.trades.length;
        if (totalTrades === 0) {
            console.log('No trades executed');
            return;
        }

        // Count wins and losses
        let wins = 0;
        let losses = 0;
        let totalPnl = 0;
        let totalWinAmount = 0;
        let totalLossAmount = 0;
        let maxWin = 0;
        let maxLoss = 0;
        let slCount = 0;
        let tpCount = 0;

        for (const trade of this.trades) {
            totalPnl += trade.netPnl;

            if (trade.netPnl > 0) {
                wins++;
                totalWinAmount += trade.netPnl;
                if (trade.netPnl > maxWin) maxWin = trade.netPnl;
            } else {
                losses++;
                totalLossAmount += Math.abs(trade.netPnl);
                if (Math.abs(trade.netPnl) > maxLoss) maxLoss = Math.abs(trade.netPnl);
            }

            if (trade.exitReason === 'sl') slCount++;
            if (trade.exitReason === 'tp') tpCount++;
        }

        // Calculate win rate
        const winRate = (wins / totalTrades) * 100;

        // Calculate profit factor
        const profitFactor = totalLossAmount === 0 ? Infinity : totalWinAmount / totalLossAmount;

        // Calculate average trade
        const avgTrade = totalPnl / totalTrades;

        // Calculate average win and loss
        const avgWin = wins === 0 ? 0 : totalWinAmount / wins;
        const avgLoss = losses === 0 ? 0 : totalLossAmount / losses;

        // Calculate daily stats
        let profitDays = 0;
        let lossDays = 0;
        let totalDays = 0;
        let prevBalance = this.initialBalance;

        for (const date in this.dailyStats) {
            totalDays++;
            const dayPnl = this.dailyStats[date].balance - prevBalance;

            if (dayPnl > 0) {
                profitDays++;
            } else if (dayPnl < 0) {
                lossDays++;
            }

            prevBalance = this.dailyStats[date].balance;
        }

        // Calculate win day rate
        const winDayRate = totalDays === 0 ? 0 : (profitDays / totalDays) * 100;

        // Calculate final return
        const totalReturn = ((this.balance - this.initialBalance) / this.initialBalance) * 100;

        // Calculate annualized return
        const years = totalDays / 252; // Assuming 252 trading days per year
        const annualizedReturn = years === 0 ? 0 : Math.pow(1 + (totalReturn / 100), 1 / years) - 1;

        // Print statistics
        console.log('\n===== BACKTEST RESULTS =====');
        console.log(`Symbol: ${this.symbol}`);
        console.log(`Period: ${new Date(this.candles[0].timestamp).toLocaleDateString()} to ${new Date(this.candles[this.candles.length - 1].timestamp).toLocaleDateString()}`);
        console.log(`Initial Balance: $${this.initialBalance.toFixed(2)}`);
        console.log(`Final Balance: $${this.balance.toFixed(2)}`);
        console.log(`Total Return: ${totalReturn.toFixed(2)}%`);
        console.log(`Annualized Return: ${(annualizedReturn * 100).toFixed(2)}%`);
        console.log(`Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
        console.log(`Total Trades: ${totalTrades}`);
        console.log(`Win Rate: ${winRate.toFixed(2)}%`);
        console.log(`Profit Factor: ${profitFactor.toFixed(2)}`);
        console.log(`Average Trade: $${avgTrade.toFixed(2)}`);
        console.log(`Average Win: $${avgWin.toFixed(2)}`);
        console.log(`Average Loss: $${avgLoss.toFixed(2)}`);
        console.log(`Max Win: $${maxWin.toFixed(2)}`);
        console.log(`Max Loss: $${maxLoss.toFixed(2)}`);
        console.log(`Stop Loss Exits: ${slCount} (${((slCount / totalTrades) * 100).toFixed(2)}%)`);
        console.log(`Take Profit Exits: ${tpCount} (${((tpCount / totalTrades) * 100).toFixed(2)}%)`);
        console.log(`Total Days: ${totalDays}`);
        console.log(`Profit Days: ${profitDays} (${winDayRate.toFixed(2)}%)`);
        console.log(`Loss Days: ${lossDays} (${(100 - winDayRate).toFixed(2)}%)`);

        return {
            symbol: this.symbol,
            initialBalance: this.initialBalance,
            finalBalance: this.balance,
            totalReturn: totalReturn,
            annualizedReturn: annualizedReturn * 100,
            maxDrawdown: this.maxDrawdown * 100,
            totalTrades: totalTrades,
            winRate: winRate,
            profitFactor: profitFactor,
            avgTrade: avgTrade,
            avgWin: avgWin,
            avgLoss: avgLoss,
            maxWin: maxWin,
            maxLoss: maxLoss,
            slCount: slCount,
            tpCount: tpCount,
            totalDays: totalDays,
            profitDays: profitDays,
            lossDays: lossDays,
            winDayRate: winDayRate,
            trades: this.trades,
            dailyStats: this.dailyStats
        };
    }

    /**
     * Generate HTML report
     * @param {string} outputPath - Path to save HTML report
     */
    generateReport(outputPath) {
        // Calculate statistics
        const stats = this.calculateStatistics();

        // Check if we have candles
        if (this.candles.length === 0) {
            console.error(`No candles loaded for ${this.symbol}, cannot generate report`);

            // Write a simple error report
            const errorHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtest Error - ${this.symbol}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Backtest Error - ${this.symbol}</h1>
        <h2>No data loaded</h2>
        <p>No candles were loaded for this symbol. Please check the data file path and format.</p>
    </div>
</body>
</html>`;

            fs.writeFileSync(outputPath, errorHtml);
            console.log(`Error report generated at ${outputPath}`);
            return;
        }

        // Generate HTML
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtest Report - ${this.symbol}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .chart-container {
            margin-bottom: 30px;
            height: 400px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>Backtest Report - ${this.symbol}</h1>
        <p>Period: ${new Date(this.candles[0].timestamp).toLocaleDateString()} to ${new Date(this.candles[this.candles.length - 1].timestamp).toLocaleDateString()}</p>

        <h2>Performance Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Return</h3>
                <div class="stat-value ${stats.totalReturn >= 0 ? 'positive' : 'negative'}">${stats.totalReturn.toFixed(2)}%</div>
            </div>
            <div class="stat-card">
                <h3>Final Balance</h3>
                <div class="stat-value ${stats.finalBalance >= stats.initialBalance ? 'positive' : 'negative'}">$${stats.finalBalance.toFixed(2)}</div>
                <div>Initial: $${stats.initialBalance.toFixed(2)}</div>
            </div>
            <div class="stat-card">
                <h3>Win Rate</h3>
                <div class="stat-value">${stats.winRate.toFixed(2)}%</div>
                <div>${stats.totalTrades} total trades</div>
            </div>
            <div class="stat-card">
                <h3>Profit Factor</h3>
                <div class="stat-value">${stats.profitFactor.toFixed(2)}</div>
            </div>
            <div class="stat-card">
                <h3>Max Drawdown</h3>
                <div class="stat-value negative">${stats.maxDrawdown.toFixed(2)}%</div>
            </div>
            <div class="stat-card">
                <h3>Win Day Rate</h3>
                <div class="stat-value">${stats.winDayRate.toFixed(2)}%</div>
                <div>${stats.totalDays} total days</div>
            </div>
        </div>

        <h2>Equity Curve</h2>
        <div class="chart-container">
            <canvas id="equityChart"></canvas>
        </div>

        <h2>Monthly Performance</h2>
        <div class="chart-container">
            <canvas id="monthlyChart"></canvas>
        </div>

        <h2>Trade Distribution</h2>
        <div class="chart-container">
            <canvas id="tradeDistChart"></canvas>
        </div>

        <script>
            // Equity curve data
            const dates = [${Object.keys(stats.dailyStats).map(date => `"${date}"`).join(', ')}];
            const balances = [${Object.values(stats.dailyStats).map(day => day.balance).join(', ')}];

            // Create equity curve chart
            const equityCtx = document.getElementById('equityChart').getContext('2d');
            new Chart(equityCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Account Balance',
                        data: balances,
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Equity Curve',
                            color: '#e0e0e0',
                            font: { size: 16 }
                        },
                        legend: {
                            labels: { color: '#e0e0e0' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#e0e0e0' },
                            grid: { color: '#333' }
                        },
                        y: {
                            ticks: { color: '#e0e0e0' },
                            grid: { color: '#333' }
                        }
                    }
                }
            });

            // Process trade data for distribution chart
            const profitBins = [-Infinity, -1000, -500, -200, -100, -50, 0, 50, 100, 200, 500, 1000, Infinity];
            const profitLabels = [
                '< -$1000', '-$1000 to -$500', '-$500 to -$200', '-$200 to -$100',
                '-$100 to -$50', '-$50 to $0', '$0 to $50', '$50 to $100',
                '$100 to $200', '$200 to $500', '$500 to $1000', '> $1000'
            ];
            const tradeCounts = Array(profitBins.length - 1).fill(0);

            // Count trades in each bin
            ${JSON.stringify(this.trades)}.forEach(trade => {
                for (let i = 0; i < profitBins.length - 1; i++) {
                    if (trade.netPnl > profitBins[i] && trade.netPnl <= profitBins[i + 1]) {
                        tradeCounts[i]++;
                        break;
                    }
                }
            });

            // Create trade distribution chart
            const tradeDistCtx = document.getElementById('tradeDistChart').getContext('2d');
            new Chart(tradeDistCtx, {
                type: 'bar',
                data: {
                    labels: profitLabels,
                    datasets: [{
                        label: 'Number of Trades',
                        data: tradeCounts,
                        backgroundColor: profitLabels.map((_, i) => {
                            if (i < 6) return '#f44336'; // Red for losses
                            return '#4caf50'; // Green for profits
                        }),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Trade Profit Distribution',
                            color: '#e0e0e0',
                            font: { size: 16 }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#e0e0e0' },
                            grid: { color: '#333' }
                        },
                        y: {
                            ticks: { color: '#e0e0e0' },
                            grid: { color: '#333' }
                        }
                    }
                }
            });

            // Process monthly performance data
            const monthlyData = {};
            ${JSON.stringify(this.trades)}.forEach(trade => {
                const date = new Date(trade.exitTime);
                const monthYear = \`\${date.getFullYear()}-\${(date.getMonth() + 1).toString().padStart(2, '0')}\`;

                if (!monthlyData[monthYear]) {
                    monthlyData[monthYear] = 0;
                }

                monthlyData[monthYear] += trade.netPnl;
            });

            const monthLabels = Object.keys(monthlyData).sort();
            const monthValues = monthLabels.map(month => monthlyData[month]);

            // Create monthly performance chart
            const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
            new Chart(monthlyCtx, {
                type: 'bar',
                data: {
                    labels: monthLabels,
                    datasets: [{
                        label: 'Monthly P&L',
                        data: monthValues,
                        backgroundColor: monthValues.map(value => value >= 0 ? '#4caf50' : '#f44336'),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Monthly Performance',
                            color: '#e0e0e0',
                            font: { size: 16 }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#e0e0e0' },
                            grid: { color: '#333' }
                        },
                        y: {
                            ticks: { color: '#e0e0e0' },
                            grid: { color: '#333' }
                        }
                    }
                }
            });
        </script>
    </div>
</body>
</html>`;

        // Write HTML to file
        fs.writeFileSync(outputPath, html);
        console.log(`Report generated at ${outputPath}`);
    }
}

module.exports = Backtest;
