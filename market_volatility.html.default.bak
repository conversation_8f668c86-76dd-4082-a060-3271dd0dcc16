<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Market Volatility</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Press+Start+2P&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
            --nasdaq: #6f42c1;
            --nasdaq-glow: rgba(111, 66, 193, 0.5);
            --sp500: #21ce99;
            --sp500-glow: rgba(33, 206, 153, 0.5);
            --gold: #FFD700;
            --gold-glow: rgba(255, 215, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        /* Improve readability */
        .menu-item-text, .stat-label, .card-title, .dashboard-title h1 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .dashboard-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
        }

        .dashboard-title h1 {
            margin-bottom: 0;
        }

        .volatility-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .dashboard-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            min-width: 150px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-value.positive {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }

        .stat-value.negative {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .stat-value.warning {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0;
        }

        .chart-container {
            position: relative;
            height: 250px;
            margin-top: 1rem;
        }

        .dashboard-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .gauge-container {
            position: relative;
            width: 100%;
            height: 200px;
            margin: 0 auto;
        }

        .gauge {
            width: 100%;
            height: 100%;
        }

        .gauge-value {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .gauge-label {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            text-align: center;
            font-family: 'Orbitron', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            transform: translateY(-50%);
        }

        .volatility-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .volatility-table th, .volatility-table td {
            padding: 0.75rem;
            text-align: center;
            border: 1px solid var(--border);
        }

        .volatility-table th {
            background-color: rgba(0, 204, 255, 0.1);
            color: var(--primary);
            font-weight: 600;
        }

        .volatility-table td {
            font-weight: 500;
        }

        .low-volatility {
            color: var(--success);
        }

        .medium-volatility {
            color: var(--warning);
        }

        .high-volatility {
            color: var(--danger);
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                flex-wrap: wrap;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<div class="dashboard-header">
<div class="dashboard-title">
<div class="volatility-icon">📊</div>
<h1>MARKET VOLATILITY</h1>
</div>
<div class="dashboard-stats">
<div class="stat-card">
<div class="stat-label">MNQ Volatility</div>
<div class="stat-value warning">MEDIUM</div>
</div>
<div class="stat-card">
<div class="stat-label">MES Volatility</div>
<div class="stat-value positive">LOW</div>
</div>
<div class="stat-card">
<div class="stat-label">MGC Volatility</div>
<div class="stat-value negative">HIGH</div>
</div>
</div>
</div>
<div class="dashboard-grid">
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Volatility Trends</h2>
</div>
<div class="chart-container">
<canvas id="volatilityChart"></canvas>
</div>
</div>
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Risk Exposure Meter</h2>
</div>
<div class="gauge-container">
<canvas id="riskGauge"></canvas>
<div class="gauge-label">CURRENT RISK LEVEL</div>
<div class="gauge-value">MEDIUM (65%)</div>
</div>
</div>
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">ATR Analysis</h2>
</div>
<table class="volatility-table">
<thead>
<tr>
<th>Instrument</th>
<th>Current ATR</th>
<th>ATR %</th>
<th>Regime</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>MNQ</strong></td>
<td>6.63</td>
<td>0.41%</td>
<td class="medium-volatility">MEDIUM</td>
</tr>
<tr>
<td><strong>MES</strong></td>
<td>2.84</td>
<td>0.32%</td>
<td class="low-volatility">LOW</td>
</tr>
<tr>
<td><strong>MGC</strong></td>
<td>1.87</td>
<td>0.52%</td>
<td class="high-volatility">HIGH</td>
</tr>
</tbody>
</table>
</div>
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Position Sizing Recommendations</h2>
</div>
<table class="volatility-table">
<thead>
<tr>
<th>Instrument</th>
<th>Base Size</th>
<th>Recommended Size</th>
<th>Adjustment</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>MNQ</strong></td>
<td>10</td>
<td>8</td>
<td class="medium-volatility">-20%</td>
</tr>
<tr>
<td><strong>MES</strong></td>
<td>10</td>
<td>12</td>
<td class="low-volatility">+20%</td>
</tr>
<tr>
<td><strong>MGC</strong></td>
<td>10</td>
<td>5</td>
<td class="high-volatility">-50%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div class="dashboard-footer">
<p>QUANTUM CAPITAL | MARKET VOLATILITY DASHBOARD | LAST UPDATED: MAY 8, 2025</p>
<p style="margin-top: 5px; font-weight: bold; color: var(--warning);">VOLATILITY REGIMES ARE DYNAMIC AND SUBJECT TO CHANGE</p>
</div>
<script>
        // Volatility Chart
        const volatilityCtx = document.getElementById('volatilityChart').getContext('2d');
        const volatilityChart = new Chart(volatilityCtx, {
            type: 'line',
            data: {
                labels: ['Apr 1', 'Apr 8', 'Apr 15', 'Apr 22', 'Apr 29', 'May 6', 'May 13', 'May 20', 'May 27', 'Jun 3', 'Jun 10', 'Jun 17'],
                datasets: [
                    {
                        label: 'MNQ ATR',
                        data: [5.2, 5.8, 6.1, 6.5, 7.2, 7.8, 7.5, 7.1, 6.8, 6.5, 6.3, 6.63],
                        borderColor: '#6f42c1',
                        backgroundColor: 'rgba(111, 66, 193, 0.1)',
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'MES ATR',
                        data: [2.1, 2.3, 2.5, 2.7, 3.0, 3.2, 3.1, 2.9, 2.8, 2.7, 2.6, 2.84],
                        borderColor: '#21ce99',
                        backgroundColor: 'rgba(33, 206, 153, 0.1)',
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'MGC ATR',
                        data: [1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 1.9, 1.8, 1.87],
                        borderColor: '#FFD700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f8fafc',
                            font: {
                                family: 'Rajdhani'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    }
                }
            }
        });

        // Risk Gauge
        const riskGaugeCtx = document.getElementById('riskGauge').getContext('2d');
        const riskGauge = new Chart(riskGaugeCtx, {
            type: 'doughnut',
            data: {
                labels: ['Low Risk', 'Medium Risk', 'High Risk'],
                datasets: [{
                    data: [35, 30, 35],
                    backgroundColor: [
                        '#00ff88',
                        '#ffcc00',
                        '#ff3366'
                    ],
                    borderColor: [
                        '#00cc66',
                        '#d4aa00',
                        '#cc2952'
                    ],
                    borderWidth: 1,
                    circumference: 180,
                    rotation: 270
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                cutout: '70%'
            }
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Market Volatility`;
            }

            // Update volatility data
            updateVolatilityData(data);

            console.log('Market volatility dashboard updated with data for:', data.instrumentCode);
        }

        // Update volatility data
        function updateVolatilityData(data) {
            // This would be implemented to update the volatility data with actual data
            // For now, we'll just log that we would update the volatility data
            console.log('Volatility data would be updated with:', data);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
