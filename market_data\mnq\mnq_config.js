// config.js - GOATED Optimized Configuration with 0 bar latency
// Using the best parameters from the grid test

module.exports = {
    // --- General Settings ---
    inputFile: './input/MNQ2020_2025430.csv',
    initialBalance: 10000,

    // --- Instrument Specifics (MNQ Specs) ---
    pointValue: 2.00,
    tickSize: 0.25,
    pricePrecision: 2,

    // --- Costs & Slippage ---
    commissionPerContract: 0.4, // Realistic commission costs
    slippagePoints: 0.75,       // Realistic slippage

    // --- Indicator Periods ---
    atrPeriod: 14,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    sma200Period: 0,
    wma50Period: 0,

    // --- Strategy Parameters ---
    fixedTpPoints: 40,
    useWmaFilter: false,
    useTwoBarColorExit: false,
    minAtrEntry: 0,
    minRsiMaSeparation: 0,

    // --- RSI Bands ---
    rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

    // --- Run Mode: OPTIMIZED CONFIGURATION ---
    isAdaptiveRun: true,

    // Using optimal parameters from grid test
    slFactors: 4.5,
    tpFactors: 3.0,
    trailFactors: 0.11,

    // 0 bar latency
    latencyDelayBars: 0,

    // Keep these for compatibility
    costGrid: null,
    riskPercentGrid: null,
    fixedContractsGrid: null,
    fixedTpPointsGrid: null,

    // Fixed parameters
    riskPercent: 0,
    fixedContracts: 10,
    maxContracts: 10,

    // For compatibility with backtest.js
    currentRiskPercent: 0,
    currentFixedContracts: 10,

    // --- Time Filter Settings ---
    timeFilterEnabled: false,

    // --- Enhanced Features Configuration ---
    // All enhanced features disabled
    useAdaptiveSlippage: false,
    minSpreadPoints: 0.25,
    defaultSpreadPoints: 0.5,
    useVolatilityPositionSizing: false,
    useDynamicPositionSizing: false,
    basePositionSize: 10,
    maxPositionSize: 10,
    minPositionSize: 10,
    useMLEntryFilter: false,
    useAdvancedMLFilter: false,
    mlEntryThreshold: 0.5,
    useTimeAnalysis: false,
    timeScoreThreshold: 0.5,
    useCircuitBreakers: false,
    maxSlippageMultiplier: 5,
    pauseTradingOnExcessiveSlippage: false,
    useSteppedTrail: false,
    trailStepSizeMultiplier: 0.1,
    spreadBufferMultiplier: 2.0
};
