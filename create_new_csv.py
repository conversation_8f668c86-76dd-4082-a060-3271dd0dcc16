#!/usr/bin/env python3
"""
Create new CSV files with the correct format.
"""

import os
import sys
import csv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_new_csv(input_file, output_file):
    """
    Create a new CSV file with the correct format.
    
    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Creating new CSV file from {input_file}...")
        
        # Read the input file
        with open(input_file, 'r') as f:
            reader = csv.reader(f)
            rows = list(reader)
        
        if not rows:
            logger.error(f"Empty file: {input_file}")
            return False
        
        # Create new rows with only the required columns
        new_rows = [['timestamp', 'open', 'high', 'low', 'close', 'volume']]
        
        # Process data rows
        for row in rows[1:]:
            if len(row) >= 6:
                new_row = row[:6]
                new_rows.append(new_row)
        
        # Write the output file
        with open(output_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(new_rows)
        
        logger.info(f"Saved {len(new_rows) - 1} rows to {output_file}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error creating new CSV file: {str(e)}")
        return False

def main():
    """Main function."""
    # Configure paths
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    output_dir = os.path.join('C:', 'backtest-bot', 'input', 'fixed')
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find CSV files
    csv_files = [f for f in os.listdir(input_dir) if f.endswith('_1m.csv')]
    
    if not csv_files:
        logger.error("No CSV files found")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files")
    
    # Process each CSV file
    for csv_file in csv_files:
        input_file = os.path.join(input_dir, csv_file)
        output_file = os.path.join(output_dir, csv_file)
        success = create_new_csv(input_file, output_file)
        
        if not success:
            logger.error(f"Failed to create new CSV file for {csv_file}")
        else:
            # Copy the fixed file back to the input directory
            fixed_file = os.path.join(output_dir, csv_file)
            with open(fixed_file, 'r') as f:
                fixed_content = f.read()
            
            with open(input_file, 'w') as f:
                f.write(fixed_content)
            
            logger.info(f"Copied fixed file back to {input_file}")
    
    logger.info("Data fixing complete!")

if __name__ == "__main__":
    main()
