/**
 * Paper Trading Configuration for MNQ
 *
 * This configuration matches the optimized backtest settings
 * that produced the 3.3 million profit over 5 years.
 */

module.exports = {
  // --- General Settings ---
  symbol: 'MNQ',
  contractMonth: 'M5', // Updated to May 2025 contract
  accountType: 'DEMO',
  initialBalance: 10000,

  // --- Instrument Specifics (MNQ Specs) ---
  pointValue: 2.00,
  tickSize: 0.25,
  pricePrecision: 2,

  // --- Costs & Slippage ---
  commissionPerContract: 0.0, // No commission for demo trading
  slippagePoints: 0.0,       // No slippage for demo trading

  // --- Indicator Periods ---
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 200, // OPTIMIZED: Enable 200 SMA for confluence filter
  wma50Period: 50,   // OPTIMIZED: Enable 50 WMA

  // --- Strategy Parameters - OPTIMIZED FOR FIXED POINTS ---
  useFixedPoints: true,           // OPTIMIZED: Use fixed points instead of ATR-based
  fixedTpPoints: 3,              // OPTIMIZED: 3-point take profit (best from backtesting)
  fixedSlPoints: 3,              // OPTIMIZED: 3-point stop loss (best from backtesting)
  disableTrailingStop: true,     // OPTIMIZED: No trailing stops
  useWmaFilter: true,            // OPTIMIZED: Enable WMA filter
  use200SmaFilter: true,         // OPTIMIZED: Enable 200 SMA confluence filter (THE GAME CHANGER!)
  useTwoBarColorExit: false,     // OPTIMIZED: Disable time-based exits
  minAtrEntry: 0.5,              // OPTIMIZED: Lower ATR threshold
  minRsiMaSeparation: 0,         // OPTIMIZED: No RSI-MA separation requirement
  requireClosedCandles: true,    // Only trade on fully closed candles

  // --- RSI Bands ---
  rsiUpperBand: 60,
  rsiLowerBand: 40,
  rsiMiddleBand: 50,

  // --- Run Mode: OPTIMIZED CONFIGURATION ---
  isAdaptiveRun: true,

  // Using optimal parameters from grid test
  slFactors: 4.5,
  tpFactors: 3.0,
  trailFactors: 0.11,

  // 0 bar latency
  latencyDelayBars: 0,

  // Position sizing
  riskPercent: 0,
  fixedContracts: 3, // Start with 3 contracts as requested
  maxContracts: 3,  // Maximum position size

  // Risk management
  dailyStopLoss: 500,      // $500 daily stop loss (5% of $10k account)
  dailyProfitTarget: 0,    // No profit target (let winners run)
  maxDrawdownPercent: 5,   // 5% maximum drawdown

  // --- Time Filter Settings ---
  timeFilterEnabled: false,

  // --- Enhanced Features Configuration ---
  // All enhanced features disabled
  useAdaptiveSlippage: false,
  minSpreadPoints: 0.25,
  defaultSpreadPoints: 0.5,
  useVolatilityPositionSizing: false,
  useDynamicPositionSizing: false,
  basePositionSize: 3,
  maxPositionSize: 3,
  minPositionSize: 3,
  useMLEntryFilter: false,
  useAdvancedMLFilter: false,
  mlEntryThreshold: 0.5,
  useTimeAnalysis: false,
  timeScoreThreshold: 0.5,
  useCircuitBreakers: false,
  maxSlippageMultiplier: 5,
  pauseTradingOnExcessiveSlippage: false,
  useSteppedTrail: false,
  trailStepSizeMultiplier: 0.1,
  spreadBufferMultiplier: 2.0
};
