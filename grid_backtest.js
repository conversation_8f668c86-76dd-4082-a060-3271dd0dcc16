/**
 * Grid Backtest - Optimized for testing multiple markets and parameters
 * Combines the best parts of backtest.js and backtest_framework.js
 */

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Default configuration
const DEFAULT_CONFIG = {
    // Instrument settings
    symbol: 'MNQ',
    pointValue: 2.0,
    commission: 0.40,
    slippage: 0.0,

    // Position sizing
    fixedContracts: 10,

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14,

    // Entry/exit parameters
    slFactors: 4.5,
    tpFactors: 3.0,
    trailFactors: 0.11,
    fixedTpPoints: 40,

    // Circuit breakers
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0,
    minRsiMaSeparation: 0,
    useWmaFilter: true,

    // Initial balance
    initialBalance: 10000,

    // Output settings
    outputDir: './output',
    pricePrecision: 2
};

// ATR Thresholds for adaptive mode
const ATR_THRESHOLDS = {
    MNQ: { low_medium: 4.7601, medium_high: 7.2605 },
    MES: { low_medium: 3.0, medium_high: 5.0 },
    MGC: { low_medium: 1.5, medium_high: 3.0 }
};

// Adaptive parameters for each volatility regime
const ADAPTIVE_PARAMS = {
    MNQ: {
        Low: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
        Medium: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
        High: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 }
    },
    MES: {
        Low: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
        Medium: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 },
        High: { slFactor: 3.0, tpFactor: 3.0, trailFactor: 0.01 }
    },
    MGC: {
        Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
    }
};

/**
 * Grid Backtest class
 */
class GridBacktest {
    constructor(config = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.symbol = this.config.symbol;
        this.candles = [];
        this.trades = [];
        this.dailyStats = {};
        this.balance = this.config.initialBalance;
        this.initialBalance = this.config.initialBalance;
        this.highWaterMark = this.config.initialBalance;
        this.maxDrawdown = 0;
        this.outputDir = this.config.outputDir;

        // Ensure output directory exists
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    /**
     * Load historical data from CSV file
     * @param {string} filePath - Path to CSV file
     * @param {Object} options - Options for loading data
     * @returns {Promise<void>}
     */
    async loadData(filePath, options = {}) {
        console.log(`Loading data from ${filePath}...`);

        return new Promise((resolve, reject) => {
            if (!fs.existsSync(filePath)) {
                console.error(`File not found: ${filePath}`);
                resolve();
                return;
            }

            const results = [];

            // Determine date range for filtering
            let startTimestamp = 0;
            let endTimestamp = Number.MAX_SAFE_INTEGER;

            if (options.dateRange) {
                if (options.dateRange.startDate) {
                    const startDate = new Date(options.dateRange.startDate);
                    startTimestamp = startDate.getTime();
                }

                if (options.dateRange.endDate) {
                    const endDate = new Date(options.dateRange.endDate);
                    endDate.setHours(23, 59, 59, 999);
                    endTimestamp = endDate.getTime();
                }

                console.log(`Date range: ${new Date(startTimestamp).toISOString().split('T')[0]} to ${
                    endTimestamp === Number.MAX_SAFE_INTEGER ? 'present' : new Date(endTimestamp).toISOString().split('T')[0]
                }`);
            }

            fs.createReadStream(filePath)
                .pipe(csv({
                    separator: ';',
                    mapHeaders: ({ header }) => header.trim().toLowerCase()
                }))
                .on('data', (data) => {
                    // Try to find timestamp/date field
                    let timestampField =
                        data.timestamp !== undefined ? 'timestamp' :
                        data.date !== undefined ? 'date' :
                        data.time !== undefined ? 'time' :
                        data.datetime !== undefined ? 'datetime' : null;

                    // Special case for the specific CSV format
                    if (!timestampField && data['time left'] !== undefined) {
                        timestampField = 'time left';
                    }

                    if (!timestampField) return;

                    // Parse timestamp
                    const timestamp = new Date(data[timestampField]).getTime();

                    // Skip data outside the specified date range
                    if ((timestamp < startTimestamp) || (timestamp > endTimestamp)) {
                        return;
                    }

                    // Format the data
                    const candle = {
                        timestamp,
                        open: parseFloat(data.open),
                        high: parseFloat(data.high),
                        low: parseFloat(data.low),
                        close: parseFloat(data.close),
                        volume: parseFloat(data.volume || 0)
                    };

                    // Validate the data
                    if (!isNaN(candle.timestamp) &&
                        !isNaN(candle.open) &&
                        !isNaN(candle.high) &&
                        !isNaN(candle.low) &&
                        !isNaN(candle.close)) {
                        results.push(candle);
                    }
                })
                .on('end', () => {
                    // Sort by timestamp
                    results.sort((a, b) => a.timestamp - b.timestamp);
                    this.candles = results;

                    console.log(`Loaded ${results.length.toLocaleString()} candles`);

                    if (results.length > 0) {
                        const startDate = new Date(results[0].timestamp);
                        const endDate = new Date(results[results.length - 1].timestamp);
                        console.log(`Data period: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);
                    }

                    resolve();
                })
                .on('error', (error) => {
                    console.error(`Error parsing CSV: ${error.message}`);
                    reject(error);
                });
        });
    }

    /**
     * Calculate SMA (Simple Moving Average)
     * @param {Array} values - Array of values
     * @param {number} period - Period for SMA
     * @returns {number} - SMA value
     */
    calculateSMA(values, period) {
        if (!values || values.length < period) return NaN;

        const sum = values.slice(values.length - period).reduce((a, b) => a + b, 0);
        return sum / period;
    }

    /**
     * Calculate WMA (Weighted Moving Average)
     * @param {Array} candles - Array of candles
     * @param {number} period - Period for WMA
     * @returns {number} - WMA value
     */
    calculateWMA(candles, period) {
        if (!candles || candles.length < period) return NaN;

        let weightedSum = 0;
        let weightSum = 0;

        for (let i = 0; i < period; i++) {
            const idx = candles.length - 1 - i;
            const weight = period - i;

            weightedSum += candles[idx].close * weight;
            weightSum += weight;
        }

        return weightedSum / weightSum;
    }

    /**
     * Calculate RSI (Relative Strength Index) - Optimized version
     * @param {Array} candles - Array of candles
     * @param {number} period - Period for RSI
     * @returns {number} - RSI value
     */
    calculateRSI(candles, period) {
        if (!candles || candles.length <= period) return NaN;

        // For efficiency, we'll use the last candle's index
        const lastIndex = candles.length - 1;

        // If we have exactly period+1 candles, calculate the simple way
        if (candles.length === period + 1) {
            let gains = 0;
            let losses = 0;

            for (let i = 1; i <= period; i++) {
                const change = candles[i].close - candles[i - 1].close;

                if (change > 0) {
                    gains += change;
                } else {
                    losses -= change;
                }
            }

            const avgGain = gains / period;
            const avgLoss = losses / period;

            if (avgLoss === 0) return 100;
            if (avgGain === 0) return 0;

            const rs = avgGain / avgLoss;
            return 100 - (100 / (1 + rs));
        }

        // For efficiency with large datasets, use the Wilder's smoothing method
        // This is much faster for calculating RSI on long series

        // First, calculate the first average gain and loss
        let sumGain = 0;
        let sumLoss = 0;

        for (let i = lastIndex - period; i < lastIndex; i++) {
            const change = candles[i + 1].close - candles[i].close;

            if (change > 0) {
                sumGain += change;
            } else {
                sumLoss -= change;
            }
        }

        // Calculate first average gain and loss
        let avgGain = sumGain / period;
        let avgLoss = sumLoss / period;

        // Calculate the current gain/loss
        const currentChange = candles[lastIndex].close - candles[lastIndex - 1].close;
        const currentGain = currentChange > 0 ? currentChange : 0;
        const currentLoss = currentChange < 0 ? -currentChange : 0;

        // Update average gain and loss using Wilder's smoothing
        avgGain = ((avgGain * (period - 1)) + currentGain) / period;
        avgLoss = ((avgLoss * (period - 1)) + currentLoss) / period;

        // Handle edge cases
        if (avgLoss === 0) return 100;
        if (avgGain === 0) return 0;

        // Calculate RS and RSI
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }

    /**
     * Calculate ATR (Average True Range)
     * @param {Array} candles - Array of candles
     * @param {number} period - Period for ATR
     * @returns {number} - ATR value
     */
    calculateATR(candles, period) {
        if (!candles || candles.length < period + 1) return NaN;

        const trValues = [];

        for (let i = 1; i < candles.length; i++) {
            const current = candles[i];
            const previous = candles[i - 1];

            const tr = Math.max(
                current.high - current.low,
                Math.abs(current.high - previous.close),
                Math.abs(current.low - previous.close)
            );

            trValues.push(tr);
        }

        // Use simple average for ATR
        const sum = trValues.slice(-period).reduce((a, b) => a + b, 0);
        return sum / period;
    }

    /**
     * Calculate all indicators for a candle
     * @param {number} index - Index of candle
     * @param {Array} preCalcRsiValues - Pre-calculated RSI values
     * @returns {Object} - Calculated indicators
     */
    calculateIndicators(index, preCalcRsiValues = []) {
        // Get candles up to current index
        const candles = this.candles.slice(0, index + 1);

        // Calculate ATR
        const atr = this.calculateATR(candles, this.config.atrPeriod);

        // Use pre-calculated RSI if available, otherwise calculate it
        const rsi = preCalcRsiValues[index] || this.calculateRSI(candles, this.config.rsiPeriod);

        // Calculate RSI-based MA more efficiently
        const rsiValues = [];
        for (let i = 0; i < this.config.rsiMaPeriod; i++) {
            const rsiIndex = index - i;
            if (rsiIndex >= 0 && preCalcRsiValues[rsiIndex]) {
                rsiValues.unshift(preCalcRsiValues[rsiIndex]);
            }
        }

        const rsiBasedMA = rsiValues.length === this.config.rsiMaPeriod ?
            this.calculateSMA(rsiValues, this.config.rsiMaPeriod) : NaN;

        // Calculate WMA
        const wma50 = this.calculateWMA(candles, this.config.wma50Period);

        // Determine ATR regime
        let atrRegime = 'Medium'; // Default regime
        const thresholds = ATR_THRESHOLDS[this.symbol] || { low_medium: 1.5, medium_high: 3.0 };

        if (atr < thresholds.low_medium) {
            atrRegime = 'Low';
        } else if (atr > thresholds.medium_high) {
            atrRegime = 'High';
        }

        return {
            atr,
            rsi,
            rsiBasedMA,
            wma50,
            atrRegime
        };
    }

    /**
     * Determine candlestick color
     * @param {Object} candle - Candle object
     * @returns {string} - 'green', 'red', or 'invalid'
     */
    candlestickColor(candle) {
        if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
            return 'invalid';
        }
        return candle.close >= candle.open ? 'green' : 'red';
    }

    /**
     * Detect 3-candle pattern
     * @param {Object} c1 - First candle
     * @param {Object} c2 - Second candle
     * @param {Object} c3 - Third candle
     * @returns {string|null} - 'bullish', 'bearish', or null
     */
    detect3(c1, c2, c3) {
        if (!c1 || !c2 || !c3) return null;

        const col1 = this.candlestickColor(c1);
        const col2 = this.candlestickColor(c2);
        const col3 = this.candlestickColor(c3);

        if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

        if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
            const engulf = c3.close > c2.open && c3.open < c2.close;
            const wick3 = Math.min(c3.open, c3.close) - c3.low;
            const wick2 = Math.min(c2.open, c2.close) - c2.low;

            if (engulf && wick3 > wick2) return 'bullish';
        }

        if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
            const engulf = c3.close < c2.open && c3.open > c2.close;
            const wick3 = c3.high - Math.max(c3.open, c3.close);
            const wick2 = c2.high - Math.max(c2.open, c2.close);

            if (engulf && wick3 > wick2) return 'bearish';
        }

        return null;
    }

    /**
     * Detect 4-candle pattern
     * @param {Object} c0 - First candle
     * @param {Object} c1 - Second candle
     * @param {Object} c2 - Third candle
     * @param {Object} c3 - Fourth candle
     * @returns {string|null} - 'bullish', 'bearish', or null
     */
    detect4(c0, c1, c2, c3) {
        if (!c0 || !c1 || !c2 || !c3) return null;

        const col0 = this.candlestickColor(c0);
        const col1 = this.candlestickColor(c1);
        const col2 = this.candlestickColor(c2);
        const col3 = this.candlestickColor(c3);

        if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

        if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
            if (c3.close > Math.max(c1.open, c2.open)) return 'bullish';
        }

        if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
            if (c3.close < Math.min(c1.open, c2.open)) return 'bearish';
        }

        return null;
    }

    /**
     * Check if entry conditions are met
     * @param {string} dir - Direction ('bullish' or 'bearish')
     * @param {string} patternType - Pattern type ('three' or 'four')
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Calculated indicators
     * @returns {boolean} - Whether entry conditions are met
     */
    entryOK(dir, patternType, candle, indicators) {
        // Basic validation
        if (isNaN(indicators.wma50) || isNaN(indicators.rsi) || isNaN(indicators.rsiBasedMA) || isNaN(indicators.atr)) {
            return false;
        }

        // WMA filter
        if (this.config.useWmaFilter &&
            ((dir === 'bullish' && candle.close <= indicators.wma50) ||
             (dir === 'bearish' && candle.close >= indicators.wma50))) {
            return false;
        }

        // RSI filter
        if ((dir === 'bullish' && indicators.rsi <= indicators.rsiBasedMA) ||
            (dir === 'bearish' && indicators.rsi >= indicators.rsiBasedMA)) {
            return false;
        }

        // Minimum ATR filter
        const minAtrEntry = this.config.minAtrEntry || 0;
        if (indicators.atr < minAtrEntry) {
            return false;
        }

        // RSI-MA separation filter
        const minRsiMaSeparation = this.config.minRsiMaSeparation || 0;
        if (Math.abs(indicators.rsi - indicators.rsiBasedMA) < minRsiMaSeparation) {
            return false;
        }

        return true;
    }

    /**
     * Run backtest with current parameters
     * @param {Object} params - Parameters for this run
     * @returns {Object} - Backtest results
     */
    runBacktest(params = {}) {
        // Apply parameters for this run
        const runConfig = { ...this.config, ...params };

        console.log(`Running backtest for ${this.symbol} with parameters:`, runConfig);

        // Performance tracking
        const startTime = Date.now();
        let lastTime = startTime;
        let indicatorTime = 0;
        let entryCheckTime = 0;
        let exitCheckTime = 0;

        function logPerformance(step) {
            const now = Date.now();
            console.log(`${step}: ${(now - lastTime) / 1000}s`);
            lastTime = now;
        }

        // Reset state
        this.balance = this.initialBalance;
        this.highWaterMark = this.initialBalance;
        this.maxDrawdown = 0;
        this.trades = [];
        this.dailyStats = {};

        // Need at least 50 candles for indicators
        if (this.candles.length < 50) {
            console.error('Not enough candles for backtest');
            return {
                success: false,
                error: 'Not enough candles for backtest'
            };
        }

        // Current position
        let position = null;

        // Daily tracking
        let currentDay = '';
        let dailyPnL = 0;
        let dailyWins = 0;
        let dailyLosses = 0;

        // Pre-calculate some indicators to improve performance
        console.log(`Pre-calculating indicators for ${this.candles.length} candles...`);
        const preCalcStartTime = Date.now();

        // Store RSI values for reuse - optimized calculation
        const rsiValues = new Array(this.candles.length).fill(NaN);
        const period = this.config.rsiPeriod;

        // We need at least period+1 candles to calculate RSI
        if (this.candles.length > period) {
            // Calculate initial gains and losses for the first period
            let sumGain = 0;
            let sumLoss = 0;

            for (let i = 1; i <= period; i++) {
                const change = this.candles[i].close - this.candles[i - 1].close;

                if (change > 0) {
                    sumGain += change;
                } else {
                    sumLoss -= change;
                }
            }

            // Calculate first RSI
            let avgGain = sumGain / period;
            let avgLoss = sumLoss / period;

            if (avgLoss === 0) {
                rsiValues[period] = 100;
            } else if (avgGain === 0) {
                rsiValues[period] = 0;
            } else {
                const rs = avgGain / avgLoss;
                rsiValues[period] = 100 - (100 / (1 + rs));
            }

            // Calculate RSI for the rest of the candles using Wilder's smoothing
            for (let i = period + 1; i < this.candles.length; i++) {
                const change = this.candles[i].close - this.candles[i - 1].close;
                const gain = change > 0 ? change : 0;
                const loss = change < 0 ? -change : 0;

                // Update average gain and loss using Wilder's smoothing
                avgGain = ((avgGain * (period - 1)) + gain) / period;
                avgLoss = ((avgLoss * (period - 1)) + loss) / period;

                if (avgLoss === 0) {
                    rsiValues[i] = 100;
                } else if (avgGain === 0) {
                    rsiValues[i] = 0;
                } else {
                    const rs = avgGain / avgLoss;
                    rsiValues[i] = 100 - (100 / (1 + rs));
                }
            }
        }

        logPerformance('Pre-calculating RSI');

        // Process each candle
        console.log(`Processing ${this.candles.length - 50} candles...`);
        for (let i = 50; i < this.candles.length; i++) {
            const candle = this.candles[i];

            // Get date string for daily tracking
            const date = new Date(candle.timestamp);
            const dateStr = date.toISOString().split('T')[0];

            // Reset daily stats on new day
            if (dateStr !== currentDay) {
                if (currentDay !== '') {
                    // Save previous day's stats
                    this.dailyStats[currentDay] = {
                        pnl: dailyPnL,
                        wins: dailyWins,
                        losses: dailyLosses,
                        trades: dailyWins + dailyLosses
                    };
                }

                // Reset for new day
                currentDay = dateStr;
                dailyPnL = 0;
                dailyWins = 0;
                dailyLosses = 0;
            }

            // Calculate indicators
            const indicatorStartTime = Date.now();
            const indicators = this.calculateIndicators(i, rsiValues);
            indicatorTime += Date.now() - indicatorStartTime;

            // Add indicators to candle for reference
            candle.indicators = indicators;

            // Check for exit if we have a position
            if (position) {
                const exitStartTime = Date.now();
                const exitResult = this.checkForExit(position, candle, indicators, i);
                exitCheckTime += Date.now() - exitStartTime;

                if (exitResult) {
                    // Update balance
                    this.balance += exitResult.pnl;
                    dailyPnL += exitResult.pnl;

                    // Update high water mark and drawdown
                    if (this.balance > this.highWaterMark) {
                        this.highWaterMark = this.balance;
                    } else {
                        const drawdown = (this.highWaterMark - this.balance) / this.highWaterMark * 100;
                        this.maxDrawdown = Math.max(this.maxDrawdown, drawdown);
                    }

                    // Update win/loss count
                    if (exitResult.pnl > 0) {
                        dailyWins++;
                    } else {
                        dailyLosses++;
                    }

                    // Save trade
                    this.trades.push(exitResult.trade);

                    // Clear position
                    position = null;
                }
            }

            // Check for entry if we don't have a position
            if (!position) {
                // Get recent candles for pattern detection
                if (i < 3) continue; // Need at least 4 candles

                const entryStartTime = Date.now();

                const c0 = this.candles[i - 3];
                const c1 = this.candles[i - 2];
                const c2 = this.candles[i - 1];
                const c3 = candle;

                // Check for patterns
                const p3 = this.detect3(c1, c2, c3);
                const p4 = this.detect4(c0, c1, c2, c3);

                let pattern = null;
                let patternType = null;

                if (p4) {
                    pattern = p4;
                    patternType = 'four';
                } else if (p3) {
                    pattern = p3;
                    patternType = 'three';
                }

                // If pattern found and entry filter passes
                if (pattern && this.entryOK(pattern, patternType, candle, indicators)) {
                    // Create position
                    position = this.createPosition(pattern, candle, indicators, i, runConfig);
                }

                entryCheckTime += Date.now() - entryStartTime;
            }
        }

        // Save last day's stats
        if (currentDay !== '') {
            this.dailyStats[currentDay] = {
                pnl: dailyPnL,
                wins: dailyWins,
                losses: dailyLosses,
                trades: dailyWins + dailyLosses
            };
        }

        // Calculate final statistics
        const stats = this.calculateStatistics();

        // Log performance summary
        const totalTime = Date.now() - startTime;
        console.log(`\n===== PERFORMANCE SUMMARY =====`);
        console.log(`Total execution time: ${totalTime / 1000}s`);
        console.log(`Indicator calculation time: ${indicatorTime / 1000}s (${(indicatorTime / totalTime * 100).toFixed(2)}%)`);
        console.log(`Entry check time: ${entryCheckTime / 1000}s (${(entryCheckTime / totalTime * 100).toFixed(2)}%)`);
        console.log(`Exit check time: ${exitCheckTime / 1000}s (${(exitCheckTime / totalTime * 100).toFixed(2)}%)`);
        console.log(`Other processing time: ${(totalTime - indicatorTime - entryCheckTime - exitCheckTime) / 1000}s (${((totalTime - indicatorTime - entryCheckTime - exitCheckTime) / totalTime * 100).toFixed(2)}%)`);

        return {
            success: true,
            stats,
            trades: this.trades,
            dailyStats: this.dailyStats,
            finalBalance: this.balance,
            maxDrawdown: this.maxDrawdown,
            performance: {
                totalTime,
                indicatorTime,
                entryCheckTime,
                exitCheckTime
            }
        };
    }

    /**
     * Create a new position
     * @param {string} direction - Trade direction ('bullish' or 'bearish')
     * @param {Object} candle - Entry candle
     * @param {Object} indicators - Calculated indicators
     * @param {number} index - Candle index
     * @param {Object} runConfig - Configuration for this run
     * @returns {Object} - Position object
     */
    createPosition(direction, candle, indicators, index, runConfig) {
        const entryPrice = candle.close;
        const atr = indicators.atr;

        // Determine parameters based on mode (adaptive or fixed)
        let slFactor, tpFactor, trailFactor;

        if (runConfig.isAdaptiveRun) {
            // Determine ATR regime
            const atrRegime = indicators.atrRegime;

            // Get parameters for current regime
            const adaptiveParams = ADAPTIVE_PARAMS[this.symbol] || ADAPTIVE_PARAMS.MNQ;
            slFactor = adaptiveParams[atrRegime].slFactor;
            tpFactor = adaptiveParams[atrRegime].tpFactor;
            trailFactor = adaptiveParams[atrRegime].trailFactor;
        } else {
            slFactor = runConfig.slFactors;
            tpFactor = runConfig.tpFactors;
            trailFactor = runConfig.trailFactors;
        }

        const slDistance = atr * slFactor;
        const tpDistance = Math.max(runConfig.fixedTpPoints || 0, atr * tpFactor);
        const contracts = runConfig.fixedContracts;

        return {
            entryTime: new Date(candle.timestamp),
            direction,
            entry: entryPrice,
            atr,
            stopLoss: direction === 'bullish' ? entryPrice - slDistance : entryPrice + slDistance,
            takeProfit: direction === 'bullish' ? entryPrice + tpDistance : entryPrice - tpDistance,
            trailStop: direction === 'bullish' ? entryPrice - (atr * trailFactor) : entryPrice + (atr * trailFactor),
            trailFactor,
            trailHigh: candle.high,
            trailLow: candle.low,
            contracts,
            entryBar: index,
            atrRegime: indicators.atrRegime
        };
    }

    /**
     * Check for exit conditions
     * @param {Object} position - Current position
     * @param {Object} candle - Current candle
     * @param {Object} indicators - Calculated indicators
     * @param {number} index - Candle index
     * @returns {Object|null} - Exit result or null if no exit
     */
    checkForExit(position, candle, indicators, index) {
        // Update trail values
        position.trailHigh = Math.max(position.trailHigh, candle.high);
        position.trailLow = Math.min(position.trailLow, candle.low);

        // Update trailing stop
        if (position.direction === 'bullish') {
            position.trailStop = Math.max(position.trailStop, position.trailHigh - (indicators.atr * position.trailFactor));
        } else {
            position.trailStop = Math.min(position.trailStop, position.trailLow + (indicators.atr * position.trailFactor));
        }

        // Check for exit conditions
        let exitReason = null;
        let exitPrice = null;

        if (position.direction === 'bullish') {
            // Stop loss hit
            if (candle.low <= position.stopLoss) {
                exitReason = 'sl';
                exitPrice = position.stopLoss;
            }
            // Take profit hit
            else if (candle.high >= position.takeProfit) {
                exitReason = 'tp';
                exitPrice = position.takeProfit;
            }
            // Trailing stop hit
            else if (candle.low <= position.trailStop) {
                exitReason = 'trail';
                exitPrice = position.trailStop;
            }
        } else { // Bearish position
            // Stop loss hit
            if (candle.high >= position.stopLoss) {
                exitReason = 'sl';
                exitPrice = position.stopLoss;
            }
            // Take profit hit
            else if (candle.low <= position.takeProfit) {
                exitReason = 'tp';
                exitPrice = position.takeProfit;
            }
            // Trailing stop hit
            else if (candle.high >= position.trailStop) {
                exitReason = 'trail';
                exitPrice = position.trailStop;
            }
        }

        // Process exit if triggered
        if (exitReason && exitPrice !== null) {
            // Apply slippage
            const slippage = this.config.slippage || 0;
            const adjustedExitPrice = position.direction === 'bullish' ?
                exitPrice - slippage : exitPrice + slippage;

            // Calculate P&L
            let pnlPoints = 0;
            if (position.direction === 'bullish') {
                pnlPoints = adjustedExitPrice - position.entry;
            } else {
                pnlPoints = position.entry - adjustedExitPrice;
            }

            // Calculate dollar P&L
            const pnl = pnlPoints * this.config.pointValue * position.contracts;

            // Calculate commission
            const commission = this.config.commission * position.contracts;

            // Calculate net P&L
            const netPnl = pnl - commission;

            // Create trade record
            const trade = {
                symbol: this.symbol,
                direction: position.direction,
                entry: position.entry,
                exit: adjustedExitPrice,
                entryTime: position.entryTime,
                exitTime: new Date(candle.timestamp),
                pnl: netPnl,
                pnlPoints,
                exitReason,
                contracts: position.contracts,
                atrRegime: position.atrRegime,
                commission
            };

            return {
                pnl: netPnl,
                trade
            };
        }

        return null;
    }

    /**
     * Calculate statistics from backtest results
     * @returns {Object} - Statistics
     */
    calculateStatistics() {
        // Count wins and losses
        let wins = 0;
        let losses = 0;
        let totalPnl = 0;
        let totalWinAmount = 0;
        let totalLossAmount = 0;

        for (const trade of this.trades) {
            totalPnl += trade.pnl;

            if (trade.pnl > 0) {
                wins++;
                totalWinAmount += trade.pnl;
            } else {
                losses++;
                totalLossAmount += Math.abs(trade.pnl);
            }
        }

        // Calculate win rate
        const winRate = this.trades.length > 0 ? (wins / this.trades.length * 100) : 0;

        // Calculate profit factor
        const profitFactor = totalLossAmount > 0 ? (totalWinAmount / totalLossAmount) :
            (totalWinAmount > 0 ? Infinity : 0);

        // Calculate average win and loss
        const avgWin = wins > 0 ? (totalWinAmount / wins) : 0;
        const avgLoss = losses > 0 ? (totalLossAmount / losses) : 0;

        // Calculate daily statistics
        let profitDays = 0;
        let lossDays = 0;

        for (const day in this.dailyStats) {
            if (this.dailyStats[day].pnl > 0) {
                profitDays++;
            } else {
                lossDays++;
            }
        }

        const totalDays = profitDays + lossDays;
        const winDayRate = totalDays > 0 ? (profitDays / totalDays * 100) : 0;

        // Calculate return
        const totalReturn = ((this.balance - this.initialBalance) / this.initialBalance) * 100;

        return {
            initialBalance: this.initialBalance,
            finalBalance: this.balance,
            totalPnl,
            totalReturn,
            totalTrades: this.trades.length,
            wins,
            losses,
            winRate,
            profitFactor,
            avgWin,
            avgLoss,
            maxDrawdown: this.maxDrawdown,
            totalDays,
            profitDays,
            lossDays,
            winDayRate
        };
    }

    /**
     * Run grid test with multiple parameter combinations
     * @param {Object} gridParams - Grid parameters
     * @returns {Array} - Array of results
     */
    runGridTest(gridParams) {
        console.log(`Running grid test for ${this.symbol}...`);

        const results = [];
        const paramCombinations = this.generateParameterCombinations(gridParams);

        console.log(`Generated ${paramCombinations.length} parameter combinations`);

        for (let i = 0; i < paramCombinations.length; i++) {
            const params = paramCombinations[i];
            console.log(`Running combination ${i + 1}/${paramCombinations.length}: ${JSON.stringify(params)}`);

            const result = this.runBacktest(params);

            if (result.success) {
                results.push({
                    params,
                    stats: result.stats
                });
            }
        }

        // Sort results by total PnL
        results.sort((a, b) => b.stats.totalPnl - a.stats.totalPnl);

        return results;
    }

    /**
     * Generate all parameter combinations for grid test
     * @param {Object} gridParams - Grid parameters
     * @returns {Array} - Array of parameter combinations
     */
    generateParameterCombinations(gridParams) {
        const combinations = [];

        // Helper function to generate combinations recursively
        function generateCombinations(keys, currentIndex, currentCombination) {
            if (currentIndex === keys.length) {
                combinations.push({ ...currentCombination });
                return;
            }

            const key = keys[currentIndex];
            const values = gridParams[key];

            for (const value of values) {
                currentCombination[key] = value;
                generateCombinations(keys, currentIndex + 1, currentCombination);
            }
        }

        // Get all keys with array values
        const keys = Object.keys(gridParams).filter(key => Array.isArray(gridParams[key]));

        // Generate combinations
        generateCombinations(keys, 0, {});

        return combinations;
    }
}

module.exports = GridBacktest;
