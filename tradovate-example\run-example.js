const { execSync } = require('child_process');
const path = require('path');

try {
  console.log('Starting Tradovate example...');
  console.log('Current directory:', __dirname);
  
  // Get the absolute path to the webpack.config.js file
  const configPath = path.resolve(__dirname, 'webpack.config.js');
  console.log('Config path:', configPath);
  
  // Run webpack dev server with the specified config
  execSync(`npx webpack serve --open --config "${configPath}"`, { 
    stdio: 'inherit',
    cwd: __dirname
  });
} catch (error) {
  console.error('Error starting Tradovate example:', error);
}
