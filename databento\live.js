/**
 * Databento Live API Client
 * For accessing real-time market data
 */

const WebSocket = require('ws');
const net = require('net');
const { EventEmitter } = require('events');

class LiveClient extends EventEmitter {
  /**
   * Create a new Live API client
   * @param {string} apiKey - Your Databento API key
   * @param {Object} options - Additional options
   */
  constructor(apiKey, options = {}) {
    super();
    this.apiKey = apiKey;
    // Based on the Python test, these are the correct endpoints
    this.wsUrl = 'wss://hist.databento.com/v0/live';
    this.rawHost = 'hist.databento.com';
    this.rawPort = 8080;
    this.useWebSocket = options.useWebSocket !== false; // Default to WebSocket
    this.heartbeatInterval = options.heartbeatInterval || 30; // seconds
    this.ws = null;
    this.rawSocket = null;
    this.isConnected = false;
    this.subscriptions = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.lastHeartbeat = 0;
    this.heartbeatTimer = null;
  }

  /**
   * Connect to the data server
   * @returns {Promise<void>}
   */
  connect() {
    if (this.useWebSocket) {
      return this.connectWebSocket();
    } else {
      return this.connectRawApi();
    }
  }

  /**
   * Connect using WebSocket API
   * @private
   * @returns {Promise<void>}
   */
  connectWebSocket() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.wsUrl);

      this.ws.on('open', () => {
        // Authenticate with API key
        console.log('WebSocket connection opened, authenticating...');
        this.ws.send(JSON.stringify({
          action: 'auth',
          key: this.apiKey
        }));
      });

      this.ws.on('message', (data) => {
        const message = JSON.parse(data);

        // Handle authentication response
        if (message.type === 'auth_response') {
          if (message.status === 'success') {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.startHeartbeatMonitoring();
            this.emit('connected');
            resolve();
          } else {
            const error = new Error(`Authentication failed: ${message.message}`);
            this.emit('error', error);
            reject(error);
          }
          return;
        }

        // Handle heartbeat
        if (message.type === 'system' && message.code === 0) {
          this.lastHeartbeat = Date.now();
          this.emit('heartbeat');
          return;
        }

        // Handle subscription data
        this.emit('data', message);

        // If the message has a symbol, emit a symbol-specific event
        if (message.symbol) {
          this.emit(`data:${message.symbol}`, message);
        }
      });

      this.ws.on('error', (error) => {
        this.emit('error', error);
        if (!this.isConnected) {
          reject(error);
        }
      });

      this.ws.on('close', (code, reason) => {
        this.isConnected = false;
        this.stopHeartbeatMonitoring();
        this.emit('disconnected', { code, reason });
        this.attemptReconnect();
      });
    });
  }

  /**
   * Connect using Raw API (TCP socket)
   * @private
   * @returns {Promise<void>}
   */
  connectRawApi() {
    return new Promise((resolve, reject) => {
      this.rawSocket = new net.Socket();

      this.rawSocket.connect(this.rawPort, this.rawHost, () => {
        // Send authentication message
        console.log('Raw socket connection opened, authenticating...');
        const authMessage = JSON.stringify({
          action: 'auth',
          key: this.apiKey
        });
        this.rawSocket.write(authMessage + '\n');
      });

      let buffer = '';
      this.rawSocket.on('data', (data) => {
        buffer += data.toString();

        // Process complete messages
        let endIndex;
        while ((endIndex = buffer.indexOf('\n')) !== -1) {
          const message = buffer.substring(0, endIndex);
          buffer = buffer.substring(endIndex + 1);

          try {
            const parsedMessage = JSON.parse(message);

            // Handle authentication response
            if (parsedMessage.type === 'auth_response') {
              if (parsedMessage.status === 'success') {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.startHeartbeatMonitoring();
                this.emit('connected');
                resolve();
              } else {
                const error = new Error(`Authentication failed: ${parsedMessage.message}`);
                this.emit('error', error);
                reject(error);
              }
              continue;
            }

            // Handle heartbeat
            if (parsedMessage.type === 'system' && parsedMessage.code === 0) {
              this.lastHeartbeat = Date.now();
              this.emit('heartbeat');
              continue;
            }

            // Handle subscription data
            this.emit('data', parsedMessage);

            // If the message has a symbol, emit a symbol-specific event
            if (parsedMessage.symbol) {
              this.emit(`data:${parsedMessage.symbol}`, parsedMessage);
            }
          } catch (error) {
            this.emit('error', new Error(`Failed to parse message: ${error.message}`));
          }
        }
      });

      this.rawSocket.on('error', (error) => {
        this.emit('error', error);
        if (!this.isConnected) {
          reject(error);
        }
      });

      this.rawSocket.on('close', () => {
        this.isConnected = false;
        this.stopHeartbeatMonitoring();
        this.emit('disconnected');
        this.attemptReconnect();
      });
    });
  }

  /**
   * Start monitoring heartbeats
   * @private
   */
  startHeartbeatMonitoring() {
    this.lastHeartbeat = Date.now();

    this.heartbeatTimer = setInterval(() => {
      const now = Date.now();
      const elapsed = (now - this.lastHeartbeat) / 1000;

      // If no heartbeat received for heartbeatInterval + 2 seconds, consider connection hung
      if (elapsed > this.heartbeatInterval + 2) {
        this.emit('error', new Error('Connection hung - no heartbeat received'));
        this.disconnect();
        this.attemptReconnect();
      }
    }, 1000); // Check every second
  }

  /**
   * Stop monitoring heartbeats
   * @private
   */
  stopHeartbeatMonitoring() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Subscribe to real-time data for a symbol
   * @param {string} dataset - Dataset code
   * @param {string} symbol - Symbol to subscribe to
   * @param {string} schema - Data schema
   * @param {Object} options - Additional options
   * @returns {Promise<void>}
   */
  async subscribe(dataset, symbols, schema, options = {}) {
    if (!this.isConnected) {
      await this.connect();
    }

    console.log(`Subscribing to ${symbols} in ${dataset} with schema ${schema}`);

    // Format symbols parameter
    const formattedSymbols = Array.isArray(symbols) ? symbols : symbols;

    const subscription = {
      action: 'subscribe',
      dataset,
      symbols: formattedSymbols,
      schema,
      ...options
    };

    console.log('Subscription request:', JSON.stringify(subscription));

    if (this.useWebSocket) {
      this.ws.send(JSON.stringify(subscription));
    } else {
      this.rawSocket.write(JSON.stringify(subscription) + '\n');
    }

    // Store subscription by dataset+schema+symbols for easier management
    const subscriptionKey = `${dataset}:${schema}:${formattedSymbols}`;
    this.subscriptions.set(subscriptionKey, subscription);

    return new Promise((resolve) => {
      const handler = (message) => {
        if (message.type === 'subscription_response' &&
            message.status === 'success') {
          console.log('Subscription confirmed:', message);
          this.removeListener('message', handler);
          resolve();
        }
      };

      this.on('message', handler);

      // Resolve after a timeout even if no explicit confirmation is received
      setTimeout(() => {
        console.log('Subscription timeout reached, assuming success');
        this.removeListener('message', handler);
        resolve();
      }, 5000);
    });
  }

  /**
   * Unsubscribe from a symbol
   * @param {string} symbol - Symbol to unsubscribe from
   */
  unsubscribe(dataset, symbols, schema) {
    // Create subscription key
    const subscriptionKey = `${dataset}:${schema}:${Array.isArray(symbols) ? symbols : symbols}`;

    if (!this.isConnected || !this.subscriptions.has(subscriptionKey)) {
      console.log(`No subscription found for ${subscriptionKey}`);
      return;
    }

    console.log(`Unsubscribing from ${subscriptionKey}`);

    const subscription = this.subscriptions.get(subscriptionKey);

    const unsubscribeMessage = {
      action: 'unsubscribe',
      dataset: subscription.dataset,
      symbols: subscription.symbols,
      schema: subscription.schema
    };

    console.log('Unsubscribe request:', JSON.stringify(unsubscribeMessage));

    if (this.useWebSocket) {
      this.ws.send(JSON.stringify(unsubscribeMessage));
    } else {
      this.rawSocket.write(JSON.stringify(unsubscribeMessage) + '\n');
    }

    this.subscriptions.delete(subscriptionKey);
  }

  /**
   * Attempt to reconnect to the server
   * @private
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('reconnect_failed');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff

    setTimeout(() => {
      this.emit('reconnecting', { attempt: this.reconnectAttempts });
      this.connect()
        .then(() => {
          // Resubscribe to all previous subscriptions
          this.subscriptions.forEach((subscription) => {
            if (this.useWebSocket) {
              this.ws.send(JSON.stringify(subscription));
            } else {
              this.rawSocket.write(JSON.stringify(subscription) + '\n');
            }
          });
        })
        .catch((error) => {
          this.emit('reconnect_error', error);
        });
    }, delay);
  }

  /**
   * Disconnect from the server
   */
  disconnect() {
    this.stopHeartbeatMonitoring();

    if (this.useWebSocket && this.ws) {
      this.ws.close();
      this.ws = null;
    } else if (this.rawSocket) {
      this.rawSocket.destroy();
      this.rawSocket = null;
    }

    this.isConnected = false;
  }
}

module.exports = LiveClient;
