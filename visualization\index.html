<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@2.0.2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial@0.1.1"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #333;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 30px;
        }
        .stats {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .stat-box {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-right: 15px;
            margin-bottom: 15px;
            min-width: 150px;
        }
        .stat-box h3 {
            margin-top: 0;
            color: #555;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .symbol-selector {
            margin-bottom: 20px;
        }
        select {
            padding: 8px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .refresh-control {
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        .last-update {
            font-style: italic;
            color: #777;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Trading Bot Visualization</h1>

        <div class="refresh-control">
            <button id="refreshBtn">Refresh Data</button>
            <label>
                <input type="checkbox" id="autoRefresh" checked> Auto-refresh (5s)
            </label>
            <span class="last-update" id="lastUpdate"></span>
        </div>

        <div class="symbol-selector">
            <label for="symbolSelect">Select Symbol: </label>
            <select id="symbolSelect"></select>
        </div>

        <div class="stats" id="statsContainer">
            <!-- Stats will be populated here -->
        </div>

        <h2>Price Chart</h2>
        <div class="chart-container">
            <canvas id="priceChart"></canvas>
        </div>

        <h2>Indicators</h2>
        <div class="chart-container">
            <canvas id="indicatorChart"></canvas>
        </div>
    </div>

    <script>
        // Global variables
        let priceChart = null;
        let indicatorChart = null;
        let marketData = {};
        let selectedSymbol = null;
        let autoRefreshInterval = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set up event listeners
            document.getElementById('refreshBtn').addEventListener('click', fetchData);
            document.getElementById('symbolSelect').addEventListener('change', function() {
                selectedSymbol = this.value;
                updateCharts();
            });
            document.getElementById('autoRefresh').addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });

            // Initial data fetch
            fetchData();

            // Start auto-refresh
            startAutoRefresh();
        });

        // Fetch data from the JSON file
        function fetchData() {
            fetch('market_data.json?' + new Date().getTime())
                .then(response => response.json())
                .then(data => {
                    marketData = data;
                    updateSymbolSelector();
                    updateCharts();
                    document.getElementById('lastUpdate').textContent = 'Last updated: ' + new Date().toLocaleTimeString();
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                    document.getElementById('lastUpdate').textContent = 'Error fetching data: ' + error.message;
                });
        }

        // Update the symbol selector dropdown
        function updateSymbolSelector() {
            const symbolSelect = document.getElementById('symbolSelect');
            const currentValue = symbolSelect.value;

            // Clear existing options
            symbolSelect.innerHTML = '';

            // Add options for each symbol
            Object.keys(marketData).forEach(symbol => {
                const option = document.createElement('option');
                option.value = symbol;
                option.textContent = symbol;
                symbolSelect.appendChild(option);
            });

            // Try to restore previous selection or select first symbol
            if (currentValue && symbolSelect.querySelector(`option[value="${currentValue}"]`)) {
                symbolSelect.value = currentValue;
            } else if (Object.keys(marketData).length > 0) {
                symbolSelect.value = Object.keys(marketData)[0];
            }

            // Update selected symbol
            selectedSymbol = symbolSelect.value;
        }

        // Update charts with the selected symbol's data
        function updateCharts() {
            if (!selectedSymbol || !marketData[selectedSymbol]) return;

            const symbolData = marketData[selectedSymbol];

            // Update stats
            updateStats(symbolData);

            // Update price chart
            updatePriceChart(symbolData);

            // Update indicator chart
            updateIndicatorChart(symbolData);
        }

        // Update statistics display
        function updateStats(symbolData) {
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = '';

            // Add candle count
            addStatBox(statsContainer, 'Candles', symbolData.candleCount);

            // Add indicator values if available
            const indicators = symbolData.indicators;
            if (indicators) {
                if (indicators.rsi !== null) addStatBox(statsContainer, 'RSI', indicators.rsi.toFixed(2));
                if (indicators.rsiBasedMA !== null) addStatBox(statsContainer, 'RSI-MA', indicators.rsiBasedMA.toFixed(2));
                if (indicators.atr !== null) addStatBox(statsContainer, 'ATR', indicators.atr.toFixed(5));
                if (indicators.wma50 !== null) addStatBox(statsContainer, 'WMA50', indicators.wma50.toFixed(2));
                if (indicators.atrRegime) addStatBox(statsContainer, 'ATR Regime', indicators.atrRegime);
            }
        }

        // Helper to add a stat box
        function addStatBox(container, label, value) {
            const box = document.createElement('div');
            box.className = 'stat-box';
            box.innerHTML = `<h3>${label}</h3><div class="stat-value">${value}</div>`;
            container.appendChild(box);
        }

        // Update the price chart
        function updatePriceChart(symbolData) {
            const ctx = document.getElementById('priceChart').getContext('2d');

            // Prepare data
            const candles = symbolData.candles;

            // Format data for candlestick chart
            const candlestickData = candles.map(c => ({
                x: new Date(c.time),
                o: c.open,
                h: c.high,
                l: c.low,
                c: c.close
            }));

            // Create or update chart
            if (priceChart) {
                // Destroy old chart and create new one (easier than updating)
                priceChart.destroy();
                priceChart = null;
            }

            // Create new chart
            priceChart = new Chart(ctx, {
                type: 'candlestick',
                data: {
                    datasets: [{
                        label: selectedSymbol,
                        data: candlestickData,
                        color: {
                            up: 'rgba(75, 192, 75, 1)',
                            down: 'rgba(255, 99, 132, 1)',
                            unchanged: 'rgba(90, 90, 90, 1)',
                        }
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'HH:mm'
                                }
                            },
                            ticks: {
                                maxRotation: 0,
                                autoSkip: true,
                                maxTicksLimit: 10
                            }
                        },
                        y: {
                            position: 'right',
                            ticks: {
                                precision: 2
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const point = context.raw;
                                    return [
                                        'Open: ' + point.o.toFixed(2),
                                        'High: ' + point.h.toFixed(2),
                                        'Low: ' + point.l.toFixed(2),
                                        'Close: ' + point.c.toFixed(2)
                                    ];
                                }
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
        }

        // Update the indicator chart
        function updateIndicatorChart(symbolData) {
            const ctx = document.getElementById('indicatorChart').getContext('2d');
            const indicators = symbolData.indicators;

            // If no indicators yet, show message
            if (!indicators || indicators.rsi === null) {
                if (indicatorChart) {
                    indicatorChart.destroy();
                    indicatorChart = null;
                }
                ctx.font = '20px Arial';
                ctx.fillStyle = '#666';
                ctx.textAlign = 'center';
                ctx.fillText('No indicator data available yet', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            // For now, just show a placeholder since we don't have historical indicator values
            const data = {
                labels: ['Current'],
                datasets: [{
                    label: 'RSI',
                    data: [indicators.rsi],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)'
                }, {
                    label: 'RSI-MA',
                    data: [indicators.rsiBasedMA],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)'
                }]
            };

            // Create or update chart
            if (indicatorChart) {
                indicatorChart.data = data;
                indicatorChart.update();
            } else {
                indicatorChart = new Chart(ctx, {
                    type: 'bar',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                min: 0,
                                max: 100
                            }
                        }
                    }
                });
            }
        }

        // Start auto-refresh
        function startAutoRefresh() {
            if (autoRefreshInterval) clearInterval(autoRefreshInterval);
            autoRefreshInterval = setInterval(fetchData, 5000);
        }

        // Stop auto-refresh
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
    </script>
</body>
</html>
