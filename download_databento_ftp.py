#!/usr/bin/env python3
"""
Download Databento data from FTP server and convert to CSV format.
"""

import os
import sys
import ftplib
import logging
import pandas as pd
from datetime import datetime
import tempfile
import zipfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import databento
try:
    import databento as db
    logger.info(f"Successfully imported databento version: {db.__version__}")
except ImportError:
    logger.error("Failed to import databento. Please install it with: pip install databento")
    sys.exit(1)

def download_from_ftp(ftp_url, output_dir, username=None, password=None):
    """
    Download data from Databento FTP server.

    Args:
        ftp_url (str): FTP URL to download from
        output_dir (str): Directory to save downloaded files
        username (str, optional): FTP username
        password (str, optional): FTP password

    Returns:
        str: Path to downloaded file
    """
    try:
        # Parse FTP URL
        if not ftp_url.startswith('ftp://'):
            logger.error(f"Invalid FTP URL: {ftp_url}")
            return None

        # Remove ftp:// prefix
        ftp_url = ftp_url[6:]

        # Split into server and path
        parts = ftp_url.split('/', 1)
        if len(parts) != 2:
            logger.error(f"Invalid FTP URL format: {ftp_url}")
            return None

        server = parts[0]
        path = '/' + parts[1]

        # Get filename from path
        filename = os.path.basename(path)
        output_file = os.path.join(output_dir, filename)

        logger.info(f"Downloading {filename} from {server}...")

        # Connect to FTP server
        ftp = ftplib.FTP(server)

        # Login with credentials if provided, otherwise anonymous login
        if username and password:
            logger.info(f"Logging in with username: {username}")
            ftp.login(username, password)
        else:
            logger.info("Attempting anonymous login")
            ftp.login()

        # Download file
        with open(output_file, 'wb') as f:
            ftp.retrbinary(f'RETR {path}', f.write)

        ftp.quit()

        logger.info(f"Downloaded {output_file}")
        return output_file

    except Exception as e:
        logger.error(f"Error downloading from FTP: {str(e)}")
        return None

def extract_zip(zip_file, output_dir):
    """
    Extract a ZIP file.

    Args:
        zip_file (str): Path to ZIP file
        output_dir (str): Directory to extract to

    Returns:
        list: List of extracted files
    """
    try:
        logger.info(f"Extracting {zip_file}...")

        extracted_files = []
        with zipfile.ZipFile(zip_file, 'r') as z:
            z.extractall(output_dir)
            extracted_files = z.namelist()

        logger.info(f"Extracted {len(extracted_files)} files")
        return [os.path.join(output_dir, f) for f in extracted_files]

    except Exception as e:
        logger.error(f"Error extracting ZIP file: {str(e)}")
        return []

def convert_dbn_to_csv(dbn_file, output_dir):
    """
    Convert a Databento DBN file to CSV format.

    Args:
        dbn_file (str): Path to the DBN file
        output_dir (str): Directory to save the CSV file

    Returns:
        str: Path to the CSV file
    """
    try:
        logger.info(f"Converting {dbn_file} to CSV...")

        # Get the base filename without extension
        base_name = os.path.basename(dbn_file)
        base_name = base_name.replace('.dbn.zst', '')

        # Extract symbol from filename
        parts = base_name.split('.')
        if len(parts) >= 3:
            symbol = parts[2]
            # Handle spread symbols (e.g., MNQM5-MNQU5)
            if '-' in symbol:
                symbol = symbol.split('-')[0]  # Use the first symbol in the spread
        else:
            symbol = "unknown"

        # Clean up the symbol name
        # Remove month/year codes to get the base symbol (e.g., MNQM5 -> MNQ)
        base_symbol = ''.join([c for c in symbol if not (c.isdigit() or c in 'FGHJKMNQUVXZ')])

        # Create output filename
        csv_file = os.path.join(output_dir, f"{base_symbol}_{symbol}.csv")

        # Load the DBN file
        dataset = db.DBNStore.from_file(dbn_file)

        # Convert to pandas DataFrame
        df = dataset.to_df()

        # Check if DataFrame is empty
        if df.empty:
            logger.warning(f"No data found in {dbn_file}")
            return None

        # Process the DataFrame based on schema
        schema = dataset.schema
        logger.info(f"Schema: {schema}")

        if schema == 'ohlcv-1m':
            # For OHLCV data, we need to format it for our trading bot
            # Rename columns if needed
            if 'ts_event' in df.columns:
                df['timestamp'] = pd.to_datetime(df['ts_event'], unit='ns')
                df = df.drop(columns=['ts_event'])

            # Make sure we have all required columns
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"Column {col} not found in {dbn_file}")
                    if col != 'timestamp':  # We already handled timestamp
                        df[col] = 0  # Add dummy column

            # Select only the columns we need
            df = df[required_columns]

            # Sort by timestamp
            df = df.sort_values('timestamp')

            # Format timestamp as ISO string
            df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')

        elif schema == 'trades':
            # For trade data, we need different columns
            if 'ts_event' in df.columns:
                df['timestamp'] = pd.to_datetime(df['ts_event'], unit='ns')
                df = df.drop(columns=['ts_event'])

            # Make sure we have all required columns
            required_columns = ['timestamp', 'price', 'size', 'side']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"Column {col} not found in {dbn_file}")
                    if col != 'timestamp':  # We already handled timestamp
                        df[col] = 0  # Add dummy column

            # Select only the columns we need
            df = df[required_columns]

            # Sort by timestamp
            df = df.sort_values('timestamp')

            # Format timestamp as ISO string
            df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')

        # Save to CSV
        df.to_csv(csv_file, index=False)
        logger.info(f"Saved {len(df)} records to {csv_file}")

        return csv_file

    except Exception as e:
        logger.error(f"Error converting {dbn_file} to CSV: {str(e)}")
        return None

def consolidate_files_by_symbol(directory):
    """
    Consolidate all CSV files for each symbol into a single file.

    Args:
        directory (str): Directory containing CSV files
    """
    # Find all CSV files
    csv_files = [f for f in os.listdir(directory) if f.endswith('.csv')]

    # Group files by base symbol
    symbol_files = {}
    for csv_file in csv_files:
        if '_' in csv_file:
            base_symbol = csv_file.split('_')[0]
            if base_symbol not in symbol_files:
                symbol_files[base_symbol] = []
            symbol_files[base_symbol].append(os.path.join(directory, csv_file))

    # Consolidate files for each symbol
    for base_symbol, files in symbol_files.items():
        if len(files) <= 1:
            continue

        logger.info(f"Consolidating {len(files)} files for {base_symbol}...")

        # Read all files
        dfs = []
        for file in files:
            try:
                df = pd.read_csv(file)
                dfs.append(df)
            except Exception as e:
                logger.error(f"Error reading {file}: {str(e)}")

        if not dfs:
            logger.warning(f"No valid data files for {base_symbol}")
            continue

        # Concatenate all DataFrames
        combined_df = pd.concat(dfs, ignore_index=True)

        # Remove duplicates
        combined_df = combined_df.drop_duplicates(subset=['timestamp'])

        # Sort by timestamp
        combined_df = combined_df.sort_values('timestamp')

        # Save to consolidated file
        output_file = os.path.join(directory, f"{base_symbol}_1m.csv")
        combined_df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(combined_df)} records to {output_file}")

def main():
    """Main function."""
    # Get input and output directories
    output_dir = os.path.join('C:', 'backtest-bot', 'input')

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Databento API key
    api_key = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq"

    # Download from FTP
    ftp_url = "ftp://ftp.databento.com/FR79HXR8/GLBX-20250513-CLXD5M5NFN"
    zip_file = download_from_ftp(ftp_url, output_dir, username=api_key, password="")

    if not zip_file:
        logger.error("Failed to download from FTP")
        return

    # Extract ZIP file
    dbn_files = extract_zip(zip_file, output_dir)

    if not dbn_files:
        logger.error("No files extracted from ZIP")
        return

    # Convert DBN files to CSV
    csv_files = []
    for dbn_file in dbn_files:
        if dbn_file.endswith('.dbn.zst'):
            csv_file = convert_dbn_to_csv(dbn_file, output_dir)
            if csv_file:
                csv_files.append(csv_file)

    logger.info(f"Converted {len(csv_files)} files to CSV")

    # Consolidate files by symbol
    consolidate_files_by_symbol(output_dir)

if __name__ == "__main__":
    main()
