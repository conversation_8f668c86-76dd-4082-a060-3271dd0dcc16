import os
import re
import shutil
from bs4 import BeautifulSoup

# Dashboards to update
DASHBOARDS = [
    "performance_comparison_dashboard.html",
    "portfolio_manager.html",
    "investor_reporting.html"
]

# Hardcoded data for all instruments
HARDCODED_DATA = {
    "MNQ": {
        "instrumentCode": "MNQ",
        "instrumentConfig": {
            "name": "Micro Nasdaq",
            "color": "#00ccff"
        },
        "backtestResults": {
            "params": {
                "stop_loss_factor": 4.5,
                "take_profit_factor": 3.0,
                "trail_factor": 0.11,
                "fixed_tp_points": 40,
                "contracts": 10,
                "commission_per_contract": 0.4,
                "slippage_per_contract": 0.75,
                "adaptive_mode": True
            },
            "metrics": {
                "total_pnl": 3337467.82,
                "win_rate": 0.7884,
                "win_day_rate": 0.981,
                "max_drawdown": 1470.43,
                "profit_factor": 31.24
            }
        }
    },
    "MGC": {
        "instrumentCode": "MGC",
        "instrumentConfig": {
            "name": "Micro Gold",
            "color": "#FFD700"
        },
        "backtestResults": {
            "params": {
                "stop_loss_factor": 8.0,
                "take_profit_factor": 7.0,
                "trail_factor": 0.02,
                "contracts": 10,
                "commission_per_contract": 0.4,
                "slippage_per_contract": 0.1,
                "adaptive_mode": True
            },
            "metrics": {
                "total_pnl": 890734.73,
                "win_rate": 0.7049,
                "win_day_rate": 0.932,
                "max_drawdown": 994.7,
                "profit_factor": 15.8
            }
        }
    },
    "MES": {
        "instrumentCode": "MES",
        "instrumentConfig": {
            "name": "Micro E-mini S&P 500",
            "color": "#ff3366"
        },
        "backtestResults": {
            "params": {
                "stop_loss_factor": 3.0,
                "take_profit_factor": 3.0,
                "trail_factor": 0.01,
                "contracts": 10,
                "commission_per_contract": 0.4,
                "slippage_per_contract": 0.25,
                "adaptive_mode": True
            },
            "metrics": {
                "total_pnl": 1010762.7,
                "win_rate": 0.7455,
                "win_day_rate": 0.947,
                "max_drawdown": 1159.36,
                "profit_factor": 18.97
            }
        }
    }
}

def update_dashboard(dashboard_file):
    """Update a dashboard file with hardcoded data"""
    print(f"Updating {dashboard_file}...")
    
    # Create a backup of the original file
    backup_file = f"{dashboard_file}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(dashboard_file, backup_file)
        print(f"Created backup of {dashboard_file} to {backup_file}")
    
    try:
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the HTML content
        soup = BeautifulSoup(content, 'html.parser')
        
        # Find the script that loads data
        data_loading_script = None
        for script in soup.find_all('script'):
            if script.string and 'document.addEventListener(\'DOMContentLoaded\'' in script.string:
                data_loading_script = script
                break
        
        if data_loading_script:
            # Replace the data loading script with hardcoded data
            new_script = soup.new_tag('script')
            new_script.string = """
document.addEventListener('DOMContentLoaded', function() {
    // Use hardcoded data instead of loading from external files
    const hardcodedData = {
        "MNQ": {
            "instrumentCode": "MNQ",
            "instrumentConfig": {
                "name": "Micro Nasdaq",
                "color": "#00ccff"
            },
            "backtestResults": {
                "params": {
                    "stop_loss_factor": 4.5,
                    "take_profit_factor": 3.0,
                    "trail_factor": 0.11,
                    "fixed_tp_points": 40,
                    "contracts": 10,
                    "commission_per_contract": 0.4,
                    "slippage_per_contract": 0.75,
                    "adaptive_mode": true
                },
                "metrics": {
                    "total_pnl": 3337467.82,
                    "win_rate": 0.7884,
                    "win_day_rate": 0.981,
                    "max_drawdown": 1470.43,
                    "profit_factor": 31.24
                }
            }
        },
        "MGC": {
            "instrumentCode": "MGC",
            "instrumentConfig": {
                "name": "Micro Gold",
                "color": "#FFD700"
            },
            "backtestResults": {
                "params": {
                    "stop_loss_factor": 8.0,
                    "take_profit_factor": 7.0,
                    "trail_factor": 0.02,
                    "contracts": 10,
                    "commission_per_contract": 0.4,
                    "slippage_per_contract": 0.1,
                    "adaptive_mode": true
                },
                "metrics": {
                    "total_pnl": 890734.73,
                    "win_rate": 0.7049,
                    "win_day_rate": 0.932,
                    "max_drawdown": 994.7,
                    "profit_factor": 15.8
                }
            }
        },
        "MES": {
            "instrumentCode": "MES",
            "instrumentConfig": {
                "name": "Micro E-mini S&P 500",
                "color": "#ff3366"
            },
            "backtestResults": {
                "params": {
                    "stop_loss_factor": 3.0,
                    "take_profit_factor": 3.0,
                    "trail_factor": 0.01,
                    "contracts": 10,
                    "commission_per_contract": 0.4,
                    "slippage_per_contract": 0.25,
                    "adaptive_mode": true
                },
                "metrics": {
                    "total_pnl": 1010762.7,
                    "win_rate": 0.7455,
                    "win_day_rate": 0.947,
                    "max_drawdown": 1159.36,
                    "profit_factor": 18.97
                }
            }
        }
    };
    
    console.log('Using hardcoded data:', hardcodedData);
    
    // Determine which instrument(s) to display
    let instrumentsToDisplay = Object.keys(hardcodedData);
    
    // Update the dashboard content with the selected instrument(s)
    updateDashboardContent(hardcodedData, instrumentsToDisplay);
});
"""
            data_loading_script.replace_with(new_script)
            
            # Write the updated content back to the file
            with open(dashboard_file, 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            print(f"Updated {dashboard_file} successfully")
            return True
        else:
            print(f"Warning: Could not find data loading script in {dashboard_file}")
            return False
    
    except Exception as e:
        print(f"Error updating {dashboard_file}: {e}")
        
        # Restore the backup
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, dashboard_file)
            print(f"Restored {dashboard_file} from backup due to error")
        
        return False

def main():
    """Main function"""
    print("Updating all dashboards with hardcoded data...")
    
    # Update each dashboard
    for dashboard in DASHBOARDS:
        dashboard_file = os.path.join("C:/backtest-bot", dashboard)
        update_dashboard(dashboard_file)
    
    print("Dashboard update complete!")

if __name__ == "__main__":
    main()
