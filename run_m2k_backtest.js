// run_m2k_backtest.js - <PERSON><PERSON>t to run Micro Russell 2000 backtest

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('=== Running Micro Russell 2000 (M2K) Backtest ===');
console.log('Using config file: ./config_m2k_backtest.js');

// Define output directory
const outputDir = './output/M2K_Backtest_Results';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`Created output directory: ${outputDir}`);
} else {
  console.log(`Output directory exists: ${outputDir}`);
}

// Create a modified version of backtest.js for the M2K backtest
const backtestContent = fs.readFileSync('backtest.js', 'utf8');

// Add pattern adapter import and replace pattern detection functions
let modifiedContent = backtestContent
  .replace('./output/MNQ_AdvancedML_2020_2025', outputDir)
  .replace('./config', './config_m2k_backtest');

// Add pattern adapter import after other requires
modifiedContent = modifiedContent.replace(
  'const enhancedPM = require(\'./enhanced_position_management\');',
  'const enhancedPM = require(\'./enhanced_position_management\');\nconst patternAdapter = require(\'./pattern_adapter\');'
);

// Replace pattern detection function calls
modifiedContent = modifiedContent.replace(
  'const p3=detect3(c1,c2,c3), p4=detect4(c0,c1,c2,c3);',
  'const p3=patternAdapter.detect3(c1,c2,c3), p4=patternAdapter.detect4(c0,c1,c2,c3);'
);

// Replace entryOK function call
modifiedContent = modifiedContent.replace(
  'if (!pos && pat && entryOK(pat, pType, c3, i, candlesForPeriod)) {',
  'if (!pos && pat && patternAdapter.entryOK(pat, pType, c3, i, candlesForPeriod)) {'
);

// Write the modified file
const modifiedBacktestFile = 'm2k_backtest_runner.js';
fs.writeFileSync(modifiedBacktestFile, modifiedContent);
console.log(`Created modified backtest file: ${modifiedBacktestFile}`);

// Set global config for pattern detection
const setupScript = `
// setup_global_config.js
global.config = require('./config_m2k_backtest');
`;
fs.writeFileSync('setup_global_config.js', setupScript);
console.log('Created global config setup script');

// Run the backtest
console.log('Starting backtest...');
try {
  // First run the setup script to set global config
  execSync('node setup_global_config.js');
  // Then run the backtest
  execSync(`node ${modifiedBacktestFile}`, { stdio: 'inherit' });
  console.log('Backtest completed successfully!');
} catch (error) {
  console.error('Error running backtest:', error.message);
}

// Clean up
console.log('Cleaning up temporary files...');
try {
  fs.unlinkSync(modifiedBacktestFile);
  fs.unlinkSync('setup_global_config.js');
  console.log('Removed temporary files');
} catch (error) {
  console.warn(`Warning: Could not remove temporary files: ${error.message}`);
}

console.log('=== Backtest Process Complete ===');
console.log(`Results should be available in: ${outputDir}`);
