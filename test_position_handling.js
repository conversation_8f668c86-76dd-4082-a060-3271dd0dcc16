/**
 * Test script for position handling
 */

const tradovateApi = require('./tradovate_api_optimized');

// Configure custom logging
const LOG_FILE = 'logs/position_test.log';
const fs = require('fs');
const path = require('path');

// Create logs directory if it doesn't exist
const logsDir = path.dirname(LOG_FILE);
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom logging function
function logToFile(message) {
    fs.appendFileSync(LOG_FILE, `${new Date().toISOString()} - ${message}\n`);
    console.log(message);
}

// Global variables
let realAccountId = null;
let activePositions = {};

/**
 * Handle position update
 * @param {Object} positionData - Position data
 */
function handlePositionUpdate(positionData) {
    try {
        logToFile(`Position update: ${JSON.stringify(positionData)}`);

        // Extract contract symbol from contractId if available
        let contractSymbol = positionData.contractId;
        if (typeof contractSymbol === 'number') {
            logToFile(`Position has numeric contractId: ${contractSymbol}`);
        }

        // Update active positions
        let positionFound = false;
        for (const positionId in activePositions) {
            const position = activePositions[positionId];

            // Match by contractId
            if (position.contractId && position.contractId === positionData.contractId) {
                positionFound = true;
                position.quantity = positionData.netPos;
                position.entryPrice = positionData.netPrice;

                logToFile(`Updated position for contractId ${position.contractId}: ${JSON.stringify(position)}`);

                // If position is closed, remove it
                if (positionData.netPos === 0) {
                    delete activePositions[positionId];
                    logToFile(`Removed position for contractId ${position.contractId}`);
                }
            }
        }

        // If this is a new position that we don't have in our activePositions
        if (!positionFound && positionData.netPos !== 0) {
            // Create a new position entry
            const newPositionId = `pos-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

            // Determine action based on netPos (positive = Buy, negative = Sell)
            const action = positionData.netPos > 0 ? 'Buy' : 'Sell';

            // Create position object
            activePositions[newPositionId] = {
                id: newPositionId,
                contractId: positionData.contractId,
                action: action,
                quantity: Math.abs(positionData.netPos),
                entryPrice: positionData.netPrice,
                timestamp: Date.now()
            };

            logToFile(`Created new position from update: ${JSON.stringify(activePositions[newPositionId])}`);
        }

        // Log all active positions
        const positionCount = Object.keys(activePositions).length;
        logToFile(`Active positions after update: ${positionCount}`);
        if (positionCount > 0) {
            logToFile(`Active positions: ${JSON.stringify(activePositions)}`);
        }
    } catch (error) {
        console.error(`Error handling position update: ${error.message}`);
        logToFile(`ERROR: Error handling position update: ${error.message}`);
    }
}

/**
 * Handle WebSocket messages
 * @param {Object} message - WebSocket message
 */
function handleWebSocketMessage(message) {
    try {
        // Log all messages for debugging
        console.log(`Received WebSocket message type: ${message.e}`);
        logToFile(`Received WebSocket message type: ${message.e}`);

        // Process different message types
        if (message.e === 'position') {
            console.log(`POSITION UPDATE: ${JSON.stringify(message.d)}`);
            logToFile(`POSITION UPDATE: ${JSON.stringify(message.d)}`);
            handlePositionUpdate(message.d);
        } else if (message.e === 'user') {
            console.log(`USER UPDATE: ${JSON.stringify(message.d)}`);
            logToFile(`USER UPDATE: ${JSON.stringify(message.d)}`);
        } else if (message.e === 'account') {
            console.log(`ACCOUNT UPDATE: ${JSON.stringify(message.d)}`);
            logToFile(`ACCOUNT UPDATE: ${JSON.stringify(message.d)}`);
        } else if (message.e === 'order') {
            console.log(`ORDER UPDATE: ${JSON.stringify(message.d)}`);
            logToFile(`ORDER UPDATE: ${JSON.stringify(message.d)}`);
        } else if (message.e === 'fill') {
            console.log(`FILL UPDATE: ${JSON.stringify(message.d)}`);
            logToFile(`FILL UPDATE: ${JSON.stringify(message.d)}`);
        } else {
            console.log(`OTHER MESSAGE: ${message.e} - ${JSON.stringify(message.d || message)}`);
            logToFile(`OTHER MESSAGE: ${message.e} - ${JSON.stringify(message.d || message)}`);
        }
    } catch (error) {
        console.error(`Error handling WebSocket message: ${error.message}`);
        logToFile(`ERROR: Error handling WebSocket message: ${error.message}`);
    }
}

/**
 * Initialize the test
 */
async function initialize() {
    try {
        console.log("Starting position handling test...");
        logToFile("Starting position handling test...");

        // Configure API with demo mode
        tradovateApi.setConfig({
            baseUrl: 'https://demo.tradovateapi.com/v1',
            wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
            mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
        });

        // Authenticate with Tradovate API
        console.log("Authenticating with Tradovate API...");
        logToFile("Authenticating with Tradovate API...");
        const authResult = await tradovateApi.authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }
        console.log("Authentication successful");
        logToFile("Authentication successful");

        // Get the real account ID
        try {
            console.log("Getting account list...");
            logToFile("Getting account list...");

            // Get account list directly
            const accountList = await tradovateApi.makeApiRequest('GET', 'account/list');

            if (accountList && Array.isArray(accountList) && accountList.length > 0) {
                // Find the demo account
                const demoAccount = accountList.find(account =>
                    account.name && account.name.toLowerCase().includes('demo'));

                if (demoAccount) {
                    realAccountId = demoAccount.id;
                    console.log(`Got real account ID: ${realAccountId} (${demoAccount.name})`);
                    logToFile(`Got real account ID: ${realAccountId} (${demoAccount.name})`);
                } else {
                    // Just use the first account
                    realAccountId = accountList[0].id;
                    console.log(`Using first account ID: ${realAccountId} (${accountList[0].name})`);
                    logToFile(`Using first account ID: ${realAccountId} (${accountList[0].name})`);
                }

                // Log all accounts
                logToFile(`All accounts: ${JSON.stringify(accountList)}`);
            } else {
                console.warn("Could not get account list, using default account ID");
                logToFile("WARNING: Could not get account list, using default account ID");
            }
        } catch (error) {
            console.error(`Error getting account list: ${error.message}`);
            logToFile(`ERROR: Error getting account list: ${error.message}`);
        }

        // Initialize WebSockets
        console.log("Initializing WebSockets...");
        logToFile("Initializing WebSockets...");
        const wsInitialized = await tradovateApi.initializeWebSockets();
        if (!wsInitialized) {
            throw new Error('Failed to initialize WebSockets');
        }
        console.log("WebSockets initialized");
        logToFile("WebSockets initialized");

        // Set up WebSocket message handler
        tradovateApi.onWebSocketMessage = handleWebSocketMessage;

        // The WebSocket initialization already subscribes to position changes
        // We'll just log the real account ID for reference
        if (realAccountId) {
            console.log(`Using real account ID for position tracking: ${realAccountId}`);
            logToFile(`Using real account ID for position tracking: ${realAccountId}`);
        }

        console.log("Test initialized successfully");
        logToFile("Test initialized successfully");
        console.log("Waiting for position updates...");
        logToFile("Waiting for position updates...");

        // Keep the process running
        setInterval(() => {
            const positionCount = Object.keys(activePositions).length;
            console.log(`Active positions: ${positionCount}`);
            logToFile(`Active positions: ${positionCount}`);

            if (positionCount > 0) {
                logToFile(`Current positions: ${JSON.stringify(activePositions)}`);
            }
        }, 10000);

    } catch (error) {
        console.error(`Initialization failed: ${error.message}`);
        logToFile(`ERROR: Initialization failed: ${error.message}`);
    }
}

// Start the test
initialize();
