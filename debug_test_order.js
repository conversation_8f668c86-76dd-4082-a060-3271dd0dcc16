/**
 * Debug Test Order Script
 *
 * This script is a simplified version of the test order functionality
 * to help debug why the test order command is not working.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');

// Default test order parameters
const DEFAULT_SYMBOL = 'MNQM5';
const DEFAULT_ACTION = 'Buy';
const DEFAULT_QTY = 1;
const DEFAULT_ORDER_TYPE = 'Market';

/**
 * Place a test order with detailed logging
 */
async function placeDebugTestOrder() {
    try {
        console.log('=== DEBUG TEST ORDER ===');
        console.log(`Placing test order for ${DEFAULT_SYMBOL}`);
        console.log(`Action: ${DEFAULT_ACTION}, Quantity: ${DEFAULT_QTY}, Type: ${DEFAULT_ORDER_TYPE}`);

        // Step 1: Authenticate with Tradovate API
        console.log('\nStep 1: Authenticating with Tradovate API...');
        const authResult = await tradovateApi.authenticate();
        console.log('Authentication result:', authResult);

        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }
        console.log('Authentication successful!');

        // Step 2: Get account information
        console.log('\nStep 2: Getting account information...');
        let accountId;
        try {
            const accounts = await tradovateApi.makeApiRequest('GET', 'account/list');
            console.log(`Found ${accounts.length} accounts:`, accounts);

            if (accounts.length > 0) {
                // Look for the demo account
                const demoAccount = accounts.find(acc => acc.name.startsWith('DEMO'));

                if (demoAccount) {
                    console.log(`Using demo account: ${demoAccount.name} (ID: ${demoAccount.id})`);
                    accountId = demoAccount.id;
                } else {
                    // If no demo account, use the first account
                    const account = accounts[0];
                    console.log(`No demo account found. Using first account: ${account.name} (ID: ${account.id})`);
                    accountId = account.id;
                }
            } else {
                throw new Error('No accounts found');
            }
        } catch (accountError) {
            console.error(`Error getting account information: ${accountError.message}`);
            throw accountError;
        }

        // Step 3: Find contract for the symbol
        console.log('\nStep 3: Finding contract for symbol...');
        let contractId;
        try {
            const contract = await tradovateApi.findContract(DEFAULT_SYMBOL);
            console.log('Contract found:', contract);

            if (contract && contract.id) {
                contractId = contract.id;
                console.log(`Using contract ID: ${contractId}`);
            } else {
                throw new Error(`Contract not found for symbol: ${DEFAULT_SYMBOL}`);
            }
        } catch (contractError) {
            console.error(`Error finding contract: ${contractError.message}`);
            throw contractError;
        }

        // Step 4: Prepare order parameters
        console.log('\nStep 4: Preparing order parameters...');
        const orderParams = {
            accountId: accountId,
            contractId: contractId,
            symbol: DEFAULT_SYMBOL,  // Add the symbol parameter
            action: DEFAULT_ACTION,
            orderQty: DEFAULT_QTY,
            orderType: DEFAULT_ORDER_TYPE,
            isAutomated: true
        };
        console.log('Order parameters:', orderParams);

        // Step 5: Place the order
        console.log('\nStep 5: Placing the order...');
        try {
            const orderResult = await tradovateApi.placeOrder(orderParams);
            console.log('Order result:', orderResult);

            if (orderResult.success) {
                console.log('\n✅ Test order placed successfully!');
                console.log(`Order ID: ${orderResult.orderId}`);
                return orderResult;
            } else {
                console.error('\n❌ Failed to place test order:', orderResult.error);
                return orderResult;
            }
        } catch (orderError) {
            console.error(`Error placing order: ${orderError.message}`);
            throw orderError;
        }
    } catch (error) {
        console.error('\n❌ Unhandled error during test order placement:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Run the test
placeDebugTestOrder()
    .then(result => {
        console.log('\nTest completed with result:', result);
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
