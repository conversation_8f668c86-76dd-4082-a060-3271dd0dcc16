/**
 * tradovate-connector.js
 * 
 * Connects to Tradovate API and feeds data to the trading bot
 */

const WebSocket = require('ws');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const tradingBot = require('./tradovate-bot');

// Tradovate API endpoints
const BASE_URL = 'https://demo.tradovateapi.com/v1';
const WS_URL = 'wss://demo.tradovateapi.com/v1/websocket';

// Global variables
let accessToken = null;
let expirationTime = null;
let ws = null;
let mdWs = null;
let reconnectInterval = null;
let subscriptions = new Map();
let accountId = null;
let contractMap = new Map();

// Authentication
async function authenticate(credentials) {
    try {
        console.log('Authenticating with Tradovate API...');
        
        const response = await axios.post(`${BASE_URL}/auth/accessTokenRequest`, {
            name: credentials.name,
            password: credentials.password,
            appId: credentials.appId,
            appVersion: credentials.appVersion,
            cid: credentials.cid,
            sec: credentials.sec
        });
        
        if (response.data && response.data.accessToken) {
            accessToken = response.data.accessToken;
            expirationTime = new Date().getTime() + (response.data.expirationTime * 1000);
            
            console.log('Authentication successful. Token expires at:', new Date(expirationTime).toLocaleString());
            
            // Get user accounts
            await getAccounts();
            
            return true;
        } else {
            console.error('Authentication failed:', response.data);
            return false;
        }
    } catch (error) {
        console.error('Authentication error:', error.response ? error.response.data : error.message);
        return false;
    }
}

// Get user accounts
async function getAccounts() {
    try {
        const response = await axios.get(`${BASE_URL}/account/list`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        if (response.data && response.data.length > 0) {
            // Use the first active account
            const activeAccount = response.data.find(acc => acc.active);
            
            if (activeAccount) {
                accountId = activeAccount.id;
                console.log(`Using account: ${activeAccount.name} (ID: ${accountId})`);
            } else {
                accountId = response.data[0].id;
                console.log(`No active account found. Using: ${response.data[0].name} (ID: ${accountId})`);
            }
            
            return true;
        } else {
            console.error('No accounts found');
            return false;
        }
    } catch (error) {
        console.error('Error getting accounts:', error.response ? error.response.data : error.message);
        return false;
    }
}

// Connect to WebSocket
function connectWebSocket() {
    if (ws && ws.readyState === WebSocket.OPEN) {
        console.log('WebSocket already connected');
        return;
    }
    
    console.log('Connecting to Tradovate WebSocket...');
    
    ws = new WebSocket(WS_URL);
    
    ws.on('open', () => {
        console.log('WebSocket connected');
        
        // Authenticate WebSocket
        ws.send(JSON.stringify({
            "e": "auth",
            "d": {
                "token": accessToken
            }
        }));
        
        // Subscribe to user sync
        ws.send(JSON.stringify({
            "e": "subscribe",
            "d": {
                "feed": "user/syncrequest"
            }
        }));
        
        // Clear reconnect interval if it exists
        if (reconnectInterval) {
            clearInterval(reconnectInterval);
            reconnectInterval = null;
        }
    });
    
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            
            // Handle different message types
            if (message.e === 'auth') {
                if (message.d.success) {
                    console.log('WebSocket authentication successful');
                } else {
                    console.error('WebSocket authentication failed:', message.d);
                }
            } else if (message.e === 'subscribe') {
                console.log(`Subscribed to ${message.d.feed}`);
            } else if (message.e === 'md') {
                // Handle market data
                handleMarketData(message.d);
            } else if (message.e === 'user') {
                // Handle user data (positions, orders, etc.)
                handleUserData(message.d);
            }
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    });
    
    ws.on('close', () => {
        console.log('WebSocket disconnected');
        
        // Reconnect after delay
        if (!reconnectInterval) {
            reconnectInterval = setInterval(() => {
                console.log('Attempting to reconnect WebSocket...');
                connectWebSocket();
            }, 5000);
        }
    });
    
    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
    });
}

// Connect to Market Data WebSocket
function connectMdWebSocket() {
    if (mdWs && mdWs.readyState === WebSocket.OPEN) {
        console.log('Market Data WebSocket already connected');
        return;
    }
    
    console.log('Connecting to Tradovate Market Data WebSocket...');
    
    mdWs = new WebSocket(WS_URL);
    
    mdWs.on('open', () => {
        console.log('Market Data WebSocket connected');
        
        // Authenticate WebSocket
        mdWs.send(JSON.stringify({
            "e": "auth",
            "d": {
                "token": accessToken
            }
        }));
    });
    
    mdWs.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            
            // Handle different message types
            if (message.e === 'auth') {
                if (message.d.success) {
                    console.log('Market Data WebSocket authentication successful');
                    
                    // Subscribe to symbols after authentication
                    subscribeToSymbols(['MNQ', 'MES', 'MGC']);
                } else {
                    console.error('Market Data WebSocket authentication failed:', message.d);
                }
            } else if (message.e === 'md') {
                // Handle market data
                handleMarketData(message.d);
            }
        } catch (error) {
            console.error('Error parsing Market Data WebSocket message:', error);
        }
    });
    
    mdWs.on('close', () => {
        console.log('Market Data WebSocket disconnected');
        
        // Reconnect after delay
        setTimeout(() => {
            console.log('Attempting to reconnect Market Data WebSocket...');
            connectMdWebSocket();
        }, 5000);
    });
    
    mdWs.on('error', (error) => {
        console.error('Market Data WebSocket error:', error);
    });
}

// Subscribe to symbols
async function subscribeToSymbols(symbols) {
    if (!mdWs || mdWs.readyState !== WebSocket.OPEN) {
        console.error('Market Data WebSocket not connected');
        return false;
    }
    
    console.log(`Subscribing to symbols: ${symbols.join(', ')}`);
    
    try {
        // First, get contract IDs for the symbols
        const contracts = await getContractIds(symbols);
        
        // Subscribe to each contract
        contracts.forEach(contract => {
            // Store subscription info
            subscriptions.set(contract.id, {
                symbol: contract.symbol,
                contractId: contract.id
            });
            
            // Store contract mapping
            contractMap.set(contract.symbol, contract.id);
            
            // Subscribe to chart data
            mdWs.send(JSON.stringify({
                "e": "subscribe",
                "d": {
                    "feed": "md/charts",
                    "contractId": contract.id,
                    "chartDescription": {
                        "underlyingType": "MinuteBar",
                        "elementSize": 1,
                        "elementSizeUnit": "Minute"
                    }
                }
            }));
            
            console.log(`Subscribed to ${contract.symbol} chart data`);
        });
        
        return true;
    } catch (error) {
        console.error('Error subscribing to symbols:', error);
        return false;
    }
}

// Get contract IDs for symbols
async function getContractIds(symbols) {
    try {
        const contracts = [];
        
        for (const symbol of symbols) {
            // Get the contract for the symbol
            const response = await axios.get(`${BASE_URL}/contract/find?name=${symbol}`, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            
            if (response.data && response.data.id) {
                contracts.push({
                    symbol: symbol,
                    id: response.data.id
                });
            } else {
                console.error(`Contract not found for symbol: ${symbol}`);
            }
        }
        
        return contracts;
    } catch (error) {
        console.error('Error getting contract IDs:', error.response ? error.response.data : error.message);
        throw error;
    }
}

// Handle market data
function handleMarketData(data) {
    // Check if it's chart data
    if (data.charts && data.charts.length > 0) {
        data.charts.forEach(chart => {
            const subscription = subscriptions.get(chart.contractId);
            
            if (subscription) {
                const symbol = subscription.symbol;
                
                // Process each bar
                chart.bars.forEach(bar => {
                    // Convert bar to candle format
                    const candle = {
                        timestamp: bar.timestamp,
                        open: bar.open,
                        high: bar.high,
                        low: bar.low,
                        close: bar.close,
                        volume: bar.volume
                    };
                    
                    // Add to global candles
                    if (global.candles[symbol]) {
                        global.candles[symbol].push(candle);
                        
                        // Keep only the last 200 candles
                        if (global.candles[symbol].length > 200) {
                            global.candles[symbol].shift();
                        }
                        
                        // Calculate indicators
                        tradingBot.calculateIndicators(symbol);
                        
                        // Process the candle
                        tradingBot.processCandle(symbol, candle);
                    }
                });
            }
        });
    }
}

// Handle user data
function handleUserData(data) {
    // Process account updates, positions, orders, etc.
    console.log('User data update:', data);
}

// Start trading bot
async function startTradingBot(credentials) {
    console.log('Starting trading bot...');
    
    // Authenticate with Tradovate API
    const authenticated = await authenticate(credentials);
    
    if (!authenticated) {
        console.error('Failed to authenticate with Tradovate API');
        return false;
    }
    
    // Connect to WebSocket
    connectWebSocket();
    
    // Connect to Market Data WebSocket
    connectMdWebSocket();
    
    console.log('Trading bot started successfully');
    return true;
}

// Export functions
module.exports = {
    startTradingBot,
    getAccessToken: () => accessToken,
    getAccountId: () => accountId
};
