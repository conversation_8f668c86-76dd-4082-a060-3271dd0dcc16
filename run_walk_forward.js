/**
 * Run Walk-Forward Test
 * 
 * This script runs the walk-forward testing module to validate the trading strategy.
 */

require('dotenv').config();
const walkForwardTest = require('./walk_forward_test');
const logger = require('./data_logger');

// Custom configuration
const config = {
    // Data settings
    symbols: ['MNQ', 'MES', 'MGC', 'M2K'],
    
    // Walk-forward settings
    inSampleDays: 90,     // 90 days for training
    outSampleDays: 30,    // 30 days for testing
    stepDays: 30,         // Move forward 30 days each iteration
    startDate: '2023-01-01',
    endDate: '2023-12-31',
    
    // Strategy parameters to optimize
    paramRanges: {
        takeProfitPoints: {
            MNQ: [5, 10, 15, 20],
            MES: [3, 5, 7, 10],
            MGC: [2, 3, 4, 5],
            M2K: [3, 5, 7, 10]
        },
        trailFactors: {
            MNQ: [0.05, 0.08, 0.11, 0.15],
            MES: [0.005, 0.01, 0.015, 0.02],
            MGC: [0.01, 0.02, 0.03, 0.04],
            M2K: [0.01, 0.02, 0.03, 0.05]
        }
    }
};

/**
 * Main function
 */
async function main() {
    try {
        console.log('Starting walk-forward testing...');
        
        // Initialize walk-forward testing module
        const initialized = await walkForwardTest.initialize(config);
        if (!initialized) {
            throw new Error('Failed to initialize walk-forward testing module');
        }
        
        // Run walk-forward test
        const results = await walkForwardTest.runTest();
        
        // Display summary
        console.log('\nWalk-Forward Test Summary:');
        console.log('==========================');
        
        let totalWindows = results.length;
        let profitableWindows = 0;
        let totalProfit = 0;
        
        for (let i = 0; i < results.length; i++) {
            const window = results[i];
            let windowProfit = 0;
            
            console.log(`\nWindow ${window.window}: ${window.inSampleStart} to ${window.outSampleEnd}`);
            console.log(`Optimal Parameters:`);
            
            for (const symbol in window.optimalParams) {
                console.log(`  ${symbol}: TP=${window.optimalParams[symbol].takeProfitPoints}, Trail=${window.optimalParams[symbol].trailFactor}`);
                windowProfit += window.testResults[symbol].netProfit;
            }
            
            console.log(`Window Net Profit: $${windowProfit.toFixed(2)}`);
            
            if (windowProfit > 0) {
                profitableWindows++;
            }
            
            totalProfit += windowProfit;
        }
        
        console.log('\nOverall Results:');
        console.log(`Total Windows: ${totalWindows}`);
        console.log(`Profitable Windows: ${profitableWindows} (${(profitableWindows / totalWindows * 100).toFixed(2)}%)`);
        console.log(`Total Net Profit: $${totalProfit.toFixed(2)}`);
        
        console.log('\nWalk-forward testing completed successfully.');
    } catch (error) {
        console.error(`Error: ${error.message}`);
        logger.logSystem(`Error in walk-forward testing: ${error.message}`, 'error');
    }
}

// Run the main function
main().catch(error => {
    console.error(`Unhandled error: ${error.message}`);
    process.exit(1);
});
