/**
 * Verify MNQ Backtest
 * Run a single backtest with known optimal parameters for MNQ
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');
const gridConfig = require('./grid_config');

// Get MNQ configuration
const symbol = 'MNQ';
const config = gridConfig.configs[symbol];
const dataPath = gridConfig.dataPaths[symbol];

// Set output directory
const outputDir = './output/verify_mnq';
config.outputDir = outputDir;

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Known optimal parameters for MNQ
const optimalParams = {
    slFactors: 4.5,
    tpFactors: 3.0,
    trailFactors: 0.11,
    fixedTpPoints: 40
};

// Date range options (1 year for testing)
const dateRange = {
    useCustomRange: true,
    startDate: '2023-01-01',
    endDate: '2023-12-31'  // Full year for testing
};

/**
 * Run verification test for MNQ
 */
async function runVerification() {
    console.log(`\n===== VERIFYING MNQ BACKTEST =====`);
    console.log(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
    console.log(`Using optimal parameters: ${JSON.stringify(optimalParams, null, 2)}`);

    // Create backtest instance with configuration
    const backtest = new GridBacktest(config);

    // Performance tracking
    const startTime = Date.now();
    let lastTime = startTime;

    function logPerformance(step) {
        const now = Date.now();
        console.log(`${step}: ${(now - lastTime) / 1000}s (Total: ${(now - startTime) / 1000}s)`);
        lastTime = now;
    }

    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { dateRange });
    logPerformance('Data loading');

    // Log data size
    console.log(`Loaded ${backtest.candles.length} candles`);

    // Run backtest with optimal parameters
    console.log(`Running backtest with optimal parameters...`);
    const result = backtest.runBacktest(optimalParams);
    logPerformance('Backtest execution');

    if (!result.success) {
        console.error(`Error running backtest: ${result.error}`);
        return;
    }

    // Save results to file
    const resultsPath = path.join(outputDir, 'verification_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(result, null, 2));

    // Generate HTML report
    generateHtmlReport(result, outputDir);

    // Print summary
    console.log(`\n===== VERIFICATION RESULTS =====`);
    console.log(`Total PnL: $${result.stats.totalPnl.toFixed(2)}`);
    console.log(`Return: ${result.stats.totalReturn.toFixed(2)}%`);
    console.log(`Win Rate: ${result.stats.winRate.toFixed(2)}%`);
    console.log(`Win Day Rate: ${result.stats.winDayRate.toFixed(2)}%`);
    console.log(`Profit Factor: ${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}`);
    console.log(`Max Drawdown: ${result.stats.maxDrawdown.toFixed(2)}%`);
    console.log(`Total Trades: ${result.stats.totalTrades}`);
    console.log(`\nResults saved to ${resultsPath}`);
}

/**
 * Generate HTML report for verification results
 * @param {Object} result - Backtest result
 * @param {string} outputDir - Output directory
 */
function generateHtmlReport(result, outputDir) {
    const reportPath = path.join(outputDir, 'verification_report.html');

    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MNQ Verification Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
        .chart {
            margin-top: 30px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MNQ Verification Report</h1>
        <p>Date range: ${dateRange.startDate} to ${dateRange.endDate}</p>
        <p>Parameters: SL=${optimalParams.slFactors}, TP=${optimalParams.tpFactors}, Trail=${optimalParams.trailFactors}, Fixed TP=${optimalParams.fixedTpPoints}</p>

        <h2>Performance Summary</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Initial Balance</td>
                <td>$${result.stats.initialBalance.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Final Balance</td>
                <td>$${result.stats.finalBalance.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Total PnL</td>
                <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Return</td>
                <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Trades</td>
                <td>${result.stats.totalTrades}</td>
            </tr>
            <tr>
                <td>Winning Trades</td>
                <td>${result.stats.wins}</td>
            </tr>
            <tr>
                <td>Losing Trades</td>
                <td>${result.stats.losses}</td>
            </tr>
            <tr>
                <td>Win Rate</td>
                <td>${result.stats.winRate.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Profit Factor</td>
                <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
            </tr>
            <tr>
                <td>Average Win</td>
                <td class="positive">$${result.stats.avgWin.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Average Loss</td>
                <td class="negative">$${result.stats.avgLoss.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Max Drawdown</td>
                <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Days</td>
                <td>${result.stats.totalDays}</td>
            </tr>
            <tr>
                <td>Profitable Days</td>
                <td>${result.stats.profitDays}</td>
            </tr>
            <tr>
                <td>Losing Days</td>
                <td>${result.stats.lossDays}</td>
            </tr>
            <tr>
                <td>Win Day Rate</td>
                <td>${result.stats.winDayRate.toFixed(2)}%</td>
            </tr>
        </table>

        <h2>Daily Performance</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>PnL</th>
                    <th>Trades</th>
                    <th>Wins</th>
                    <th>Losses</th>
                    <th>Win Rate</th>
                </tr>
            </thead>
            <tbody>
                ${Object.entries(result.dailyStats).map(([date, stats]) => `
                <tr>
                    <td>${date}</td>
                    <td class="${stats.pnl >= 0 ? 'positive' : 'negative'}">$${stats.pnl.toFixed(2)}</td>
                    <td>${stats.trades}</td>
                    <td>${stats.wins}</td>
                    <td>${stats.losses}</td>
                    <td>${stats.trades > 0 ? ((stats.wins / stats.trades) * 100).toFixed(2) : 0}%</td>
                </tr>
                `).join('')}
            </tbody>
        </table>

        <h2>Trade Exit Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Exit Reason</th>
                    <th>Count</th>
                    <th>Win Count</th>
                    <th>Loss Count</th>
                    <th>Win Rate</th>
                    <th>Avg PnL</th>
                </tr>
            </thead>
            <tbody>
                ${(() => {
                    const exitStats = {};

                    // Group trades by exit reason
                    for (const trade of result.trades) {
                        if (!exitStats[trade.exitReason]) {
                            exitStats[trade.exitReason] = {
                                count: 0,
                                winCount: 0,
                                lossCount: 0,
                                totalPnl: 0
                            };
                        }

                        exitStats[trade.exitReason].count++;
                        exitStats[trade.exitReason].totalPnl += trade.pnl;

                        if (trade.pnl > 0) {
                            exitStats[trade.exitReason].winCount++;
                        } else {
                            exitStats[trade.exitReason].lossCount++;
                        }
                    }

                    // Generate HTML for each exit reason
                    return Object.entries(exitStats).map(([reason, stats]) => `
                    <tr>
                        <td>${reason}</td>
                        <td>${stats.count}</td>
                        <td>${stats.winCount}</td>
                        <td>${stats.lossCount}</td>
                        <td>${stats.count > 0 ? ((stats.winCount / stats.count) * 100).toFixed(2) : 0}%</td>
                        <td class="${stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${(stats.totalPnl / stats.count).toFixed(2)}</td>
                    </tr>
                    `).join('');
                })()}
            </tbody>
        </table>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`HTML report generated at ${reportPath}`);
}

// Run verification
runVerification().catch(error => {
    console.error(`Error running verification:`, error);
});
