<!DOCTYPE html>

<html>
<head>
<title>Investor Reporting</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        /* Original styles for this dashboard */
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 2rem;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header-left h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header-left p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .header-right {
            display: flex;
            gap: 1rem;
        }

        .report-button {
            background-color: var(--primary);
            color: white;
            border: 1px solid var(--primary-dark);
            border-radius: 0.375rem;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
        }

        .report-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0 15px var(--primary-glow);
        }

        .report-button:active {
            transform: translateY(-1px);
            box-shadow: 0 0 5px var(--primary-glow);
        }

        .report-period {
            display: flex;
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            overflow: hidden;
            margin-bottom: 2rem;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        }

        .report-period button {
            padding: 0.75rem 1.5rem;
            border: none;
            background: none;
            font-family: 'Rajdhani', sans-serif;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            border-right: 1px solid var(--border);
        }

        .report-period button:last-child {
            border-right: none;
        }

        .report-period button:hover {
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.1);
        }

        .report-period button.active {
            background-color: var(--primary);
            color: var(--dark);
            box-shadow: 0 0 10px var(--primary-glow);
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.15);
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .summary-card.primary::before { background: linear-gradient(to right, var(--primary), var(--primary-light)); }
        .summary-card.success::before { background: linear-gradient(to right, var(--success), var(--primary)); }
        .summary-card.warning::before { background: linear-gradient(to right, var(--warning), var(--primary)); }
        .summary-card.danger::before { background: linear-gradient(to right, var(--danger), var(--primary)); }

        .summary-card h3 {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .summary-value {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-family: 'Orbitron', sans-serif;
        }

        .summary-card.primary .summary-value {
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }
        .summary-card.success .summary-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .summary-card.warning .summary-value {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }
        .summary-card.danger .summary-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .summary-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            font-family: 'Rajdhani', sans-serif;
            color: var(--text-secondary);
        }

        .summary-change.positive {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        .summary-change.negative {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        .table-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2rem;
            overflow-x: auto;
            position: relative;
        }

        .table-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .table-container h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border: 1px solid var(--border);
            font-family: 'Rajdhani', sans-serif;
        }

        .data-table th {
            font-weight: 600;
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.1);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .data-table tr:hover {
            background-color: rgba(0, 204, 255, 0.05);
        }

        .positive {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        .negative {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .attribution-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .attribution-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .attribution-container h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .attribution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .attribution-card {
            background-color: rgba(0, 204, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.25rem;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .attribution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }

        .attribution-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
        }

        .attribution-card.positive::before { background: linear-gradient(to right, var(--success), var(--primary)); }
        .attribution-card.negative::before { background: linear-gradient(to right, var(--danger), var(--primary)); }
        .attribution-card.neutral::before { background: linear-gradient(to right, var(--primary), var(--primary-light)); }

        .attribution-card h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .attribution-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-family: 'Orbitron', sans-serif;
        }

        .attribution-card.positive .attribution-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .attribution-card.negative .attribution-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }
        .attribution-card.neutral .attribution-value {
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .attribution-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-family: 'Rajdhani', sans-serif;
        }
    </style>
</head>
<body>
<div class="container">
<div class="header">
<div class="header-left">
<h1>Investor Reporting</h1>
<p>Comprehensive performance reports for investors</p>
</div>
<div class="header-right">
<button class="report-button">
<span>📊</span> Generate PDF Report
                </button>
<button class="report-button">
<span>📧</span> Email to Investors
                </button>
</div>
</div>
<div class="report-period">
<button class="active">Monthly (May 2025)</button>
<button>Quarterly (Q2 2025)</button>
<button>Year-to-Date</button>
<button>Trailing 12 Months</button>
<button>Since Inception</button>
<button>Custom Period</button>
</div>
<div class="summary-grid">
<div class="summary-card primary">
<h3>Period Return</h3>
<div class="summary-value">+42.3%</div>
<div class="summary-change positive">+39.1% vs S&amp;P 500</div>
</div>
<div class="summary-card success">
<h3>Annualized Return</h3>
<div class="summary-value">+1,042%</div>
<div class="summary-change positive">+1,030% vs S&amp;P 500</div>
</div>
<div class="summary-card warning">
<h3>Sharpe Ratio</h3>
<div class="summary-value">5.12</div>
<div class="summary-change positive">Top 1% of all funds</div>
</div>
<div class="summary-card danger">
<h3>Max Drawdown</h3>
<div class="summary-value">-0.22%</div>
<div class="summary-change positive">Minimal risk exposure</div>
</div>
</div>
<div class="chart-grid">
<div class="chart-container">
<h3>Cumulative Return vs Benchmarks</h3>
<div class="chart-wrapper">
<canvas id="cumulativeReturnChart"></canvas>
</div>
</div>
<div class="chart-container">
<h3>Monthly Returns</h3>
<div class="chart-wrapper">
<canvas id="monthlyReturnsChart"></canvas>
</div>
</div>
</div>
<div class="attribution-container">
<h3>Performance Attribution</h3>
<div class="attribution-grid">
<div class="attribution-card positive">
<h4>Strategy Alpha</h4>
<div class="attribution-value">+38.7%</div>
<div class="attribution-desc">Return from trading strategy edge</div>
</div>
<div class="attribution-card neutral">
<h4>Market Beta</h4>
<div class="attribution-value">+2.5%</div>
<div class="attribution-desc">Return from market exposure</div>
</div>
<div class="attribution-card positive">
<h4>Volatility Timing</h4>
<div class="attribution-value">+4.8%</div>
<div class="attribution-desc">Return from volatility regime adaptation</div>
</div>
<div class="attribution-card negative">
<h4>Trading Costs</h4>
<div class="attribution-value">-3.7%</div>
<div class="attribution-desc">Impact of commissions and slippage</div>
</div>
</div>
</div>
<div class="table-container">
<h3>Daily Performance Log</h3>
<table class="data-table">
<thead>
<tr>
<th>Date</th>
<th>Return</th>
<th>Trades</th>
<th>Win Rate</th>
<th>Profit Factor</th>
<th>Max Drawdown</th>
<th>S&amp;P 500</th>
<th>Alpha</th>
</tr>
</thead>
<tbody>
<tr>
<td>May 31, 2025</td>
<td class="positive">+2.1%</td>
<td>34</td>
<td>79.4%</td>
<td>22.7</td>
<td>$124</td>
<td class="positive">+0.3%</td>
<td class="positive">+1.8%</td>
</tr>
<tr>
<td>May 30, 2025</td>
<td class="positive">****%</td>
<td>31</td>
<td>80.6%</td>
<td>24.3</td>
<td>$98</td>
<td class="negative">-0.2%</td>
<td class="positive">+2.1%</td>
</tr>
<tr>
<td>May 29, 2025</td>
<td class="positive">+2.3%</td>
<td>37</td>
<td>78.4%</td>
<td>21.5</td>
<td>$145</td>
<td class="positive">+0.5%</td>
<td class="positive">+1.8%</td>
</tr>
<tr>
<td>May 28, 2025</td>
<td class="positive">+2.5%</td>
<td>42</td>
<td>81.0%</td>
<td>25.2</td>
<td>$112</td>
<td class="positive">+0.7%</td>
<td class="positive">+1.8%</td>
</tr>
<tr>
<td>May 27, 2025</td>
<td class="positive">+1.8%</td>
<td>29</td>
<td>75.9%</td>
<td>18.7</td>
<td>$156</td>
<td class="negative">-0.4%</td>
<td class="positive">+2.2%</td>
</tr>
<tr>
<td>May 26, 2025</td>
<td class="positive">+2.2%</td>
<td>35</td>
<td>77.1%</td>
<td>20.3</td>
<td>$132</td>
<td class="positive">+0.2%</td>
<td class="positive">****%</td>
</tr>
<tr>
<td>May 25, 2025</td>
<td class="positive">****%</td>
<td>33</td>
<td>78.8%</td>
<td>22.1</td>
<td>$118</td>
<td class="positive">+0.1%</td>
<td class="positive">****%</td>
</tr>
</tbody>
</table>
</div>
</div>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Cumulative Return Chart
            const cumulativeReturnCtx = document.getElementById('cumulativeReturnChart').getContext('2d');

            // Generate dates for the month
            const dates = [];
            const today = new Date(2025, 4, 31); // May 31, 2025
            for (let i = 30; i >= 0; i--) {
                const date = new Date(2025, 4, 31 - i);
                dates.push(date.toISOString().split('T')[0]);
            }

            // Generate cumulative return data
            const strategyReturns = [];
            const spxReturns = [];
            const nasdaqReturns = [];

            let strategyValue = 100;
            let spxValue = 100;
            let nasdaqValue = 100;

            dates.forEach((date, i) => {
                // Simulate daily returns
                const dailyReturn = (Math.random() * 0.02) + 0.01; // 1-3% daily return
                strategyValue *= (1 + dailyReturn);

                // Simulate benchmark returns
                const spxReturn = (Math.random() * 0.01) - 0.002; // -0.2% to 0.8% daily return
                const nasdaqReturn = (Math.random() * 0.015) - 0.003; // -0.3% to 1.2% daily return

                spxValue *= (1 + spxReturn);
                nasdaqValue *= (1 + nasdaqReturn);

                strategyReturns.push(strategyValue);
                spxReturns.push(spxValue);
                nasdaqReturns.push(nasdaqValue);
            });

            new Chart(cumulativeReturnCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'Trading Strategy',
                            data: strategyReturns,
                            borderColor: 'rgba(0, 204, 255, 1)',
                            backgroundColor: 'rgba(0, 204, 255, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'S&P 500',
                            data: spxReturns,
                            borderColor: 'rgba(255, 51, 102, 1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: 'NASDAQ',
                            data: nasdaqReturns,
                            borderColor: 'rgba(255, 204, 0, 1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Value (Starting at 100)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed.y;
                                    const initialValue = 100;
                                    const percentChange = ((value - initialValue) / initialValue) * 100;
                                    return `${context.dataset.label}: ${percentChange.toFixed(2)}%`;
                                }
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // Monthly Returns Chart
            const monthlyReturnsCtx = document.getElementById('monthlyReturnsChart').getContext('2d');

            const months = ['Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May'];
            const monthlyReturns = [48.3, 59.8, 61.2, 55.4, 68.7, 42.3];
            const spxMonthlyReturns = [1.2, 2.3, -0.8, 1.5, 3.2, 0.9];

            new Chart(monthlyReturnsCtx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: 'Trading Strategy',
                            data: monthlyReturns,
                            backgroundColor: 'rgba(0, 204, 255, 0.7)',
                            borderColor: 'rgba(0, 204, 255, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: 'S&P 500',
                            data: spxMonthlyReturns,
                            backgroundColor: 'rgba(255, 51, 102, 0.7)',
                            borderColor: 'rgba(255, 51, 102, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Return (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.parsed.y}%`;
                                }
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // Report period buttons
            const reportPeriodButtons = document.querySelectorAll('.report-period button');

            reportPeriodButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    reportPeriodButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // In a real implementation, this would update the data
                    console.log('Switched to report period: ' + this.textContent);
                });
            });
        });
    </script>
<!-- Dashboard Integration -->
<script>
        // Wait for dashboard to initialize
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header-left h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Investor Reporting`;
            }

            // Update charts with data
            updateCharts(data);

            console.log('Investor dashboard updated with data for:', data.instrumentCode);
        }

        // Update charts with data
        function updateCharts(data) {
            // Update equity chart
            updateEquityChart(data);

            // Update monthly returns chart
            updateMonthlyReturnsChart(data);

            // Update attribution data
            updateAttributionData(data);
        }

        // Update equity chart
        function updateEquityChart(data) {
            const dailyPnL = data.dailyPnL || [];

            if (dailyPnL.length === 0) {
                console.warn('No daily PnL data available');
                return;
            }

            // Prepare data
            const labels = dailyPnL.map(day => day.date);
            const equityValues = [];

            let cumulativeEquity = 0;
            dailyPnL.forEach(day => {
                cumulativeEquity += (day.netProfit || 0);
                equityValues.push(cumulativeEquity);
            });

            // Create chart config using the chart utilities
            const chartConfig = dashboardChartUtils.createLineChartConfig(
                labels,
                [{
                    label: 'Equity',
                    data: equityValues,
                    color: data.instrumentConfig.color
                }]
            );

            // Get the chart context
            const ctx = document.getElementById('equity-chart').getContext('2d');

            // Create or update chart
            if (window.equityChart) {
                window.equityChart.data = chartConfig.data;
                window.equityChart.update();
            } else {
                window.equityChart = new Chart(ctx, chartConfig);
            }
        }

        // Update monthly returns chart
        function updateMonthlyReturnsChart(data) {
            const dailyPnL = data.dailyPnL || [];

            if (dailyPnL.length === 0) {
                console.warn('No daily PnL data available');
                return;
            }

            // Group by month
            const monthlyReturns = {};
            dailyPnL.forEach(day => {
                const date = new Date(day.date);
                const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

                if (!monthlyReturns[monthKey]) {
                    monthlyReturns[monthKey] = 0;
                }

                monthlyReturns[monthKey] += (day.netProfit || 0);
            });

            // Prepare data
            const labels = Object.keys(monthlyReturns);
            const returns = Object.values(monthlyReturns);

            // Create chart config using the chart utilities
            const chartConfig = dashboardChartUtils.createBarChartConfig(
                labels,
                [{
                    label: 'Monthly Returns',
                    data: returns,
                    color: data.instrumentConfig.color
                }]
            );

            // Get the chart context
            const ctx = document.getElementById('monthly-returns-chart').getContext('2d');

            // Create or update chart
            if (window.monthlyReturnsChart) {
                window.monthlyReturnsChart.data = chartConfig.data;
                window.monthlyReturnsChart.update();
            } else {
                window.monthlyReturnsChart = new Chart(ctx, chartConfig);
            }
        }

        // Update attribution data
        function updateAttributionData(data) {
            // This would be implemented based on your specific attribution data structure
            console.log('Attribution data would be updated with:', data);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
