/**
 * Data Cache Manager
 * 
 * This module provides a cache for market data and other frequently accessed data.
 */

const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const logger = require('./data_logger');

/**
 * Data Cache Manager Class
 */
class DataCacheManager extends EventEmitter {
    /**
     * Constructor
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
        super();
        
        this.options = {
            maxCacheSize: options.maxCacheSize || 1000,
            cacheTTL: options.cacheTTL || 60000, // 1 minute
            persistCache: options.persistCache !== false,
            cacheDir: options.cacheDir || path.join(__dirname, 'cache'),
            debug: options.debug || false
        };
        
        // State
        this.cache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            size: 0
        };
        this.cleanupTimer = null;
        
        // Create cache directory if it doesn't exist
        if (this.options.persistCache) {
            try {
                if (!fs.existsSync(this.options.cacheDir)) {
                    fs.mkdirSync(this.options.cacheDir, { recursive: true });
                }
            } catch (error) {
                this.emit('debug', `Error creating cache directory: ${error.message}`);
            }
        }
        
        // Start cleanup timer
        this.startCleanupTimer();
        
        // Load persisted cache
        if (this.options.persistCache) {
            this.loadCache();
        }
        
        // Debug logging
        if (this.options.debug) {
            this.on('debug', (message) => {
                logger.logSystem(`[DataCache] ${message}`, 'debug');
            });
        }
    }
    
    /**
     * Set a cache item
     * @param {string} key - Cache key
     * @param {*} value - Cache value
     * @param {Object} options - Cache options
     * @param {number} options.ttl - Time to live in milliseconds
     * @param {boolean} options.persist - Whether to persist the cache item
     * @returns {boolean} - True if the item was cached
     */
    set(key, value, options = {}) {
        // Check cache size
        if (this.cache.size >= this.options.maxCacheSize && !this.cache.has(key)) {
            this.evictOldest();
        }
        
        const ttl = options.ttl || this.options.cacheTTL;
        const persist = options.persist !== undefined ? options.persist : this.options.persistCache;
        
        // Create cache item
        const item = {
            value,
            expires: Date.now() + ttl,
            persist
        };
        
        // Set cache item
        this.cache.set(key, item);
        this.cacheStats.size = this.cache.size;
        
        this.emit('debug', `Cache item set: ${key}`);
        this.emit('set', key, value);
        
        // Persist cache item
        if (persist) {
            this.persistItem(key, item);
        }
        
        return true;
    }
    
    /**
     * Get a cache item
     * @param {string} key - Cache key
     * @returns {*} - Cache value or undefined if not found
     */
    get(key) {
        if (!this.cache.has(key)) {
            this.cacheStats.misses++;
            this.emit('debug', `Cache miss: ${key}`);
            this.emit('miss', key);
            return undefined;
        }
        
        const item = this.cache.get(key);
        
        // Check if item has expired
        if (item.expires < Date.now()) {
            this.cache.delete(key);
            this.cacheStats.size = this.cache.size;
            this.cacheStats.misses++;
            this.emit('debug', `Cache item expired: ${key}`);
            this.emit('expire', key);
            return undefined;
        }
        
        this.cacheStats.hits++;
        this.emit('debug', `Cache hit: ${key}`);
        this.emit('hit', key);
        
        return item.value;
    }
    
    /**
     * Delete a cache item
     * @param {string} key - Cache key
     * @returns {boolean} - True if the item was deleted
     */
    delete(key) {
        if (!this.cache.has(key)) {
            return false;
        }
        
        const item = this.cache.get(key);
        
        // Delete cache item
        this.cache.delete(key);
        this.cacheStats.size = this.cache.size;
        
        this.emit('debug', `Cache item deleted: ${key}`);
        this.emit('delete', key);
        
        // Delete persisted cache item
        if (item.persist) {
            this.deletePersistedItem(key);
        }
        
        return true;
    }
    
    /**
     * Clear the cache
     */
    clear() {
        this.emit('debug', `Clearing cache (${this.cache.size} items)`);
        
        // Clear cache
        this.cache.clear();
        this.cacheStats.size = 0;
        
        this.emit('clear');
        
        // Clear persisted cache
        if (this.options.persistCache) {
            this.clearPersistedCache();
        }
    }
    
    /**
     * Evict the oldest cache item
     */
    evictOldest() {
        if (this.cache.size === 0) {
            return;
        }
        
        let oldestKey = null;
        let oldestExpires = Infinity;
        
        // Find oldest item
        for (const [key, item] of this.cache) {
            if (item.expires < oldestExpires) {
                oldestKey = key;
                oldestExpires = item.expires;
            }
        }
        
        if (oldestKey) {
            this.delete(oldestKey);
            this.cacheStats.evictions++;
            this.emit('debug', `Cache item evicted: ${oldestKey}`);
            this.emit('evict', oldestKey);
        }
    }
    
    /**
     * Start cleanup timer
     */
    startCleanupTimer() {
        this.stopCleanupTimer();
        
        this.emit('debug', 'Starting cleanup timer');
        
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.options.cacheTTL / 2);
    }
    
    /**
     * Stop cleanup timer
     */
    stopCleanupTimer() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
            this.emit('debug', 'Stopped cleanup timer');
        }
    }
    
    /**
     * Cleanup expired cache items
     */
    cleanup() {
        const now = Date.now();
        let expiredCount = 0;
        
        this.emit('debug', 'Cleaning up expired cache items');
        
        // Find expired items
        for (const [key, item] of this.cache) {
            if (item.expires < now) {
                this.cache.delete(key);
                expiredCount++;
                
                // Delete persisted cache item
                if (item.persist) {
                    this.deletePersistedItem(key);
                }
            }
        }
        
        this.cacheStats.size = this.cache.size;
        
        if (expiredCount > 0) {
            this.emit('debug', `Cleaned up ${expiredCount} expired cache items`);
            this.emit('cleanup', expiredCount);
        }
    }
    
    /**
     * Persist a cache item
     * @param {string} key - Cache key
     * @param {Object} item - Cache item
     */
    persistItem(key, item) {
        if (!this.options.persistCache) {
            return;
        }
        
        try {
            const filePath = path.join(this.options.cacheDir, `${encodeURIComponent(key)}.json`);
            
            fs.writeFileSync(filePath, JSON.stringify({
                key,
                value: item.value,
                expires: item.expires
            }));
            
            this.emit('debug', `Cache item persisted: ${key}`);
        } catch (error) {
            this.emit('debug', `Error persisting cache item ${key}: ${error.message}`);
        }
    }
    
    /**
     * Delete a persisted cache item
     * @param {string} key - Cache key
     */
    deletePersistedItem(key) {
        if (!this.options.persistCache) {
            return;
        }
        
        try {
            const filePath = path.join(this.options.cacheDir, `${encodeURIComponent(key)}.json`);
            
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                this.emit('debug', `Persisted cache item deleted: ${key}`);
            }
        } catch (error) {
            this.emit('debug', `Error deleting persisted cache item ${key}: ${error.message}`);
        }
    }
    
    /**
     * Clear persisted cache
     */
    clearPersistedCache() {
        if (!this.options.persistCache) {
            return;
        }
        
        try {
            const files = fs.readdirSync(this.options.cacheDir);
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    fs.unlinkSync(path.join(this.options.cacheDir, file));
                }
            }
            
            this.emit('debug', `Cleared persisted cache (${files.length} items)`);
        } catch (error) {
            this.emit('debug', `Error clearing persisted cache: ${error.message}`);
        }
    }
    
    /**
     * Load persisted cache
     */
    loadCache() {
        if (!this.options.persistCache) {
            return;
        }
        
        try {
            const files = fs.readdirSync(this.options.cacheDir);
            let loadedCount = 0;
            
            for (const file of files) {
                if (file.endsWith('.json')) {
                    try {
                        const filePath = path.join(this.options.cacheDir, file);
                        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                        
                        // Check if item has expired
                        if (data.expires > Date.now()) {
                            this.cache.set(data.key, {
                                value: data.value,
                                expires: data.expires,
                                persist: true
                            });
                            
                            loadedCount++;
                        } else {
                            // Delete expired item
                            fs.unlinkSync(filePath);
                        }
                    } catch (error) {
                        this.emit('debug', `Error loading persisted cache item ${file}: ${error.message}`);
                    }
                }
            }
            
            this.cacheStats.size = this.cache.size;
            
            this.emit('debug', `Loaded ${loadedCount} persisted cache items`);
        } catch (error) {
            this.emit('debug', `Error loading persisted cache: ${error.message}`);
        }
    }
    
    /**
     * Get cache stats
     * @returns {Object} - Cache stats
     */
    getStats() {
        return {
            ...this.cacheStats,
            hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0
        };
    }
}

module.exports = DataCacheManager;
