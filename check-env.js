/**
 * Simple script to check if .env file is being loaded correctly
 */

console.log('Starting environment check...');

// Load environment variables from .env file
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Check if .env file exists
const envPath = path.resolve(__dirname, '.env');
console.log(`Checking for .env file at: ${envPath}`);
console.log(`File exists: ${fs.existsSync(envPath)}`);

// If .env file exists, show its content (excluding sensitive data)
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const safeContent = envContent.replace(/DATABENTO_API_KEY=.*/g, 'DATABENTO_API_KEY=***')
                               .replace(/TRADOVATE_API_KEY=.*/g, 'TRADOVATE_API_KEY=***')
                               .replace(/TRADOVATE_PASSWORD=.*/g, 'TRADOVATE_PASSWORD=***');
  console.log('Content of .env file:');
  console.log(safeContent);
}

// Load environment variables
const result = dotenv.config();
console.log('dotenv.config() result:', result.error ? result.error.message : 'Success');

// Show all environment variables (excluding sensitive data)
console.log('Environment variables:');
Object.keys(process.env).forEach(key => {
  if (key.includes('KEY') || key.includes('PASSWORD') || key.includes('SECRET')) {
    console.log(`${key}=***`);
  } else {
    console.log(`${key}=${process.env[key]}`);
  }
});

console.log('Environment check complete.');
