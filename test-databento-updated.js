/**
 * Test script for updated Databento integration
 */

require('dotenv').config();
const DabentoClient = require('./databento');
const logger = require('./logger');

// Use the exact API key from the Python example
const API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq";

console.log(`Using Databento API key: ${API_KEY.substring(0, 8)}...`);

// Create Databento client
const client = new DabentoClient(API_KEY);

// Test functions
async function testHistorical() {
  try {
    console.log('Testing historical data...');
    
    // Get available datasets
    console.log('Getting available datasets...');
    const datasets = await client.historical.getDatasets();
    console.log(`Found ${datasets.length} datasets`);
    datasets.forEach(dataset => {
      console.log(`- ${dataset}`);
    });
    
    // Check if GLBX.MDP3 is in the available datasets
    const hasGlbx = datasets.includes('GLBX.MDP3');
    if (hasGlbx) {
      console.log('GLBX.MDP3 dataset found');
    } else {
      console.log('GLBX.MDP3 dataset not found in available datasets');
    }
    
    // Get historical data for ES.FUT
    console.log('\nGetting historical data for ES.FUT...');
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 1); // 1 day ago
    
    const historicalData = await client.historical.getTimeseries(
      'GLBX.MDP3',
      'ES.FUT',
      startDate,
      endDate,
      'trades',
      { stype_in: 'parent' }
    );
    
    console.log('Historical data response:', historicalData.message);
    
    return true;
  } catch (error) {
    console.error('Error testing historical data:', error);
    return false;
  }
}

async function testLiveWebSocket() {
  try {
    console.log('\nTesting live data via WebSocket...');
    
    // Connect to live data
    console.log('Connecting to live data...');
    client.live.on('connected', () => {
      console.log('Connected to Databento live data');
    });
    
    client.live.on('error', (error) => {
      console.error('Live data error:', error);
    });
    
    client.live.on('disconnected', () => {
      console.log('Disconnected from Databento live data');
    });
    
    client.live.on('heartbeat', () => {
      console.log('Received heartbeat');
    });
    
    await client.live.connect();
    
    // Subscribe to ES.FUT
    console.log('Subscribing to ES.FUT...');
    await client.live.subscribe(
      'GLBX.MDP3',
      'ES.FUT',
      'trades',
      { stype_in: 'parent' }
    );
    
    // Set up data handler
    let messageCount = 0;
    const dataHandler = (data) => {
      messageCount++;
      if (messageCount <= 5) {
        console.log('Received data:', data);
      }
    };
    
    client.live.on('data', dataHandler);
    
    // Wait for some data
    console.log('Waiting for data (30 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    console.log(`Received ${messageCount} messages`);
    
    // Unsubscribe and disconnect
    console.log('Unsubscribing and disconnecting...');
    client.live.unsubscribe('GLBX.MDP3', 'ES.FUT', 'trades');
    client.live.removeListener('data', dataHandler);
    client.live.disconnect();
    
    return true;
  } catch (error) {
    console.error('Error testing live data:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('Starting Databento integration tests...');
  
  let success = true;
  
  // Test historical data
  if (await testHistorical()) {
    console.log('\n✅ Historical data test passed');
  } else {
    console.log('\n❌ Historical data test failed');
    success = false;
  }
  
  // Test live data
  if (await testLiveWebSocket()) {
    console.log('\n✅ Live data test passed');
  } else {
    console.log('\n❌ Live data test failed');
    success = false;
  }
  
  console.log('\nTests completed.');
  if (success) {
    console.log('All tests passed! 🎉');
  } else {
    console.log('Some tests failed. 😢');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
