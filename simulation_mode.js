/**
 * Simulation Mode for Trading Bot
 *
 * This module provides a simulation mode for the trading bot that doesn't place actual orders
 * but simulates trading activity based on historical or real-time data.
 */

const fs = require('fs');
const path = require('path');
const logger = require('./data_logger');
const SimpleCache = require('./simple_cache');

// Configuration
const DEFAULT_SLIPPAGE = 0.75; // Default slippage in points
const DEFAULT_COMMISSION = 0.40; // Default commission per side in dollars
const DEFAULT_LATENCY = 0; // Default latency in bars (0 = no latency)

// State
let isRunning = false;
let marketData = {};
let activeOrders = {};
let activePositions = {};
let filledOrders = [];
let closedPositions = [];
let lastCandleTimestamps = {};
let simulationConfig = {
    slippage: DEFAULT_SLIPPAGE,
    commission: DEFAULT_COMMISSION,
    latency: DEFAULT_LATENCY,
    useHistoricalData: false,
    historicalDataPath: '',
    symbols: [],
    accountBalance: 10000,
    maxPositionSize: 10
};

// Caches
const indicatorCache = new SimpleCache({
    maxSize: 100,
    ttl: 60000, // 1 minute
    debug: true
});

/**
 * Initialize simulation mode
 * @param {Object} config - Simulation configuration
 * @returns {Promise<boolean>} - True if initialization was successful
 */
async function initialize(config = {}) {
    try {
        logger.logSystem('Initializing simulation mode...', 'info');

        // Update configuration
        simulationConfig = {
            ...simulationConfig,
            ...config
        };

        // Initialize market data for each symbol
        await initializeMarketData();

        // Load historical data if configured
        if (simulationConfig.useHistoricalData && simulationConfig.historicalDataPath) {
            await loadHistoricalData();
        }

        isRunning = true;
        logger.logSystem('Simulation mode initialized successfully', 'info');
        console.log('Simulation mode initialized successfully');

        return true;
    } catch (error) {
        logger.logSystem(`Initialization failed: ${error.message}`, 'error');
        console.error(`Initialization failed: ${error.message}`);
        return false;
    }
}

/**
 * Initialize market data for all symbols
 * @returns {Promise<void>}
 */
async function initializeMarketData() {
    for (const symbol of simulationConfig.symbols) {
        marketData[symbol] = {
            candles: [],
            indicators: {},
            positions: [],
            orders: []
        };

        logger.logSystem(`Initialized market data for ${symbol}`, 'info');
    }

    logger.logSystem('Market data initialized for all symbols', 'info');
}

/**
 * Load historical data
 * @returns {Promise<boolean>} - True if data was loaded successfully
 */
async function loadHistoricalData() {
    try {
        logger.logSystem(`Loading historical data from ${simulationConfig.historicalDataPath}...`, 'info');

        for (const symbol of simulationConfig.symbols) {
            const filePath = path.join(simulationConfig.historicalDataPath, `${symbol.toLowerCase()}_data.json`);

            if (fs.existsSync(filePath)) {
                const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

                if (data && data.candles && data.candles.length > 0) {
                    marketData[symbol].candles = data.candles;
                    logger.logSystem(`Loaded ${data.candles.length} candles for ${symbol}`, 'info');
                } else {
                    logger.logSystem(`No candles found in historical data for ${symbol}`, 'warning');
                }
            } else {
                logger.logSystem(`Historical data file not found for ${symbol}: ${filePath}`, 'warning');
            }
        }

        logger.logSystem('Historical data loaded successfully', 'info');
        return true;
    } catch (error) {
        logger.logSystem(`Error loading historical data: ${error.message}`, 'error');
        return false;
    }
}

/**
 * Process a candle
 * @param {string} symbol - Symbol
 * @param {Object} candle - Candle data
 */
function processCandle(symbol, candle) {
    try {
        // Format candle
        const formattedCandle = {
            timestamp: new Date(candle.timestamp).getTime(),
            open: candle.open,
            high: candle.high,
            low: candle.low,
            close: candle.close,
            volume: candle.volume || 0
        };

        // Check if we've already processed this candle
        if (lastCandleTimestamps[symbol] === formattedCandle.timestamp) {
            return;
        }

        // Update last candle timestamp
        lastCandleTimestamps[symbol] = formattedCandle.timestamp;

        // Add candle to market data
        marketData[symbol].candles.push(formattedCandle);

        // Limit candles array to 1000 candles
        if (marketData[symbol].candles.length > 1000) {
            marketData[symbol].candles.shift();
        }

        // Calculate indicators
        calculateIndicators(symbol);

        // Check for trading signals
        checkForSignals(symbol, formattedCandle);

        // Process pending orders
        processPendingOrders(symbol, formattedCandle);

        // Update positions
        updatePositions(symbol, formattedCandle);

        logger.logSystem(`Processed candle for ${symbol}: ${JSON.stringify(formattedCandle)}`, 'debug');
    } catch (error) {
        logger.logSystem(`Error processing candle for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Calculate indicators for a symbol
 * @param {string} symbol - Symbol
 */
function calculateIndicators(symbol) {
    try {
        const candles = marketData[symbol].candles;

        // Need at least 50 candles for indicators
        if (candles.length < 50) {
            return;
        }

        // Check cache first
        const cacheKey = `${symbol}-indicators-${candles[candles.length - 1].timestamp}`;
        const cachedIndicators = indicatorCache.get(cacheKey);

        if (cachedIndicators) {
            marketData[symbol].indicators = cachedIndicators;
            logger.logSystem(`Using cached indicators for ${symbol}`, 'debug');
            return;
        }

        // Get close prices
        const closes = candles.map(c => c.close);

        // Calculate ATR (Average True Range)
        const atr = calculateATR(candles, 14);

        // Calculate RSI (14-period)
        const rsi = calculateRSI(candles, 14);

        // Calculate RSI values for the last 22 candles to get enough data for the RSI-MA
        const rsiValues = [];
        for (let i = 0; i < 22; i++) {
            if (candles.length > i + 14) { // Need at least 14 candles to calculate RSI
                const rsiValue = calculateRSI(candles.slice(0, candles.length - i), 14);
                rsiValues.unshift(rsiValue);
            }
        }

        // Calculate SMA of RSI values (8-period RSI-based MA)
        const rsiBasedMA = rsiValues.length >= 8 ? calculateSMA(rsiValues, 8) : 50;

        // Calculate WMA (50-period Weighted Moving Average)
        const wma50 = calculateWMA(candles, 50);

        // Calculate SMA (200-period Simple Moving Average) - optional
        const sma200 = calculateSMA(closes, 200);

        // Store indicators
        const indicators = {
            atr,
            rsi,
            rsiBasedMA,
            wma50,
            sma200,
            upperBand: 60,
            middleBand: 50,
            lowerBand: 40
        };

        marketData[symbol].indicators = indicators;

        // Cache indicators
        indicatorCache.set(cacheKey, indicators);

        logger.logSystem(`Calculated indicators for ${symbol}: ATR=${atr.toFixed(2)}, RSI=${rsi.toFixed(2)}, RSI-MA=${rsiBasedMA.toFixed(2)}, WMA50=${wma50.toFixed(2)}`, 'debug');
    } catch (error) {
        logger.logSystem(`Error calculating indicators for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Calculate ATR (Average True Range)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - ATR value
 */
function calculateATR(candles, period) {
    // Simple ATR calculation
    let trSum = 0;

    for (let i = 1; i < period + 1; i++) {
        const high = candles[candles.length - i].high;
        const low = candles[candles.length - i].low;
        const prevClose = candles[candles.length - i - 1].close;

        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);

        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
    }

    return trSum / period;
}

/**
 * Calculate RSI (Relative Strength Index)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - RSI value
 */
function calculateRSI(candles, period) {
    if (!candles || candles.length <= period) {
        return 50; // Default value if not enough data
    }

    // Get close prices
    const closes = candles.map(c => c.close);

    // Calculate price changes
    const changes = [];
    for (let i = 1; i < closes.length; i++) {
        changes.push(closes[i] - closes[i - 1]);
    }

    // Calculate initial averages
    let gainSum = 0;
    let lossSum = 0;

    for (let i = 0; i < period; i++) {
        const change = changes[i];
        if (change > 0) {
            gainSum += change;
        } else {
            lossSum -= change; // Make loss positive
        }
    }

    let avgGain = gainSum / period;
    let avgLoss = lossSum / period;

    // Calculate RSI using Wilder's smoothing method
    for (let i = period; i < changes.length; i++) {
        const change = changes[i];
        let currentGain = 0;
        let currentLoss = 0;

        if (change > 0) {
            currentGain = change;
        } else {
            currentLoss = -change; // Make loss positive
        }

        // Use Wilder's smoothing
        avgGain = ((avgGain * (period - 1)) + currentGain) / period;
        avgLoss = ((avgLoss * (period - 1)) + currentLoss) / period;
    }

    // Handle division by zero
    if (avgLoss === 0) {
        return 100;
    }

    // Calculate RS and RSI
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));

    return rsi;
}

/**
 * Calculate WMA (Weighted Moving Average)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - WMA value
 */
function calculateWMA(candles, period) {
    // Simple WMA calculation
    let sum = 0;
    let weightSum = 0;

    for (let i = 0; i < period; i++) {
        const weight = period - i;
        const price = candles[candles.length - 1 - i].close;

        sum += price * weight;
        weightSum += weight;
    }

    return sum / weightSum;
}

/**
 * Calculate SMA (Simple Moving Average)
 * @param {Array} values - Values to calculate SMA on
 * @param {number} period - Period
 * @returns {number} - SMA value
 */
function calculateSMA(values, period) {
    // Simple SMA calculation
    let sum = 0;

    for (let i = 0; i < period; i++) {
        sum += values[values.length - 1 - i];
    }

    return sum / period;
}

/**
 * Check for trading signals
 * @param {string} symbol - Symbol
 * @param {Object} candle - Candle data
 */
function checkForSignals(symbol, candle) {
    try {
        // Get market data
        const data = marketData[symbol];

        // Need at least 50 candles and indicators
        if (!data || !data.candles || data.candles.length < 50 || !data.indicators) {
            return;
        }

        // Get indicators
        const indicators = data.indicators;

        // Get recent candles for pattern detection
        const candles = data.candles;
        const currentIndex = candles.length - 1;

        if (currentIndex < 3) {
            return; // Need at least 4 candles for pattern detection
        }

        const c0 = candles[currentIndex - 3]; // 4th last candle
        const c1 = candles[currentIndex - 2]; // 3rd last candle
        const c2 = candles[currentIndex - 1]; // 2nd last candle
        const c3 = candle; // Current candle

        // Log indicator values for debugging
        logger.logSystem(`Signal check for ${symbol}: RSI=${indicators.rsi.toFixed(2)}, RSI-MA=${indicators.rsiBasedMA.toFixed(2)}, Close=${candle.close}, WMA50=${indicators.wma50.toFixed(2)}`, 'debug');

        // Detect patterns
        const pattern3 = detect3CandlePattern(c1, c2, c3);
        const pattern4 = detect4CandlePattern(c0, c1, c2, c3);

        // Determine pattern and type
        let pattern = null;
        let patternType = null;

        if (pattern4) {
            pattern = pattern4;
            patternType = 'four';
        } else if (pattern3) {
            pattern = pattern3;
            patternType = 'three';
        }

        // If no pattern detected, exit
        if (!pattern) {
            return;
        }

        // Check RSI conditions
        const rsiAboveMA = indicators.rsi > indicators.rsiBasedMA;
        const priceAboveWMA = candle.close > indicators.wma50;
        const rsiBelowMA = indicators.rsi < indicators.rsiBasedMA;
        const priceBelowWMA = candle.close < indicators.wma50;

        // Calculate RSI-MA separation
        const rsiMaSeparation = Math.abs(indicators.rsi - indicators.rsiBasedMA);
        const minSeparation = 0.5; // Minimum required separation

        logger.logSystem(`Conditions for ${symbol}: Pattern=${pattern}, Type=${patternType}, RSI > RSI-MA: ${rsiAboveMA}, Price > WMA50: ${priceAboveWMA}, RSI < RSI-MA: ${rsiBelowMA}, Price < WMA50: ${priceBelowWMA}, RSI-MA Separation: ${rsiMaSeparation.toFixed(2)}`, 'debug');

        // Calculate stop loss and take profit based on ATR
        const atr = indicators.atr;
        let stopLossPoints, takeProfitPoints, trailFactor, fixedTpPoints = 0;

        if (symbol === 'MNQ') {
            stopLossPoints = atr * 4.5;
            takeProfitPoints = atr * 3.0;
            trailFactor = 0.11;
            fixedTpPoints = 40; // Fixed TP points for MNQ
        } else if (symbol === 'MES') {
            stopLossPoints = atr * 3.0;
            takeProfitPoints = atr * 3.0;
            trailFactor = 0.01;
        } else if (symbol === 'MGC') {
            stopLossPoints = atr * 8.0;
            takeProfitPoints = atr * 7.0;
            trailFactor = 0.02;
        } else {
            stopLossPoints = atr * 3.0;
            takeProfitPoints = atr * 2.0;
            trailFactor = 0.05;
        }

        // Entry validation with pattern and RSI logic
        let validEntry = false;

        if (pattern === 'bullish') {
            // For bullish entries: RSI must be above its MA AND price must be above WMA50
            // For three-candle patterns, enforce RSI > RSI-MA
            if (patternType === 'three' && (!rsiAboveMA || !priceAboveWMA || rsiMaSeparation <= minSeparation)) {
                return;
            }

            // For four-candle patterns, still check price > WMA50
            if (patternType === 'four' && !priceAboveWMA) {
                return;
            }

            validEntry = true;

            // Long signal
            logger.logSystem(`Long signal for ${symbol}: Pattern=${patternType}, RSI=${indicators.rsi.toFixed(2)}, RSI-MA=${indicators.rsiBasedMA.toFixed(2)}, Close=${candle.close}, WMA50=${indicators.wma50.toFixed(2)}`, 'info');

            // Place long order
            const orderParams = {
                symbol,
                action: 'Buy',
                quantity: simulationConfig.maxPositionSize || 5,
                stopLossPrice: candle.close - stopLossPoints,
                takeProfitPrice: candle.close + Math.max(fixedTpPoints, takeProfitPoints),
                trailFactor
            };

            placeMarketOrder(orderParams);
        }
        else if (pattern === 'bearish') {
            // For bearish entries: RSI must be below its MA AND price must be below WMA50
            // For three-candle patterns, enforce RSI < RSI-MA
            if (patternType === 'three' && (!rsiBelowMA || !priceBelowWMA || rsiMaSeparation <= minSeparation)) {
                return;
            }

            // For four-candle patterns, still check price < WMA50
            if (patternType === 'four' && !priceBelowWMA) {
                return;
            }

            validEntry = true;

            // Short signal
            logger.logSystem(`Short signal for ${symbol}: Pattern=${patternType}, RSI=${indicators.rsi.toFixed(2)}, RSI-MA=${indicators.rsiBasedMA.toFixed(2)}, Close=${candle.close}, WMA50=${indicators.wma50.toFixed(2)}`, 'info');

            // Place short order
            const orderParams = {
                symbol,
                action: 'Sell',
                quantity: simulationConfig.maxPositionSize || 5,
                stopLossPrice: candle.close + stopLossPoints,
                takeProfitPrice: candle.close - Math.max(fixedTpPoints, takeProfitPoints),
                trailFactor
            };

            placeMarketOrder(orderParams);
        }
    } catch (error) {
        logger.logSystem(`Error checking for signals for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Detect 3-candle pattern
 * @param {Object} c1 - First candle
 * @param {Object} c2 - Second candle
 * @param {Object} c3 - Third candle
 * @returns {string|null} - Pattern direction or null
 */
function detect3CandlePattern(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;

        if (engulf && wick3 > wick2) return 'bullish';
    }

    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);

        if (engulf && wick3 > wick2) return 'bearish';
    }

    return null;
}

/**
 * Detect 4-candle pattern
 * @param {Object} c0 - First candle
 * @param {Object} c1 - Second candle
 * @param {Object} c2 - Third candle
 * @param {Object} c3 - Fourth candle
 * @returns {string|null} - Pattern direction or null
 */
function detect4CandlePattern(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c2.open)) return 'bullish';
    }

    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c2.open)) return 'bearish';
    }

    return null;
}

/**
 * Determine candle color
 * @param {Object} candle - Candle data
 * @returns {string} - Candle color
 */
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji';
}

/**
 * Process pending orders
 * @param {string} symbol - Symbol
 * @param {Object} candle - Candle data
 */
function processPendingOrders(symbol, candle) {
    try {
        // Get pending orders for this symbol
        const pendingOrders = Object.values(activeOrders).filter(order =>
            order.symbol === symbol && order.status === 'Pending');

        if (pendingOrders.length === 0) {
            return;
        }

        logger.logSystem(`Processing ${pendingOrders.length} pending orders for ${symbol}`, 'info');

        // Process each pending order
        for (const order of pendingOrders) {
            // Apply latency if configured
            if (simulationConfig.latency > 0) {
                // Skip processing if we haven't reached the latency period
                if (order.latencyBars < simulationConfig.latency) {
                    order.latencyBars++;
                    continue;
                }
            }

            // Determine fill price with slippage
            let fillPrice;
            if (order.orderType === 'Market') {
                // For market orders, apply slippage
                if (order.action === 'Buy') {
                    fillPrice = candle.open + simulationConfig.slippage;
                } else {
                    fillPrice = candle.open - simulationConfig.slippage;
                }
            } else if (order.orderType === 'Limit') {
                // For limit orders, check if price was reached
                if (order.action === 'Buy' && candle.low <= order.price) {
                    fillPrice = order.price;
                } else if (order.action === 'Sell' && candle.high >= order.price) {
                    fillPrice = order.price;
                } else {
                    // Price not reached, skip this order
                    continue;
                }
            } else if (order.orderType === 'Stop') {
                // For stop orders, check if price was reached
                if (order.action === 'Buy' && candle.high >= order.price) {
                    fillPrice = order.price + simulationConfig.slippage;
                } else if (order.action === 'Sell' && candle.low <= order.price) {
                    fillPrice = order.price - simulationConfig.slippage;
                } else {
                    // Price not reached, skip this order
                    continue;
                }
            } else {
                // Unknown order type, skip this order
                logger.logSystem(`Unknown order type: ${order.orderType}`, 'warning');
                continue;
            }

            // Fill the order
            fillOrder(order, fillPrice, candle.timestamp);
        }
    } catch (error) {
        logger.logSystem(`Error processing pending orders for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Fill an order
 * @param {Object} order - Order to fill
 * @param {number} fillPrice - Fill price
 * @param {number} timestamp - Fill timestamp
 */
function fillOrder(order, fillPrice, timestamp) {
    try {
        // Update order status
        order.status = 'Filled';
        order.fillPrice = fillPrice;
        order.fillTimestamp = timestamp;

        logger.logSystem(`Filled order ${order.id} for ${order.symbol} at ${fillPrice}`, 'info');

        // Add to filled orders
        filledOrders.push({ ...order });

        // Create position
        createPosition(order);

        // Remove from active orders
        delete activeOrders[order.id];
    } catch (error) {
        logger.logSystem(`Error filling order ${order.id}: ${error.message}`, 'error');
    }
}

/**
 * Create a position from a filled order
 * @param {Object} order - Filled order
 */
function createPosition(order) {
    try {
        // Check if we already have a position for this symbol
        const existingPosition = Object.values(activePositions).find(position =>
            position.symbol === order.symbol);

        if (existingPosition) {
            // Update existing position
            if (existingPosition.action === order.action) {
                // Same direction, increase position
                const totalQuantity = existingPosition.quantity + order.quantity;
                const totalCost = (existingPosition.entryPrice * existingPosition.quantity) +
                                 (order.fillPrice * order.quantity);

                existingPosition.quantity = totalQuantity;
                existingPosition.entryPrice = totalCost / totalQuantity;

                logger.logSystem(`Updated position for ${order.symbol}: ${JSON.stringify(existingPosition)}`, 'info');
            } else {
                // Opposite direction, reduce or close position
                if (existingPosition.quantity > order.quantity) {
                    // Reduce position
                    existingPosition.quantity -= order.quantity;

                    // Calculate P&L
                    let pnl = 0;
                    if (existingPosition.action === 'Buy') {
                        pnl = (order.fillPrice - existingPosition.entryPrice) * order.quantity;
                    } else {
                        pnl = (existingPosition.entryPrice - order.fillPrice) * order.quantity;
                    }

                    // Subtract commission
                    pnl -= simulationConfig.commission * order.quantity;

                    // Update account balance
                    simulationConfig.accountBalance += pnl;

                    logger.logSystem(`Reduced position for ${order.symbol}, P&L: ${pnl}`, 'info');
                } else if (existingPosition.quantity < order.quantity) {
                    // Close position and open new one in opposite direction

                    // Calculate P&L for closed position
                    let pnl = 0;
                    if (existingPosition.action === 'Buy') {
                        pnl = (order.fillPrice - existingPosition.entryPrice) * existingPosition.quantity;
                    } else {
                        pnl = (existingPosition.entryPrice - order.fillPrice) * existingPosition.quantity;
                    }

                    // Subtract commission
                    pnl -= simulationConfig.commission * existingPosition.quantity;

                    // Update account balance
                    simulationConfig.accountBalance += pnl;

                    // Create new position
                    const newQuantity = order.quantity - existingPosition.quantity;

                    activePositions[order.id] = {
                        id: order.id,
                        symbol: order.symbol,
                        action: order.action,
                        quantity: newQuantity,
                        entryPrice: order.fillPrice,
                        entryTimestamp: order.fillTimestamp,
                        stopLossPrice: order.stopLossPrice,
                        takeProfitPrice: order.takeProfitPrice
                    };

                    // Add closed position to history
                    closedPositions.push({
                        ...existingPosition,
                        exitPrice: order.fillPrice,
                        exitTimestamp: order.fillTimestamp,
                        pnl
                    });

                    // Remove old position
                    delete activePositions[existingPosition.id];

                    logger.logSystem(`Closed position for ${order.symbol}, P&L: ${pnl}`, 'info');
                    logger.logSystem(`Created new position for ${order.symbol}: ${JSON.stringify(activePositions[order.id])}`, 'info');
                } else {
                    // Close position exactly

                    // Calculate P&L
                    let pnl = 0;
                    if (existingPosition.action === 'Buy') {
                        pnl = (order.fillPrice - existingPosition.entryPrice) * existingPosition.quantity;
                    } else {
                        pnl = (existingPosition.entryPrice - order.fillPrice) * existingPosition.quantity;
                    }

                    // Subtract commission
                    pnl -= simulationConfig.commission * existingPosition.quantity;

                    // Update account balance
                    simulationConfig.accountBalance += pnl;

                    // Add closed position to history
                    closedPositions.push({
                        ...existingPosition,
                        exitPrice: order.fillPrice,
                        exitTimestamp: order.fillTimestamp,
                        pnl
                    });

                    // Remove position
                    delete activePositions[existingPosition.id];

                    logger.logSystem(`Closed position for ${order.symbol}, P&L: ${pnl}`, 'info');
                }
            }
        } else {
            // Create new position
            activePositions[order.id] = {
                id: order.id,
                symbol: order.symbol,
                action: order.action,
                quantity: order.quantity,
                entryPrice: order.fillPrice,
                entryTimestamp: order.fillTimestamp,
                stopLossPrice: order.stopLossPrice,
                takeProfitPrice: order.takeProfitPrice
            };

            logger.logSystem(`Created position for ${order.symbol}: ${JSON.stringify(activePositions[order.id])}`, 'info');
        }
    } catch (error) {
        logger.logSystem(`Error creating position from order ${order.id}: ${error.message}`, 'error');
    }
}

/**
 * Update positions
 * @param {string} symbol - Symbol
 * @param {Object} candle - Candle data
 */
function updatePositions(symbol, candle) {
    try {
        // Get positions for this symbol
        const positions = Object.values(activePositions).filter(position =>
            position.symbol === symbol);

        if (positions.length === 0) {
            return;
        }

        // Update each position
        for (const position of positions) {
            // Check stop loss
            if (position.stopLossPrice !== undefined) {
                if (position.action === 'Buy' && candle.low <= position.stopLossPrice) {
                    // Stop loss hit for long position
                    closePosition(position, position.stopLossPrice, candle.timestamp, 'Stop Loss');
                    continue;
                } else if (position.action === 'Sell' && candle.high >= position.stopLossPrice) {
                    // Stop loss hit for short position
                    closePosition(position, position.stopLossPrice, candle.timestamp, 'Stop Loss');
                    continue;
                }
            }

            // Check take profit
            if (position.takeProfitPrice !== undefined) {
                if (position.action === 'Buy' && candle.high >= position.takeProfitPrice) {
                    // Take profit hit for long position
                    closePosition(position, position.takeProfitPrice, candle.timestamp, 'Take Profit');
                    continue;
                } else if (position.action === 'Sell' && candle.low <= position.takeProfitPrice) {
                    // Take profit hit for short position
                    closePosition(position, position.takeProfitPrice, candle.timestamp, 'Take Profit');
                    continue;
                }
            }

            // Update trailing stop if applicable
            if (position.trailFactor !== undefined) {
                if (position.action === 'Buy') {
                    // Calculate new stop loss for long position
                    const trailAmount = (candle.close - position.entryPrice) * position.trailFactor;
                    const newStopLoss = position.entryPrice + trailAmount;

                    // Only move stop loss up
                    if (newStopLoss > position.stopLossPrice) {
                        position.stopLossPrice = newStopLoss;
                        logger.logSystem(`Updated trailing stop for ${position.symbol} to ${newStopLoss}`, 'info');
                    }
                } else {
                    // Calculate new stop loss for short position
                    const trailAmount = (position.entryPrice - candle.close) * position.trailFactor;
                    const newStopLoss = position.entryPrice - trailAmount;

                    // Only move stop loss down
                    if (newStopLoss < position.stopLossPrice) {
                        position.stopLossPrice = newStopLoss;
                        logger.logSystem(`Updated trailing stop for ${position.symbol} to ${newStopLoss}`, 'info');
                    }
                }
            }
        }
    } catch (error) {
        logger.logSystem(`Error updating positions for ${symbol}: ${error.message}`, 'error');
    }
}

/**
 * Close a position
 * @param {Object} position - Position to close
 * @param {number} exitPrice - Exit price
 * @param {number} timestamp - Exit timestamp
 * @param {string} reason - Reason for closing
 */
function closePosition(position, exitPrice, timestamp, reason) {
    try {
        // Calculate P&L
        let pnl = 0;
        if (position.action === 'Buy') {
            pnl = (exitPrice - position.entryPrice) * position.quantity;
        } else {
            pnl = (position.entryPrice - exitPrice) * position.quantity;
        }

        // Subtract commission
        pnl -= simulationConfig.commission * position.quantity;

        // Update account balance
        simulationConfig.accountBalance += pnl;

        // Add closed position to history
        closedPositions.push({
            ...position,
            exitPrice,
            exitTimestamp: timestamp,
            exitReason: reason,
            pnl
        });

        logger.logSystem(`Closed position for ${position.symbol} (${reason}), P&L: ${pnl}`, 'info');

        // Remove position
        delete activePositions[position.id];
    } catch (error) {
        logger.logSystem(`Error closing position ${position.id}: ${error.message}`, 'error');
    }
}

/**
 * Place a market order
 * @param {Object} orderParams - Order parameters
 * @returns {Object} - Order result
 */
function placeMarketOrder(orderParams) {
    try {
        // Validate order parameters
        if (!orderParams.symbol) {
            throw new Error('Symbol is required');
        }

        if (!orderParams.action) {
            throw new Error('Action is required');
        }

        if (!orderParams.quantity) {
            throw new Error('Quantity is required');
        }

        // Check if we have market data for this symbol
        if (!marketData[orderParams.symbol]) {
            throw new Error(`No market data for symbol: ${orderParams.symbol}`);
        }

        // Check if we have enough candles
        if (marketData[orderParams.symbol].candles.length === 0) {
            throw new Error(`No candles for symbol: ${orderParams.symbol}`);
        }

        // Check position size limits
        if (orderParams.quantity > simulationConfig.maxPositionSize) {
            orderParams.quantity = simulationConfig.maxPositionSize;
            logger.logSystem(`Position size limited to ${simulationConfig.maxPositionSize}`, 'warning');
        }

        // Generate order ID
        const orderId = `${orderParams.symbol}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // Create order
        const order = {
            id: orderId,
            symbol: orderParams.symbol,
            action: orderParams.action,
            quantity: orderParams.quantity,
            orderType: 'Market',
            status: 'Pending',
            timestamp: Date.now(),
            latencyBars: 0,
            stopLossPrice: orderParams.stopLossPrice,
            takeProfitPrice: orderParams.takeProfitPrice,
            trailFactor: orderParams.trailFactor
        };

        // Add to active orders
        activeOrders[orderId] = order;

        logger.logSystem(`Placed market order for ${orderParams.symbol}: ${JSON.stringify(order)}`, 'info');

        return {
            success: true,
            orderId,
            order
        };
    } catch (error) {
        logger.logSystem(`Error placing market order: ${error.message}`, 'error');

        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get the status of the simulation
 * @returns {Object} - Status object
 */
function getStatus() {
    return {
        isRunning,
        accountBalance: simulationConfig.accountBalance,
        activePositions: Object.keys(activePositions).length,
        activeOrders: Object.keys(activeOrders).length,
        filledOrders: filledOrders.length,
        closedPositions: closedPositions.length,
        config: simulationConfig
    };
}

/**
 * Get active positions
 * @returns {Object} - Active positions
 */
function getPositions() {
    return activePositions;
}

/**
 * Get active orders
 * @returns {Object} - Active orders
 */
function getOrders() {
    return activeOrders;
}

/**
 * Get closed positions
 * @returns {Array} - Closed positions
 */
function getClosedPositions() {
    return closedPositions;
}

/**
 * Get filled orders
 * @returns {Array} - Filled orders
 */
function getFilledOrders() {
    return filledOrders;
}

/**
 * Get market data
 * @param {string} symbol - Symbol
 * @returns {Object} - Market data
 */
function getMarketData(symbol) {
    return marketData[symbol];
}

/**
 * Shutdown simulation mode
 * @returns {Promise<boolean>} - True if shutdown was successful
 */
async function shutdown() {
    try {
        logger.logSystem('Shutting down simulation mode...', 'info');

        // Reset state
        isRunning = false;

        // Destroy caches
        indicatorCache.destroy();

        logger.logSystem('Simulation mode shutdown complete', 'info');
        console.log('Simulation mode shutdown complete');

        return true;
    } catch (error) {
        logger.logSystem(`Shutdown failed: ${error.message}`, 'error');
        console.error(`Shutdown failed: ${error.message}`);
        return false;
    }
}

// Export functions
module.exports = {
    initialize,
    processCandle,
    placeMarketOrder,
    getStatus,
    getPositions,
    getOrders,
    getClosedPositions,
    getFilledOrders,
    getMarketData,
    shutdown
};
