// config.js - MGC Optimized Baseline (SL=8.0, TP=7.0, Tr=0.02, Latency=0, Slip=0.2)

module.exports = {
    // --- General Settings ---
    inputFile: 'C:/backtest-bot/input/MGC2020_2025.csv',
    initialBalance: 10000,

    // --- Instrument Specifics (MGC Specs) ---
    pointValue: 10.00,      // MGC point value
    tickSize: 0.10,         // MGC tick size
    pricePrecision: 2,      // MGC price precision

    // --- Costs & Slippage ---
    commissionPerContract: 0.4, // Commission estimate
    slippagePoints: 0.2,       // Slippage cost estimate

    // --- Indicator Periods ---
    atrPeriod: 14, rsiPeriod: 14, rsiMaPeriod: 8, sma200Period: 0, wma50Period: 0,

    // --- Strategy Parameters ---
    fixedTpPoints: 40.0,       // Minimum TP in points ($400) - Likely irrelevant with tight trail
    useWmaFilter: false, useTwoBarColorExit: false,
    minAtrEntry: 0, minRsiMaSeparation: 0, // Filters Disabled

    // --- RSI Bands ---
    rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

    // --- Run Mode: SINGLE RUN ---
    isAdaptiveRun: false,
    costGrid: null, riskPercentGrid: null, fixedContractsGrid: null, fixedTpPointsGrid: null,

    // *** Optimized MGC parameters (Single values) ***
    slFactors: 8.0,     // Optimized SL for MGC
    tpFactors: 7.0,     // Optimized TP for MGC
    trailFactors: 0.02, // Optimized Trail for MGC
    // ***********************************************
    riskPercent: 0,
    fixedContracts: 10,  // Number of contracts
    maxContracts: 30,

    // --- Latency Simulation ---
    latencyDelayBars: 0, // NO Latency

    // --- Time Filter Settings ---
    timeFilterEnabled: false,
    timeFilter: { start: 8, end: 15 },
    cstOffset: -5,
};
