#!/usr/bin/env python3
"""
Generate synthetic data for backtesting.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Symbol configurations
SYMBOL_CONFIG = {
    'MNQ': {
        'base_price': 17500,
        'volatility': 50,
        'point_value': 0.5,
        'min_tick': 0.25,
        'trades_per_day': 35
    },
    'MES': {
        'base_price': 5200,
        'volatility': 10,
        'point_value': 5.0,
        'min_tick': 0.25,
        'trades_per_day': 30
    },
    'MGC': {
        'base_price': 2500,
        'volatility': 20,
        'point_value': 10.0,
        'min_tick': 0.1,
        'trades_per_day': 25
    },
    'M2K': {
        'base_price': 2000,
        'volatility': 15,
        'point_value': 5.0,
        'min_tick': 0.1,
        'trades_per_day': 20
    }
}

def generate_price_data(symbol, start_date, days):
    """
    Generate synthetic price data.

    Args:
        symbol (str): Symbol
        start_date (datetime): Start date
        days (int): Number of days

    Returns:
        pd.DataFrame: DataFrame with price data
    """
    config = SYMBOL_CONFIG[symbol]

    # Calculate number of minutes
    minutes_per_day = 24 * 60
    total_minutes = days * minutes_per_day

    # Generate timestamps
    timestamps = [start_date + timedelta(minutes=i) for i in range(total_minutes)]

    # Generate price data
    base_price = config['base_price']
    volatility = config['volatility'] / 1000  # Convert to percentage
    min_tick = config['min_tick']

    # Generate random walk
    np.random.seed(42)  # For reproducibility

    # Use a simpler approach to generate prices
    prices = np.zeros(total_minutes)
    prices[0] = base_price

    for i in range(1, total_minutes):
        # Random price change with some mean reversion
        price_change = np.random.normal(0, volatility * base_price)
        mean_reversion = (base_price - prices[i-1]) * 0.001
        prices[i] = prices[i-1] + price_change + mean_reversion

        # Ensure price doesn't go negative or too high
        prices[i] = max(prices[i], min_tick)
        prices[i] = min(prices[i], base_price * 2)

        # Round to min tick
        prices[i] = round(prices[i] / min_tick) * min_tick

    # Generate OHLC data
    data = []
    for i in range(total_minutes):
        # Calculate open, high, low, close
        open_price = prices[i]

        # Random high and low around the open price
        high_price = open_price * (1 + np.random.uniform(0, volatility))
        low_price = open_price * (1 - np.random.uniform(0, volatility))

        # Close price is a random value between high and low
        close_price = np.random.uniform(low_price, high_price)

        # Ensure high >= open >= low and high >= close >= low
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)

        # Round to min tick
        open_price = round(open_price / min_tick) * min_tick
        high_price = round(high_price / min_tick) * min_tick
        low_price = round(low_price / min_tick) * min_tick
        close_price = round(close_price / min_tick) * min_tick

        # Generate volume
        volume = int(np.random.exponential(1000))

        # Add to data
        data.append({
            'timestamp': timestamps[i].strftime('%Y-%m-%dT%H:%M:%SZ'),
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })

    # Create DataFrame
    df = pd.DataFrame(data)

    return df

def save_to_csv(df, symbol, output_dir):
    """
    Save DataFrame to CSV.

    Args:
        df (pd.DataFrame): DataFrame
        symbol (str): Symbol
        output_dir (str): Output directory

    Returns:
        str: Path to CSV file
    """
    output_file = os.path.join(output_dir, f"{symbol}_1m.csv")
    df.to_csv(output_file, index=False)
    logger.info(f"Saved {len(df)} rows to {output_file}")
    return output_file

def main():
    """Main function."""
    # Configure paths
    output_dir = os.path.join('C:', 'backtest-bot', 'input')

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate data for each symbol
    symbols = ['MNQ', 'MES', 'MGC', 'M2K']
    start_date = datetime(2025, 1, 1)
    days = 30

    for symbol in symbols:
        logger.info(f"Generating data for {symbol}...")
        df = generate_price_data(symbol, start_date, days)
        save_to_csv(df, symbol, output_dir)

    logger.info("Data generation complete!")

if __name__ == "__main__":
    main()
