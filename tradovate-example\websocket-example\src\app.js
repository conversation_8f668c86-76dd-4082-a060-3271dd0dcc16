// app.js
// WebSocket example for Tradovate API

import { URLs } from '../tutorialsURLs.js'
import { credentials } from '../tutorialsCredentials.js'
import { connect } from '../src/connect.js'
import { getAccessToken } from '../src/storage.js'
import { TradovateSocket } from './TradovateSocket.js'
import { renderETH } from './renderETH.js'
import { renderQuote } from './renderQuote.js'
import { renderDOM } from './renderDOM.js'
import { renderHistogram } from './renderHistogram.js'
import { renderPosition } from './renderPosition.js'

const { WS_DEMO_URL, MD_DEMO_WS_URL } = URLs

// Main function to run our application
const main = async () => {
    console.log('Starting Tradovate WebSocket example...')

    try {
        // First, connect to get an access token
        console.log('Connecting to Tradovate API to get access token...')
        await connect(credentials)

        // Create and connect TradovateSocket for main API
        console.log('Creating main TradovateSocket...')
        const tradovateSocket = new TradovateSocket({ debugLabel: 'Main API' })

        // Create TradovateSocket for market data
        console.log('Creating market data TradovateSocket...')
        const marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' })

        // Connect to the WebSocket and store it globally
        window.tradovateSocket = tradovateSocket
        window.marketDataSocket = marketDataSocket

        // Connect main socket and handle successful connection
        await tradovateSocket.connect(WS_DEMO_URL, (socket) => {
            console.log('Main WebSocket connected and authorized')

            // Update UI to show connected state
            updateConnectionStatus('connected')
            enableButtons()
        })

        // Set up event listeners for UI buttons
        setupEventListeners(tradovateSocket, marketDataSocket)

        console.log('WebSocket setup complete. Use window.tradovateSocket to access the TradovateSocket instance.')
    } catch (error) {
        console.error('Error setting up WebSocket:', error)
        updateConnectionStatus('disconnected')
    }
}

/**
 * Set up event listeners for UI buttons
 * @param {TradovateSocket} socket - Main TradovateSocket instance
 * @param {TradovateSocket} marketDataSocket - Market data TradovateSocket instance
 */
const setupEventListeners = (socket, marketDataSocket) => {
    // HTML elements - Main controls
    const $connectBtn = document.getElementById('connect-btn')
    const $disconnectBtn = document.getElementById('disconnect-btn')
    const $userAccountsBtn = document.getElementById('user-accounts-btn')
    const $userSyncBtn = document.getElementById('user-sync-btn')
    const $ethDetailsBtn = document.getElementById('eth-details-btn')
    const $statusInd = document.getElementById('status')

    // HTML elements - Quote data
    const $outlet = document.getElementById('outlet')
    const $watchBtn = document.getElementById('watch-btn')
    const $unwatchBtn = document.getElementById('unwatch-btn')
    const $symbolInput = document.getElementById('symbol-input')

    // HTML elements - DOM data
    const $domOutlet = document.getElementById('dom-outlet')
    const $watchDomBtn = document.getElementById('watch-dom-btn')
    const $unwatchDomBtn = document.getElementById('unwatch-dom-btn')
    const $domSymbolInput = document.getElementById('dom-symbol-input')

    // HTML elements - Histogram data
    const $histogramOutlet = document.getElementById('histogram-outlet')
    const $watchHistogramBtn = document.getElementById('watch-histogram-btn')
    const $unwatchHistogramBtn = document.getElementById('unwatch-histogram-btn')
    const $histogramSymbolInput = document.getElementById('histogram-symbol-input')

    // HTML elements - Chart data
    const $chartOutlet = document.getElementById('chart-outlet')
    const $getChartBtn = document.getElementById('get-chart-btn')
    const $chartSymbolInput = document.getElementById('chart-symbol-input')
    const $chartType = document.getElementById('chart-type')
    const $chartElements = document.getElementById('chart-elements')
    const $chartElementSize = document.getElementById('chart-element-size')

    // HTML elements - P&L data
    const $plOutlet = document.getElementById('pl-outlet')
    const $posList = document.getElementById('position-list')
    const $openPL = document.getElementById('open-pl')
    const $buyBtn = document.getElementById('buy-btn')
    const $sellBtn = document.getElementById('sell-btn')
    const $plSymbolInput = document.getElementById('pl-symbol-input')
    const $plQtyInput = document.getElementById('pl-qty-input')

    // Variables for market data subscriptions
    let unsubscribeQuote = null
    let unsubscribeDOM = null
    let unsubscribeHistogram = null
    let unsubscribeChart = null
    let unsubscribeUserSync = null
    let lastQuoteSymbol = null
    let lastDOMSymbol = null
    let lastHistogramSymbol = null
    let lastChartSymbol = null

    // Variables for chart data
    let allBars = []
    let stockChart = null

    // Variables for P&L calculation
    let pls = []
    let availableAccounts = []

    // Connect button
    if ($connectBtn) {
        $connectBtn.addEventListener('click', async () => {
            try {
                updateConnectionStatus('connecting')
                await socket.connect()

                // Also connect the market data socket
                console.log('Connecting to market data WebSocket...')
                const { token } = getAccessToken()
                await marketDataSocket.connect(MD_DEMO_WS_URL, token)

                // Update connection status indicator
                updateMarketDataStatus(marketDataSocket)

                // Add event listener to update status indicator
                marketDataSocket.ws.addEventListener('message', () => {
                    updateMarketDataStatus(marketDataSocket)
                })

                updateConnectionStatus('connected')
                enableButtons()
            } catch (error) {
                console.error('Error connecting to WebSocket:', error)
                updateConnectionStatus('disconnected')
            }
        })
    }

    // Disconnect button
    if ($disconnectBtn) {
        $disconnectBtn.addEventListener('click', () => {
            // Disconnect both sockets
            socket.disconnect()
            marketDataSocket.disconnect()

            // Reset status indicators
            updateConnectionStatus('disconnected')
            $statusInd.style.backgroundColor = 'red'

            // Clear outlets
            $outlet.innerHTML = ''
            $domOutlet.innerHTML = ''
            $histogramOutlet.innerHTML = ''

            // Reset subscriptions
            unsubscribeQuote = null
            unsubscribeDOM = null
            unsubscribeHistogram = null
            unsubscribeChart = null
            lastQuoteSymbol = null
            lastDOMSymbol = null
            lastHistogramSymbol = null
            lastChartSymbol = null
            allBars = []

            disableButtons()
        })
    }

    // Helper function to get available accounts
    const getAvailableAccounts = async () => {
        try {
            if (availableAccounts.length === 0) {
                const response = await socket.send({ url: 'user/accounts' })
                availableAccounts = response
                console.log('Available accounts:', availableAccounts)
            }
            return availableAccounts
        } catch (error) {
            console.error('Error fetching user accounts:', error)
            return []
        }
    }

    // User accounts button
    if ($userAccountsBtn) {
        $userAccountsBtn.addEventListener('click', async () => {
            try {
                const response = await socket.send({ url: 'user/accounts' })
                availableAccounts = response
                console.log('User accounts:', response)
            } catch (error) {
                console.error('Error fetching user accounts:', error)
            }
        })
    }

    // User sync button
    if ($userSyncBtn) {
        $userSyncBtn.addEventListener('click', async () => {
            try {
                // Sync user data and calculate P&L
                await syncUserData()
            } catch (error) {
                console.error('Error syncing user data:', error)
            }
        })
    }

    // ETH details button
    if ($ethDetailsBtn) {
        $ethDetailsBtn.addEventListener('click', async () => {
            try {
                console.log('Fetching ETH product details...')

                // Send request for ETH product details
                const response = await socket.send({
                    url: 'product/find',
                    query: 'name=ETH'
                })

                console.log('ETH product details:', response)

                // Create HTML element to display the ETH details
                const div = document.createElement('div')
                div.innerHTML = renderETH(response)

                // Add or replace the content in the outlet
                if ($outlet.firstElementChild) {
                    $outlet.firstElementChild.replaceWith(div)
                } else {
                    $outlet.appendChild(div)
                }
            } catch (error) {
                console.error('Error fetching ETH details:', error)

                // Display error message in the outlet
                const errorDiv = document.createElement('div')
                errorDiv.innerHTML = `
                    <div class="error-message">
                        <h3>Error Fetching ETH Details</h3>
                        <p>${error.message}</p>
                    </div>
                `

                if ($outlet.firstElementChild) {
                    $outlet.firstElementChild.replaceWith(errorDiv)
                } else {
                    $outlet.appendChild(errorDiv)
                }
            }
        })
    }

    // Watch button (subscribe to market data)
    if ($watchBtn) {
        $watchBtn.addEventListener('click', async () => {
            try {
                // Get symbol from input
                const symbol = $symbolInput.value.trim()

                if (!symbol) {
                    console.error('Please enter a symbol')
                    return
                }

                // Unsubscribe from previous subscription if exists
                if (unsubscribeQuote) {
                    unsubscribeQuote()
                    unsubscribeQuote = null
                }

                console.log(`Subscribing to quotes for ${symbol}...`)

                // Store the current symbol
                lastQuoteSymbol = symbol

                // Subscribe to quotes for the symbol
                unsubscribeQuote = await marketDataSocket.subscribe({
                    url: 'md/subscribequote',
                    body: { symbol },
                    subscription: (data) => {
                        // Process quote data
                        if (data && data.quotes && data.quotes.length > 0) {
                            const quote = data.quotes[0]

                            // Create HTML element to display the quote
                            const div = document.createElement('div')
                            div.innerHTML = renderQuote(symbol, quote.entries || {})

                            // Add or replace the content in the outlet
                            if ($outlet.firstElementChild) {
                                $outlet.firstElementChild.replaceWith(div)
                            } else {
                                $outlet.appendChild(div)
                            }
                        }
                    }
                })

                // Enable unwatch button
                $unwatchBtn.disabled = false

                console.log(`Successfully subscribed to quotes for ${symbol}`)
            } catch (error) {
                console.error('Error subscribing to quotes:', error)

                // Display error message in the outlet
                const errorDiv = document.createElement('div')
                errorDiv.innerHTML = `
                    <div class="error-message">
                        <h3>Error Subscribing to Quotes</h3>
                        <p>${error.message}</p>
                    </div>
                `

                if ($outlet.firstElementChild) {
                    $outlet.firstElementChild.replaceWith(errorDiv)
                } else {
                    $outlet.appendChild(errorDiv)
                }
            }
        })
    }

    // Unwatch button (unsubscribe from market data)
    if ($unwatchBtn) {
        $unwatchBtn.addEventListener('click', () => {
            if (unsubscribeQuote) {
                console.log(`Unsubscribing from quotes for ${lastQuoteSymbol}...`)
                unsubscribeQuote()
                unsubscribeQuote = null
                lastQuoteSymbol = null

                // Clear outlet
                $outlet.innerHTML = ''

                // Disable unwatch button
                $unwatchBtn.disabled = true

                console.log('Successfully unsubscribed from quotes')
            }
        })
    }

    // Watch DOM button (subscribe to DOM data)
    if ($watchDomBtn) {
        $watchDomBtn.addEventListener('click', async () => {
            try {
                // Get symbol from input
                const symbol = $domSymbolInput.value.trim()

                if (!symbol) {
                    console.error('Please enter a symbol for DOM')
                    return
                }

                // Unsubscribe from previous subscription if exists
                if (unsubscribeDOM) {
                    unsubscribeDOM()
                    unsubscribeDOM = null
                }

                console.log(`Subscribing to DOM for ${symbol}...`)

                // Store the current symbol
                lastDOMSymbol = symbol

                // Subscribe to DOM for the symbol
                unsubscribeDOM = await marketDataSocket.subscribe({
                    url: 'md/subscribedom',
                    body: { symbol },
                    subscription: (data) => {
                        // Process DOM data
                        if (data && data.doms && data.doms.length > 0) {
                            const dom = data.doms[0]

                            // Create HTML element to display the DOM
                            const div = document.createElement('div')
                            div.innerHTML = renderDOM(symbol, dom)

                            // Add or replace the content in the outlet
                            if ($domOutlet.firstElementChild) {
                                $domOutlet.firstElementChild.replaceWith(div)
                            } else {
                                $domOutlet.appendChild(div)
                            }
                        }
                    }
                })

                // Enable unwatch button
                $unwatchDomBtn.disabled = false

                console.log(`Successfully subscribed to DOM for ${symbol}`)
            } catch (error) {
                console.error('Error subscribing to DOM:', error)

                // Display error message in the outlet
                const errorDiv = document.createElement('div')
                errorDiv.innerHTML = `
                    <div class="error-message">
                        <h3>Error Subscribing to DOM</h3>
                        <p>${error.message}</p>
                    </div>
                `

                if ($domOutlet.firstElementChild) {
                    $domOutlet.firstElementChild.replaceWith(errorDiv)
                } else {
                    $domOutlet.appendChild(errorDiv)
                }
            }
        })
    }

    // Unwatch DOM button (unsubscribe from DOM data)
    if ($unwatchDomBtn) {
        $unwatchDomBtn.addEventListener('click', () => {
            if (unsubscribeDOM) {
                console.log(`Unsubscribing from DOM for ${lastDOMSymbol}...`)
                unsubscribeDOM()
                unsubscribeDOM = null
                lastDOMSymbol = null

                // Clear outlet
                $domOutlet.innerHTML = ''

                // Disable unwatch button
                $unwatchDomBtn.disabled = true

                console.log('Successfully unsubscribed from DOM')
            }
        })
    }

    // Watch Histogram button (subscribe to histogram data)
    if ($watchHistogramBtn) {
        $watchHistogramBtn.addEventListener('click', async () => {
            try {
                // Get symbol from input
                const symbol = $histogramSymbolInput.value.trim()

                if (!symbol) {
                    console.error('Please enter a symbol for histogram')
                    return
                }

                // Unsubscribe from previous subscription if exists
                if (unsubscribeHistogram) {
                    unsubscribeHistogram()
                    unsubscribeHistogram = null
                }

                console.log(`Subscribing to histogram for ${symbol}...`)

                // Store the current symbol
                lastHistogramSymbol = symbol

                // Subscribe to histogram for the symbol
                unsubscribeHistogram = await marketDataSocket.subscribe({
                    url: 'md/subscribehistogram',
                    body: { symbol },
                    subscription: (data) => {
                        // Process histogram data
                        if (data && data.histograms && data.histograms.length > 0) {
                            const histogram = data.histograms[0]

                            // Create HTML element to display the histogram
                            const div = document.createElement('div')
                            div.innerHTML = renderHistogram(symbol, histogram)

                            // Add or replace the content in the outlet
                            if ($histogramOutlet.firstElementChild) {
                                $histogramOutlet.firstElementChild.replaceWith(div)
                            } else {
                                $histogramOutlet.appendChild(div)
                            }
                        }
                    }
                })

                // Enable unwatch button
                $unwatchHistogramBtn.disabled = false

                console.log(`Successfully subscribed to histogram for ${symbol}`)
            } catch (error) {
                console.error('Error subscribing to histogram:', error)

                // Display error message in the outlet
                const errorDiv = document.createElement('div')
                errorDiv.innerHTML = `
                    <div class="error-message">
                        <h3>Error Subscribing to Histogram</h3>
                        <p>${error.message}</p>
                    </div>
                `

                if ($histogramOutlet.firstElementChild) {
                    $histogramOutlet.firstElementChild.replaceWith(errorDiv)
                } else {
                    $histogramOutlet.appendChild(errorDiv)
                }
            }
        })
    }

    // Unwatch Histogram button (unsubscribe from histogram data)
    if ($unwatchHistogramBtn) {
        $unwatchHistogramBtn.addEventListener('click', () => {
            if (unsubscribeHistogram) {
                console.log(`Unsubscribing from histogram for ${lastHistogramSymbol}...`)
                unsubscribeHistogram()
                unsubscribeHistogram = null
                lastHistogramSymbol = null

                // Clear outlet
                $histogramOutlet.innerHTML = ''

                // Disable unwatch button
                $unwatchHistogramBtn.disabled = true

                console.log('Successfully unsubscribed from histogram')
            }
        })
    }

    // Buy and Sell buttons for P&L calculation
    if ($buyBtn && $sellBtn) {
        // Helper function to handle buy/sell actions
        const handleOrder = (action) => async () => {
            try {
                // Get accounts
                const accounts = await getAvailableAccounts()

                if (accounts.length === 0) {
                    console.error('No accounts available')
                    return
                }

                // Get first available account
                const { name, id } = accounts[0]

                // Get symbol and quantity
                const symbol = $plSymbolInput.value.trim()
                const qty = parseInt($plQtyInput.value, 10)

                if (!symbol || isNaN(qty) || qty <= 0) {
                    console.error('Please enter a valid symbol and quantity')
                    return
                }

                console.log(`Placing ${action} order for ${qty} ${symbol}...`)

                // Place order
                const response = await socket.send({
                    url: '/order/placeOrder',
                    body: {
                        action: action,
                        symbol: symbol,
                        orderQty: qty,
                        orderType: 'Market',
                        accountName: name,
                        accountId: id,
                        isAutomated: true // Required for algorithmic trading
                    }
                })

                console.log(`${action} order placed:`, response)

                // Sync user data to update positions
                await syncUserData()
            } catch (error) {
                console.error(`Error placing ${action} order:`, error)
            }
        }

        // Add event listeners
        $buyBtn.addEventListener('click', handleOrder('Buy'))
        $sellBtn.addEventListener('click', handleOrder('Sell'))
    }

    // Helper function to calculate total P&L
    const calculateTotalPL = () => {
        const totalPL = pls.reduce((total, { pl }) => total + pl, 0)
        $openPL.textContent = `$${totalPL.toFixed(2)}`
        $openPL.className = totalPL >= 0 ? '' : 'negative'
    }

    // Helper function to sync user data and calculate P&L
    const syncUserData = async () => {
        try {
            // Get user ID from access token
            const { userId } = getAccessToken()

            if (!userId) {
                console.error('User ID not found in access token')
                return
            }

            // Unsubscribe from previous subscription if exists
            if (unsubscribeUserSync) {
                unsubscribeUserSync()
                unsubscribeUserSync = null
            }

            console.log('Syncing user data...')

            // Subscribe to user sync
            unsubscribeUserSync = await socket.subscribe({
                url: 'user/syncrequest',
                body: { users: [userId] },
                subscription: (data) => {
                    // Process initial response
                    if (data.users) {
                        const { positions, contracts, products } = data

                        // Process positions
                        positions.forEach(async (pos) => {
                            // Skip positions with no net position
                            if (pos.netPos === 0 && pos.prevPos === 0) return

                            // Get contract name
                            const contract = contracts.find(c => c.id === pos.contractId)
                            if (!contract) return

                            const { name } = contract

                            // Get product for value per point
                            const product = products.find(p => p.name.startsWith(name.substring(0, 3)))
                            if (!product) return

                            const vpp = product.valuePerPoint

                            // Subscribe to quotes for real-time P&L calculation
                            const unsubscribePositionQuote = await marketDataSocket.subscribe({
                                url: 'md/subscribequote',
                                body: { symbol: name },
                                subscription: (quoteData) => {
                                    if (quoteData && quoteData.quotes && quoteData.quotes.length > 0) {
                                        const quote = quoteData.quotes[0]

                                        // Get buy price (net price or previous price)
                                        const buyPrice = pos.netPrice || pos.prevPrice

                                        // Get current price from quote
                                        const currentPrice = quote.entries && quote.entries.Trade ?
                                            quote.entries.Trade.price : 0

                                        // Calculate P&L
                                        // P&L = (Current Price - Buy Price) * Value Per Point * Contract Qty
                                        const pl = (currentPrice - buyPrice) * vpp * pos.netPos

                                        // Update position display
                                        const positionHTML = renderPosition(name, pl, pos.netPos)

                                        // Check if position already exists in the list
                                        const existingItem = document.querySelector(`#position-list li[data-name="${name}"]`)

                                        if (existingItem) {
                                            existingItem.outerHTML = positionHTML
                                        } else {
                                            const element = document.createElement('div')
                                            element.innerHTML = positionHTML
                                            $posList.appendChild(element.firstElementChild)
                                        }

                                        // Update P&L array
                                        const existingPL = pls.find(p => p.name === name)
                                        if (existingPL) {
                                            existingPL.pl = pl
                                        } else {
                                            pls.push({ name, pl })
                                        }

                                        // Calculate total P&L
                                        calculateTotalPL()
                                    }
                                }
                            })
                        })
                    }
                }
            })

            console.log('User data synced')
        } catch (error) {
            console.error('Error syncing user data:', error)
        }
    }

    /**
     * Helper functions for chart handling
     */

    // Create a regular chart (candlestick)
    const createRegularChart = (symbol, chartType, elementSize) => {
        return new CanvasJS.StockChart("chart-outlet", {
            theme: "light2",
            title: {
                text: `${symbol} Chart (${chartType}, ${elementSize})`
            },
            charts: [{
                axisX: {
                    crosshair: {
                        enabled: true,
                        snapToDataPoint: true
                    }
                },
                axisY: {
                    prefix: "$",
                    crosshair: {
                        enabled: true,
                        snapToDataPoint: true
                    }
                },
                data: [{
                    type: "candlestick",
                    risingColor: "#008000",
                    fallingColor: "#FF0000",
                    dataPoints: allBars
                }]
            }],
            navigator: {
                slider: {
                    minimum: allBars.length > 0 ? allBars[0].x : new Date('2020-01-01'),
                    maximum: allBars.length > 0 ? allBars[allBars.length - 1].x : new Date()
                }
            },
            rangeSelector: {
                enabled: true
            }
        });
    };

    // Create a tick chart (line)
    const createTickChart = (symbol, chartType) => {
        return new CanvasJS.StockChart("chart-outlet", {
            theme: "light2",
            title: {
                text: `${symbol} Tick Chart`
            },
            charts: [{
                axisX: {
                    crosshair: {
                        enabled: true,
                        snapToDataPoint: true
                    }
                },
                axisY: {
                    prefix: "$",
                    crosshair: {
                        enabled: true,
                        snapToDataPoint: true
                    }
                },
                data: [{
                    type: "line",
                    lineThickness: 2,
                    markerSize: 0,
                    dataPoints: allBars
                }]
            }],
            navigator: {
                slider: {
                    minimum: allBars.length > 0 ? allBars[0].x : new Date('2020-01-01'),
                    maximum: allBars.length > 0 ? allBars[allBars.length - 1].x : new Date()
                }
            },
            rangeSelector: {
                enabled: true
            }
        });
    };

    // Handle regular chart data (OHLC)
    const handleRegularChartData = (chart) => {
        if (chart.bars && chart.bars.length > 0) {
            // Add bars to the allBars array
            chart.bars.forEach(bar => {
                const { high, low, open, close, timestamp } = bar;
                allBars.push({
                    x: new Date(timestamp),
                    y: [open, high, low, close]
                });
            });

            // Sort bars by timestamp
            allBars.sort((a, b) => a.x - b.x);

            // Render the chart
            stockChart.render();

            console.log(`Processed ${chart.bars.length} bars, total: ${allBars.length}`);
        }
    };

    // Handle tick chart data
    const handleTickChartData = (chart) => {
        if (chart.tks && chart.tks.length > 0) {
            const { bt: baseTimestamp, bp: basePrice, ts: tickSize } = chart;

            // Process ticks
            chart.tks.forEach(tick => {
                const { t: relativeTime, p: relativePrice } = tick;

                // Calculate actual timestamp and price
                const timestamp = new Date(baseTimestamp + relativeTime);
                const price = (basePrice + relativePrice) * tickSize;

                // Add to data points
                allBars.push({
                    x: timestamp,
                    y: price
                });
            });

            // Sort ticks by timestamp
            allBars.sort((a, b) => a.x - b.x);

            // Render the chart
            stockChart.render();

            console.log(`Processed ${chart.tks.length} ticks, total: ${allBars.length}`);
        }
    };

    // Get Chart button (get chart data)
    if ($getChartBtn) {
        $getChartBtn.addEventListener('click', async () => {
            try {
                // Get chart parameters from inputs
                const symbol = $chartSymbolInput.value.trim();
                const chartType = $chartType.value;
                const elementSize = parseInt($chartElementSize.value);
                const numElements = parseInt($chartElements.value);

                if (!symbol) {
                    console.error('Please enter a symbol for chart');
                    return;
                }

                // Reset chart data
                allBars = [];

                // Unsubscribe from previous subscription if exists
                if (unsubscribeChart) {
                    unsubscribeChart();
                    unsubscribeChart = null;
                }

                console.log(`Getting chart data for ${symbol}...`);

                // Store the current symbol
                lastChartSymbol = symbol;

                // Clear chart outlet
                $chartOutlet.innerHTML = '';

                // Create appropriate chart based on type
                if (chartType === 'Tick') {
                    stockChart = createTickChart(symbol, chartType);
                } else {
                    stockChart = createRegularChart(symbol, chartType, elementSize);
                }

                // Subscribe to chart data
                unsubscribeChart = await marketDataSocket.subscribe({
                    url: 'md/getchart',
                    body: {
                        symbol: symbol,
                        chartDescription: {
                            underlyingType: chartType,
                            elementSize: chartType === 'Tick' || chartType === 'DailyBar' ? 1 : elementSize,
                            elementSizeUnit: 'UnderlyingUnits',
                            withHistogram: false
                        },
                        timeRange: {
                            asMuchAsElements: numElements,
                            asFarAsTimestamp: "2020-05-01T19:45:00.000Z"
                        }
                    },
                    subscription: (chart) => {
                        // Check for end of history
                        if (chart.eoh) {
                            console.log('End of chart history reached');
                            return;
                        }

                        // Process chart data based on type
                        if (chartType === 'Tick') {
                            handleTickChartData(chart);
                        } else {
                            handleRegularChartData(chart);
                        }
                    }
                });

                console.log(`Successfully requested chart data for ${symbol}`);
            } catch (error) {
                console.error('Error getting chart data:', error);

                // Display error message in the outlet
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = `
                    <div class="error-message">
                        <h3>Error Getting Chart Data</h3>
                        <p>${error.message}</p>
                    </div>
                `;

                if ($chartOutlet.firstElementChild) {
                    $chartOutlet.firstElementChild.replaceWith(errorDiv);
                } else {
                    $chartOutlet.appendChild(errorDiv);
                }
            }
        });
    }
}

/**
 * Update the market data connection status indicator
 * @param {TradovateSocket} socket - Market data socket
 */
const updateMarketDataStatus = (socket) => {
    const $statusInd = document.getElementById('status')

    if (!$statusInd || !socket || !socket.ws) return

    $statusInd.style.backgroundColor =
        socket.ws.readyState === 0 ? 'gold' :      // connecting
        socket.ws.readyState === 1 ? 'green' :     // connected
        socket.ws.readyState === 2 ? 'orange' :    // closing
        socket.ws.readyState === 3 ? 'red' :       // closed
        'silver'                                   // unknown
}

/**
 * Update the connection status in the UI
 * @param {string} status - Connection status (connected, disconnected, connecting)
 */
const updateConnectionStatus = (status) => {
    const connectionStatus = document.getElementById('connection-status')
    if (connectionStatus) {
        connectionStatus.className = `connection-status ${status}`
        connectionStatus.textContent = status.charAt(0).toUpperCase() + status.slice(1)
    }
}

/**
 * Enable UI buttons when connected
 */
const enableButtons = () => {
    document.getElementById('connect-btn').disabled = true
    document.getElementById('disconnect-btn').disabled = false
    document.getElementById('user-accounts-btn').disabled = false
    document.getElementById('user-sync-btn').disabled = false
    document.getElementById('eth-details-btn').disabled = false

    // Enable market data buttons
    document.getElementById('watch-btn').disabled = false
    document.getElementById('unwatch-btn').disabled = true
    document.getElementById('watch-dom-btn').disabled = false
    document.getElementById('unwatch-dom-btn').disabled = true
    document.getElementById('watch-histogram-btn').disabled = false
    document.getElementById('unwatch-histogram-btn').disabled = true
    document.getElementById('get-chart-btn').disabled = false

    // Enable P&L buttons
    document.getElementById('buy-btn').disabled = false
    document.getElementById('sell-btn').disabled = false

    // Sync user data to initialize P&L calculation
    setTimeout(() => {
        const userSyncBtn = document.getElementById('user-sync-btn')
        if (userSyncBtn) {
            userSyncBtn.click()
        }
    }, 1000)
}

/**
 * Disable UI buttons when disconnected
 */
const disableButtons = () => {
    document.getElementById('connect-btn').disabled = false
    document.getElementById('disconnect-btn').disabled = true
    document.getElementById('user-accounts-btn').disabled = true
    document.getElementById('user-sync-btn').disabled = true
    document.getElementById('eth-details-btn').disabled = true

    // Disable market data buttons
    document.getElementById('watch-btn').disabled = true
    document.getElementById('unwatch-btn').disabled = true
    document.getElementById('watch-dom-btn').disabled = true
    document.getElementById('unwatch-dom-btn').disabled = true
    document.getElementById('watch-histogram-btn').disabled = true
    document.getElementById('unwatch-histogram-btn').disabled = true
    document.getElementById('get-chart-btn').disabled = true

    // Disable P&L buttons
    document.getElementById('buy-btn').disabled = true
    document.getElementById('sell-btn').disabled = true
}

// Run the main function when the page loads
document.addEventListener('DOMContentLoaded', main)
