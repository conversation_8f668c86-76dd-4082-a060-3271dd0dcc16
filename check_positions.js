const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
    baseUrl: 'https://demo.tradovateapi.com/v1',
    accountId: '4690440'
};

// Read token from file
function readTokenFromFile() {
    try {
        const tokenFilePath = path.join(__dirname, 'tradovate_token.json');
        if (fs.existsSync(tokenFilePath)) {
            const tokenData = JSON.parse(fs.readFileSync(tokenFilePath, 'utf8'));
            if (tokenData && tokenData.accessToken && tokenData.expirationTime) {
                const expirationTime = new Date(tokenData.expirationTime);
                if (expirationTime > new Date()) {
                    console.log(`Using cached token, expires at: ${expirationTime.toLocaleString()}`);
                    return tokenData.accessToken;
                } else {
                    console.log('Cached token has expired, need to re-authenticate');
                    return null;
                }
            }
        }
    } catch (error) {
        console.error('Error reading token file:', error.message);
    }
    return null;
}

// Authenticate with Tradovate API
async function authenticate() {
    try {
        const cachedToken = readTokenFromFile();
        if (cachedToken) {
            return cachedToken;
        }

        console.log('Authenticating with Tradovate API...');
        const response = await axios.post(`${config.baseUrl}/auth/accesstokenrequest`, {
            name: 'bravesbeatmets',
            password: 'Braves12$',
            appId: 'Trading Bot',
            appVersion: '0.0.1',
            cid: '6186',
            sec: '69311dad-75a7-49c6-9958-00ab2c4f1ab6',
            deviceId: '25e3f568-2890-5442-1e40-b9e8983af7e7'
        });

        if (response.data && response.data.accessToken) {
            console.log('Authentication successful!');
            return response.data.accessToken;
        } else {
            throw new Error('Authentication failed: No access token in response');
        }
    } catch (error) {
        console.error('Authentication error:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
        throw error;
    }
}

// Get real account ID
async function getRealAccountId(token) {
    try {
        console.log('Getting account information...');
        const response = await axios.get(`${config.baseUrl}/account/list`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.data && response.data.length > 0) {
            console.log(`Found ${response.data.length} accounts`);

            // Find the demo account
            const demoAccount = response.data.find(account =>
                account.name && account.name.includes('DEMO')
            );

            if (demoAccount) {
                console.log(`Found demo account: ${demoAccount.name} (ID: ${demoAccount.id})`);
                return demoAccount.id;
            } else {
                console.log('No demo account found, using first account in list');
                return response.data[0].id;
            }
        } else {
            throw new Error('No accounts found');
        }
    } catch (error) {
        console.error('Error getting account information:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
        throw error;
    }
}

// Get positions
async function getPositions(token, accountId) {
    try {
        console.log(`Getting positions for account ID: ${accountId}...`);
        const response = await axios.get(`${config.baseUrl}/position/list?accountId=${accountId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log(`Found ${response.data.length} positions`);

        // Filter for active positions (netPos != 0)
        const activePositions = response.data.filter(position => position.netPos !== 0);
        console.log(`Active positions: ${activePositions.length}`);

        // Display active positions
        if (activePositions.length > 0) {
            console.log('\nActive Positions:');
            activePositions.forEach(position => {
                console.log(`Symbol: ${position.contractId}, Net Position: ${position.netPos}, P/L: ${(position.boughtValue - position.soldValue).toFixed(2)}`);
            });
        } else {
            console.log('No active positions found');
        }

        return activePositions;
    } catch (error) {
        console.error('Error getting positions:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
        throw error;
    }
}

// Get contract details
async function getContractDetails(token, contractId) {
    try {
        console.log(`Getting contract details for ID: ${contractId}...`);
        const response = await axios.get(`${config.baseUrl}/contract/find?id=${contractId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.data) {
            console.log(`Contract: ${response.data.name}`);
            return response.data;
        } else {
            throw new Error('No contract found');
        }
    } catch (error) {
        console.error('Error getting contract details:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
        return { name: `Unknown (ID: ${contractId})` };
    }
}

// Main function
async function main() {
    try {
        // Authenticate
        const token = await authenticate();

        // Get real account ID
        const accountId = await getRealAccountId(token);

        // Get positions
        const positions = await getPositions(token, accountId);

        // Get contract details for each position
        for (const position of positions) {
            const contract = await getContractDetails(token, position.contractId);
            console.log(`Contract details for position ${position.contractId}: ${contract.name}`);
        }

        console.log('\nCheck completed successfully');
    } catch (error) {
        console.error('Error in main function:', error.message);
    }
}

// Run the main function
main();
