#!/usr/bin/env python3
"""
Convert Databento DBN files to CSV format for use with the trading bot.
"""

import os
import sys
import glob
import logging
import pandas as pd
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import databento
try:
    import databento as db
    logger.info(f"Successfully imported databento version: {db.__version__}")
except ImportError:
    logger.error("Failed to import databento. Please install it with: pip install databento")
    sys.exit(1)

def convert_dbn_to_csv(dbn_file, output_dir=None):
    """
    Convert a Databento DBN file to CSV format.

    Args:
        dbn_file (str): Path to the DBN file
        output_dir (str, optional): Directory to save the CSV file. Defaults to same directory as DBN file.

    Returns:
        str: Path to the CSV file
    """
    try:
        logger.info(f"Converting {dbn_file} to CSV...")

        # Get the base filename without extension
        base_name = os.path.basename(dbn_file)
        base_name = base_name.replace('.dbn.zst', '')

        # Extract symbol from filename
        parts = base_name.split('.')
        if len(parts) >= 3:
            symbol = parts[2]
            # Handle spread symbols (e.g., MNQM5-MNQU5)
            if '-' in symbol:
                symbol = symbol.split('-')[0]  # Use the first symbol in the spread
        else:
            symbol = "unknown"

        # Clean up the symbol name
        # Remove month/year codes to get the base symbol (e.g., MNQM5 -> MNQ)
        base_symbol = ''.join([c for c in symbol if not (c.isdigit() or c in 'FGHJKMNQUVXZ')])

        # Determine output directory
        if output_dir is None:
            output_dir = os.path.dirname(dbn_file)

        # Create output filename
        csv_file = os.path.join(output_dir, f"{base_symbol}_{symbol}.csv")

        # Load the DBN file
        dataset = db.DBNStore.from_file(dbn_file)

        # Convert to pandas DataFrame
        df = dataset.to_df()

        # Check if DataFrame is empty
        if df.empty:
            logger.warning(f"No data found in {dbn_file}")
            return None

        # Process the DataFrame based on schema
        schema = dataset.schema
        logger.info(f"Schema: {schema}")

        if schema == 'ohlcv-1m':
            # For OHLCV data, we need to format it for our trading bot
            # Rename columns if needed
            if 'ts_event' in df.columns:
                df['timestamp'] = pd.to_datetime(df['ts_event'], unit='ns')
                df = df.drop(columns=['ts_event'])

            # Make sure we have all required columns
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"Column {col} not found in {dbn_file}")
                    if col != 'timestamp':  # We already handled timestamp
                        df[col] = 0  # Add dummy column

            # Select only the columns we need
            df = df[required_columns]

            # Sort by timestamp
            df = df.sort_values('timestamp')

            # Format timestamp as ISO string
            df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')

        elif schema == 'trades':
            # For trade data, we need different columns
            if 'ts_event' in df.columns:
                df['timestamp'] = pd.to_datetime(df['ts_event'], unit='ns')
                df = df.drop(columns=['ts_event'])

            # Make sure we have all required columns
            required_columns = ['timestamp', 'price', 'size', 'side']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"Column {col} not found in {dbn_file}")
                    if col != 'timestamp':  # We already handled timestamp
                        df[col] = 0  # Add dummy column

            # Select only the columns we need
            df = df[required_columns]

            # Sort by timestamp
            df = df.sort_values('timestamp')

            # Format timestamp as ISO string
            df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')

        # Save to CSV
        df.to_csv(csv_file, index=False)
        logger.info(f"Saved {len(df)} records to {csv_file}")

        return csv_file

    except Exception as e:
        logger.error(f"Error converting {dbn_file} to CSV: {str(e)}")
        return None

def process_all_dbn_files(input_dir, output_dir=None):
    """
    Process all DBN files in the input directory.

    Args:
        input_dir (str): Directory containing DBN files
        output_dir (str, optional): Directory to save CSV files. Defaults to input_dir.
    """
    # Find all DBN files
    dbn_files = glob.glob(os.path.join(input_dir, "*.dbn.zst"))
    logger.info(f"Found {len(dbn_files)} DBN files")

    # If no files found, try a different pattern
    if len(dbn_files) == 0:
        dbn_files = glob.glob(os.path.join(input_dir, "*dbn.zst"))
        logger.info(f"Found {len(dbn_files)} DBN files with alternative pattern")

    # Process each file
    converted_files = []
    for dbn_file in dbn_files:
        csv_file = convert_dbn_to_csv(dbn_file, output_dir)
        if csv_file:
            converted_files.append(csv_file)

    logger.info(f"Converted {len(converted_files)} files")

    # Create consolidated files for each symbol
    consolidate_files_by_symbol(output_dir or input_dir)

def consolidate_files_by_symbol(directory):
    """
    Consolidate all CSV files for each symbol into a single file.

    Args:
        directory (str): Directory containing CSV files
    """
    # Find all CSV files
    csv_files = glob.glob(os.path.join(directory, "*.csv"))

    # Group files by base symbol
    symbol_files = {}
    for csv_file in csv_files:
        base_name = os.path.basename(csv_file)
        if '_' in base_name:
            base_symbol = base_name.split('_')[0]
            if base_symbol not in symbol_files:
                symbol_files[base_symbol] = []
            symbol_files[base_symbol].append(csv_file)

    # Consolidate files for each symbol
    for base_symbol, files in symbol_files.items():
        if len(files) <= 1:
            continue

        logger.info(f"Consolidating {len(files)} files for {base_symbol}...")

        # Read all files
        dfs = []
        for file in files:
            try:
                df = pd.read_csv(file)
                dfs.append(df)
            except Exception as e:
                logger.error(f"Error reading {file}: {str(e)}")

        if not dfs:
            logger.warning(f"No valid data files for {base_symbol}")
            continue

        # Concatenate all DataFrames
        combined_df = pd.concat(dfs, ignore_index=True)

        # Remove duplicates
        combined_df = combined_df.drop_duplicates(subset=['timestamp'])

        # Sort by timestamp
        combined_df = combined_df.sort_values('timestamp')

        # Save to consolidated file
        output_file = os.path.join(directory, f"{base_symbol}_1m.csv")
        combined_df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(combined_df)} records to {output_file}")

def main():
    """Main function."""
    # Get input and output directories
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    output_dir = input_dir  # Save in the same directory

    # Process all DBN files
    process_all_dbn_files(input_dir, output_dir)

if __name__ == "__main__":
    main()
