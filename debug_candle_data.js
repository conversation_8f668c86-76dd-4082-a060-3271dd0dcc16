const fs = require('fs');
const csv = require('csv-parser');

console.log('🔍 DEBUG: Checking for corrupted candle data...');

const inputFile = 'C:/backtest-bot/input/MNQ_lastyear.csv';

// Check the file format
const headerLine = fs.readFileSync(inputFile, 'utf8').split('\n')[0];
const isDatabentoFormat = headerLine.includes('ts_event');
const separator = headerLine.includes(';') ? ';' : ',';

console.log(`CSV format: ${isDatabentoFormat ? 'Databento' : 'Standard'}, separator: '${separator}'`);

let totalCandles = 0;
let corruptedCandles = 0;
let normalCandles = 0;
let spreadCandles = 0;

fs.createReadStream(inputFile)
    .pipe(csv({
        separator: separator,
        mapHeaders: ({ header }) => header.trim()
    }))
    .on('data', d => {
        let open, high, low, close, timestampSeconds, symbol;

        if (isDatabentoFormat) {
            // Databento format
            open = parseFloat(d['open']);
            high = parseFloat(d['high']);
            low = parseFloat(d['low']);
            close = parseFloat(d['close']);
            symbol = d['symbol'];

            // Parse timestamp from ts_event field
            const timeString = d['ts_event'];
            if (timeString) {
                try {
                    const parsedDate = new Date(timeString);
                    if (!isNaN(parsedDate.getTime())) {
                        timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                    }
                } catch (e) {
                    // Skip invalid timestamps
                }
            }

            // Check for spread symbols
            if (symbol && symbol.includes('-')) {
                spreadCandles++;
                if (spreadCandles <= 5) {
                    console.log(`📊 SPREAD SYMBOL: ${symbol} - O=${open}, H=${high}, L=${low}, C=${close}`);
                }
                return; // Skip spread symbols
            }

            // Filter by symbol if needed (include all MNQ contracts)
            if (symbol && !symbol.startsWith('MNQ')) {
                return; // Skip non-MNQ symbols
            }
        } else {
            // Standard format
            open = +d['Open'];
            high = +d['High'];
            low = +d['Low'];
            close = +d['Close'];
            const timeString = d['Time'] || d['Date'] || d['Time left'];

            if (timeString) {
                let parsedDate;
                try { parsedDate = new Date(timeString); } catch (e) {}
                if (parsedDate && !isNaN(parsedDate.getTime())) {
                    timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                } else if (timeString && !isNaN(Number(timeString))) {
                    const tsNum = Number(timeString);
                    timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                }
            }
        }

        if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            // Skip invalid data
            return;
        }

        totalCandles++;
        
        // Check for corrupted prices (MNQ should be in 17000-22000 range typically)
        if (close < 10000 || open < 10000 || high < 10000 || low < 10000 || 
            close > 25000 || open > 25000 || high > 25000 || low > 25000) {
            corruptedCandles++;
            if (corruptedCandles <= 10) {
                console.log(`🚨 CORRUPTED CANDLE ${totalCandles}:`);
                console.log(`   Symbol: ${symbol || 'N/A'}`);
                console.log(`   OHLC: ${open}, ${high}, ${low}, ${close}`);
                console.log(`   Timestamp: ${new Date(timestampSeconds * 1000).toISOString()}`);
                console.log('');
            }
        } else {
            normalCandles++;
            if (normalCandles <= 3) {
                console.log(`✅ Normal candle ${totalCandles}: ${symbol || 'N/A'} - OHLC: ${open}, ${high}, ${low}, ${close}`);
            }
        }
        
        // Stop after checking 5000 candles
        if (totalCandles >= 5000) {
            console.log(`\n📊 SAMPLE ANALYSIS (first 5000 candles):`);
            console.log(`   Total candles processed: ${totalCandles}`);
            console.log(`   Normal candles: ${normalCandles}`);
            console.log(`   Corrupted candles: ${corruptedCandles}`);
            console.log(`   Spread candles found: ${spreadCandles}`);
            process.exit(0);
        }
    })
    .on('end', () => {
        console.log(`\n📊 FINAL ANALYSIS:`);
        console.log(`   Total candles: ${totalCandles}`);
        console.log(`   Normal candles: ${normalCandles}`);
        console.log(`   Corrupted candles: ${corruptedCandles}`);
        console.log(`   Spread candles: ${spreadCandles}`);
    })
    .on('error', (err) => {
        console.error(`Error reading CSV file:`, err);
    });
