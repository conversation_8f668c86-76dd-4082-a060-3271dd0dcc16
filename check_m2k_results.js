// check_m2k_results.js - <PERSON><PERSON><PERSON> to check for M2K grid test results

const fs = require('fs');
const path = require('path');

// Possible output directories to check
const possibleDirs = [
  './output/M2K_GridTest',
  './output/M2K_GridTest_2020_2025',
  './output',
  '.'
];

console.log('Searching for M2K grid test results...');

// Check each possible directory
possibleDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`\nChecking directory: ${dir}`);
    const files = fs.readdirSync(dir);
    
    if (files.length === 0) {
      console.log(`  Directory exists but is empty.`);
    } else {
      console.log(`  Files found (${files.length}):`);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        console.log(`  - ${file} (${stats.size} bytes, modified: ${stats.mtime})`);
      });
    }
  } else {
    console.log(`\nDirectory does not exist: ${dir}`);
  }
});

// Check if the grid test is still running
console.log('\nChecking for running node processes:');
try {
  const { execSync } = require('child_process');
  const processes = execSync('tasklist /fi "imagename eq node.exe"').toString();
  console.log(processes);
} catch (error) {
  console.log('Could not check running processes:', error.message);
}

console.log('\nIf you don\'t see any results files, the grid test might have completed without generating output.');
console.log('Check your backtest.js and config_m2k_grid.js files to ensure they\'re correctly configured to save results.');