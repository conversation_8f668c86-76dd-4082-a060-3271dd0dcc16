/**
 * Render a position item with P&L information
 * @param {string} symbol - Contract symbol
 * @param {number} pl - Profit/Loss value
 * @param {number} netPos - Net position (positive for long, negative for short)
 * @returns {string} HTML string for the position item
 */
export const renderPosition = (symbol, pl, netPos) => {
    const isPositive = pl >= 0;
    const formattedPL = pl.toFixed(2);
    const positionType = netPos > 0 ? 'LONG' : 'SHORT';
    const absPos = Math.abs(netPos);
    
    return `
        <li class="pl-item" data-name="${symbol}">
            <div class="pl-symbol">${symbol}</div>
            <div class="pl-details">
                <div class="pl-position">${positionType} ${absPos}</div>
                <div class="pl-value ${isPositive ? 'positive' : 'negative'}">
                    ${isPositive ? '+' : ''}$${formattedPL}
                </div>
            </div>
        </li>
    `;
};
