/**
 * performance-analysis.js
 * Analyze daily and weekly performance of each symbol
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');
const { mnqConfig, mesConfig, mgcConfig } = require('../multi_symbol_config');

// Function to analyze performance for a specific symbol
async function analyzePerformance(config) {
    console.log(`\n=== ANALYZING PERFORMANCE FOR ${config.symbol} ===\n`);
    
    // Run backtest
    const result = await runBacktest(config, true); // Pass true to get detailed trade data
    
    // Group trades by day and week
    const dailyPerformance = {};
    const weeklyPerformance = {};
    
    // Process each trade
    result.trades.forEach(trade => {
        // Parse dates
        const exitDate = new Date(trade.ExitTime);
        
        // Format date strings
        const dayKey = exitDate.toISOString().split('T')[0]; // YYYY-MM-DD
        const weekKey = getWeekNumber(exitDate);
        
        // Initialize if not exists
        if (!dailyPerformance[dayKey]) {
            dailyPerformance[dayKey] = {
                trades: 0,
                wins: 0,
                losses: 0,
                pnl: 0,
                winRate: 0
            };
        }
        
        if (!weeklyPerformance[weekKey]) {
            weeklyPerformance[weekKey] = {
                trades: 0,
                wins: 0,
                losses: 0,
                pnl: 0,
                winRate: 0
            };
        }
        
        // Update counts
        dailyPerformance[dayKey].trades++;
        weeklyPerformance[weekKey].trades++;
        
        // Update wins/losses and PnL
        const pnl = parseFloat(trade.PnL_Net);
        if (pnl > 0) {
            dailyPerformance[dayKey].wins++;
            weeklyPerformance[weekKey].wins++;
        } else {
            dailyPerformance[dayKey].losses++;
            weeklyPerformance[weekKey].losses++;
        }
        
        dailyPerformance[dayKey].pnl += pnl;
        weeklyPerformance[weekKey].pnl += pnl;
    });
    
    // Calculate win rates
    Object.keys(dailyPerformance).forEach(day => {
        const perf = dailyPerformance[day];
        perf.winRate = perf.trades > 0 ? (perf.wins / perf.trades * 100).toFixed(2) : 0;
    });
    
    Object.keys(weeklyPerformance).forEach(week => {
        const perf = weeklyPerformance[week];
        perf.winRate = perf.trades > 0 ? (perf.wins / perf.trades * 100).toFixed(2) : 0;
    });
    
    // Convert to arrays for sorting
    const dailyArray = Object.keys(dailyPerformance).map(day => ({
        date: day,
        ...dailyPerformance[day],
        pnl: parseFloat(dailyPerformance[day].pnl.toFixed(2))
    }));
    
    const weeklyArray = Object.keys(weeklyPerformance).map(week => ({
        week,
        ...weeklyPerformance[week],
        pnl: parseFloat(weeklyPerformance[week].pnl.toFixed(2))
    }));
    
    // Sort by date/week
    dailyArray.sort((a, b) => a.date.localeCompare(b.date));
    weeklyArray.sort((a, b) => a.week.localeCompare(b.week));
    
    // Calculate statistics
    const dailyStats = calculateStats(dailyArray.map(d => d.pnl));
    const weeklyStats = calculateStats(weeklyArray.map(w => w.pnl));
    
    // Count profitable days and weeks
    const profitableDays = dailyArray.filter(d => d.pnl > 0).length;
    const profitableWeeks = weeklyArray.filter(w => w.pnl > 0).length;
    
    const dailyWinRate = (profitableDays / dailyArray.length * 100).toFixed(2);
    const weeklyWinRate = (profitableWeeks / weeklyArray.length * 100).toFixed(2);
    
    // Display summary
    console.log(`\n=== DAILY PERFORMANCE SUMMARY FOR ${config.symbol} ===`);
    console.log(`Total Days: ${dailyArray.length}`);
    console.log(`Profitable Days: ${profitableDays} (${dailyWinRate}%)`);
    console.log(`Average Daily P&L: $${dailyStats.mean.toFixed(2)}`);
    console.log(`Median Daily P&L: $${dailyStats.median.toFixed(2)}`);
    console.log(`Best Day: $${dailyStats.max.toFixed(2)}`);
    console.log(`Worst Day: $${dailyStats.min.toFixed(2)}`);
    console.log(`Standard Deviation: $${dailyStats.stdDev.toFixed(2)}`);
    
    console.log(`\n=== WEEKLY PERFORMANCE SUMMARY FOR ${config.symbol} ===`);
    console.log(`Total Weeks: ${weeklyArray.length}`);
    console.log(`Profitable Weeks: ${profitableWeeks} (${weeklyWinRate}%)`);
    console.log(`Average Weekly P&L: $${weeklyStats.mean.toFixed(2)}`);
    console.log(`Median Weekly P&L: $${weeklyStats.median.toFixed(2)}`);
    console.log(`Best Week: $${weeklyStats.max.toFixed(2)}`);
    console.log(`Worst Week: $${weeklyStats.min.toFixed(2)}`);
    console.log(`Standard Deviation: $${weeklyStats.stdDev.toFixed(2)}`);
    
    // Save results to file
    const resultsDir = path.join(__dirname, 'performance_results');
    if (!fs.existsSync(resultsDir)) {
        fs.mkdirSync(resultsDir);
    }
    
    fs.writeFileSync(
        path.join(resultsDir, `${config.symbol}_daily_performance.json`),
        JSON.stringify(dailyArray, null, 2)
    );
    
    fs.writeFileSync(
        path.join(resultsDir, `${config.symbol}_weekly_performance.json`),
        JSON.stringify(weeklyArray, null, 2)
    );
    
    fs.writeFileSync(
        path.join(resultsDir, `${config.symbol}_performance_summary.json`),
        JSON.stringify({
            symbol: config.symbol,
            daily: {
                totalDays: dailyArray.length,
                profitableDays,
                dailyWinRate,
                stats: dailyStats
            },
            weekly: {
                totalWeeks: weeklyArray.length,
                profitableWeeks,
                weeklyWinRate,
                stats: weeklyStats
            }
        }, null, 2)
    );
    
    return {
        symbol: config.symbol,
        daily: {
            totalDays: dailyArray.length,
            profitableDays,
            dailyWinRate,
            stats: dailyStats
        },
        weekly: {
            totalWeeks: weeklyArray.length,
            profitableWeeks,
            weeklyWinRate,
            stats: weeklyStats
        }
    };
}

// Helper function to get week number (YYYY-WW format)
function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
    const week = Math.floor((d.getTime() - new Date(d.getFullYear(), 0, 4).getTime()) / 86400000 / 7) + 1;
    return `${d.getFullYear()}-W${week.toString().padStart(2, '0')}`;
}

// Helper function to calculate statistics
function calculateStats(values) {
    if (values.length === 0) return { mean: 0, median: 0, min: 0, max: 0, stdDev: 0 };
    
    // Sort values for median and min/max
    const sortedValues = [...values].sort((a, b) => a - b);
    
    // Calculate mean
    const sum = values.reduce((acc, val) => acc + val, 0);
    const mean = sum / values.length;
    
    // Calculate median
    const mid = Math.floor(sortedValues.length / 2);
    const median = sortedValues.length % 2 === 0
        ? (sortedValues[mid - 1] + sortedValues[mid]) / 2
        : sortedValues[mid];
    
    // Calculate min and max
    const min = sortedValues[0];
    const max = sortedValues[sortedValues.length - 1];
    
    // Calculate standard deviation
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    return { mean, median, min, max, stdDev };
}

// Main function to run analysis for all symbols
async function runAllAnalyses() {
    const results = [];
    
    // Analyze MNQ
    const mnqResult = await analyzePerformance(mnqConfig);
    results.push(mnqResult);
    
    // Analyze MES
    const mesResult = await analyzePerformance(mesConfig);
    results.push(mesResult);
    
    // Analyze MGC
    const mgcResult = await analyzePerformance(mgcConfig);
    results.push(mgcResult);
    
    // Display combined summary
    console.log("\n=== COMBINED PERFORMANCE SUMMARY ===\n");
    console.log("Symbol | Daily Win % | Avg Daily P&L | Weekly Win % | Avg Weekly P&L");
    console.log("-------|-------------|---------------|--------------|---------------");
    
    results.forEach(result => {
        console.log(`${result.symbol.padEnd(7)} | ${result.daily.dailyWinRate.padEnd(13)}% | $${result.daily.stats.mean.toFixed(2).padEnd(13)} | ${result.weekly.weeklyWinRate.padEnd(12)}% | $${result.weekly.stats.mean.toFixed(2)}`);
    });
    
    // Save combined results
    fs.writeFileSync(
        path.join(__dirname, 'performance_results', 'combined_performance_summary.json'),
        JSON.stringify(results, null, 2)
    );
}

// Run all analyses
runAllAnalyses().catch(console.error);
