/**
 * M2K Optimized Grid Test
 * Run a grid test on M2K to find optimal parameters using the optimized grid testing system
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');
const gridConfig = require('./grid_config');

// Get M2K configuration
const symbol = 'M2K';
const config = gridConfig.configs[symbol];
const dataPath = gridConfig.dataPaths[symbol];

// Set output directory
const outputDir = './output/m2k_optimized_grid_test';
config.outputDir = outputDir;

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Grid parameters to test
const gridParams = {
    // SL factors to test
    slFactors: [4.0, 5.0, 6.0, 7.0, 8.0],
    
    // TP factors to test
    tpFactors: [3.0, 4.0, 5.0, 6.0, 7.0],
    
    // Trail factors to test
    trailFactors: [0.03, 0.05, 0.08, 0.1, 0.15],
    
    // Fixed TP points to test
    fixedTpPoints: [0, 20, 40, 60]
};

// Focused grid parameters (fewer combinations for faster testing)
const focusedGridParams = {
    // SL factors to test
    slFactors: [5.0, 6.0, 7.0],
    
    // TP factors to test
    tpFactors: [4.0, 5.0, 6.0],
    
    // Trail factors to test
    trailFactors: [0.03, 0.05, 0.08],
    
    // Fixed TP points to test
    fixedTpPoints: [0, 40]
};

// Date range options
const dateRange = {
    useCustomRange: true,
    startDate: '2024-01-01',
    endDate: '2024-01-31'  // One month for testing
};

// Parse command line arguments
const args = process.argv.slice(2);
const useFocused = args.includes('--focused');
const selectedParams = useFocused ? focusedGridParams : gridParams;

/**
 * Run grid test for M2K
 */
async function runGridTest() {
    console.log(`\n===== RUNNING M2K OPTIMIZED GRID TEST =====`);
    console.log(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
    console.log(`Using ${useFocused ? 'focused' : 'full'} grid test mode`);
    console.log(`Grid parameters: ${JSON.stringify(selectedParams, null, 2)}`);
    
    // Create backtest instance with configuration
    const backtest = new GridBacktest(config);
    
    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { dateRange });
    
    // Run grid test
    console.log(`Running grid test with ${useFocused ? 'focused' : 'full'} parameters...`);
    const results = backtest.runGridTest(selectedParams);
    
    // Save results to file
    const resultsFilename = `grid_test_results_${useFocused ? 'focused' : 'full'}.json`;
    const resultsPath = path.join(outputDir, resultsFilename);
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    
    // Generate HTML report
    generateHtmlReport(results, outputDir);
    
    // Print top 5 results
    console.log(`\n===== TOP 5 PARAMETER COMBINATIONS =====`);
    for (let i = 0; i < Math.min(5, results.length); i++) {
        const result = results[i];
        console.log(`${i + 1}. SL=${result.params.slFactors}, TP=${result.params.tpFactors}, Trail=${result.params.trailFactors}, Fixed TP=${result.params.fixedTpPoints}`);
        console.log(`   PnL: $${result.stats.totalPnl.toFixed(2)}, Win Rate: ${result.stats.winRate.toFixed(2)}%, Win Day Rate: ${result.stats.winDayRate.toFixed(2)}%`);
    }
    
    console.log(`\nGrid test completed. Results saved to ${resultsPath}`);
}

/**
 * Generate HTML report for grid test results
 * @param {Array} results - Grid test results
 * @param {string} outputDir - Output directory
 */
function generateHtmlReport(results, outputDir) {
    const reportFilename = `grid_test_report_${useFocused ? 'focused' : 'full'}.html`;
    const reportPath = path.join(outputDir, reportFilename);
    
    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M2K Optimized Grid Test Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>M2K Optimized Grid Test Report</h1>
        <p>Date range: ${dateRange.startDate} to ${dateRange.endDate}</p>
        <p>Mode: ${useFocused ? 'Focused' : 'Full'} grid test</p>
        
        <h2>Top 20 Parameter Combinations</h2>
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>SL</th>
                    <th>TP</th>
                    <th>Trail</th>
                    <th>Fixed TP</th>
                    <th>Total PnL</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Win Day Rate</th>
                    <th>Profit Factor</th>
                    <th>Max DD</th>
                    <th>Trades</th>
                </tr>
            </thead>
            <tbody>
                ${results.slice(0, 20).map((result, index) => `
                <tr>
                    <td>${index + 1}</td>
                    <td>${result.params.slFactors}</td>
                    <td>${result.params.tpFactors}</td>
                    <td>${result.params.trailFactors}</td>
                    <td>${result.params.fixedTpPoints}</td>
                    <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
                    <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
                    <td>${result.stats.winRate.toFixed(2)}%</td>
                    <td>${result.stats.winDayRate.toFixed(2)}%</td>
                    <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
                    <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
                    <td>${result.stats.totalTrades}</td>
                </tr>
                `).join('')}
            </tbody>
        </table>
        
        <h2>Parameter Analysis</h2>
        <p>This section shows the average performance for each parameter value.</p>
        
        <h3>Stop Loss Factors</h3>
        <table>
            <thead>
                <tr>
                    <th>SL Factor</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'slFactors')}
            </tbody>
        </table>
        
        <h3>Take Profit Factors</h3>
        <table>
            <thead>
                <tr>
                    <th>TP Factor</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'tpFactors')}
            </tbody>
        </table>
        
        <h3>Trail Factors</h3>
        <table>
            <thead>
                <tr>
                    <th>Trail Factor</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'trailFactors')}
            </tbody>
        </table>
        
        <h3>Fixed TP Points</h3>
        <table>
            <thead>
                <tr>
                    <th>Fixed TP</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'fixedTpPoints')}
            </tbody>
        </table>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(reportPath, html);
    console.log(`HTML report generated at ${reportPath}`);
}

/**
 * Analyze parameter performance
 * @param {Array} results - Grid test results
 * @param {string} paramName - Parameter name
 * @returns {string} - HTML table rows
 */
function analyzeParameter(results, paramName) {
    // Group results by parameter value
    const paramGroups = {};
    
    for (const result of results) {
        const paramValue = result.params[paramName];
        
        if (!paramGroups[paramValue]) {
            paramGroups[paramValue] = {
                totalPnl: 0,
                totalWinRate: 0,
                totalWinDayRate: 0,
                count: 0
            };
        }
        
        paramGroups[paramValue].totalPnl += result.stats.totalPnl;
        paramGroups[paramValue].totalWinRate += result.stats.winRate;
        paramGroups[paramValue].totalWinDayRate += result.stats.winDayRate;
        paramGroups[paramValue].count++;
    }
    
    // Calculate averages and generate HTML
    let html = '';
    
    for (const paramValue in paramGroups) {
        const group = paramGroups[paramValue];
        const avgPnl = group.totalPnl / group.count;
        const avgWinRate = group.totalWinRate / group.count;
        const avgWinDayRate = group.totalWinDayRate / group.count;
        
        html += `
        <tr>
            <td>${paramValue}</td>
            <td class="${avgPnl >= 0 ? 'positive' : 'negative'}">$${avgPnl.toFixed(2)}</td>
            <td>${avgWinRate.toFixed(2)}%</td>
            <td>${avgWinDayRate.toFixed(2)}%</td>
            <td>${group.count}</td>
        </tr>`;
    }
    
    return html;
}

// Run grid test
runGridTest().catch(error => {
    console.error(`Error running grid test:`, error);
});
