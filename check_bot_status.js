/**
 * Enhanced Trading Bot Status Checker
 *
 * This script checks if the trading bot is running and displays its status.
 * It also provides a way to view the latest indicator values and trade decisions.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { exec } = require('child_process');
const bot = require('./demo_trading_optimized');
const logger = require('./data_logger');

// Configuration
const config = {
    logDir: './logs',
    indicatorLogFile: 'indicators.log',
    edgeLogFile: 'edge_criteria.log',
    systemLogFile: 'system.log',
    maxLines: 20 // Maximum number of lines to display
};

/**
 * Check if the trading bot process is running
 * @returns {Promise<boolean>} - True if the bot is running
 */
async function isBotRunning() {
    try {
        // First check if the bot has set its running status in the logger
        if (logger.isBotRunning && logger.isBotRunning()) {
            return true;
        }

        // Then try to get status from the bot module
        try {
            const status = bot.getStatus();
            if (status && status.isInitialized) {
                return true;
            }
        } catch (e) {
            console.log('Could not get status from bot module');
        }

        // If we couldn't get status from the module, check if the process is running
        console.log('Checking if demo_trading_optimized.js process is running...');

        return new Promise((resolve) => {
            // Simpler command that just checks if any node process is running
            // This is less precise but more reliable for demo purposes
            const command = 'tasklist | findstr "node.exe"';

            exec(command, (error, stdout, stderr) => {
                if (error) {
                    console.error(`Error checking bot status: ${error.message}`);
                    resolve(false);
                    return;
                }

                if (stderr) {
                    console.error(`Error checking bot status: ${stderr}`);
                    resolve(false);
                    return;
                }

                // If we find any node.exe process, assume the bot is running
                // This is not perfect but good enough for our demo
                const isRunning = stdout.toLowerCase().includes('node.exe');

                if (isRunning) {
                    console.log('Found node.exe process, assuming bot is running');
                } else {
                    console.log('No node.exe process found');
                }

                resolve(isRunning);
            });
        });
    } catch (error) {
        console.error(`Unexpected error checking bot status: ${error.message}`);
        return false;
    }
}

/**
 * Get the latest indicator values from the log file
 * @returns {Promise<Array>} - Latest indicator values
 */
async function getLatestIndicators() {
    return new Promise((resolve) => {
        const indicatorLogPath = path.join(config.logDir, config.indicatorLogFile);

        if (!fs.existsSync(indicatorLogPath)) {
            resolve([]);
            return;
        }

        // Read the last N lines of the indicator log file
        const command = process.platform === 'win32'
            ? `powershell -Command "Get-Content -Tail ${config.maxLines} '${indicatorLogPath}'"`
            : `tail -n ${config.maxLines} "${indicatorLogPath}"`;

        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error reading indicator log: ${error.message}`);
                resolve([]);
                return;
            }

            if (stderr) {
                console.error(`Error reading indicator log: ${stderr}`);
                resolve([]);
                return;
            }

            // Parse the log lines
            const lines = stdout.split('\n').filter(line => line.trim() !== '');
            resolve(lines);
        });
    });
}

/**
 * Get the latest edge criteria from the log file
 * @returns {Promise<Array>} - Latest edge criteria
 */
async function getLatestEdgeCriteria() {
    return new Promise((resolve) => {
        const edgeLogPath = path.join(config.logDir, config.edgeLogFile);

        if (!fs.existsSync(edgeLogPath)) {
            resolve([]);
            return;
        }

        // Read the last N lines of the edge criteria log file
        const command = process.platform === 'win32'
            ? `powershell -Command "Get-Content -Tail ${config.maxLines} '${edgeLogPath}'"`
            : `tail -n ${config.maxLines} "${edgeLogPath}"`;

        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error reading edge criteria log: ${error.message}`);
                resolve([]);
                return;
            }

            if (stderr) {
                console.error(`Error reading edge criteria log: ${stderr}`);
                resolve([]);
                return;
            }

            // Parse the log lines
            const lines = stdout.split('\n').filter(line => line.trim() !== '');
            resolve(lines);
        });
    });
}

/**
 * Get the latest system log entries
 * @returns {Promise<Array>} - Latest system log entries
 */
async function getLatestSystemLogs() {
    return new Promise((resolve) => {
        const systemLogPath = path.join(config.logDir, config.systemLogFile);

        if (!fs.existsSync(systemLogPath)) {
            resolve([]);
            return;
        }

        // Read the last N lines of the system log file
        const command = process.platform === 'win32'
            ? `powershell -Command "Get-Content -Tail ${config.maxLines} '${systemLogPath}'"`
            : `tail -n ${config.maxLines} "${systemLogPath}"`;

        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error reading system log: ${error.message}`);
                resolve([]);
                return;
            }

            if (stderr) {
                console.error(`Error reading system log: ${stderr}`);
                resolve([]);
                return;
            }

            // Parse the log lines
            const lines = stdout.split('\n').filter(line => line.trim() !== '');
            resolve(lines);
        });
    });
}

/**
 * Display the bot status
 */
async function displayBotStatus() {
    console.log('='.repeat(80));
    console.log('TRADING BOT STATUS CHECK');
    console.log('='.repeat(80));

    // Check if the bot is running
    const isRunning = await isBotRunning();
    console.log(`Bot Running: ${isRunning ? 'YES' : 'NO'}`);
    console.log('-'.repeat(80));

    // Try to get detailed status from the bot module
    try {
        const status = bot.getStatus();
        console.log('Bot Details:');
        console.log(`Is Connected: ${status.isConnected}`);
        console.log(`Is Initialized: ${status.isInitialized}`);
        console.log(`Active Positions: ${status.activePositions}`);
        console.log(`Active Orders: ${status.activeOrders}`);

        if (status.circuitBreakers) {
            console.log('\nCircuit Breakers:');
            console.log(`Is Tripped: ${status.circuitBreakers.isTripped}`);
            console.log(`Daily Loss: ${status.circuitBreakers.dailyLoss}`);
            console.log(`Last Reset: ${status.circuitBreakers.lastResetTime}`);
        }
    } catch (error) {
        console.log('Could not get detailed status from bot module.');
    }

    console.log('-'.repeat(80));

    if (!isRunning) {
        console.log('The trading bot is not detected as running.');
        console.log('To start the bot, run: node demo_trading_optimized.js');
        console.log('-'.repeat(80));
        console.log('Showing logs anyway in case the bot is actually running but not detected...');
    }

    // Get the latest indicator values
    const indicators = await getLatestIndicators();
    console.log('LATEST INDICATOR VALUES:');
    if (indicators.length === 0) {
        console.log('No indicator data available.');
    } else {
        indicators.slice(-10).forEach(line => console.log(line));
    }
    console.log('-'.repeat(80));

    // Get the latest edge criteria
    const edgeCriteria = await getLatestEdgeCriteria();
    console.log('LATEST EDGE CRITERIA (Which filters passed/failed):');
    if (edgeCriteria.length === 0) {
        console.log('No edge criteria data available.');
    } else {
        edgeCriteria.slice(-10).forEach(line => console.log(line));
    }
    console.log('-'.repeat(80));

    // Get the latest system logs
    const systemLogs = await getLatestSystemLogs();
    console.log('LATEST SYSTEM LOGS:');
    if (systemLogs.length === 0) {
        console.log('No system log data available.');
    } else {
        systemLogs.slice(-10).forEach(line => console.log(line));
    }
    console.log('='.repeat(80));

    // Check if the bot is not initialized
    try {
        const status = bot.getStatus();
        if (!status.isInitialized) {
            console.log('\nBot is not initialized. Would you like to initialize it? (y/n)');
            process.stdin.once('data', async (data) => {
                const input = data.toString().trim().toLowerCase();
                if (input === 'y' || input === 'yes') {
                    console.log('\nAttempting to initialize the bot...');
                    const result = await bot.initialize();
                    console.log(`Initialization result: ${result}`);

                    // Check status again after initialization
                    setTimeout(() => {
                        try {
                            const newStatus = bot.getStatus();
                            console.log('\nUpdated Bot Status:');
                            console.log('=================');
                            console.log(`Is Connected: ${newStatus.isConnected}`);
                            console.log(`Is Initialized: ${newStatus.isInitialized}`);
                            process.exit(0);
                        } catch (error) {
                            console.error('Error getting updated status:', error);
                            process.exit(1);
                        }
                    }, 5000);
                } else {
                    console.log('Bot initialization cancelled.');
                    process.exit(0);
                }
            });
        } else {
            // Exit if the bot is already initialized
            process.exit(0);
        }
    } catch (error) {
        // Exit if we can't get the status
        process.exit(0);
    }
}

// Run the status check
displayBotStatus().catch(error => {
    console.error(`Error checking bot status: ${error.message}`);
    process.exit(1);
});
