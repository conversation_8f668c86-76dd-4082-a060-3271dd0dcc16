/**
 * Simple Cache
 *
 * A lightweight cache for storing frequently accessed data
 */

const logger = require('./data_logger');

/**
 * Simple Cache Class
 */
class SimpleCache {
    /**
     * Constructor
     * @param {Object} options - Options
     */
    constructor(options = {}) {
        this.options = {
            maxSize: options.maxSize || 1000,
            ttl: options.ttl || 60000, // 1 minute
            debug: options.debug || false,
            onMiss: options.onMiss || null // Callback function to fetch data on cache miss
        };

        this.cache = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            evictions: 0,
            asyncFetches: 0
        };

        // Start cleanup interval
        this.cleanupInterval = setInterval(() => {
            this.cleanup();
        }, this.options.ttl / 2);

        // Log function
        this.log = (message, level = 'info') => {
            if (this.options.debug || level !== 'debug') {
                logger.logSystem(`[SimpleCache] ${message}`, level);
            }
        };

        this.log('Cache initialized');
    }

    /**
     * Set a value in the cache
     * @param {string} key - Cache key
     * @param {*} value - Value to cache
     * @param {number} ttl - Time to live in milliseconds (optional)
     * @returns {boolean} - True if set successfully
     */
    set(key, value, ttl = null) {
        // Check if we need to evict an item
        if (!this.cache.has(key) && this.cache.size >= this.options.maxSize) {
            this.evictOldest();
        }

        const expires = Date.now() + (ttl || this.options.ttl);

        this.cache.set(key, {
            value,
            expires
        });

        this.stats.sets++;
        this.log(`Set: ${key}`, 'debug');

        return true;
    }

    /**
     * Get a value from the cache
     * @param {string} key - Cache key
     * @returns {*} - Cached value or undefined if not found
     */
    get(key) {
        if (!this.cache.has(key)) {
            this.stats.misses++;
            this.log(`Miss: ${key}`, 'debug');
            return undefined;
        }

        const item = this.cache.get(key);

        // Check if expired
        if (item.expires < Date.now()) {
            this.cache.delete(key);
            this.stats.misses++;
            this.log(`Expired: ${key}`, 'debug');
            return undefined;
        }

        this.stats.hits++;
        this.log(`Hit: ${key}`, 'debug');

        return item.value;
    }

    /**
     * Check if a key exists in the cache
     * @param {string} key - Cache key
     * @returns {boolean} - True if key exists and is not expired
     */
    has(key) {
        if (!this.cache.has(key)) {
            return false;
        }

        const item = this.cache.get(key);

        // Check if expired
        if (item.expires < Date.now()) {
            this.cache.delete(key);
            return false;
        }

        return true;
    }

    /**
     * Delete a key from the cache
     * @param {string} key - Cache key
     * @returns {boolean} - True if key was deleted
     */
    delete(key) {
        return this.cache.delete(key);
    }

    /**
     * Clear the cache
     */
    clear() {
        this.cache.clear();
        this.log('Cache cleared');
    }

    /**
     * Cleanup expired items
     */
    cleanup() {
        const now = Date.now();
        let count = 0;

        for (const [key, item] of this.cache.entries()) {
            if (item.expires < now) {
                this.cache.delete(key);
                count++;
            }
        }

        if (count > 0) {
            this.log(`Cleaned up ${count} expired items`, 'debug');
        }
    }

    /**
     * Evict the oldest item from the cache
     */
    evictOldest() {
        if (this.cache.size === 0) {
            return;
        }

        let oldestKey = null;
        let oldestTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.expires < oldestTime) {
                oldestKey = key;
                oldestTime = item.expires;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.stats.evictions++;
            this.log(`Evicted: ${oldestKey}`, 'debug');
        }
    }

    /**
     * Get a value from the cache asynchronously, using the onMiss callback if provided
     * @param {string} key - Cache key
     * @returns {Promise<*>} - Promise resolving to cached value or undefined if not found and no onMiss callback
     */
    async getAsync(key) {
        // First try to get from cache
        const cachedValue = this.get(key);
        if (cachedValue !== undefined) {
            return cachedValue;
        }

        // If we have an onMiss callback, use it to fetch the data
        if (typeof this.options.onMiss === 'function') {
            try {
                this.log(`Async fetch: ${key}`, 'debug');
                this.stats.asyncFetches++;

                const value = await this.options.onMiss(key);

                // Cache the result if it's not null or undefined
                if (value !== null && value !== undefined) {
                    this.set(key, value);
                    this.log(`Async fetch success: ${key}`, 'debug');
                    return value;
                } else {
                    this.log(`Async fetch returned null/undefined: ${key}`, 'debug');
                    return undefined;
                }
            } catch (error) {
                this.log(`Async fetch error: ${key} - ${error.message}`, 'error');
                return undefined;
            }
        }

        return undefined;
    }

    /**
     * Get cache statistics
     * @returns {Object} - Cache statistics
     */
    getStats() {
        return {
            ...this.stats,
            size: this.cache.size,
            hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
        };
    }

    /**
     * Destroy the cache
     */
    destroy() {
        clearInterval(this.cleanupInterval);
        this.cache.clear();
        this.log('Cache destroyed');
    }
}

module.exports = SimpleCache;
