/**
 * optimized-config-test.js
 * Test the optimized configurations for MNQ and MGC
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');

// MNQ Optimized Configuration
const mnqOptimizedConfig = {
  // General Settings
  symbol: 'MNQ',
  contractMonth: 'M4',
  initialBalance: 10000,

  // Instrument Specifics
  pointValue: 2.00,
  tickSize: 0.25,
  pricePrecision: 2,

  // Costs & Slippage
  commissionPerContract: 0.40,
  slippagePoints: 0.75,

  // Indicator Periods
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 200,
  wma50Period: 50,

  // Strategy Parameters
  fixedTpPoints: 40,
  useWmaFilter: false,
  useSma200Filter: false,
  useRsiFilter: true,
  useTwoBarColorExit: false,
  minAtrEntry: 0,
  minRsiMaSeparation: 0,

  // RSI Bands
  rsiUpperBand: 60,
  rsiLowerBand: 40,
  rsiMiddleBand: 50,

  // Run Mode: OPTIMIZED CONFIGURATION
  isAdaptiveRun: true,

  // Optimal parameters from grid test
  slFactors: 4.5,
  tpFactors: 3.0,
  trailFactors: 0.11,

  // Position sizing
  fixedContracts: 10,
  maxContracts: 10,

  // Risk management
  dailyStopLoss: 500,
  dailyProfitTarget: 0,
  maxDrawdownPercent: 5,

  // Input file
  inputFile: 'C:/backtest-bot/input/MNQ_2020_2025.csv',

  // ATR thresholds
  atrThresholds: { low_medium: 4.7601, medium_high: 7.2605 },

  // Adaptive parameters
  adaptiveParams: {
    Low: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
    Medium: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 },
    High: { slFactor: 4.5, tpFactor: 3.0, trailFactor: 0.11 }
  }
};

// MGC Optimized Configuration
const mgcOptimizedConfig = {
  // General Settings
  symbol: 'MGC',
  contractMonth: 'M4',
  initialBalance: 10000,

  // Instrument Specifics
  pointValue: 10.00,
  tickSize: 0.10,
  pricePrecision: 1,

  // Costs & Slippage
  commissionPerContract: 0.40,
  slippagePoints: 0.10,

  // Indicator Periods
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 200,
  wma50Period: 50,

  // Strategy Parameters
  fixedTpPoints: 0,
  useWmaFilter: false,
  useSma200Filter: false,
  useRsiFilter: true,
  useTwoBarColorExit: false,
  minAtrEntry: 0,
  minRsiMaSeparation: 0,

  // RSI Bands
  rsiUpperBand: 60,
  rsiLowerBand: 40,
  rsiMiddleBand: 50,

  // Run Mode: OPTIMIZED CONFIGURATION
  isAdaptiveRun: true,

  // Optimal parameters from grid test
  slFactors: 8.0,
  tpFactors: 7.0,
  trailFactors: 0.02,

  // Position sizing
  fixedContracts: 10,
  maxContracts: 30,

  // Risk management
  dailyStopLoss: 500,
  dailyProfitTarget: 0,
  maxDrawdownPercent: 5,

  // Input file
  inputFile: 'C:/backtest-bot/input/MGC2020_2025.csv',

  // ATR thresholds
  atrThresholds: { low_medium: 1.5, medium_high: 3.0 },

  // Adaptive parameters
  adaptiveParams: {
    Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
    Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
    High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
  }
};

// Function to run test with a specific configuration
async function runConfigTest(config) {
    console.log(`\n=== RUNNING TEST FOR ${config.symbol} WITH OPTIMIZED CONFIGURATION ===\n`);

    console.log("Configuration:");
    console.log(`Symbol: ${config.symbol}`);
    console.log(`SL Factor: ${config.slFactors}`);
    console.log(`TP Factor: ${config.tpFactors}`);
    console.log(`Trail Factor: ${config.trailFactors}`);
    console.log(`Fixed TP Points: ${config.fixedTpPoints}`);
    console.log(`Commission: ${config.commissionPerContract}`);
    console.log(`Slippage: ${config.slippagePoints}`);
    console.log(`Fixed Contracts: ${config.fixedContracts}`);
    console.log(`WMA Filter: ${config.useWmaFilter}`);
    console.log(`SMA200 Filter: ${config.useSma200Filter}`);
    console.log(`RSI Filter: ${config.useRsiFilter}`);

    // Run backtest
    const result = await runBacktest(config);

    // Calculate win rate
    const winRate = (result.wins / result.totalTrades * 100).toFixed(2);

    // Calculate P&L
    const pnl = result.finalBalance - config.initialBalance;

    // Display results
    console.log(`\nResults:`);
    console.log(`Total trades: ${result.totalTrades}`);
    console.log(`Wins: ${result.wins} (${winRate}%)`);
    console.log(`Losses: ${result.losses} (${(100 - parseFloat(winRate)).toFixed(2)}%)`);
    console.log(`P&L: $${pnl.toFixed(2)}`);
    console.log(`Max drawdown: $${result.maxDrawdown.toFixed(2)}`);

    return {
        symbol: config.symbol,
        totalTrades: result.totalTrades,
        wins: result.wins,
        losses: result.losses,
        winRate: `${winRate}%`,
        pnl: pnl.toFixed(2),
        maxDrawdown: result.maxDrawdown.toFixed(2)
    };
}

// Main function to run all tests
async function runAllTests() {
    const results = [];

    // Run MNQ test
    const mnqResult = await runConfigTest(mnqOptimizedConfig);
    results.push(mnqResult);

    // Run MGC test
    const mgcResult = await runConfigTest(mgcOptimizedConfig);
    results.push(mgcResult);

    // Display summary table
    console.log("\n=== OPTIMIZED CONFIG TEST SUMMARY ===\n");
    console.table(results);

    // Save results to file
    const resultsFile = path.join(__dirname, 'optimized_config_results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    console.log(`\nResults saved to ${resultsFile}`);
}

// Run all tests
runAllTests().catch(console.error);
