/**
 * Fix for Trailing Stop Functionality
 *
 * This script adds enhanced logging and fixes for the trailing stop functionality
 * in the trading bot.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');

// Global variables
let activePositions = {};
let lastCandles = {};
let atrValues = {};

/**
 * Initialize the fix
 */
async function initialize() {
    try {
        console.log('Initializing trailing stop fix...');

        // Configure API with demo mode
        tradovateApi.setConfig({
            baseUrl: 'https://demo.tradovateapi.com/v1',
            wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
            mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
        });

        // Sync positions with API
        await syncPositionsWithAPI();

        console.log('Initialization complete.');
        return true;
    } catch (error) {
        console.error(`Error initializing: ${error.message}`);
        return false;
    }
}

/**
 * Sync positions with API
 */
async function syncPositionsWithAPI() {
    try {
        console.log('Syncing positions with API...');

        // Get account ID
        const accountId = ********; // DEMO4690440 account ID

        // Get positions from API
        const positions = await tradovateApi.makeApiRequest('GET', `position/list?accountId=${accountId}`);
        console.log(`Found ${positions.length} positions from API`);

        // Filter for active positions (netPos != 0)
        const activeApiPositions = positions.filter(position => position.netPos !== 0);
        console.log(`Active positions: ${activeApiPositions.length}`);

        // Display active positions
        if (activeApiPositions.length > 0) {
            console.log('\nActive Positions:');
            activeApiPositions.forEach(position => {
                console.log(`Symbol: ${position.contractId}, Net Position: ${position.netPos}, P/L: ${(position.boughtValue - position.soldValue).toFixed(2)}`);

                // Add to our tracking
                const positionId = `pos-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
                const action = position.netPos > 0 ? 'Buy' : 'Sell';

                activePositions[positionId] = {
                    id: positionId,
                    contractId: position.contractId,
                    action: action,
                    quantity: Math.abs(position.netPos),
                    entryPrice: position.netPrice,
                    stopLossPrice: action === 'Buy' ?
                        position.netPrice - 9999 : // Very far away for long (no initial stop loss)
                        position.netPrice + 9999,  // Very far away for short (no initial stop loss)
                    takeProfitPrice: action === 'Buy' ?
                        position.netPrice * 1.01 : // 1% above for long
                        position.netPrice * 0.99,  // 1% below for short
                    trailFactor: 1.0, // Default trail factor
                    timestamp: Date.now(),
                    trailHigh: action === 'Buy' ? position.netPrice : null,
                    trailLow: action === 'Sell' ? position.netPrice : null,
                    atr: 1.0 // Default ATR
                };
            });
        } else {
            console.log('No active positions found');
        }

        return activeApiPositions;
    } catch (error) {
        console.error(`Error syncing positions: ${error.message}`);
        return [];
    }
}

/**
 * Update trailing stops for all positions
 */
async function updateTrailingStops() {
    try {
        console.log('\nUpdating trailing stops for all positions...');

        // Check if we have any positions
        const positionCount = Object.keys(activePositions).length;
        if (positionCount === 0) {
            console.log('No positions to update');
            return true;
        }

        console.log(`Updating trailing stops for ${positionCount} positions...`);

        // Get latest market data for all symbols
        const symbols = [...new Set(Object.values(activePositions).map(p => p.symbol || 'MNQM5'))];
        const marketData = {};

        // Get latest market data for each symbol
        for (const symbol of symbols) {
            try {
                // Get the latest candle from the API
                const chartData = await tradovateApi.makeApiRequest('GET',
                    `md/charts?symbol=${symbol}&chartDescription=1m&elementSize=1`);

                if (chartData && chartData.length > 0 && chartData[0].elements && chartData[0].elements.length > 0) {
                    const latestElement = chartData[0].elements[chartData[0].elements.length - 1];

                    // Create candle object
                    const latestCandle = {
                        open: latestElement.open,
                        high: latestElement.high,
                        low: latestElement.low,
                        close: latestElement.close,
                        timestamp: latestElement.timestamp
                    };

                    marketData[symbol] = latestCandle;
                    lastCandles[symbol] = latestCandle;

                    // Calculate ATR if we have enough data
                    if (chartData[0].elements.length >= 14) {
                        const trValues = [];
                        const elements = chartData[0].elements.slice(-14); // Last 14 candles

                        // Calculate True Range values
                        for (let i = 1; i < elements.length; i++) {
                            const current = elements[i];
                            const previous = elements[i - 1];

                            const tr1 = current.high - current.low;
                            const tr2 = Math.abs(current.high - previous.close);
                            const tr3 = Math.abs(current.low - previous.close);

                            trValues.push(Math.max(tr1, tr2, tr3));
                        }

                        // Calculate ATR (simple average of TR values)
                        const atr = trValues.reduce((sum, val) => sum + val, 0) / trValues.length;
                        atrValues[symbol] = atr;

                        console.log(`Calculated ATR for ${symbol}: ${atr.toFixed(2)}`);
                    } else {
                        // Use default ATR values if not enough data
                        atrValues[symbol] = symbol.includes('MNQ') ? 10.0 :
                                           symbol.includes('MES') ? 5.0 :
                                           symbol.includes('MGC') ? 2.0 : 1.0;

                        console.log(`Using default ATR for ${symbol}: ${atrValues[symbol].toFixed(2)}`);
                    }
                } else {
                    console.log(`No chart data available for ${symbol}, using last known values`);

                    // Use last known values or defaults
                    if (lastCandles[symbol]) {
                        marketData[symbol] = lastCandles[symbol];
                    } else {
                        // Create default candle
                        const basePrice = symbol.includes('MNQ') ? 21500 :
                                         symbol.includes('MES') ? 5200 :
                                         symbol.includes('MGC') ? 2400 : 1000;

                        marketData[symbol] = {
                            open: basePrice,
                            high: basePrice + 5,
                            low: basePrice - 5,
                            close: basePrice,
                            timestamp: Date.now()
                        };

                        lastCandles[symbol] = marketData[symbol];
                    }

                    // Use default ATR values
                    if (!atrValues[symbol]) {
                        atrValues[symbol] = symbol.includes('MNQ') ? 10.0 :
                                           symbol.includes('MES') ? 5.0 :
                                           symbol.includes('MGC') ? 2.0 : 1.0;
                    }
                }
            } catch (error) {
                console.error(`Error getting market data for ${symbol}: ${error.message}`);

                // Use last known values or defaults if API call fails
                if (lastCandles[symbol]) {
                    marketData[symbol] = lastCandles[symbol];
                } else {
                    // Create default candle
                    const basePrice = symbol.includes('MNQ') ? 21500 :
                                     symbol.includes('MES') ? 5200 :
                                     symbol.includes('MGC') ? 2400 : 1000;

                    marketData[symbol] = {
                        open: basePrice,
                        high: basePrice + 5,
                        low: basePrice - 5,
                        close: basePrice,
                        timestamp: Date.now()
                    };

                    lastCandles[symbol] = marketData[symbol];
                }

                // Use default ATR values
                if (!atrValues[symbol]) {
                    atrValues[symbol] = symbol.includes('MNQ') ? 10.0 :
                                       symbol.includes('MES') ? 5.0 :
                                       symbol.includes('MGC') ? 2.0 : 1.0;
                }
            }
        }

        // Update each position
        for (const positionId in activePositions) {
            const position = activePositions[positionId];

            // Get symbol
            const symbol = position.symbol || 'MNQM5'; // Default to MNQM5 if no symbol

            // Get latest candle
            const latestCandle = marketData[symbol];

            if (!latestCandle) {
                console.log(`No market data available for ${symbol}, skipping position ${positionId}`);
                continue;
            }

            console.log(`\nPosition ${positionId} (${symbol}): ${position.action} ${position.quantity} @ ${position.entryPrice}`);
            console.log(`Latest candle: O=${latestCandle.open}, H=${latestCandle.high}, L=${latestCandle.low}, C=${latestCandle.close}`);

            // Update trailing stop based on position direction
            if (position.action === 'Buy') {
                updateLongTrailingStop(position, latestCandle);
            } else if (position.action === 'Sell') {
                updateShortTrailingStop(position, latestCandle);
            }
        }

        console.log('\nTrailing stop update complete.');
        return true;
    } catch (error) {
        console.error(`Error updating trailing stops: ${error.message}`);
        return false;
    }
}

/**
 * Update trailing stop for long position
 */
function updateLongTrailingStop(position, latestCandle) {
    try {
        // Calculate profit/loss in points
        const currentPnL = latestCandle.close - position.entryPrice;
        const pnlPercent = (currentPnL / position.entryPrice) * 100;

        console.log(`Long position current P&L: ${currentPnL.toFixed(2)} points (${pnlPercent.toFixed(2)}%)`);

        // Update the highest price seen during the trade
        position.trailHigh = position.trailHigh ? Math.max(position.trailHigh, latestCandle.high) : latestCandle.high;

        // Get the ATR value from our calculated values
        const symbol = position.symbol || 'MNQM5';
        const atr = atrValues[symbol] || 10.0; // Default to 10.0 if no ATR available
        position.atr = atr;

        // Calculate profit in points for stepped trailing stop
        const profitPoints = position.trailHigh - position.entryPrice;
        const profitPercent = (profitPoints / position.entryPrice) * 100;

        // Calculate trail factor based on profit level (stepped trailing stop)
        let trailFactor = position.trailFactor; // Default trail factor

        // Adjust trail factor based on profit level
        if (profitPoints > atr * 3) {
            // More than 3 ATR in profit - tighten the trail
            trailFactor = 0.5; // Tighter trail
            console.log(`Profit > 3 ATR (${(atr * 3).toFixed(2)}), using tighter trail factor: ${trailFactor}`);
        } else if (profitPoints > atr * 2) {
            // More than 2 ATR in profit - slightly tighten the trail
            trailFactor = 0.75; // Slightly tighter trail
            console.log(`Profit > 2 ATR (${(atr * 2).toFixed(2)}), using slightly tighter trail factor: ${trailFactor}`);
        } else if (profitPoints > atr) {
            // More than 1 ATR in profit - use standard trail
            trailFactor = 1.0; // Standard trail
            console.log(`Profit > 1 ATR (${atr.toFixed(2)}), using standard trail factor: ${trailFactor}`);
        } else {
            // Less than 1 ATR in profit - use wider trail
            trailFactor = 1.5; // Wider trail
            console.log(`Profit < 1 ATR (${atr.toFixed(2)}), using wider trail factor: ${trailFactor}`);
        }

        // Update the position's trail factor
        position.trailFactor = trailFactor;

        // Calculate new trail stop price based on ATR
        const newStopPrice = position.trailHigh - (atr * trailFactor);

        console.log(`Trail High: ${position.trailHigh.toFixed(2)}, ATR: ${atr.toFixed(2)}, Trail Factor: ${trailFactor}`);
        console.log(`Current stop: ${position.stopLossPrice.toFixed(2)}, New stop: ${newStopPrice.toFixed(2)}`);

        // Only move stop loss up (for long positions)
        if (newStopPrice > position.stopLossPrice) {
            position.stopLossPrice = newStopPrice;
            console.log(`Updated ATR-based trailing stop to ${newStopPrice.toFixed(2)}`);

            // Try to update the actual stop loss order if we have one
            if (position.stopLossOrderId) {
                console.log(`Attempting to update stop loss order ${position.stopLossOrderId} to new price: ${newStopPrice}`);

                // Call the API to update the order
                tradovateApi.modifyOrder({
                    orderId: position.stopLossOrderId,
                    stopPrice: newStopPrice
                }).then(result => {
                    if (result.success) {
                        console.log(`Successfully updated stop loss order ${position.stopLossOrderId} to new price: ${newStopPrice}`);
                    } else {
                        console.error(`Failed to update stop loss order: ${result.error || 'Unknown error'}`);
                    }
                }).catch(error => {
                    console.error(`Error updating stop loss order: ${error.message}`);
                });
            }
        } else {
            console.log(`No update needed (new stop price is not higher than current stop)`);
        }

        // Check if stop loss is hit
        if (latestCandle.low <= position.stopLossPrice) {
            console.log(`Trailing stop would be hit at ${position.stopLossPrice.toFixed(2)} (low: ${latestCandle.low.toFixed(2)})`);
        }

        // Check if take profit is hit
        if (latestCandle.high >= position.takeProfitPrice) {
            console.log(`Take profit would be hit at ${position.takeProfitPrice.toFixed(2)} (high: ${latestCandle.high.toFixed(2)})`);
        }
    } catch (error) {
        console.error(`Error updating long trailing stop: ${error.message}`);
    }
}

/**
 * Update trailing stop for short position
 */
function updateShortTrailingStop(position, latestCandle) {
    try {
        // Calculate profit/loss in points
        const currentPnL = position.entryPrice - latestCandle.close;
        const pnlPercent = (currentPnL / position.entryPrice) * 100;

        console.log(`Short position current P&L: ${currentPnL.toFixed(2)} points (${pnlPercent.toFixed(2)}%)`);

        // Update the lowest price seen during the trade
        position.trailLow = position.trailLow ? Math.min(position.trailLow, latestCandle.low) : latestCandle.low;

        // Get the ATR value from our calculated values
        const symbol = position.symbol || 'MNQM5';
        const atr = atrValues[symbol] || 10.0; // Default to 10.0 if no ATR available
        position.atr = atr;

        // Calculate profit in points for stepped trailing stop
        const profitPoints = position.entryPrice - position.trailLow;
        const profitPercent = (profitPoints / position.entryPrice) * 100;

        // Calculate trail factor based on profit level (stepped trailing stop)
        let trailFactor = position.trailFactor; // Default trail factor

        // Adjust trail factor based on profit level
        if (profitPoints > atr * 3) {
            // More than 3 ATR in profit - tighten the trail
            trailFactor = 0.5; // Tighter trail
            console.log(`Profit > 3 ATR (${(atr * 3).toFixed(2)}), using tighter trail factor: ${trailFactor}`);
        } else if (profitPoints > atr * 2) {
            // More than 2 ATR in profit - slightly tighten the trail
            trailFactor = 0.75; // Slightly tighter trail
            console.log(`Profit > 2 ATR (${(atr * 2).toFixed(2)}), using slightly tighter trail factor: ${trailFactor}`);
        } else if (profitPoints > atr) {
            // More than 1 ATR in profit - use standard trail
            trailFactor = 1.0; // Standard trail
            console.log(`Profit > 1 ATR (${atr.toFixed(2)}), using standard trail factor: ${trailFactor}`);
        } else {
            // Less than 1 ATR in profit - use wider trail
            trailFactor = 1.5; // Wider trail
            console.log(`Profit < 1 ATR (${atr.toFixed(2)}), using wider trail factor: ${trailFactor}`);
        }

        // Update the position's trail factor
        position.trailFactor = trailFactor;

        // Calculate new trail stop price based on ATR
        const newStopPrice = position.trailLow + (atr * trailFactor);

        console.log(`Trail Low: ${position.trailLow.toFixed(2)}, ATR: ${atr.toFixed(2)}, Trail Factor: ${trailFactor}`);
        console.log(`Current stop: ${position.stopLossPrice.toFixed(2)}, New stop: ${newStopPrice.toFixed(2)}`);

        // Only move stop loss down (for short positions)
        if (newStopPrice < position.stopLossPrice) {
            position.stopLossPrice = newStopPrice;
            console.log(`Updated ATR-based trailing stop to ${newStopPrice.toFixed(2)}`);

            // Try to update the actual stop loss order if we have one
            if (position.stopLossOrderId) {
                console.log(`Attempting to update stop loss order ${position.stopLossOrderId} to new price: ${newStopPrice}`);

                // Call the API to update the order
                tradovateApi.modifyOrder({
                    orderId: position.stopLossOrderId,
                    stopPrice: newStopPrice
                }).then(result => {
                    if (result.success) {
                        console.log(`Successfully updated stop loss order ${position.stopLossOrderId} to new price: ${newStopPrice}`);
                    } else {
                        console.error(`Failed to update stop loss order: ${result.error || 'Unknown error'}`);
                    }
                }).catch(error => {
                    console.error(`Error updating stop loss order: ${error.message}`);
                });
            }
        } else {
            console.log(`No update needed (new stop price is not lower than current stop)`);
        }

        // Check if stop loss is hit
        if (latestCandle.high >= position.stopLossPrice) {
            console.log(`Trailing stop would be hit at ${position.stopLossPrice.toFixed(2)} (high: ${latestCandle.high.toFixed(2)})`);
        }

        // Check if take profit is hit
        if (latestCandle.low <= position.takeProfitPrice) {
            console.log(`Take profit would be hit at ${position.takeProfitPrice.toFixed(2)} (low: ${latestCandle.low.toFixed(2)})`);
        }
    } catch (error) {
        console.error(`Error updating short trailing stop: ${error.message}`);
    }
}

// Removed simulated data functions as we now use real market data

// Run the test if this file is executed directly
if (require.main === module) {
    initialize()
        .then(() => updateTrailingStops())
        .then(() => {
            console.log('\nTest complete.');
            process.exit(0);
        })
        .catch(error => {
            console.error(`Unhandled error: ${error.message}`);
            process.exit(1);
        });
}

module.exports = {
    initialize,
    syncPositionsWithAPI,
    updateTrailingStops
};
