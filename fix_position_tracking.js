/**
 * Fix for Position Tracking
 * 
 * This script adds enhanced position tracking functionality to ensure positions
 * are properly tracked across different bot sessions.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');
const fs = require('fs');
const path = require('path');

// Global variables
const positionCacheFile = path.join(__dirname, 'position_cache.json');

/**
 * Initialize the fix
 */
async function initialize() {
    try {
        console.log('Initializing position tracking fix...');
        
        // Configure API with demo mode
        tradovateApi.setConfig({
            baseUrl: 'https://demo.tradovateapi.com/v1',
            wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
            mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
        });
        
        // Sync positions with API and save to cache
        await syncAndSavePositions();
        
        console.log('Initialization complete.');
        return true;
    } catch (error) {
        console.error(`Error initializing: ${error.message}`);
        return false;
    }
}

/**
 * Sync positions with API and save to cache
 */
async function syncAndSavePositions() {
    try {
        console.log('Syncing positions with API and saving to cache...');
        
        // Get account ID
        const accountId = ********; // DEMO4690440 account ID
        
        // Get positions from API
        const positions = await tradovateApi.makeApiRequest('GET', `position/list?accountId=${accountId}`);
        console.log(`Found ${positions.length} positions from API`);
        
        // Filter for active positions (netPos != 0)
        const activeApiPositions = positions.filter(position => position.netPos !== 0);
        console.log(`Active positions: ${activeApiPositions.length}`);
        
        // Create position tracking objects
        const trackedPositions = {};
        
        // Display active positions
        if (activeApiPositions.length > 0) {
            console.log('\nActive Positions:');
            activeApiPositions.forEach(position => {
                console.log(`Symbol: ${position.contractId}, Net Position: ${position.netPos}, P/L: ${(position.boughtValue - position.soldValue).toFixed(2)}`);
                
                // Get contract details
                getContractDetails(position.contractId)
                    .then(contract => {
                        if (contract) {
                            console.log(`Contract details for ${position.contractId}: ${contract.name}`);
                        }
                    })
                    .catch(error => {
                        console.error(`Error getting contract details: ${error.message}`);
                    });
                
                // Add to our tracking
                const positionId = `pos-${position.contractId}-${Date.now()}`;
                const action = position.netPos > 0 ? 'Buy' : 'Sell';
                
                trackedPositions[positionId] = {
                    id: positionId,
                    contractId: position.contractId,
                    action: action,
                    quantity: Math.abs(position.netPos),
                    entryPrice: position.netPrice,
                    stopLossPrice: action === 'Buy' ?
                        position.netPrice - 9999 : // Very far away for long (no initial stop loss)
                        position.netPrice + 9999,  // Very far away for short (no initial stop loss)
                    takeProfitPrice: action === 'Buy' ?
                        position.netPrice * 1.01 : // 1% above for long
                        position.netPrice * 0.99,  // 1% below for short
                    trailFactor: 1.0, // Default trail factor
                    timestamp: Date.now(),
                    trailHigh: action === 'Buy' ? position.netPrice : null,
                    trailLow: action === 'Sell' ? position.netPrice : null,
                    atr: 1.0, // Default ATR
                    apiPositionId: position.id
                };
            });
            
            // Save to cache file
            savePositionsToCache(trackedPositions);
        } else {
            console.log('No active positions found');
            // Clear cache file if no positions
            savePositionsToCache({});
        }
        
        return activeApiPositions;
    } catch (error) {
        console.error(`Error syncing positions: ${error.message}`);
        return [];
    }
}

/**
 * Get contract details
 */
async function getContractDetails(contractId) {
    try {
        const response = await tradovateApi.makeApiRequest('GET', `contract/find?id=${contractId}`);
        return response;
    } catch (error) {
        console.error(`Error getting contract details: ${error.message}`);
        return null;
    }
}

/**
 * Save positions to cache file
 */
function savePositionsToCache(positions) {
    try {
        fs.writeFileSync(positionCacheFile, JSON.stringify(positions, null, 2));
        console.log(`Saved ${Object.keys(positions).length} positions to cache file: ${positionCacheFile}`);
        return true;
    } catch (error) {
        console.error(`Error saving positions to cache: ${error.message}`);
        return false;
    }
}

/**
 * Load positions from cache file
 */
function loadPositionsFromCache() {
    try {
        if (fs.existsSync(positionCacheFile)) {
            const data = fs.readFileSync(positionCacheFile, 'utf8');
            const positions = JSON.parse(data);
            console.log(`Loaded ${Object.keys(positions).length} positions from cache file: ${positionCacheFile}`);
            return positions;
        } else {
            console.log('No position cache file found');
            return {};
        }
    } catch (error) {
        console.error(`Error loading positions from cache: ${error.message}`);
        return {};
    }
}

/**
 * Check if positions match between API and cache
 */
async function checkPositionConsistency() {
    try {
        console.log('Checking position consistency between API and cache...');
        
        // Get positions from API
        const accountId = ********; // DEMO4690440 account ID
        const apiPositions = await tradovateApi.makeApiRequest('GET', `position/list?accountId=${accountId}`);
        const activeApiPositions = apiPositions.filter(position => position.netPos !== 0);
        
        // Get positions from cache
        const cachedPositions = loadPositionsFromCache();
        
        console.log(`API positions: ${activeApiPositions.length}, Cached positions: ${Object.keys(cachedPositions).length}`);
        
        // Check for missing positions in cache
        const missingInCache = [];
        activeApiPositions.forEach(apiPos => {
            let found = false;
            for (const posId in cachedPositions) {
                const cachedPos = cachedPositions[posId];
                if (cachedPos.apiPositionId === apiPos.id || 
                    (cachedPos.contractId === apiPos.contractId && 
                     Math.abs(cachedPos.quantity) === Math.abs(apiPos.netPos))) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                missingInCache.push(apiPos);
            }
        });
        
        // Check for positions in cache that don't exist in API
        const extraInCache = [];
        for (const posId in cachedPositions) {
            const cachedPos = cachedPositions[posId];
            let found = false;
            for (const apiPos of activeApiPositions) {
                if (cachedPos.apiPositionId === apiPos.id || 
                    (cachedPos.contractId === apiPos.contractId && 
                     Math.abs(cachedPos.quantity) === Math.abs(apiPos.netPos))) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                extraInCache.push(cachedPos);
            }
        }
        
        console.log(`Positions missing in cache: ${missingInCache.length}`);
        console.log(`Extra positions in cache: ${extraInCache.length}`);
        
        if (missingInCache.length > 0 || extraInCache.length > 0) {
            console.log('Position inconsistency detected. Syncing positions...');
            await syncAndSavePositions();
            return false;
        } else {
            console.log('Positions are consistent between API and cache');
            return true;
        }
    } catch (error) {
        console.error(`Error checking position consistency: ${error.message}`);
        return false;
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    initialize()
        .then(() => checkPositionConsistency())
        .then(result => {
            console.log('\nTest complete.');
            process.exit(result ? 0 : 1);
        })
        .catch(error => {
            console.error(`Unhandled error: ${error.message}`);
            process.exit(1);
        });
}

module.exports = {
    initialize,
    syncAndSavePositions,
    loadPositionsFromCache,
    checkPositionConsistency
};
