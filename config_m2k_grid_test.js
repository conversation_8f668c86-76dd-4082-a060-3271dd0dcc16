// config_m2k_grid_test.js
module.exports = {
    // --- Essential Settings ---
    inputFile: 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv', // !! MUST BE UPDATED !!
    outputDir: './output/M2K_AdvancedGrid_Test_Results', // Output directory for this test
    initialBalance: 10000,
    pointValue: 5.00,         // M2K point value
    commissionPerContract: 0.40, // Per contract, one way. Round trip will be 2x this if applied per side.
                                 // The script seems to apply it once per trade (as a round trip cost) in logAndStoreExit.
    slippagePoints: 0.20,       // Slippage in points (e.g., 0.20 for M2K could be 1 tick if tick size is 0.1)
    pricePrecision: 2,        // For formatting P&L, prices

    // --- Run Mode ---
    isAdaptiveRun: false, // Set to false for parameter grid testing

    // --- Parameter Grid Settings ---
    // These arrays will be used to generate combinations if isAdaptiveRun is false
    slFactors: [8.0, 9.0], // Example: [7.0, 8.0, 9.0] - keep small for faster initial tests
    tpFactors: [7.0, 8.0, 9.0], // Example: [6.0, 7.0, 8.0, 9.0]
    trailFactors: [0.01, 0.02], // Example: [0.01, 0.02, 0.03]
    // To grid trailStepSizeMultiplier, set useSteppedTrail: true and provide an array here:
    // trailStepSizeMultiplier: [1.0, 1.5, 2.0], // Only if useSteppedTrail = true

    // --- Position Sizing ---
    // Option 1: Fixed Contracts
    fixedContracts: 10,          // Start with 1 for baseline, then can increase or use risk %
    // Option 2: Risk Percentage (if riskPercent > 0, fixedContracts is ignored by sizing logic)
    riskPercent: 0,           // e.g., 0.01 for 1% of equity. Set to 0 to use fixedContracts.
    maxContracts: 10,           // Maximum contracts if using riskPercent

    // --- Latency & Fixed TP ---
    latencyDelayBars: 0,        // Number of bars to delay entry/exit execution (0 for none)
    fixedTpPoints: 0,           // Minimum Take Profit in POINTS (0 to disable, TP will be ATR-based)

    // --- Indicator Periods ---
    atrPeriod: 14,
    sma200Period: 200,
    wma50Period: 50,
    rsiPeriod: 14,
    rsiMaPeriod: 9,

    // --- Entry Filters ---
    useWmaFilter: true,
    useRsiFilter: true,         // General RSI filter (RSI vs RSI MA)
    minAtrEntry: 0.1,           // Minimum ATR value in points for entry (M2K specific, e.g., 1 tick if tick size is 0.1)
    minRsiMaSeparation: 2,      // Minimum absolute difference between RSI and its MA

    // --- Advanced Features (Set to false for initial baseline grid test) ---
    useMLEntryFilter: false,
    useAdvancedMLFilter: false, // Requires useMLEntryFilter to be true
    mlConfig: {                 // Placeholder for ML Filter configurations
        // modelPath: './models/m2k_model.json', // Example
        // scalerPath: './models/m2k_scaler.json', // Example
        // featureSet: ['atrRegime', 'hourOfDay', 'rsi', 'rsiMa', 'timeScore', ...], // Example
        // entryThreshold: 0.6, // Example
        // patternLookback: 5 // For ML feature generation
    },

    useTimeAnalysis: false,
    timeAnalysisConfig: {       // Placeholder for Time Analysis configurations
        // performanceDataPath: './analysis_data/m2k_time_performance.json', // Example
        // scoreWeights: { hour: 0.6, day: 0.4 } // Example
    },

    useDynamicPositionSizing: false,
    dynamicPositionSizingConfig: { // Placeholder for Dynamic Position Sizing configurations
        // baseRiskFraction: 0.005, // e.g., 0.5% base risk
        // maxRiskFraction: 0.02,   // e.g., 2% max risk
        // atrMultiplier: 1.5,
        // timeScoreSensitivity: 0.5,
        // mlScoreSensitivity: 0.5
    },
    useVolatilityPositionSizing: false, // If true, needs spread_volatility_utils.js and riskPercent > 0

    // --- Exit Logic ---
    useTwoBarColorExit: true,   // Exit on two consecutive counter-trend bars
    useSteppedTrail: false,     // If true, uses enhancedPM.js and trailStepSizeMultiplier
    trailStepSizeMultiplier: 1.0, // Default if not gridding. If gridding, array is in grid params section.
    useAdaptiveSlippage: false, // If true, uses enhancedPM.js and spreadUtils.js

    // --- Time Filter (Example: Common US market hours in UTC) ---
    // RTH for M2K is typically 9:30 AM - 4:00 PM ET
    // Convert to UTC: 13:30 UTC - 20:00 UTC (during standard time)
    // Or 14:30 UTC - 21:00 UTC (during daylight saving time)
    // For simplicity, can use a broader range or adjust based on DST.
    // This example is for EST/EDT generally, adjust as needed.
    timeFilterEnabled: false,
    activeHours: [13, 14, 15, 16, 17, 18, 19, 20, 21], // Approx 8 AM ET to 5 PM ET

    // --- ATR Thresholds (for adaptive logic if enabled, or ML features) ---
    atrThresholds: {
        low_medium: 0.3,   // M2K specific: e.g., ATR < 0.3 points is Low
        medium_high: 0.8   // M2K specific: e.g., ATR > 0.8 points is High
                           // Medium is between these two. Adjust based on M2K typical ATR.
    },
    // --- Adaptive Parameters (used if isAdaptiveRun = true) ---
    adaptiveParams: {
        Low:    { slFactor: 9.0, tpFactor: 7.0, trailFactor: 0.02 },
        Medium: { slFactor: 8.0, tpFactor: 8.0, trailFactor: 0.015 },
        High:   { slFactor: 7.0, tpFactor: 9.0, trailFactor: 0.01 }
    },
};