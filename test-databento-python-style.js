/**
 * Test script for Databento integration
 * Following the Python example from the documentation
 */

require('dotenv').config();
const axios = require('axios');

// Check if API key is available
if (!process.env.DATABENTO_API_KEY || process.env.DATABENTO_API_KEY === 'your_databento_api_key') {
  console.error('DATABENTO_API_KEY environment variable is required. Please update your .env file with your actual Databento API key.');
  process.exit(1);
}

console.log(`Using Databento API key: ${process.env.DATABENTO_API_KEY.substring(0, 5)}...`);

// Create a client with the API key
const client = axios.create({
  baseURL: 'https://api.databento.com/v0',
  headers: {
    'Authorization': `Bearer ${process.env.DATABENTO_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// Test functions
async function testLicensing() {
  try {
    console.log('Testing licensing API...');
    
    // Get licensing information
    console.log('Getting licensing information...');
    const response = await client.get('/licensing/me');
    console.log('Licensing information:', response.data);
    
    return true;
  } catch (error) {
    console.error('Error testing licensing API:', error.response ? 
      `${error.response.status} ${error.response.statusText} - ${JSON.stringify(error.response.data)}` : 
      error.message);
    return false;
  }
}

async function testHistorical() {
  try {
    console.log('\nTesting historical API...');
    
    // Get historical data
    console.log('Getting historical data...');
    const response = await client.get('/historical/datasets');
    console.log('Historical datasets:', response.data);
    
    return true;
  } catch (error) {
    console.error('Error testing historical API:', error.response ? 
      `${error.response.status} ${error.response.statusText} - ${JSON.stringify(error.response.data)}` : 
      error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('Starting Databento Python-style tests...');
  
  let success = true;
  
  // Test licensing API
  if (await testLicensing()) {
    console.log('\n✅ Licensing API test passed');
  } else {
    console.log('\n❌ Licensing API test failed');
    success = false;
  }
  
  // Test historical API
  if (await testHistorical()) {
    console.log('\n✅ Historical API test passed');
  } else {
    console.log('\n❌ Historical API test failed');
    success = false;
  }
  
  console.log('\nTests completed.');
  if (success) {
    console.log('All tests passed! 🎉');
  } else {
    console.log('Some tests failed. 😢');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
