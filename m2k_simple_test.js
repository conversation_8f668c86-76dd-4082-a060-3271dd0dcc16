// m2k_simple_test.js - Standalone M2K test script

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

console.log("Starting M2K Simple Test...");

// --- Configuration ---
const config = {
  inputFile: 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv',
  outputDir: './output/M2K_SimpleTest',
  initialBalance: 10000,
  pointValue: 5.00,
  slFactor: 6.0,
  tpFactor: 5.0,
  trailFactor: 0.05,
  fixedContracts: 10,
  commissionPerContract: 0.40,
  slippagePoints: 0.20,
  atrPeriod: 14
};

// --- Setup ---
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
  console.log(`Created output directory: ${config.outputDir}`);
} else {
  console.log(`Output directory exists: ${config.outputDir}`);
}

// --- Data Loading ---
console.log(`Loading data from: ${config.inputFile}`);
const allCandles = [];

// Check if file exists
if (!fs.existsSync(config.inputFile)) {
  console.error(`Error: Input file not found at path: ${config.inputFile}`);
  process.exit(1);
}

// Load data
fs.createReadStream(config.inputFile)
  .pipe(csv({ separator: ';' }))
  .on('data', (data) => {
    const open = parseFloat(data.Open);
    const high = parseFloat(data.High);
    const low = parseFloat(data.Low);
    const close = parseFloat(data.Close);
    const timeString = data.Time || data.Date || data['Time left'];
    
    let timestamp;
    try {
      timestamp = new Date(timeString).getTime() / 1000;
    } catch (e) {
      return; // Skip invalid dates
    }
    
    if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close) && !isNaN(timestamp)) {
      allCandles.push({ timestamp, open, high, low, close });
    }
  })
  .on('end', () => {
    console.log(`Loaded ${allCandles.length} candles`);
    
    if (allCandles.length === 0) {
      console.error("No valid data loaded. Check your CSV format.");
      process.exit(1);
    }
    
    // Sort candles by timestamp
    allCandles.sort((a, b) => a.timestamp - b.timestamp);
    
    // Calculate ATR
    calculateATR(allCandles, config.atrPeriod);
    
    // Run simple backtest
    const results = runSimpleBacktest(allCandles, config);
    
    // Display results
    console.log("\n--- BACKTEST RESULTS ---");
    console.log(`Total Trades: ${results.totalTrades}`);
    console.log(`Winning Trades: ${results.wins}`);
    console.log(`Losing Trades: ${results.losses}`);
    console.log(`Win Rate: ${results.winRate.toFixed(2)}%`);
    console.log(`Total P&L: $${results.totalPnL.toFixed(2)}`);
    console.log(`Profit Factor: ${results.profitFactor.toFixed(2)}`);
    
    // Save results
    const resultsJson = JSON.stringify(results, null, 2);
    fs.writeFileSync(path.join(config.outputDir, 'results.json'), resultsJson);
    console.log(`Results saved to: ${path.join(config.outputDir, 'results.json')}`);
  })
  .on('error', (err) => {
    console.error("Error reading CSV:", err);
    process.exit(1);
  });

// --- Calculate ATR ---
function calculateATR(candles, period) {
  // First calculate True Range for each candle
  for (let i = 0; i < candles.length; i++) {
    if (i === 0) {
      candles[i].tr = candles[i].high - candles[i].low;
    } else {
      const prev = candles[i-1];
      candles[i].tr = Math.max(
        candles[i].high - candles[i].low,
        Math.abs(candles[i].high - prev.close),
        Math.abs(candles[i].low - prev.close)
      );
    }
  }
  
  // Then calculate ATR as simple moving average of TR
  for (let i = 0; i < candles.length; i++) {
    if (i < period - 1) {
      candles[i].atr = candles[i].tr;
    } else {
      let sum = 0;
      for (let j = i - period + 1; j <= i; j++) {
        sum += candles[j].tr;
      }
      candles[i].atr = sum / period;
    }
  }
}

// --- Simple Backtest Function ---
function runSimpleBacktest(candles, config) {
  console.log("Running simple backtest...");
  
  let balance = config.initialBalance;
  let position = null;
  let trades = [];
  let wins = 0;
  let losses = 0;
  let grossProfit = 0;
  let grossLoss = 0;
  
  // Need at least 3 candles for pattern detection
  for (let i = 3; i < candles.length; i++) {
    const c0 = candles[i-3];
    const c1 = candles[i-2];
    const c2 = candles[i-1];
    const c3 = candles[i];
    
    // Skip if ATR is not available
    if (!c3.atr || isNaN(c3.atr) || c3.atr <= 0) continue;
    
    // Manage existing position
    if (position) {
      // Update trail stop
      if (position.direction === 'long') {
        position.trailStop = Math.max(position.trailStop, 
          c3.high - (c3.atr * config.trailFactor));
      } else {
        position.trailStop = Math.min(position.trailStop, 
          c3.low + (c3.atr * config.trailFactor));
      }
      
      // Check for exit conditions
      let exitPrice = null;
      let exitReason = null;
      
      if (position.direction === 'long') {
        // Stop loss hit
        if (c3.low <= position.stopLoss) {
          exitPrice = position.stopLoss;
          exitReason = 'stop_loss';
        }
        // Take profit hit
        else if (c3.high >= position.takeProfit) {
          exitPrice = position.takeProfit;
          exitReason = 'take_profit';
        }
        // Trail stop hit
        else if (c3.low <= position.trailStop) {
          exitPrice = position.trailStop;
          exitReason = 'trail_stop';
        }
      } else { // Short position
        // Stop loss hit
        if (c3.high >= position.stopLoss) {
          exitPrice = position.stopLoss;
          exitReason = 'stop_loss';
        }
        // Take profit hit
        else if (c3.low <= position.takeProfit) {
          exitPrice = position.takeProfit;
          exitReason = 'take_profit';
        }
        // Trail stop hit
        else if (c3.high >= position.trailStop) {
          exitPrice = position.trailStop;
          exitReason = 'trail_stop';
        }
      }
      
      // Process exit if conditions met
      if (exitPrice !== null) {
        const pnlPoints = position.direction === 'long' ? 
          exitPrice - position.entry : position.entry - exitPrice;
        const pnlDollars = pnlPoints * config.pointValue * config.fixedContracts;
        const commission = config.commissionPerContract * config.fixedContracts;
        const slippage = config.slippagePoints * config.pointValue * config.fixedContracts;
        const netPnl = pnlDollars - commission - slippage;
        
        balance += netPnl;
        
        trades.push({
          entry: position.entry,
          exit: exitPrice,
          direction: position.direction,
          pnl: netPnl,
          reason: exitReason
        });
        
        if (netPnl > 0) {
          wins++;
          grossProfit += netPnl;
        } else {
          losses++;
          grossLoss += Math.abs(netPnl);
        }
        
        position = null;
      }
    }
    
    // Look for new entry if no position
    if (!position) {
      // Simple pattern: bullish if previous candle is green and current is green
      const isBullish = c2.close > c2.open && c3.open < c3.close;
      // Simple pattern: bearish if previous candle is red and current is red
      const isBearish = c2.close < c2.open && c3.open > c3.close;
      
      if (isBullish) {
        position = {
          direction: 'long',
          entry: c3.close,
          stopLoss: c3.close - (c3.atr * config.slFactor),
          takeProfit: c3.close + (c3.atr * config.tpFactor),
          trailStop: c3.close - (c3.atr * config.trailFactor),
          entryTime: c3.timestamp
        };
      } else if (isBearish) {
        position = {
          direction: 'short',
          entry: c3.close,
          stopLoss: c3.close + (c3.atr * config.slFactor),
          takeProfit: c3.close - (c3.atr * config.tpFactor),
          trailStop: c3.close + (c3.atr * config.trailFactor),
          entryTime: c3.timestamp
        };
      }
    }
  }
  
  // Close any open position at the end
  if (position) {
    const lastCandle = candles[candles.length - 1];
    const pnlPoints = position.direction === 'long' ? 
      lastCandle.close - position.entry : position.entry - lastCandle.close;
    const pnlDollars = pnlPoints * config.pointValue * config.fixedContracts;
    const commission = config.commissionPerContract * config.fixedContracts;
    const netPnl = pnlDollars - commission;
    
    balance += netPnl;
    
    trades.push({
      entry: position.entry,
      exit: lastCandle.close,
      direction: position.direction,
      pnl: netPnl,
      reason: 'end_of_data'
    });
    
    if (netPnl > 0) {
      wins++;
      grossProfit += netPnl;
    } else {
      losses++;
      grossLoss += Math.abs(netPnl);
    }
  }
  
  // Calculate metrics
  const totalTrades = wins + losses;
  const winRate = totalTrades > 0 ? (wins / totalTrades * 100) : 0;
  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;
  const totalPnL = balance - config.initialBalance;
  
  return {
    totalTrades,
    wins,
    losses,
    winRate,
    totalPnL,
    profitFactor,
    finalBalance: balance,
    trades
  };
}