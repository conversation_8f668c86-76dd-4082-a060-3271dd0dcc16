<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily PnL Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>

    <!-- Load dashboard integration scripts -->
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <script src="dashboard_integration.js"></script>
    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        /* Improve readability */
        .menu-item-text, .stat-label, .card-title, .dashboard-title h1 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .dashboard-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
        }

        .dashboard-title h1 {
            margin-bottom: 0;
        }

        .pnl-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .dashboard-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            min-width: 150px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-value.positive {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }

        .stat-value.negative {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0;
        }

        .chart-container {
            position: relative;
            height: 250px;
            margin-top: 1rem;
        }

        .dashboard-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .pnl-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .pnl-table th, .pnl-table td {
            padding: 0.75rem;
            text-align: center;
            border: 1px solid var(--border);
        }

        .pnl-table th {
            background-color: rgba(0, 204, 255, 0.1);
            color: var(--primary);
            font-weight: 600;
            font-family: 'Orbitron', sans-serif;
        }

        .pnl-table td {
            font-weight: 500;
        }

        .pnl-positive {
            color: var(--success);
        }

        .pnl-negative {
            color: var(--danger);
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                flex-wrap: wrap;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="dashboard-title">
            <div class="pnl-icon">💰</div>
            <h1>DAILY PNL DASHBOARD</h1>
        </div>
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-label">Total P&L</div>
                <div class="stat-value positive">$5,238,965.25</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Avg Daily P&L</div>
                <div class="stat-value positive">$3,702.80</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Win Day Rate</div>
                <div class="stat-value">95.3%</div>
            </div>
        </div>
    </div>

    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Daily P&L Chart</h2>
            </div>
            <div class="chart-container">
                <canvas id="dailyPnlChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">P&L Distribution</h2>
            </div>
            <div class="chart-container">
                <canvas id="pnlDistributionChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">P&L by Day of Week</h2>
            </div>
            <div class="chart-container">
                <canvas id="pnlByDayChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Recent Daily P&L</h2>
            </div>
            <table class="pnl-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>P&L</th>
                        <th>Trades</th>
                        <th>Win Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>May 8, 2025</td>
                        <td class="pnl-positive">$4,250.75</td>
                        <td>42</td>
                        <td>78.6%</td>
                    </tr>
                    <tr>
                        <td>May 7, 2025</td>
                        <td class="pnl-positive">$3,875.20</td>
                        <td>38</td>
                        <td>76.3%</td>
                    </tr>
                    <tr>
                        <td>May 6, 2025</td>
                        <td class="pnl-positive">$4,120.50</td>
                        <td>45</td>
                        <td>80.0%</td>
                    </tr>
                    <tr>
                        <td>May 5, 2025</td>
                        <td class="pnl-positive">$3,950.30</td>
                        <td>40</td>
                        <td>77.5%</td>
                    </tr>
                    <tr>
                        <td>May 4, 2025</td>
                        <td class="pnl-negative">-$250.45</td>
                        <td>35</td>
                        <td>65.7%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="dashboard-footer">
        <p>QUANTUM CAPITAL | DAILY PNL DASHBOARD | LAST UPDATED: MAY 8, 2025</p>
        <p style="margin-top: 5px; font-weight: bold; color: var(--success);">95.3% PROFITABLE DAYS ACROSS ALL INSTRUMENTS</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Daily PnL Chart
            const dailyPnlCtx = document.getElementById('dailyPnlChart').getContext('2d');
            new Chart(dailyPnlCtx, {
                type: 'bar',
                data: {
                    labels: ['Apr 8', 'Apr 15', 'Apr 22', 'Apr 29', 'May 6'],
                    datasets: [{
                        label: 'Daily P&L',
                        data: [3850.25, 4120.75, 3975.50, 4250.30, 3950.45],
                        backgroundColor: [
                            'rgba(0, 255, 136, 0.7)',
                            'rgba(0, 255, 136, 0.7)',
                            'rgba(0, 255, 136, 0.7)',
                            'rgba(0, 255, 136, 0.7)',
                            'rgba(0, 255, 136, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 255, 136, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false,
                            labels: {
                                color: '#f8fafc',
                                font: {
                                    family: 'Rajdhani'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1
                        }
                    }
                }
            });

            // P&L Distribution Chart
            const pnlDistributionCtx = document.getElementById('pnlDistributionChart').getContext('2d');
            new Chart(pnlDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Profit > $5,000', 'Profit $2,500-$5,000', 'Profit $0-$2,500', 'Loss'],
                    datasets: [{
                        data: [25, 45, 25, 5],
                        backgroundColor: [
                            'rgba(0, 255, 136, 0.9)',
                            'rgba(0, 255, 136, 0.7)',
                            'rgba(0, 255, 136, 0.5)',
                            'rgba(255, 51, 102, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 255, 136, 1)',
                            'rgba(255, 51, 102, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#f8fafc',
                                font: {
                                    family: 'Rajdhani'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1
                        }
                    }
                }
            });

            // P&L by Day of Week Chart
            const pnlByDayCtx = document.getElementById('pnlByDayChart').getContext('2d');
            new Chart(pnlByDayCtx, {
                type: 'bar',
                data: {
                    labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
                    datasets: [{
                        label: 'Average P&L',
                        data: [3750.25, 4050.75, 3850.50, 3950.30, 3910.45],
                        backgroundColor: 'rgba(0, 204, 255, 0.7)',
                        borderColor: 'rgba(0, 204, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            },
                            ticks: {
                                color: '#94a3b8'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false,
                            labels: {
                                color: '#f8fafc',
                                font: {
                                    family: 'Rajdhani'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1
                        }
                    }
                }
            });
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Daily PnL`;
            }

            // Update daily PnL data
            updateDailyPnLData(data);

            console.log('Daily PnL dashboard updated with data for:', data.instrumentCode);
        }

        // Update daily PnL data
        function updateDailyPnLData(data) {
            // This would be implemented to update the daily PnL data with actual data
            // For now, we'll just log that we would update the daily PnL data
            console.log('Daily PnL data would be updated with:', data);
        }
    </script>
</body>
</html>
