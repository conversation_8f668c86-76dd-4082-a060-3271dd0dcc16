/**
 * Generate CSV Trade Log for MNQ Last Year
 * Uses the same logic as backtest.js to identify trade entry points
 * Outputs CSV format: YYYY-MM-DD HH:MM:SS,long/short (NYC EST time)
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Import the same config and logic as backtest.js
const config = require('./config');

console.log("=== MNQ Trade Log CSV Generator ===");
console.log("Using the same logic as backtest.js to identify trade entries");

// Output settings
const outputDir = 'C:/backtest-bot/output/MNQ_TradeLog_CSV';
const csvOutputFile = path.join(outputDir, 'MNQ_Trade_Entries_LastYear.csv');

// Create output directory
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`Created output directory: ${outputDir}`);
}

// Trade log array to store all entries
let tradeEntries = [];

// Load and process data
console.log("Loading MNQ data...");
const allCandles = [];

// Read the MNQ data file
fs.createReadStream(config.inputFile)
    .pipe(csv())
    .on('data', (row) => {
        // Parse the CSV row into candle format (Databento format)
        const candle = {
            timestamp: new Date(row.ts_event || row.timestamp || row.date || row.Date),
            open: parseFloat(row.open || row.Open),
            high: parseFloat(row.high || row.High),
            low: parseFloat(row.low || row.Low),
            close: parseFloat(row.close || row.Close),
            volume: parseInt(row.volume || row.Volume || 0),
            symbol: row.symbol || 'MNQ'
        };

        // Skip invalid rows
        if (isNaN(candle.open) || isNaN(candle.close) || !candle.timestamp || isNaN(candle.timestamp.getTime())) {
            return;
        }

        // Filter for MNQ symbols only and last year
        if (candle.symbol && candle.symbol.startsWith('MNQ')) {
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

            if (candle.timestamp >= oneYearAgo) {
                allCandles.push(candle);
            }
        }
    })
    .on('end', () => {
        console.log(`Loaded ${allCandles.length} candles for the last year`);

        if (allCandles.length === 0) {
            console.error("No candles loaded. Check the input file path and format.");
            return;
        }

        // Process candles to find trade entries
        processTradeEntries();
    })
    .on('error', (error) => {
        console.error("Error reading CSV file:", error);
    });

// Function to process candles and identify trade entries
function processTradeEntries() {
    console.log("Processing candles to identify trade entries...");

    // Need at least 50 candles for indicators
    if (allCandles.length < 50) {
        console.error("Not enough candles for analysis");
        return;
    }

    // Process each candle starting from index 50 (after indicator warmup)
    for (let i = 50; i < allCandles.length; i++) {
        const candle = allCandles[i];

        // Calculate indicators for this candle
        const indicators = calculateIndicators(i);

        // Check for trade signals using the same logic as backtest.js
        const signal = checkForTradeSignal(candle, indicators, i);

        if (signal) {
            // Convert timestamp to NYC EST
            const nycTime = convertToNYCTime(candle.timestamp);

            tradeEntries.push({
                timestamp: nycTime,
                direction: signal.direction,
                price: candle.close,
                rsi: indicators.rsi,
                rsiMa: indicators.rsiMa,
                atr: indicators.atr
            });

            console.log(`Trade found: ${nycTime} ${signal.direction} at ${candle.close}`);
        }
    }

    // Generate CSV file
    generateCSV();
}

// Calculate indicators (same logic as backtest.js)
function calculateIndicators(index) {
    const candles = allCandles.slice(0, index + 1);

    // RSI calculation
    const rsi = calculateRSI(candles, config.rsiPeriod);
    const rsiMa = calculateRSISMA(candles, config.rsiPeriod, config.rsiMaPeriod);

    // ATR calculation
    const atr = calculateATR(candles, config.atrPeriod);

    // WMA calculation (if used)
    const wma50 = config.wma50Period > 0 ? calculateWMA(candles, config.wma50Period) : 0;

    return {
        rsi,
        rsiMa,
        atr,
        wma50
    };
}

// Check for trade signal (simplified version of backtest.js logic)
function checkForTradeSignal(candle, indicators, index) {
    // Need at least 4 candles for pattern detection
    if (index < 4) return null;

    const candles = allCandles.slice(index - 3, index + 1); // Last 4 candles

    // Check for 3-candle and 4-candle patterns
    const pattern3 = detect3CandlePattern(candles.slice(-3));
    const pattern4 = detect4CandlePattern(candles);

    let signal = null;

    // Check 3-candle pattern
    if (pattern3) {
        if (pattern3 === 'bullish' && indicators.rsi > indicators.rsiMa) {
            signal = { direction: 'long', pattern: '3-candle' };
        } else if (pattern3 === 'bearish' && indicators.rsi < indicators.rsiMa) {
            signal = { direction: 'short', pattern: '3-candle' };
        }
    }

    // Check 4-candle pattern (if no 3-candle signal)
    if (!signal && pattern4) {
        if (pattern4 === 'bullish' && indicators.rsi > indicators.rsiMa) {
            signal = { direction: 'long', pattern: '4-candle' };
        } else if (pattern4 === 'bearish' && indicators.rsi < indicators.rsiMa) {
            signal = { direction: 'short', pattern: '4-candle' };
        }
    }

    return signal;
}

// 3-candle pattern detection (same as demo bot)
function detect3CandlePattern(candles) {
    if (candles.length !== 3) return null;

    const [c1, c2, c3] = candles;
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    // Bullish pattern: green-red-green
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        const c2BodyHigh = Math.max(c2.open, c2.close);
        if (c3.close > c2BodyHigh) {
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-red
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        const c2BodyLow = Math.min(c2.open, c2.close);
        if (c3.close < c2BodyLow) {
            return 'bearish';
        }
    }

    return null;
}

// 4-candle pattern detection (same as demo bot)
function detect4CandlePattern(candles) {
    if (candles.length !== 4) return null;

    const [c0, c1, c2, c3] = candles;
    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    // Bullish pattern: green-red-red-green
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        const c1BodyHigh = Math.max(c1.open, c1.close);
        const c2BodyHigh = Math.max(c2.open, c2.close);
        const highestRedBody = Math.max(c1BodyHigh, c2BodyHigh);

        if (c3.close > highestRedBody) {
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-green-red
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        const c1BodyLow = Math.min(c1.open, c1.close);
        const c2BodyLow = Math.min(c2.open, c2.close);
        const lowestGreenBody = Math.min(c1BodyLow, c2BodyLow);

        if (c3.close < lowestGreenBody) {
            return 'bearish';
        }
    }

    return null;
}

// Determine candle color
function candlestickColor(candle) {
    if (candle.close > candle.open) {
        return 'green';
    } else if (candle.close < candle.open) {
        return 'red';
    } else {
        return 'doji';
    }
}

// Convert timestamp to NYC EST
function convertToNYCTime(utcTimestamp) {
    const date = new Date(utcTimestamp);

    // Convert to EST (UTC-5) or EDT (UTC-4) depending on daylight saving
    const estOffset = -5; // EST is UTC-5
    const edtOffset = -4; // EDT is UTC-4

    // Simple daylight saving check (March to November)
    const month = date.getUTCMonth();
    const isDST = month >= 2 && month <= 10; // Rough DST period

    const offset = isDST ? edtOffset : estOffset;
    const nycTime = new Date(date.getTime() + (offset * 60 * 60 * 1000));

    // Format as YYYY-MM-DD HH:MM:SS
    return nycTime.toISOString().slice(0, 19).replace('T', ' ');
}

// Generate CSV file
function generateCSV() {
    console.log(`\nGenerating CSV file with ${tradeEntries.length} trade entries...`);

    if (tradeEntries.length === 0) {
        console.log("No trade entries found.");
        return;
    }

    // Create CSV content
    let csvContent = 'timestamp,direction\n';

    tradeEntries.forEach(entry => {
        csvContent += `${entry.timestamp},${entry.direction}\n`;
    });

    // Write CSV file
    fs.writeFileSync(csvOutputFile, csvContent);

    console.log(`\n=== CSV GENERATION COMPLETE ===`);
    console.log(`File saved: ${csvOutputFile}`);
    console.log(`Total trade entries: ${tradeEntries.length}`);

    // Show sample entries
    console.log(`\nSample entries:`);
    tradeEntries.slice(0, 10).forEach(entry => {
        console.log(`${entry.timestamp},${entry.direction}`);
    });

    if (tradeEntries.length > 10) {
        console.log(`... and ${tradeEntries.length - 10} more entries`);
    }
}

// ===== INDICATOR CALCULATION FUNCTIONS =====

// Calculate RSI
function calculateRSI(candles, period) {
    if (candles.length <= period) return 50;

    const changes = [];
    for (let i = 1; i < candles.length; i++) {
        changes.push(candles[i].close - candles[i - 1].close);
    }

    let avgGain = 0;
    let avgLoss = 0;

    // Initial calculation
    for (let i = 0; i < period; i++) {
        const change = changes[i];
        if (change >= 0) {
            avgGain += change;
        } else {
            avgLoss += Math.abs(change);
        }
    }

    avgGain /= period;
    avgLoss /= period;

    // Smoothed calculation for remaining periods
    for (let i = period; i < changes.length; i++) {
        const change = changes[i];
        if (change >= 0) {
            avgGain = (avgGain * (period - 1) + change) / period;
            avgLoss = (avgLoss * (period - 1)) / period;
        } else {
            avgGain = (avgGain * (period - 1)) / period;
            avgLoss = (avgLoss * (period - 1) + Math.abs(change)) / period;
        }
    }

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

// Calculate RSI SMA
function calculateRSISMA(candles, rsiPeriod, smaPeriod) {
    if (candles.length < rsiPeriod + smaPeriod) return 50;

    const rsiValues = [];

    // Calculate RSI for each period
    for (let i = rsiPeriod; i <= candles.length; i++) {
        const subset = candles.slice(0, i);
        rsiValues.push(calculateRSI(subset, rsiPeriod));
    }

    // Calculate SMA of RSI values
    if (rsiValues.length < smaPeriod) return 50;

    const recentRsiValues = rsiValues.slice(-smaPeriod);
    const sum = recentRsiValues.reduce((a, b) => a + b, 0);
    return sum / smaPeriod;
}

// Calculate ATR
function calculateATR(candles, period) {
    if (candles.length <= period) return 0;

    let trSum = 0;

    for (let i = 1; i <= period; i++) {
        const current = candles[candles.length - i];
        const previous = candles[candles.length - i - 1];

        const tr1 = current.high - current.low;
        const tr2 = Math.abs(current.high - previous.close);
        const tr3 = Math.abs(current.low - previous.close);

        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
    }

    return trSum / period;
}

// Calculate WMA
function calculateWMA(candles, period) {
    if (candles.length < period) return 0;

    const prices = candles.slice(-period).map(c => c.close);
    let weightedSum = 0;
    let weightSum = 0;

    for (let i = 0; i < prices.length; i++) {
        const weight = i + 1;
        weightedSum += prices[i] * weight;
        weightSum += weight;
    }

    return weightedSum / weightSum;
}
