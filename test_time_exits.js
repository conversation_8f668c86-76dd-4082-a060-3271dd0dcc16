const fs = require('fs');
const path = require('path');
const config = require('./config.js');

// Test different time-based exit strategies
const timeExitTests = [
    // Quick scalp tests
    { name: "1-Candle Exit", maxTime: 1, tp: 3, description: "Exit after 1 candle, 3pt TP" },
    { name: "2-Candle Exit", maxTime: 2, tp: 3, description: "Exit after 2 candles, 3pt TP" },
    { name: "3-Candle Exit", maxTime: 3, tp: 5, description: "Exit after 3 candles, 5pt TP" },
    
    // Short time tests
    { name: "5-Min Exit", maxTime: 5, tp: 5, description: "5 minute max, 5pt TP" },
    { name: "10-Min Exit", maxTime: 10, tp: 7, description: "10 minute max, 7pt TP" },
    { name: "15-Min Exit", maxTime: 15, tp: 10, description: "15 minute max, 10pt TP" },
    
    // Medium time tests  
    { name: "25-Min Exit", maxTime: 25, tp: 10, description: "25 minute max, 10pt TP" },
    { name: "30-Min Exit", maxTime: 30, tp: 15, description: "30 minute max, 15pt TP" },
];

console.log("🕒 TIME-BASED EXIT STRATEGY TESTER");
console.log("==================================");
console.log("Testing different time limits for scalping exits...\n");

// Display test matrix
console.log("📋 TEST MATRIX:");
console.log("===============");
timeExitTests.forEach((test, i) => {
    console.log(`${i + 1}. ${test.name}: ${test.description}`);
});

console.log("\n💡 RECOMMENDATIONS:");
console.log("===================");

console.log("\n🚀 ULTRA-QUICK SCALPS (1-3 candles):");
console.log("   • 1-2 candles: Immediate exit after pattern completion");
console.log("   • 3 candles: Quick confirmation then exit");
console.log("   • Pros: Minimal exposure, quick profits");
console.log("   • Cons: May miss larger moves");

console.log("\n⚡ SHORT SCALPS (5-15 minutes):");
console.log("   • 5-10 minutes: Classic scalping timeframe");
console.log("   • 15 minutes: Allow for small trend development");
console.log("   • Pros: Balance of speed and profit potential");
console.log("   • Cons: Still vulnerable to quick reversals");

console.log("\n🎯 MEDIUM SCALPS (25-30 minutes):");
console.log("   • 25-30 minutes: Your suggested maximum");
console.log("   • Pros: Allow trends to develop, avoid noise");
console.log("   • Cons: Longer exposure to market risk");

console.log("\n🔍 TESTING STRATEGY:");
console.log("====================");
console.log("1. Start with 1-3 candle exits (immediate scalps)");
console.log("2. Test 5-10 minute exits (classic scalping)");
console.log("3. Compare win rates and profit per trade");
console.log("4. Find the sweet spot for your patterns");

console.log("\n⚙️ IMPLEMENTATION OPTIONS:");
console.log("===========================");

console.log("\n📝 Option 1: Immediate Exit (1-3 candles)");
console.log("   config.useImmediateExit = true");
console.log("   config.immediateCandleExit = 1  // or 2, 3");
console.log("   config.fixedTpPointsGrid = [3, 5]");

console.log("\n📝 Option 2: Time-Based Exit (5-30 minutes)");
console.log("   config.useImmediateExit = false");
console.log("   config.maxTimeExitMinutes = 10  // or 5, 15, 25, 30");
console.log("   config.fixedTpPointsGrid = [5, 7, 10]");

console.log("\n📝 Option 3: Hybrid (Quick TP + Time Backup)");
console.log("   config.fixedTpPointsGrid = [3, 5]  // Quick TP");
console.log("   config.maxTimeExitMinutes = 10     // Time backup");

console.log("\n🎲 WHICH TEST WOULD YOU LIKE TO RUN?");
console.log("=====================================");
console.log("1. Ultra-quick (1-3 candles)");
console.log("2. Short scalps (5-15 minutes)");
console.log("3. Medium scalps (25-30 minutes)");
console.log("4. All tests (comprehensive)");

console.log("\n💭 YOUR THOUGHTS:");
console.log("=================");
console.log("• You mentioned not wanting to hold too long");
console.log("• 1-3 candle exits might be perfect for your style");
console.log("• Quick 3-5 point TPs with immediate exits");
console.log("• This matches your scalping approach");

console.log("\n🚀 READY TO TEST!");
console.log("==================");
console.log("Choose your preferred test and I'll update the config!");

// Function to update config for specific test
function updateConfigForTest(testType) {
    const configPath = path.join(__dirname, 'config.js');
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    switch(testType) {
        case 'immediate':
            configContent = configContent.replace(
                /useImmediateExit: false/,
                'useImmediateExit: true'
            );
            configContent = configContent.replace(
                /immediateCandleExit: 1/,
                'immediateCandleExit: 1'
            );
            configContent = configContent.replace(
                /fixedTpPointsGrid: \[.*?\]/,
                'fixedTpPointsGrid: [3, 5]'
            );
            break;
            
        case 'short':
            configContent = configContent.replace(
                /useImmediateExit: true/,
                'useImmediateExit: false'
            );
            configContent = configContent.replace(
                /maxTimeExitMinutes: \[.*?\]/,
                'maxTimeExitMinutes: [5, 10, 15]'
            );
            configContent = configContent.replace(
                /fixedTpPointsGrid: \[.*?\]/,
                'fixedTpPointsGrid: [5, 7, 10]'
            );
            break;
            
        case 'medium':
            configContent = configContent.replace(
                /useImmediateExit: true/,
                'useImmediateExit: false'
            );
            configContent = configContent.replace(
                /maxTimeExitMinutes: \[.*?\]/,
                'maxTimeExitMinutes: [25, 30]'
            );
            configContent = configContent.replace(
                /fixedTpPointsGrid: \[.*?\]/,
                'fixedTpPointsGrid: [10, 15]'
            );
            break;
    }
    
    fs.writeFileSync(configPath, configContent);
    console.log(`✅ Config updated for ${testType} test!`);
}

// Export for use in other scripts
module.exports = {
    timeExitTests,
    updateConfigForTest
};
