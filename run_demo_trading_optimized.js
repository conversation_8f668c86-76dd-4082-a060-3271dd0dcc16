/**
 * Optimized Demo Trading Bot Runner
 *
 * This script provides a command-line interface for controlling the optimized demo trading bot.
 */

const readline = require('readline');
const demoTrading = require('./demo_trading_optimized');
const logger = require('./data_logger');
const fs = require('fs');
const path = require('path');

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '> '
});

// State
let isRunning = false;
let isShuttingDown = false;

// Command history
const commandHistory = [];
const MAX_HISTORY_SIZE = 50;

// Help text
const helpText = `
=== Optimized Trading Bot Commands ===
start - Start the trading bot
stop - Stop the trading bot
status - Check the status of the trading bot
orders - List all active orders
positions - List all open positions
logs - Show recent logs
stats - Show performance statistics
cache - Show cache statistics
debug-chart - Show chart data cache
contracts - List available contracts
mode - Show current trading mode (demo/live)
use-demo - Switch to demo trading mode
use-live - Switch to live trading mode (requires account ID)
set-live-account - Set live account ID
clear - Clear the console
exit - Exit the program
help - Show this help message
`;

// Welcome message
console.log('INFO: Automated log backup enabled');
console.log('=== Optimized Trading Bot CLI ===');
console.log('Type "help" for a list of available commands');
console.log('Currently in DEMO mode. Type "use-live" to switch to live trading mode.');
rl.prompt();

// Handle commands
rl.on('line', async (line) => {
    const command = line.trim();

    // Add command to history
    if (command && command !== commandHistory[0]) {
        commandHistory.unshift(command);
        if (commandHistory.length > MAX_HISTORY_SIZE) {
            commandHistory.pop();
        }
    }

    // Process command
    try {
        if (commands[command]) {
            await commands[command]();
        } else if (command) {
            console.log(`Unknown command: ${command}`);
            console.log('Type "help" for a list of available commands');
        }
    } catch (error) {
        console.error(`Error executing command: ${error.message}`);
    }

    rl.prompt();
}).on('close', () => {
    console.log('Shutting down...');

    // Shutdown the bot if it's running
    if (isRunning && !isShuttingDown) {
        isShuttingDown = true;
        demoTrading.shutdown()
            .then(() => {
                console.log('Exiting...');
                process.exit(0);
            })
            .catch((error) => {
                console.error(`Error shutting down: ${error.message}`);
                process.exit(1);
            });
    } else {
        console.log('Exiting...');
        process.exit(0);
    }
});

// Command handlers
const commands = {
    start: async () => {
        if (isRunning) {
            console.log('Demo trading bot is already running');
            return;
        }

        console.log('Starting demo trading bot...');

        try {
            const result = await demoTrading.initialize();

            if (result) {
                isRunning = true;
                console.log('Demo trading bot started successfully');
            } else {
                console.error('ERROR: Failed to initialize demo trading bot');
                console.log('Failed to start demo trading bot');
            }
        } catch (error) {
            console.error(`ERROR: ${error.message}`);
            console.log('Failed to start demo trading bot');
        }
    },

    stop: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }

        console.log('Stopping demo trading bot...');

        try {
            isShuttingDown = true;
            const result = await demoTrading.shutdown();

            if (result) {
                isRunning = false;
                isShuttingDown = false;
                console.log('Demo trading bot stopped successfully');
            } else {
                isShuttingDown = false;
                console.error('ERROR: Failed to shutdown demo trading bot');
                console.log('Failed to stop demo trading bot');
            }
        } catch (error) {
            isShuttingDown = false;
            console.error(`ERROR: ${error.message}`);
            console.log('Failed to stop demo trading bot');
        }
    },

    status: async () => {
        if (!isRunning) {
            console.log('Demo trading bot status: STOPPED');
            return;
        }

        try {
            const status = demoTrading.getStatus();

            console.log('\n=== Demo Trading Bot Status ===');
            console.log(`Connection: ${status.isConnected ? 'CONNECTED' : 'DISCONNECTED'}`);
            console.log(`Initialization: ${status.isInitialized ? 'INITIALIZED' : 'NOT INITIALIZED'}`);
            console.log(`Active Positions: ${status.activePositions}`);
            console.log(`Active Orders: ${status.activeOrders}`);
            console.log('\nCircuit Breakers:');
            console.log(`  Status: ${status.circuitBreakers.isTripped ? 'TRIPPED' : 'NORMAL'}`);
            console.log(`  Consecutive Losses: ${status.circuitBreakers.consecutiveLossCount}`);
            console.log(`  Daily Loss: $${status.circuitBreakers.dailyLoss.toFixed(2)}`);

            if (status.circuitBreakers.lastResetTime) {
                console.log(`  Last Reset: ${status.circuitBreakers.lastResetTime.toLocaleString()}`);
            }
        } catch (error) {
            console.error(`Error getting status: ${error.message}`);
        }
    },

    orders: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }

        try {
            const orders = demoTrading.getOrders();

            console.log('\n=== Active Orders ===');

            if (Object.keys(orders).length === 0) {
                console.log('No active orders');
            } else {
                console.log('ID\t\tSymbol\tAction\tQty\tPrice\tStatus');
                console.log('----------------------------------------------------------');

                for (const orderId in orders) {
                    const order = orders[orderId];
                    console.log(`${orderId}\t${order.symbol}\t${order.action}\t${order.quantity}\t${order.entryPrice}\t${order.status || 'Pending'}`);
                }
            }
        } catch (error) {
            console.error(`Error getting orders: ${error.message}`);
        }
    },

    positions: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }

        try {
            const positions = demoTrading.getPositions();

            console.log('\n=== Active Positions ===');

            if (Object.keys(positions).length === 0) {
                console.log('No active positions');
            } else {
                console.log('ID\t\tSymbol\tAction\tQty\tEntry\tStop\tTarget');
                console.log('----------------------------------------------------------');

                for (const positionId in positions) {
                    const position = positions[positionId];
                    console.log(`${positionId}\t${position.symbol}\t${position.action}\t${position.quantity}\t${position.entryPrice}\t${position.stopLossPrice}\t${position.takeProfitPrice}`);
                }
            }
        } catch (error) {
            console.error(`Error getting positions: ${error.message}`);
        }
    },

    logs: async () => {
        try {
            const logFile = path.join(__dirname, 'logs', 'system.log');

            if (!fs.existsSync(logFile)) {
                console.log('No log file found');
                return;
            }

            // Read the last 20 lines of the log file
            const logs = fs.readFileSync(logFile, 'utf8').split('\n').slice(-20);

            console.log('\n=== Recent Logs ===');
            logs.forEach(log => {
                if (log.trim()) {
                    console.log(log);
                }
            });
        } catch (error) {
            console.error(`Error reading logs: ${error.message}`);
        }
    },

    stats: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }

        try {
            const status = demoTrading.getStatus();

            console.log('\n=== Performance Statistics ===');
            console.log('Note: Statistics will be available after trading activity');

            // In a real implementation, you would collect and display performance statistics here
            console.log('\nTrading Activity:');
            console.log(`  Active Positions: ${status.activePositions}`);
            console.log(`  Active Orders: ${status.activeOrders}`);

            console.log('\nRisk Management:');
            console.log(`  Circuit Breaker Status: ${status.circuitBreakers.isTripped ? 'TRIPPED' : 'NORMAL'}`);
            console.log(`  Consecutive Losses: ${status.circuitBreakers.consecutiveLossCount}`);
            console.log(`  Daily Loss: $${status.circuitBreakers.dailyLoss.toFixed(2)}`);
        } catch (error) {
            console.error(`Error getting statistics: ${error.message}`);
        }
    },

    cache: async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }

        try {
            const status = demoTrading.getStatus();

            console.log('\n=== Cache Statistics ===');

            if (status.cacheStats) {
                console.log('\nContract Cache:');
                console.log(`  Size: ${status.cacheStats.contractCache.size}`);
                console.log(`  Hits: ${status.cacheStats.contractCache.hits}`);
                console.log(`  Misses: ${status.cacheStats.contractCache.misses}`);
                console.log(`  Hit Rate: ${(status.cacheStats.contractCache.hitRate * 100).toFixed(2)}%`);

                console.log('\nChart Data Cache:');
                console.log(`  Size: ${status.cacheStats.chartDataCache.size}`);
                console.log(`  Hits: ${status.cacheStats.chartDataCache.hits}`);
                console.log(`  Misses: ${status.cacheStats.chartDataCache.misses}`);
                console.log(`  Hit Rate: ${(status.cacheStats.chartDataCache.hitRate * 100).toFixed(2)}%`);
            } else {
                console.log('No cache statistics available');
            }
        } catch (error) {
            console.error(`Error getting cache statistics: ${error.message}`);
        }
    },

    clear: async () => {
        console.clear();
        console.log('=== Optimized Demo Trading Bot CLI ===');
        console.log('Type "help" for a list of available commands');
    },

    contracts: async () => {
        console.log('Listing available contracts...');

        try {
            // Initialize the API if not already running
            if (!isRunning) {
                console.log('Initializing API for contract listing...');
                await demoTrading.initializeApi();
            }

            // List available contracts
            await demoTrading.listContracts();

            console.log('Contract listing complete');
        } catch (error) {
            console.error(`Error listing contracts: ${error.message}`);
        }
    },

    mode: async () => {
        try {
            const mode = demoTrading.getTradingMode();
            console.log(`\nCurrent trading mode: ${mode.name.toUpperCase()}`);
            console.log(`Account ID: ${mode.accountId || 'Not set'}`);
            console.log(`Position size: ${mode.positionSize} contracts`);
            console.log(`Requires confirmation: ${mode.requireConfirmation ? 'Yes' : 'No'}`);
        } catch (error) {
            console.error(`Error getting trading mode: ${error.message}`);
        }
    },

    'use-demo': async () => {
        try {
            const result = await demoTrading.setTradingMode('demo');
            if (result) {
                console.log('\nSwitched to DEMO trading mode');
                console.log('Using demo account ID: 4690440');
                console.log('Position size: 5 contracts');
            } else {
                console.error('Failed to switch to demo mode');
            }
        } catch (error) {
            console.error(`Error switching to demo mode: ${error.message}`);
        }
    },

    'use-live': async () => {
        try {
            const liveAccountId = demoTrading.getLiveAccountId();

            if (!liveAccountId) {
                console.log('\nLive account ID is not set.');
                console.log('Please set your live account ID first using the "set-live-account" command.');
                return;
            }

            console.log('\nWARNING: You are about to switch to LIVE trading mode.');
            console.log('This will use real money and place real trades.');
            console.log(`Live account ID: ${liveAccountId}`);
            console.log('Position size: 2 contracts');

            // Switch to live mode immediately
            const result = await demoTrading.setTradingMode('live');
            if (result) {
                console.log('\nSwitched to LIVE trading mode');
                console.log(`Using live account ID: ${liveAccountId}`);
                console.log('Position size: 2 contracts');
            } else {
                console.error('Failed to switch to live mode');
            }
        } catch (error) {
            console.error(`Error switching to live mode: ${error.message}`);
        }
    },

    'set-live-account': async () => {
        console.log('\nEnter your live account ID:');

        // Set up a one-time listener for the account ID
        rl.once('line', async (input) => {
            const accountId = input.trim();
            if (accountId) {
                const result = await demoTrading.setLiveAccountId(accountId);
                if (result) {
                    console.log(`\nLive account ID set to: ${accountId}`);
                    console.log('You can now switch to live mode using the "use-live" command');
                } else {
                    console.error('Failed to set live account ID');
                }
            } else {
                console.log('Invalid account ID. Operation cancelled.');
            }
            rl.prompt();
        });

        return; // Skip the normal prompt
    },

    'debug-chart': async () => {
        if (!isRunning) {
            console.log('Demo trading bot is not running');
            return;
        }

        try {
            const marketDataService = require('./market-data-service');
            const chartDataCache = marketDataService.chartDataCache || {};
            const symbols = Object.keys(chartDataCache);

            console.log('\n=== Chart Data Cache ===');

            if (symbols.length === 0) {
                console.log('No chart data available');
                return;
            }

            for (const symbol of symbols) {
                console.log(`\nSymbol: ${symbol}`);
                const chartData = marketDataService.getChartData(symbol, '1m', 5);

                if (chartData.length === 0) {
                    console.log('  No data available');
                    continue;
                }

                console.log('  Latest 5 candles:');
                for (const candle of chartData) {
                    const date = new Date(candle.timestamp);
                    console.log(`  ${date.toISOString()} - O: ${candle.open}, H: ${candle.high}, L: ${candle.low}, C: ${candle.close}, V: ${candle.volume}`);
                }
            }
        } catch (error) {
            console.error(`Error getting chart data cache: ${error.message}`);
        }
    },

    help: async () => {
        console.log(helpText);
    },

    exit: async () => {
        rl.close();
    }
};

// Handle SIGINT (Ctrl+C)
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT. Shutting down...');
    rl.close();
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(`Uncaught Exception: ${error.message}`);
    console.error(error.stack);

    // Shutdown the bot if it's running
    if (isRunning && !isShuttingDown) {
        isShuttingDown = true;
        demoTrading.shutdown()
            .then(() => {
                console.log('Exiting due to uncaught exception...');
                process.exit(1);
            })
            .catch((shutdownError) => {
                console.error(`Error shutting down: ${shutdownError.message}`);
                process.exit(1);
            });
    } else {
        console.log('Exiting due to uncaught exception...');
        process.exit(1);
    }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Promise Rejection:');
    console.error(reason);

    // Don't exit, just log the error
});
