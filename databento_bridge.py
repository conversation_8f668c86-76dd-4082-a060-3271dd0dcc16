#!/usr/bin/env python3
"""
Databento Bridge - Low-latency IPC bridge between Node.js and Databento Python client

This script:
1. Connects to Databento using the official Python client
2. Listens for commands from Node.js via stdin
3. Sends data back to Node.js via stdout
4. Handles both historical and real-time data
"""

import sys
import json
import time
import logging
import threading
import signal
import random
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Configure logging to file to avoid interfering with stdout
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='databento_bridge.log'
)
logger = logging.getLogger(__name__)

# Try to import databento
try:
    import databento as db
    logger.info(f"Successfully imported databento version: {db.__version__}")
except ImportError:
    logger.error("Failed to import databento. Please install it with: pip install databento")
    print(json.dumps({"type": "error", "message": "Failed to import databento. Please install it with: pip install databento"}))
    sys.exit(1)

# Global variables
API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq"  # Default API key, can be overridden
historical_client = None
live_client = None
live_running = False
subscriptions = {}
command_handlers = {}

def init_historical_client():
    """Initialize the historical client."""
    global historical_client
    if historical_client is None:
        logger.info("Initializing historical client...")
        historical_client = db.Historical(key=API_KEY)
        logger.info("Historical client initialized")
    return historical_client

def init_live_client():
    """Initialize the live client."""
    global live_client
    if live_client is None:
        logger.info("Initializing live client...")
        live_client = db.Live(key=API_KEY)
        logger.info("Live client initialized")
    return live_client

def send_response(response_type: str, data: Any, request_id: Optional[str] = None):
    """Send a response back to Node.js."""
    response = {
        "type": response_type,
        "data": data
    }
    if request_id:
        response["request_id"] = request_id

    try:
        # Try to serialize the response to JSON
        json_response = json.dumps(response)

        # Send the response as a single line of JSON
        print(json_response, flush=True)
    except TypeError as e:
        # If we can't serialize the data, try to convert it to a string
        logger.error(f"Error serializing response: {str(e)}")

        # Create a new response with the error message
        error_response = {
            "type": "error",
            "data": {
                "message": f"Error serializing response: {str(e)}",
                "original_type": response_type
            }
        }
        if request_id:
            error_response["request_id"] = request_id

        # Send the error response
        print(json.dumps(error_response), flush=True)

def handle_get_datasets(params: Dict, request_id: Optional[str] = None):
    """Handle the get_datasets command."""
    try:
        client = init_historical_client()
        datasets = client.metadata.list_datasets()

        # Convert datasets to a list of strings
        dataset_list = [str(d) for d in datasets]

        send_response("datasets", dataset_list, request_id)
    except Exception as e:
        logger.error(f"Error getting datasets: {str(e)}")
        send_response("error", {"message": f"Error getting datasets: {str(e)}"}, request_id)

def handle_get_historical(params: Dict, request_id: Optional[str] = None):
    """Handle the get_historical command."""
    try:
        client = init_historical_client()

        # Parse parameters
        dataset = params.get("dataset", "GLBX.MDP3")
        symbols = params.get("symbols", "ES.FUT")
        schema_param = params.get("schema", "trades")
        stype_in = params.get("stype_in", "parent")

        # Map schema parameter to valid Databento schema
        # Valid schemas: 'mbo', 'mbp-1', 'mbp-10', 'tbbo', 'trades', 'ohlcv-1s', 'ohlcv-1m', 'ohlcv-1h', 'ohlcv-1d'
        schema_map = {
            "ohlcv-1": "ohlcv-1m",  # Map ohlcv-1 to ohlcv-1m
            "ohlcv": "ohlcv-1m",    # Map ohlcv to ohlcv-1m
            "trades": "trades",      # Keep trades as is
            "mbp-1": "mbp-1",        # Keep mbp-1 as is
            "mbp-10": "mbp-10"       # Keep mbp-10 as is
        }

        schema = schema_map.get(schema_param, schema_param)  # Use the provided schema if it's valid

        # Validate schema against known valid schemas
        valid_schemas = ['mbo', 'mbp-1', 'mbp-10', 'tbbo', 'trades', 'ohlcv-1s', 'ohlcv-1m', 'ohlcv-1h', 'ohlcv-1d',
                         'definition', 'statistics', 'status', 'imbalance', 'ohlcv-eod', 'cmbp-1', 'cbbo-1s',
                         'cbbo-1m', 'tcbbo', 'bbo-1s', 'bbo-1m']

        # For historical data, we can use ohlcv-1m
        if schema not in valid_schemas:
            schema = "ohlcv-1m"  # Default to ohlcv-1m for historical data

        # Parse dates
        start_str = params.get("start")
        end_str = params.get("end")

        if start_str:
            start = datetime.fromisoformat(start_str.replace('Z', '+00:00'))
        else:
            start = datetime.now() - timedelta(days=1)

        if end_str:
            end = datetime.fromisoformat(end_str.replace('Z', '+00:00'))
        else:
            end = datetime.now()

        logger.info(f"Getting historical data for {symbols} from {start} to {end} with schema {schema}")

        # Get historical data
        data = client.timeseries.get_range(
            dataset=dataset,
            symbols=symbols,
            start=start,
            end=end,
            schema=schema,
            stype_in=stype_in
        )

        # Convert data to a list of dictionaries
        records = []

        # For debugging
        logger.info(f"Data type: {type(data)}")
        logger.info(f"Data length: {len(data) if hasattr(data, '__len__') else 'unknown'}")

        try:
            # Try to iterate through the data
            for record in data:
                # Manually convert record to a dictionary
                record_dict = {}

                # For debugging
                logger.info(f"Record type: {type(record)}")

                # Get all attributes of the record
                for attr in dir(record):
                    # Skip private attributes and methods
                    if attr.startswith('_') or callable(getattr(record, attr)):
                        continue

                    try:
                        # Get the attribute value
                        value = getattr(record, attr)

                        # Handle nested objects
                        if hasattr(value, '__dict__'):
                            # Convert nested object to dictionary
                            nested_dict = {}
                            for nested_attr in dir(value):
                                if nested_attr.startswith('_') or callable(getattr(value, nested_attr)):
                                    continue
                                try:
                                    nested_value = getattr(value, nested_attr)
                                    # Skip methods and complex objects
                                    if not callable(nested_value) and (
                                        isinstance(nested_value, (int, float, str, bool, type(None))) or
                                        nested_value is None
                                    ):
                                        nested_dict[nested_attr] = nested_value
                                except Exception as e:
                                    logger.error(f"Error getting nested attribute {nested_attr}: {str(e)}")
                            record_dict[attr] = nested_dict
                        # Skip methods and complex objects
                        elif not callable(value) and (
                            isinstance(value, (int, float, str, bool, type(None))) or
                            value is None
                        ):
                            record_dict[attr] = value
                    except Exception as e:
                        logger.error(f"Error getting attribute {attr}: {str(e)}")

                # Add record type
                record_dict['record_type'] = record.__class__.__name__

                # Convert timestamps to ISO format strings
                if hasattr(record, 'ts_event'):
                    ts_event = getattr(record, 'ts_event')
                    if isinstance(ts_event, int):
                        record_dict['ts_event'] = datetime.fromtimestamp(ts_event / 1e9).isoformat()

                if hasattr(record, 'ts_recv'):
                    ts_recv = getattr(record, 'ts_recv')
                    if isinstance(ts_recv, int):
                        record_dict['ts_recv'] = datetime.fromtimestamp(ts_recv / 1e9).isoformat()

                # Add basic OHLCV fields if available
                for field in ['open', 'high', 'low', 'close', 'volume']:
                    if hasattr(record, field):
                        record_dict[field] = getattr(record, field)

                records.append(record_dict)
        except Exception as e:
            logger.error(f"Error iterating through data: {str(e)}")

            # Try to extract basic information
            if hasattr(data, 'schema'):
                records.append({
                    'record_type': 'DataInfo',
                    'schema': getattr(data, 'schema', 'unknown'),
                    'message': 'Could not iterate through data'
                })

        send_response("historical", records, request_id)
    except Exception as e:
        logger.error(f"Error getting historical data: {str(e)}")
        send_response("error", {"message": f"Error getting historical data: {str(e)}"}, request_id)

import databento as db

def live_data_callback(record):
    """Callback for live data."""
    try:
        # Create a simplified record dictionary based on record type
        record_dict = {}

        # Get the record type
        record_type = type(record).__name__ if hasattr(record, '__class__') else "Unknown"

        # Process based on record type
        if record_type == "TradeMsg":
            # This is what we want - a trade message
            # TradeMsg objects don't have a symbol attribute, but they have instrument_id
            instrument_id = int(record.instrument_id) if hasattr(record, 'instrument_id') else 0
            price = float(record.price) if hasattr(record, 'price') else 0.0
            size = float(record.size) if hasattr(record, 'size') else 0.0
            side = str(record.side) if hasattr(record, 'side') else 'unknown'

            # Convert timestamp to ISO format
            ts_event = datetime.fromtimestamp(record.ts_event / 1e9).isoformat() if hasattr(record, 'ts_event') else datetime.now().isoformat()

            # Look up the symbol from the instrument_id
            symbol = 'unknown'
            if hasattr(live_data_callback, 'instrument_id_to_symbol_map') and instrument_id in live_data_callback.instrument_id_to_symbol_map:
                symbol = live_data_callback.instrument_id_to_symbol_map[instrument_id]

            # If we don't have a symbol mapping, use a hardcoded one for known instrument IDs
            if symbol == 'unknown':
                if instrument_id == 4916:
                    symbol = 'ESM5'  # ES May 2025
                elif instrument_id == 42005804:
                    symbol = 'NQM5'  # NQ May 2025
                elif instrument_id == 287:
                    symbol = 'GCM5'  # GC May 2025
                elif instrument_id == 6188:
                    symbol = 'RTYM5'  # RTY May 2025

            # Log the trade data with the resolved symbol
            logger.info(f"TRADE DATA: symbol={symbol} instrument_id={instrument_id} price={price} size={size} side={side}")

            # Create a simplified trade record
            record_dict = {
                'record_type': 'TradeMsg',
                'symbol': symbol,
                'price': price,
                'size': size,
                'side': side,
                'instrument_id': instrument_id,
                'ts_event': ts_event
            }

            # Update the last trade time
            if not hasattr(live_data_callback, 'last_trade_time'):
                live_data_callback.last_trade_time = time.time()
            live_data_callback.last_trade_time = time.time()

            # CRITICAL: Send market data format for candle building
            # The bot expects market data in a specific format to build candles
            market_data = {
                'symbol': symbol,
                'price': price,
                'size': size,
                'volume': size,
                'timestamp': ts_event,
                'side': side,
                'instrument_id': instrument_id,
                'type': 'trade'
            }

            # Send market data for candle building
            send_response("market_data", market_data)

        elif record_type == "SymbolMappingMsg":
            # Symbol mapping message - store for future reference
            symbol = str(record.symbol) if hasattr(record, 'symbol') else 'unknown'
            instrument_id = int(record.instrument_id) if hasattr(record, 'instrument_id') else 0

            logger.info(f"Symbol mapping: {symbol} -> {instrument_id}")

            # Initialize the mapping dictionaries if they don't exist
            if not hasattr(live_data_callback, 'symbol_to_instrument_id_map'):
                live_data_callback.symbol_to_instrument_id_map = {}
            if not hasattr(live_data_callback, 'instrument_id_to_symbol_map'):
                live_data_callback.instrument_id_to_symbol_map = {}

            # Store both ways for easy lookup
            live_data_callback.symbol_to_instrument_id_map[symbol] = instrument_id
            live_data_callback.instrument_id_to_symbol_map[instrument_id] = symbol

            record_dict = {
                'record_type': 'SymbolMappingMsg',
                'symbol': symbol,
                'instrument_id': instrument_id,
                'stype_in_symbol': str(record.stype_in_symbol) if hasattr(record, 'stype_in_symbol') else 'unknown'
            }

        elif record_type == "ErrorMsg":
            # Error message - log it
            error_msg = str(record.err) if hasattr(record, 'err') else "Unknown error"
            logger.error(f"Databento error: {error_msg}")

            record_dict = {
                'record_type': 'ErrorMsg',
                'message': error_msg,
                'timestamp': datetime.now().isoformat()
            }

        else:
            # Other message types - just log basic info
            logger.info(f"Received {record_type} message")

            # Create a basic record with all available attributes
            record_dict = {
                'record_type': record_type,
                'timestamp': datetime.now().isoformat()
            }

            # Add all available attributes
            for attr in dir(record):
                if not attr.startswith('_') and not callable(getattr(record, attr)):
                    try:
                        value = getattr(record, attr)
                        # Try to convert to a simple type
                        if isinstance(value, (int, float, str, bool)):
                            record_dict[attr] = value
                        else:
                            record_dict[attr] = str(value)
                    except:
                        pass

        # Send the processed record to Node.js
        send_response("live_data", record_dict)

        # No simulation - we only want real market data

    except Exception as e:
        logger.error(f"Error in live data callback: {str(e)}")
        # Send a simplified error response that won't cause JSON serialization issues
        send_response("error", {
            "message": f"Error in live data callback: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })

# No simulation functions - we only want real market data

def live_error_callback(exception):
    """Callback for live data errors."""
    logger.error(f"Error in live data: {str(exception)}")
    send_response("error", {"message": f"Error in live data: {str(exception)}"})

def handle_subscribe(params: Dict, request_id: Optional[str] = None):
    """Handle the subscribe command."""
    global live_running, subscriptions

    try:
        client = init_live_client()

        # Parse parameters
        dataset = params.get("dataset", "GLBX.MDP3")
        symbols = params.get("symbols", "ES.FUT")
        schema_param = params.get("schema", "trades")
        stype_in = params.get("stype_in", "parent")

        # Map schema parameter to valid Databento schema
        schema_map = {
            "ohlcv-1": "ohlcv-1m",  # Map ohlcv-1 to ohlcv-1m
            "ohlcv": "ohlcv-1m",    # Map ohlcv to ohlcv-1m
            "trades": "trades",      # Keep trades as is - try this first
            "mbp-1": "mbp-1",        # Keep mbp-1 as is
            "mbp-10": "mbp-10"       # Keep mbp-10 as is
        }

        # Log the original schema parameter
        logger.info(f"Original schema parameter: {schema_param}")

        # Use the provided schema if it's valid, otherwise use trades
        schema = schema_map.get(schema_param, schema_param)

        # Validate schema against known valid schemas
        valid_schemas = ['mbo', 'mbp-1', 'mbp-10', 'tbbo', 'trades', 'ohlcv-1s', 'ohlcv-1m', 'ohlcv-1h', 'ohlcv-1d',
                         'definition', 'statistics', 'status', 'imbalance', 'ohlcv-eod', 'cmbp-1', 'cbbo-1s',
                         'cbbo-1m', 'tcbbo', 'bbo-1s', 'bbo-1m']

        if schema not in valid_schemas:
            schema = "trades"  # Default to trades if not a valid schema

        # Log the final schema and stype_in
        logger.info(f"Using schema: {schema} and stype_in: {stype_in}")

        # For a scalping strategy with 1-minute timeframes, the 'trades' schema is most appropriate
        # It provides every trade event which is sufficient for building candles and technical indicators
        if schema != "trades":
            logger.info(f"Overriding schema {schema} with 'trades' for high-frequency trading strategy")
            schema = "trades"

        # Create subscription key
        subscription_key = f"{dataset}:{schema}:{symbols}"

        # Check if already subscribed
        if subscription_key in subscriptions:
            send_response("subscribe", {"message": f"Already subscribed to {subscription_key}"}, request_id)
            return

        # Add callback if not already added
        if not live_running:
            client.add_callback(live_data_callback, live_error_callback)

        # Simple subscription - just like the working test
        logger.info(f"Subscribing to {symbols} in {dataset} with schema {schema} and stype_in {stype_in}")
        client.subscribe(
            dataset=dataset,
            schema=schema,
            symbols=symbols,
            stype_in=stype_in
        )
        logger.info(f"Successfully subscribed to {symbols}")

        # Start the client if not already running
        if not live_running:
            logger.info("Starting live client...")
            client.start()
            live_running = True
            logger.info("Live client started")

            # No simulation - we only want real market data
            logger.info("No simulation will be used - only real market data")

        # Store subscription
        subscriptions[subscription_key] = {
            "dataset": dataset,
            "symbols": symbols,
            "schema": schema,
            "stype_in": stype_in
        }

        send_response("subscribe", {"message": f"Subscribed to {subscription_key}"}, request_id)
    except Exception as e:
        logger.error(f"Error subscribing: {str(e)}")
        send_response("error", {"message": f"Error subscribing: {str(e)}"}, request_id)

def handle_unsubscribe(params: Dict, request_id: Optional[str] = None):
    """Handle the unsubscribe command."""
    global live_running, subscriptions, live_client

    try:
        # Parse parameters
        dataset = params.get("dataset", "GLBX.MDP3")
        symbols = params.get("symbols", "ES.FUT")
        schema = params.get("schema", "trades")

        # Create subscription key
        subscription_key = f"{dataset}:{schema}:{symbols}"

        # Check if subscribed
        if subscription_key not in subscriptions:
            send_response("unsubscribe", {"message": f"Not subscribed to {subscription_key}"}, request_id)
            return

        # Remove subscription
        del subscriptions[subscription_key]

        # If no more subscriptions, stop the client
        if not subscriptions and live_running and live_client:
            logger.info("Stopping live client...")
            live_client.stop()
            live_running = False
            logger.info("Live client stopped")

        send_response("unsubscribe", {"message": f"Unsubscribed from {subscription_key}"}, request_id)
    except Exception as e:
        logger.error(f"Error unsubscribing: {str(e)}")
        send_response("error", {"message": f"Error unsubscribing: {str(e)}"}, request_id)

def handle_set_api_key(params: Dict, request_id: Optional[str] = None):
    """Handle the set_api_key command."""
    global API_KEY, historical_client, live_client, live_running

    try:
        # Get the new API key
        new_api_key = params.get("api_key")
        if not new_api_key:
            send_response("error", {"message": "No API key provided"}, request_id)
            return

        # Stop live client if running
        if live_running and live_client:
            logger.info("Stopping live client...")
            live_client.stop()
            live_running = False
            logger.info("Live client stopped")

        # Update API key
        API_KEY = new_api_key

        # Reset clients
        historical_client = None
        live_client = None

        send_response("set_api_key", {"message": f"API key updated to {new_api_key[:8]}..."}, request_id)
    except Exception as e:
        logger.error(f"Error setting API key: {str(e)}")
        send_response("error", {"message": f"Error setting API key: {str(e)}"}, request_id)

def handle_ping(params: Dict, request_id: Optional[str] = None):
    """Handle the ping command."""
    send_response("pong", {"timestamp": datetime.now().isoformat()}, request_id)

# Register command handlers
command_handlers["get_datasets"] = handle_get_datasets
command_handlers["get_historical"] = handle_get_historical
command_handlers["subscribe"] = handle_subscribe
command_handlers["unsubscribe"] = handle_unsubscribe
command_handlers["set_api_key"] = handle_set_api_key
command_handlers["ping"] = handle_ping

def process_command(command_json: str):
    """Process a command from Node.js."""
    try:
        command = json.loads(command_json)
        command_type = command.get("type")
        params = command.get("params", {})
        request_id = command.get("request_id")

        logger.info(f"Received command: {command_type}")

        if command_type in command_handlers:
            command_handlers[command_type](params, request_id)
        else:
            logger.error(f"Unknown command: {command_type}")
            send_response("error", {"message": f"Unknown command: {command_type}"}, request_id)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON: {command_json}")
        send_response("error", {"message": f"Invalid JSON: {command_json}"})
    except Exception as e:
        logger.error(f"Error processing command: {str(e)}")
        send_response("error", {"message": f"Error processing command: {str(e)}"})

def cleanup():
    """Clean up resources."""
    global live_running, live_client

    if live_running and live_client:
        logger.info("Stopping live client...")
        live_client.stop()
        live_running = False
        logger.info("Live client stopped")

def signal_handler(sig, frame):
    """Handle signals."""
    logger.info("Received signal, cleaning up...")
    cleanup()
    sys.exit(0)

def main():
    """Main function."""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Send ready message
    send_response("ready", {"message": "Databento bridge ready"})

    # Process commands from stdin
    for line in sys.stdin:
        line = line.strip()
        if line:
            process_command(line)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Unhandled exception: {str(e)}")
        send_response("error", {"message": f"Unhandled exception: {str(e)}"})
    finally:
        cleanup()
