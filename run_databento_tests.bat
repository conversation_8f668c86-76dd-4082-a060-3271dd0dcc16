@echo off
echo Running Databento tests...

echo.
echo Step 1: Install and verify Databento package
python install_databento.py
if %ERRORLEVEL% neq 0 (
    echo Failed to install Databento package
    exit /b 1
)

echo.
echo Step 2: Test historical data access
python test_databento_historical.py
if %ERRORLEVEL% neq 0 (
    echo Historical data test failed
    exit /b 1
)

echo.
echo Step 3: Test live data access
python test_databento_live.py
if %ERRORLEVEL% neq 0 (
    echo Live data test failed
    exit /b 1
)

echo.
echo Step 4: Run comprehensive test
python test_databento_python.py
if %ERRORLEVEL% neq 0 (
    echo Comprehensive test failed
    exit /b 1
)

echo.
echo All tests completed successfully
