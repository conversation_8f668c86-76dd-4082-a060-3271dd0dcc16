const fs = require('fs');
const path = require('path');
const config = require('./config.js');

// --- Helper Functions (copied from backtest.js) ---
function SMA(arr, period, index) { 
    if (!arr || index < period - 1 || arr.length <= index) return NaN; 
    let sum = 0; 
    let validCount = 0; 
    for (let j = index - period + 1; j <= index; j++) { 
        if (typeof arr[j] === 'number' && !isNaN(arr[j])) { 
            sum += arr[j]; 
            validCount++; 
        } else { 
            return NaN; 
        } 
    } 
    return validCount === period ? sum / period : NaN; 
}

function WMA(arr, period, index) { 
    if (!arr || index < period - 1 || arr.length <= index) return NaN; 
    let weightedSum = 0, weightSum = 0, validCount = 0; 
    for (let j = 0; j < period; j++) { 
        const idx = index - j, weight = period - j; 
        if (idx < 0) return NaN; 
        if (typeof arr[idx] === 'number' && !isNaN(arr[idx])) { 
            weightedSum += arr[idx] * weight; 
            weightSum += weight; 
            validCount++; 
        } else { 
            return NaN; 
        } 
    } 
    return validCount === period ? weightedSum / weightSum : NaN; 
}

function RSI(arr, period, index) { 
    if (!arr || index < period || arr.length <= index) return NaN; 
    let gains = 0, losses = 0; 
    let validDeltas = 0; 
    for (let j = index - period + 1; j <= index; j++) { 
        if (j > 0 && typeof arr[j - 1] === 'number' && !isNaN(arr[j - 1]) && typeof arr[j] === 'number' && !isNaN(arr[j])) { 
            const delta = arr[j] - arr[j - 1]; 
            if (delta > 0) { 
                gains += delta; 
            } else { 
                losses -= delta; 
            } 
            validDeltas++; 
        } else { 
            return NaN; 
        } 
    } 
    if (validDeltas < period) return NaN; 
    if (losses === 0) return 100; 
    if (gains === 0) return 0; 
    const avgGain = gains / period, avgLoss = losses / period, rs = avgGain / avgLoss; 
    return 100 - (100 / (1 + rs)); 
}

function candlestickColor(candle) { 
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) return 'invalid'; 
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji'; 
}

function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;
    const col1 = candlestickColor(c1),
          col2 = candlestickColor(c2),
          col3 = candlestickColor(c3);
    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }
    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;
    const col0 = candlestickColor(c0),
          col1 = candlestickColor(c1),
          col2 = candlestickColor(c2),
          col3 = candlestickColor(c3);
    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        return 'bullish';
    }
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        return 'bearish';
    }
    return null;
}

function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod) {
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
        return false;
    }

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // USER'S EXACT EDGE: Price above/below BOTH MAs for trend direction
    if (config.use200SmaFilter) {
        // For LONG trades: price must be above BOTH 50 WMA AND 200 SMA
        if (dir === 'bullish') {
            if (c3.close <= c3.wma50 || c3.close <= c3.sma200) {
                return false;
            }
        }
        // For SHORT trades: price must be below BOTH 50 WMA AND 200 SMA
        if (dir === 'bearish') {
            if (c3.close >= c3.wma50 || c3.close >= c3.sma200) {
                return false;
            }
        }
    }

    // RSI filter
    if ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa)) {
        return false;
    }

    return true;
}

// --- Main Debug Function ---
function debugPatterns() {
    console.log("🔍 DEBUGGING PATTERN DETECTION AND MAs");
    console.log("=====================================");
    
    // Load data
    const inputFile = path.join(__dirname, 'input', 'MNQ_lastyear.csv');
    const csvContent = fs.readFileSync(inputFile, 'utf8');
    const lines = csvContent.trim().split('\n');
    const header = lines[0];
    
    console.log(`📊 Loaded ${lines.length - 1} candles`);
    console.log(`📋 Header: ${header}`);
    
    // Parse candles using same logic as backtest.js
    const candles = [];
    const isDatabentoFormat = header.includes('ts_event');

    for (let i = 1; i < Math.min(lines.length, 5000); i++) { // Test first 5000 candles
        const parts = lines[i].split(',');
        if (parts.length >= 9) {
            let open, high, low, close, timestampSeconds;

            if (isDatabentoFormat) {
                // Databento format
                open = parseFloat(parts[4]);
                high = parseFloat(parts[5]);
                low = parseFloat(parts[6]);
                close = parseFloat(parts[7]);

                // Parse timestamp from ts_event field
                const timeString = parts[0];
                if (timeString) {
                    try {
                        const parsedDate = new Date(timeString);
                        if (!isNaN(parsedDate.getTime())) {
                            timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                        }
                    } catch (e) {
                        // Skip invalid timestamps
                        continue;
                    }
                }
            }

            if (!isNaN(timestampSeconds) && !isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {
                candles.push({
                    timestamp: timestampSeconds,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: parseFloat(parts[8])
                });
            }
        }
    }
    
    console.log(`✅ Parsed ${candles.length} candles`);
    
    // Calculate indicators
    const closes = candles.map(c => c.close);
    
    console.log("\n🧮 CALCULATING INDICATORS...");
    console.log("============================");
    
    for (let i = 0; i < candles.length; i++) {
        candles[i].sma200 = SMA(closes, config.sma200Period, i);
        candles[i].wma50 = WMA(closes, config.wma50Period, i);
        candles[i].rsi = RSI(closes, config.rsiPeriod, i);
        candles[i].rsiMa = SMA(candles.map(c => c.rsi), config.rsiMaPeriod, i);
        
        // Simple ATR calculation
        if (i === 0) {
            candles[i].atr = candles[i].high - candles[i].low;
        } else {
            const tr = Math.max(
                candles[i].high - candles[i].low,
                Math.abs(candles[i].high - candles[i-1].close),
                Math.abs(candles[i].low - candles[i-1].close)
            );
            candles[i].atr = SMA(candles.slice(0, i+1).map(c => c.atr || tr), config.atrPeriod, i);
        }
    }
    
    // Test pattern detection and filters
    console.log("\n🎯 TESTING PATTERN DETECTION...");
    console.log("===============================");
    
    let patternCount = 0;
    let validEntryCount = 0;
    let bullishPatterns = 0;
    let bearishPatterns = 0;
    let filterRejects = {
        wma: 0,
        sma200: 0,
        rsi: 0,
        indicators: 0
    };
    
    for (let i = 250; i < Math.min(candles.length, 1000); i++) { // Start after 250 candles so SMA200 is valid
        const c0 = candles[i-3];
        const c1 = candles[i-2]; 
        const c2 = candles[i-1];
        const c3 = candles[i];
        
        // Check patterns
        const p3 = detect3(c1, c2, c3);
        const p4 = detect4(c0, c1, c2, c3);
        
        let pattern = null;
        let patternType = null;
        
        if (p4) {
            pattern = p4;
            patternType = 'four';
        } else if (p3) {
            pattern = p3;
            patternType = 'three';
        }
        
        if (pattern) {
            patternCount++;
            if (pattern === 'bullish') bullishPatterns++;
            if (pattern === 'bearish') bearishPatterns++;
            
            // Test entry filters
            if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
                filterRejects.indicators++;
                continue;
            }
            
            const passedEntry = entryOK(pattern, patternType, c3, i, candles);
            
            if (passedEntry) {
                validEntryCount++;
                
                // Log first few valid entries for inspection
                if (validEntryCount <= 5) {
                    console.log(`\n📍 VALID ENTRY #${validEntryCount}:`);
                    console.log(`   Pattern: ${pattern} (${patternType})`);
                    console.log(`   Timestamp: ${new Date(c3.timestamp * 1000).toISOString()}`);
                    console.log(`   Price: ${c3.close}`);
                    console.log(`   WMA50: ${c3.wma50.toFixed(2)}`);
                    console.log(`   SMA200: ${c3.sma200.toFixed(2)}`);
                    console.log(`   RSI: ${c3.rsi.toFixed(2)}`);
                    console.log(`   RSI-MA: ${c3.rsiMa.toFixed(2)}`);
                    console.log(`   ATR: ${c3.atr.toFixed(2)}`);
                    console.log(`   Colors: ${candlestickColor(c1)}-${candlestickColor(c2)}-${candlestickColor(c3)}`);
                }
            } else {
                // Check which filter rejected it
                if (config.useWmaFilter && ((pattern === 'bullish' && c3.close <= c3.wma50) || (pattern === 'bearish' && c3.close >= c3.wma50))) {
                    filterRejects.wma++;
                } else if (config.use200SmaFilter) {
                    if (pattern === 'bullish' && (c3.wma50 >= c3.sma200 || c3.close <= c3.sma200)) {
                        filterRejects.sma200++;
                    } else if (pattern === 'bearish' && (c3.wma50 <= c3.sma200 || c3.close >= c3.sma200)) {
                        filterRejects.sma200++;
                    }
                } else if ((pattern === 'bullish' && c3.rsi <= c3.rsiMa) || (pattern === 'bearish' && c3.rsi >= c3.rsiMa)) {
                    filterRejects.rsi++;
                }
            }
        }
    }
    
    console.log("\n📊 PATTERN DETECTION SUMMARY:");
    console.log("=============================");
    console.log(`Total Patterns Found: ${patternCount}`);
    console.log(`  - Bullish: ${bullishPatterns}`);
    console.log(`  - Bearish: ${bearishPatterns}`);
    console.log(`Valid Entries: ${validEntryCount}`);
    console.log(`Entry Rate: ${((validEntryCount / patternCount) * 100).toFixed(1)}%`);
    
    console.log("\n🚫 FILTER REJECTIONS:");
    console.log("=====================");
    console.log(`Indicator NaN: ${filterRejects.indicators}`);
    console.log(`WMA Filter: ${filterRejects.wma}`);
    console.log(`200 SMA Filter: ${filterRejects.sma200}`);
    console.log(`RSI Filter: ${filterRejects.rsi}`);
    
    console.log("\n⚙️ CONFIG SETTINGS:");
    console.log("===================");
    console.log(`useWmaFilter: ${config.useWmaFilter}`);
    console.log(`use200SmaFilter: ${config.use200SmaFilter}`);
    console.log(`WMA Period: ${config.wma50Period}`);
    console.log(`SMA Period: ${config.sma200Period}`);
    console.log(`RSI Period: ${config.rsiPeriod}`);
    console.log(`RSI MA Period: ${config.rsiMaPeriod}`);
}

// Run the debug
debugPatterns();
