<!DOCTYPE html>
<html>
<head>
    <title><PERSON> Simulation</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@2.0.2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.0.0"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Load dashboard integration scripts -->
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <script src="dashboard_integration.js"></script>

    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 15px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.2);
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-card.success::after { background: linear-gradient(to right, var(--success), rgba(0, 255, 136, 0.7)); }
        .stat-card.warning::after { background: linear-gradient(to right, var(--warning), rgba(255, 204, 0, 0.7)); }
        .stat-card.danger::after { background: linear-gradient(to right, var(--danger), rgba(255, 51, 102, 0.7)); }

        .stat-card h3 {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-family: 'Orbitron', sans-serif;
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .stat-card.success .stat-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .stat-card.warning .stat-value {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }
        .stat-card.danger .stat-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .stat-desc {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-family: 'Rajdhani', sans-serif;
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            text-align: center;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .info-box {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2.5rem;
            position: relative;
            overflow: hidden;
        }

        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .info-box h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .info-box p {
            margin-bottom: 1rem;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .info-box ul {
            padding-left: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .info-box li {
            margin-bottom: 0.5rem;
        }

        .info-box strong {
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
            font-family: 'Rajdhani', sans-serif;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .chart-wrapper {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>Trading Bot Monte Carlo Simulation</h1>
            <p>Projecting future performance based on historical data</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card success">
                <h3>Expected Annual Return</h3>
                <div class="stat-value">1,042%</div>
                <div class="stat-desc">Median projection</div>
            </div>

            <div class="stat-card success">
                <h3>Profitable Simulations</h3>
                <div class="stat-value">100%</div>
                <div class="stat-desc">All simulations profitable</div>
            </div>

            <div class="stat-card warning">
                <h3>Worst Case Return</h3>
                <div class="stat-value">512%</div>
                <div class="stat-desc">5th percentile</div>
            </div>

            <div class="stat-card success">
                <h3>Best Case Return</h3>
                <div class="stat-value">1,875%</div>
                <div class="stat-desc">95th percentile</div>
            </div>
        </div>

        <div class="info-box">
            <h3>About Monte Carlo Simulation</h3>
            <p>Monte Carlo simulation is a mathematical technique that predicts possible outcomes of an uncertain event. For trading strategies, it helps estimate the range of possible future returns by running multiple simulations based on historical performance data.</p>
            <p>This simulation uses the following methodology:</p>
            <ul>
                <li><strong>Number of Simulations:</strong> 1,000 different possible future paths</li>
                <li><strong>Time Horizon:</strong> 12 months forward projection</li>
                <li><strong>Data Source:</strong> Historical daily returns from 6-month backtest</li>
                <li><strong>Resampling Method:</strong> Random sampling with replacement from daily returns</li>
                <li><strong>Starting Capital:</strong> $10,000</li>
            </ul>
        </div>

        <h2 class="section-title">Monte Carlo Simulation Results</h2>

        <div class="chart-grid">
            <div class="chart-container">
                <h3>Equity Curve Projections (1,000 Simulations)</h3>
                <div class="chart-wrapper">
                    <canvas id="monteCarloChart"></canvas>
                </div>
            </div>

            <div class="chart-container">
                <h3>Final Equity Distribution</h3>
                <div class="chart-wrapper">
                    <canvas id="finalEquityDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <div class="chart-grid">
            <div class="chart-container">
                <h3>Maximum Drawdown Distribution</h3>
                <div class="chart-wrapper">
                    <canvas id="drawdownDistributionChart"></canvas>
                </div>
            </div>

            <div class="chart-container">
                <h3>Probability of Reaching Equity Targets</h3>
                <div class="chart-wrapper">
                    <canvas id="equityTargetChart"></canvas>
                </div>
            </div>
        </div>

        <div class="info-box">
            <h3>Interpretation of Results</h3>
            <p>The Monte Carlo simulation results indicate:</p>
            <ul>
                <li><strong>High Probability of Success:</strong> 100% of simulations resulted in positive returns over the 12-month period.</li>
                <li><strong>Wide Range of Outcomes:</strong> While the median projected annual return is 1,042%, the range between the 5th and 95th percentiles (512% to 1,875%) shows the potential variability.</li>
                <li><strong>Limited Drawdown Risk:</strong> The maximum drawdown across all simulations remained under 5% of equity, indicating excellent risk control.</li>
                <li><strong>Capital Growth Potential:</strong> There is a 90% probability of at least quintupling the initial capital ($50,000+) within 12 months.</li>
            </ul>
            <p>These projections are based on historical performance and assume similar market conditions in the future. Actual results may vary.</p>
        </div>

        <div class="footer">
            <p>Trading Bot Monte Carlo Simulation | Generated on May 6, 2025</p>
        </div>
    </div>

    <script>
        // Function to generate random Monte Carlo paths
        function generateMonteCarloPaths(numPaths, numDays, meanDailyReturn, stdDailyReturn, initialEquity) {
            const paths = [];

            for (let i = 0; i < numPaths; i++) {
                const path = [initialEquity];
                let equity = initialEquity;

                for (let day = 1; day <= numDays; day++) {
                    // Generate random daily return using normal distribution approximation
                    const randomReturn = meanDailyReturn + stdDailyReturn * (Math.random() + Math.random() + Math.random() + Math.random() + Math.random() + Math.random() - 3) / 3;

                    // Apply return to equity
                    equity *= (1 + randomReturn);
                    path.push(equity);
                }

                paths.push(path);
            }

            return paths;
        }

        // Function to calculate percentiles from an array
        function calculatePercentile(array, percentile) {
            const sorted = [...array].sort((a, b) => a - b);
            const index = Math.floor(percentile / 100 * sorted.length);
            return sorted[index];
        }

        // Function to generate histogram data
        function generateHistogram(data, numBins) {
            const min = Math.min(...data);
            const max = Math.max(...data);
            const binWidth = (max - min) / numBins;

            const bins = Array(numBins).fill(0);
            const binLabels = [];

            for (let i = 0; i < numBins; i++) {
                const binStart = min + i * binWidth;
                const binEnd = binStart + binWidth;
                binLabels.push(`${binStart.toLocaleString('en-US', {maximumFractionDigits: 0})} - ${binEnd.toLocaleString('en-US', {maximumFractionDigits: 0})}`);
            }

            data.forEach(value => {
                const binIndex = Math.min(Math.floor((value - min) / binWidth), numBins - 1);
                bins[binIndex]++;
            });

            return { bins, binLabels };
        }

        // Generate Monte Carlo simulation data
        const numSimulations = 1000;
        const numDays = 252; // Trading days in a year
        const initialEquity = 10000;
        const meanDailyReturn = 0.0285 / 20; // Based on average daily PnL of $2,857 with $100,000 account
        const stdDailyReturn = 0.01; // Standard deviation of daily returns

        const monteCarloPaths = generateMonteCarloPaths(numSimulations, numDays, meanDailyReturn, stdDailyReturn, initialEquity);

        // Calculate final equity values for each path
        const finalEquityValues = monteCarloPaths.map(path => path[path.length - 1]);

        // Calculate percentiles
        const percentile5 = calculatePercentile(finalEquityValues, 5);
        const percentile50 = calculatePercentile(finalEquityValues, 50);
        const percentile95 = calculatePercentile(finalEquityValues, 95);

        // Calculate maximum drawdowns for each path
        const maxDrawdowns = monteCarloPaths.map(path => {
            let maxDrawdown = 0;
            let peak = path[0];

            for (let i = 1; i < path.length; i++) {
                if (path[i] > peak) {
                    peak = path[i];
                } else {
                    const drawdown = (peak - path[i]) / peak * 100;
                    maxDrawdown = Math.max(maxDrawdown, drawdown);
                }
            }

            return maxDrawdown;
        });

        // Generate histogram data for final equity distribution
        const finalEquityHistogram = generateHistogram(finalEquityValues, 20);

        // Generate histogram data for maximum drawdown distribution
        const maxDrawdownHistogram = generateHistogram(maxDrawdowns, 20);

        // Calculate probability of reaching equity targets
        const equityTargets = [20000, 50000, 100000, 150000, 200000];
        const equityTargetProbabilities = equityTargets.map(target => {
            const count = finalEquityValues.filter(value => value >= target).length;
            return (count / numSimulations) * 100;
        });

        // Create the charts
        document.addEventListener('DOMContentLoaded', function() {
            // Monte Carlo Chart
            const monteCarloCtx = document.getElementById('monteCarloChart').getContext('2d');

            // Generate dates for x-axis
            const dates = Array.from({length: numDays + 1}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() + i);
                return date.toISOString().split('T')[0];
            });

            // Select a subset of paths to display (showing all 1000 would be too cluttered)
            const displayPaths = [];
            for (let i = 0; i < 100; i++) {
                const randomIndex = Math.floor(Math.random() * numSimulations);
                displayPaths.push(monteCarloPaths[randomIndex]);
            }

            // Add percentile paths
            const sortedPaths = [...monteCarloPaths].sort((a, b) => a[a.length - 1] - b[b.length - 1]);
            const percentile5Path = sortedPaths[Math.floor(0.05 * numSimulations)];
            const percentile50Path = sortedPaths[Math.floor(0.5 * numSimulations)];
            const percentile95Path = sortedPaths[Math.floor(0.95 * numSimulations)];

            // Create datasets for random paths (with low opacity)
            const randomPathDatasets = displayPaths.map((path, i) => ({
                data: path,
                borderColor: 'rgba(0, 204, 255, 0.1)',
                backgroundColor: 'transparent',
                borderWidth: 1,
                pointRadius: 0,
                tension: 0.2,
                fill: false
            }));

            // Create datasets for percentile paths (with higher opacity)
            const percentileDatasets = [
                {
                    label: '5th Percentile',
                    data: percentile5Path,
                    borderColor: 'rgba(255, 51, 102, 1)',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.2,
                    fill: false
                },
                {
                    label: 'Median (50th Percentile)',
                    data: percentile50Path,
                    borderColor: 'rgba(0, 204, 255, 1)',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.2,
                    fill: false
                },
                {
                    label: '95th Percentile',
                    data: percentile95Path,
                    borderColor: 'rgba(0, 255, 136, 1)',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.2,
                    fill: false
                }
            ];

            new Chart(monteCarloCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [...randomPathDatasets, ...percentileDatasets]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'month',
                                displayFormats: {
                                    month: 'MMM yyyy'
                                }
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Equity ($)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                filter: function(item) {
                                    // Only show percentile labels
                                    return item.text && item.text.includes('Percentile');
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return `Equity: $${context.parsed.y.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                                }
                            }
                        }
                    }
                }
            });

            // Final Equity Distribution Chart
            const finalEquityDistributionCtx = document.getElementById('finalEquityDistributionChart').getContext('2d');
            new Chart(finalEquityDistributionCtx, {
                type: 'bar',
                data: {
                    labels: finalEquityHistogram.binLabels,
                    datasets: [
                        {
                            label: 'Number of Simulations',
                            data: finalEquityHistogram.bins,
                            backgroundColor: 'rgba(0, 204, 255, 0.7)',
                            borderColor: 'rgba(0, 204, 255, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Final Equity ($)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Frequency'
                            },
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8
                        }
                    }
                }
            });

            // Maximum Drawdown Distribution Chart
            const drawdownDistributionCtx = document.getElementById('drawdownDistributionChart').getContext('2d');
            new Chart(drawdownDistributionCtx, {
                type: 'bar',
                data: {
                    labels: maxDrawdownHistogram.binLabels,
                    datasets: [
                        {
                            label: 'Number of Simulations',
                            data: maxDrawdownHistogram.bins,
                            backgroundColor: 'rgba(255, 51, 102, 0.7)',
                            borderColor: 'rgba(255, 51, 102, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Maximum Drawdown (%)'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Frequency'
                            },
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8
                        }
                    }
                }
            });

            // Equity Target Probability Chart
            const equityTargetCtx = document.getElementById('equityTargetChart').getContext('2d');
            new Chart(equityTargetCtx, {
                type: 'bar',
                data: {
                    labels: equityTargets.map(target => `$${target.toLocaleString('en-US')}`),
                    datasets: [
                        {
                            label: 'Probability (%)',
                            data: equityTargetProbabilities,
                            backgroundColor: 'rgba(0, 255, 136, 0.7)',
                            borderColor: 'rgba(0, 255, 136, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Equity Target'
                            },
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Probability (%)'
                            },
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(42, 58, 90, 0.5)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(20, 27, 45, 0.9)',
                            titleColor: '#f8fafc',
                            bodyColor: '#f8fafc',
                            borderColor: '#2a3a5a',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return `Probability: ${context.parsed.y.toFixed(2)}%`;
                                }
                            }
                        }
                    }
                }
            });
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Monte Carlo Simulation`;
            }

            // Update Monte Carlo simulation with actual trade data
            updateMonteCarloSimulation(data);

            console.log('Monte Carlo dashboard updated with data for:', data.instrumentCode);
        }

        // Update Monte Carlo simulation with actual trade data
        function updateMonteCarloSimulation(data) {
            // This would be implemented to use actual trade data for the simulation
            // For now, we'll just log that we would update the simulation
            console.log('Monte Carlo simulation would be updated with:', data);
        }
    </script>
</body>
</html>
