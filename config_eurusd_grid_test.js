/**
 * EURUSD Grid Test Configuration
 *
 * This file contains the configuration for grid testing EURUSD forex pair.
 */

module.exports = {
    // --- Essential Settings ---
    symbol: 'EURUSD',        // Symbol must be specified
    inputFile: 'C:/backtest-bot/input/EURUSD_2020_2025.csv',
    outputDir: './output/EURUSD_GridTest_Results',
    initialBalance: 10000,
    pointValue: 1.0,         // Each pip is worth $1 per standard lot (100,000 units)
    commissionPerContract: 0.0, // Forex typically has no commission but uses spread
    slippagePoints: 0.0002,   // 2 pips of slippage (typical for EURUSD)
    pricePrecision: 5,        // Forex typically has 4 or 5 decimal places

    // --- Run Mode ---
    isAdaptiveRun: false, // Set to false for parameter grid testing

    // --- Parameter Grid Settings ---
    // These arrays will be used to generate combinations for grid testing
    slFactors: [3.0, 4.0, 5.0, 6.0, 7.0, 8.0],
    tpFactors: [2.0, 3.0, 4.0, 5.0, 6.0, 7.0],
    trailFactors: [0.01, 0.02, 0.03, 0.05, 0.07, 0.10],
    fixedTpPoints: [0, 0.0010, 0.0020], // In pips (0, 10 pips, 20 pips)

    // --- Indicator Periods ---
    atrPeriod: 14,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    sma200Period: 0,
    wma50Period: 50,

    // --- Strategy Parameters ---
    useWmaFilter: true,
    useTwoBarColorExit: false,
    minAtrEntry: 0.0005,
    minRsiMaSeparation: 1.0,

    // --- RSI Bands ---
    rsiUpperBand: 60,
    rsiLowerBand: 40,
    rsiMiddleBand: 50,

    // --- ATR Thresholds for Adaptive Mode ---
    atrThresholds: {
        low_medium: 0.0010, // 10 pips
        medium_high: 0.0020 // 20 pips
    },

    // Adaptive parameters for different volatility regimes
    adaptiveParams: {
        Low: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 },
        Medium: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 },
        High: { slFactor: 5.0, tpFactor: 4.0, trailFactor: 0.03 }
    },

    // 0 bar latency for optimal performance
    latencyDelayBars: 0,

    // Keep these for compatibility
    costGrid: null,
    riskPercentGrid: null,
    fixedContractsGrid: null,
    fixedTpPointsGrid: null,

    // Fixed parameters
    riskPercent: 0,
    fixedContracts: 10,     // Using 10 mini lots (10,000 units each)
    maxContracts: 10,

    // For compatibility with backtest.js
    currentRiskPercent: 0,
    currentFixedContracts: 10,

    // --- Time Filter Settings ---
    timeFilterEnabled: false,  // Disable time filtering for 24/5 trading
    activeHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
};
