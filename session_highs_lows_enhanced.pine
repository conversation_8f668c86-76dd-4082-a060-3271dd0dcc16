//@version=6
indicator("Asian & London Session Highs/Lows - Enhanced", overlay=true, max_lines_count=500)

// === INPUTS === //
asianHighColor  = input.color(color.green, "Asian High Line Color")
asianLowColor   = input.color(color.red, "Asian Low Line Color")
londonHighColor = input.color(color.blue, "London High Line Color")
londonLowColor  = input.color(color.orange, "London Low Line Color")
lineWidth       = input.int(2, "Line Thickness", minval=1, maxval=5)
tzOffset        = input.int(0, "Time Zone Offset from UTC (e.g. -4 for EDT, +3 for EEST)")
lineExtension   = input.int(20, "Bars to Extend Line Right", minval=1, maxval=500)

// === NEW INPUTS FOR MULTIPLE DAYS === //
showMultipleDays = input.bool(false, "Show Multiple Days", group="Historical Sessions")
daysToShow       = input.int(5, "Number of Days to Show", minval=1, maxval=10, group="Historical Sessions")
showLabels       = input.bool(true, "Show Session Labels", group="Historical Sessions")
labelSize        = input.string("small", "Label Size", options=["tiny", "small", "normal", "large"], group="Historical Sessions")
fadeOlderLines   = input.bool(true, "Fade Older Lines", group="Historical Sessions")
fadeStrength     = input.int(70, "Fade Transparency %", minval=0, maxval=90, group="Historical Sessions")

// === ADDITIONAL FEATURES === //
showBreakouts    = input.bool(false, "Show Breakout Alerts", group="Breakout Detection")
breakoutColor    = input.color(color.yellow, "Breakout Alert Color", group="Breakout Detection")
showSessionBoxes = input.bool(false, "Show Session Time Boxes", group="Visual Enhancements")
asianBoxColor    = input.color(color.new(color.green, 95), "Asian Session Box Color", group="Visual Enhancements")
londonBoxColor   = input.color(color.new(color.blue, 95), "London Session Box Color", group="Visual Enhancements")

// === HELPER FUNCTIONS === //
// Function to get session times for any day offset
getSessionTimes(dayOffset) =>
    currentTime = time
    dayMs = 24 * 60 * 60 * 1000
    targetDay = currentTime - (dayOffset * dayMs)

    year_  = year(targetDay)
    month_ = month(targetDay)
    day_   = dayofmonth(targetDay)

    asian_start  = timestamp("UTC", year_, month_, day_, 0 - tzOffset, 0)
    asian_end    = timestamp("UTC", year_, month_, day_, 8 - tzOffset, 0)
    london_start = timestamp("UTC", year_, month_, day_, 8 - tzOffset, 0)
    london_end   = timestamp("UTC", year_, month_, day_, 12 - tzOffset, 0)

    [asian_start, asian_end, london_start, london_end]

// Function to calculate transparency based on day age
getLineTransparency(dayAge) =>
    if fadeOlderLines and dayAge > 0
        math.min(fadeStrength + (dayAge * 10), 90)
    else
        0

// === CURRENT DAY SESSION LOGIC === //
year_  = year
month_ = month
day_   = dayofmonth

asian_start_time  = timestamp("UTC", year_, month_, day_, 0 - tzOffset, 0)
asian_end_time    = timestamp("UTC", year_, month_, day_, 8 - tzOffset, 0)
london_start_time = timestamp("UTC", year_, month_, day_, 8 - tzOffset, 0)
london_end_time   = timestamp("UTC", year_, month_, day_, 12 - tzOffset, 0)

asianSession  = time >= asian_start_time and time < asian_end_time
londonSession = time >= london_start_time and time < london_end_time

// === SESSION H/L VARIABLES === //
var float asianHigh = na
var float asianLow = na
var int asianHighBar = na
var int asianLowBar = na

var float londonHigh = na
var float londonLow = na
var int londonHighBar = na
var int londonLowBar = na

// === LINE ARRAYS FOR MULTIPLE DAYS === //
var array<line> asianHighLines = array.new<line>()
var array<line> asianLowLines = array.new<line>()
var array<line> londonHighLines = array.new<line>()
var array<line> londonLowLines = array.new<line>()
var array<label> sessionLabels = array.new<label>()
var array<box> sessionBoxes = array.new<box>()

// === CURRENT DAY LINE VARIABLES === //
var line asianHighLine = na
var line asianLowLine = na
var line londonHighLine = na
var line londonLowLine = na

// === TRACK ASIAN SESSION === //
if asianSession
    if na(asianHigh) or high > asianHigh
        asianHigh := high
        asianHighBar := bar_index
    if na(asianLow) or low < asianLow
        asianLow := low
        asianLowBar := bar_index

    // Create session box if enabled
    if showSessionBoxes and na(asianHigh[1])
        sessionBox = box.new(left=bar_index, top=high, right=bar_index + 1, bottom=low, bgcolor=asianBoxColor, border_color=color.new(asianHighColor, 50))
        array.push(sessionBoxes, sessionBox)

else if not asianSession and asianSession[1]
    // End of Asian session - draw lines
    line.delete(asianHighLine)
    line.delete(asianLowLine)

    transparency = getLineTransparency(0)
    asianHighLine := line.new(x1=asianHighBar, y1=asianHigh, x2=asianHighBar + lineExtension, y2=asianHigh, color=color.new(asianHighColor, transparency), width=lineWidth, extend=extend.none)
    asianLowLine  := line.new(x1=asianLowBar, y1=asianLow, x2=asianLowBar + lineExtension, y2=asianLow, color=color.new(asianLowColor, transparency), width=lineWidth, extend=extend.none)

    // Add to arrays for multiple days feature
    if showMultipleDays
        array.push(asianHighLines, asianHighLine)
        array.push(asianLowLines, asianLowLine)

        // Add labels if enabled
        if showLabels
            labelText = "AH: " + str.tostring(asianHigh, "#.##")
            highLabel = label.new(x=asianHighBar + lineExtension/2, y=asianHigh, text=labelText, style=label.style_label_down, color=asianHighColor, textcolor=color.white, size=labelSize)
            array.push(sessionLabels, highLabel)

            labelText := "AL: " + str.tostring(asianLow, "#.##")
            lowLabel = label.new(x=asianLowBar + lineExtension/2, y=asianLow, text=labelText, style=label.style_label_up, color=asianLowColor, textcolor=color.white, size=labelSize)
            array.push(sessionLabels, lowLabel)

    // Reset variables
    asianHigh := na
    asianLow := na
    asianHighBar := na
    asianLowBar := na

// === TRACK LONDON SESSION === //
if londonSession
    if na(londonHigh) or high > londonHigh
        londonHigh := high
        londonHighBar := bar_index
    if na(londonLow) or low < londonLow
        londonLow := low
        londonLowBar := bar_index

    // Create session box if enabled
    if showSessionBoxes and na(londonHigh[1])
        sessionBox = box.new(left=bar_index, top=high, right=bar_index + 1, bottom=low, bgcolor=londonBoxColor, border_color=color.new(londonHighColor, 50))
        array.push(sessionBoxes, sessionBox)

else if not londonSession and londonSession[1]
    // End of London session - draw lines
    line.delete(londonHighLine)
    line.delete(londonLowLine)

    transparency = getLineTransparency(0)
    londonHighLine := line.new(x1=londonHighBar, y1=londonHigh, x2=londonHighBar + lineExtension, y2=londonHigh, color=color.new(londonHighColor, transparency), width=lineWidth, extend=extend.none)
    londonLowLine  := line.new(x1=londonLowBar, y1=londonLow, x2=londonLowBar + lineExtension, y2=londonLow, color=color.new(londonLowColor, transparency), width=lineWidth, extend=extend.none)

    // Add to arrays for multiple days feature
    if showMultipleDays
        array.push(londonHighLines, londonHighLine)
        array.push(londonLowLines, londonLowLine)

        // Add labels if enabled
        if showLabels
            labelText = "LH: " + str.tostring(londonHigh, "#.##")
            highLabel = label.new(x=londonHighBar + lineExtension/2, y=londonHigh, text=labelText, style=label.style_label_down, color=londonHighColor, textcolor=color.white, size=labelSize)
            array.push(sessionLabels, highLabel)

            labelText := "LL: " + str.tostring(londonLow, "#.##")
            lowLabel = label.new(x=londonLowBar + lineExtension/2, y=londonLow, text=labelText, style=label.style_label_up, color=londonLowColor, textcolor=color.white, size=labelSize)
            array.push(sessionLabels, lowLabel)

    // Reset variables
    londonHigh := na
    londonLow := na
    londonHighBar := na
    londonLowBar := na

// === HISTORICAL SESSION CALCULATION === //
if showMultipleDays and barstate.islast
    // Clear existing historical lines first
    for i = 0 to array.size(asianHighLines) - 1
        line.delete(array.get(asianHighLines, i))
    for i = 0 to array.size(asianLowLines) - 1
        line.delete(array.get(asianLowLines, i))
    for i = 0 to array.size(londonHighLines) - 1
        line.delete(array.get(londonHighLines, i))
    for i = 0 to array.size(londonLowLines) - 1
        line.delete(array.get(londonLowLines, i))
    for i = 0 to array.size(sessionLabels) - 1
        label.delete(array.get(sessionLabels, i))

    // Clear arrays
    array.clear(asianHighLines)
    array.clear(asianLowLines)
    array.clear(londonHighLines)
    array.clear(londonLowLines)
    array.clear(sessionLabels)

    // Calculate historical sessions for each day
    for dayOffset = 1 to daysToShow
        [asian_start, asian_end, london_start, london_end] = getSessionTimes(dayOffset)

        // Find Asian session high/low
        var float histAsianHigh = na
        var float histAsianLow = na
        var int histAsianHighBar = na
        var int histAsianLowBar = na

        // Find London session high/low
        var float histLondonHigh = na
        var float histLondonLow = na
        var int histLondonHighBar = na
        var int histLondonLowBar = na

        // Scan through bars to find session highs/lows
        for i = 1 to 5000  // Look back up to 5000 bars
            if bar_index - i >= 0
                barTime = time[i]
                barHigh = high[i]
                barLow = low[i]
                barIdx = bar_index - i

                // Check if bar is in Asian session
                if barTime >= asian_start and barTime < asian_end
                    if na(histAsianHigh) or barHigh > histAsianHigh
                        histAsianHigh := barHigh
                        histAsianHighBar := barIdx
                    if na(histAsianLow) or barLow < histAsianLow
                        histAsianLow := barLow
                        histAsianLowBar := barIdx

                // Check if bar is in London session
                if barTime >= london_start and barTime < london_end
                    if na(histLondonHigh) or barHigh > histLondonHigh
                        histLondonHigh := barHigh
                        histLondonHighBar := barIdx
                    if na(histLondonLow) or barLow < histLondonLow
                        histLondonLow := barLow
                        histLondonLowBar := barIdx

        // Draw historical Asian session lines
        if not na(histAsianHigh) and not na(histAsianLow)
            transparency = getLineTransparency(dayOffset)

            asianHighLineHist = line.new(x1=histAsianHighBar, y1=histAsianHigh, x2=histAsianHighBar + lineExtension, y2=histAsianHigh, color=color.new(asianHighColor, transparency), width=lineWidth, extend=extend.none)
            asianLowLineHist = line.new(x1=histAsianLowBar, y1=histAsianLow, x2=histAsianLowBar + lineExtension, y2=histAsianLow, color=color.new(asianLowColor, transparency), width=lineWidth, extend=extend.none)

            array.push(asianHighLines, asianHighLineHist)
            array.push(asianLowLines, asianLowLineHist)

            // Add labels if enabled
            if showLabels
                asianHighLabelText = "AH-" + str.tostring(dayOffset) + ": " + str.tostring(histAsianHigh, "#.##")
                highLabel = label.new(x=histAsianHighBar + lineExtension/2, y=histAsianHigh, text=asianHighLabelText, style=label.style_label_down, color=color.new(asianHighColor, transparency), textcolor=color.white, size=labelSize)
                array.push(sessionLabels, highLabel)

                asianLowLabelText = "AL-" + str.tostring(dayOffset) + ": " + str.tostring(histAsianLow, "#.##")
                lowLabel = label.new(x=histAsianLowBar + lineExtension/2, y=histAsianLow, text=asianLowLabelText, style=label.style_label_up, color=color.new(asianLowColor, transparency), textcolor=color.white, size=labelSize)
                array.push(sessionLabels, lowLabel)

        // Draw historical London session lines
        if not na(histLondonHigh) and not na(histLondonLow)
            londonTransparency = getLineTransparency(dayOffset)

            londonHighLineHist = line.new(x1=histLondonHighBar, y1=histLondonHigh, x2=histLondonHighBar + lineExtension, y2=histLondonHigh, color=color.new(londonHighColor, londonTransparency), width=lineWidth, extend=extend.none)
            londonLowLineHist = line.new(x1=histLondonLowBar, y1=histLondonLow, x2=histLondonLowBar + lineExtension, y2=histLondonLow, color=color.new(londonLowColor, londonTransparency), width=lineWidth, extend=extend.none)

            array.push(londonHighLines, londonHighLineHist)
            array.push(londonLowLines, londonLowLineHist)

            // Add labels if enabled
            if showLabels
                londonHighLabelText = "LH-" + str.tostring(dayOffset) + ": " + str.tostring(histLondonHigh, "#.##")
                londonHighLabel = label.new(x=histLondonHighBar + lineExtension/2, y=histLondonHigh, text=londonHighLabelText, style=label.style_label_down, color=color.new(londonHighColor, londonTransparency), textcolor=color.white, size=labelSize)
                array.push(sessionLabels, londonHighLabel)

                londonLowLabelText = "LL-" + str.tostring(dayOffset) + ": " + str.tostring(histLondonLow, "#.##")
                londonLowLabel = label.new(x=histLondonLowBar + lineExtension/2, y=histLondonLow, text=londonLowLabelText, style=label.style_label_up, color=color.new(londonLowColor, londonTransparency), textcolor=color.white, size=labelSize)
                array.push(sessionLabels, londonLowLabel)

// === BREAKOUT DETECTION === //
// Define breakout conditions
asianHighBreakout = showBreakouts and not na(asianHigh) and close > asianHigh
asianLowBreakdown = showBreakouts and not na(asianLow) and close < asianLow
londonHighBreakout = showBreakouts and not na(londonHigh) and close > londonHigh
londonLowBreakdown = showBreakouts and not na(londonLow) and close < londonLow

// Generate alerts
if asianHighBreakout
    alert("Asian High Breakout: " + str.tostring(close), alert.freq_once_per_bar)
if asianLowBreakdown
    alert("Asian Low Breakdown: " + str.tostring(close), alert.freq_once_per_bar)
if londonHighBreakout
    alert("London High Breakout: " + str.tostring(close), alert.freq_once_per_bar)
if londonLowBreakdown
    alert("London Low Breakdown: " + str.tostring(close), alert.freq_once_per_bar)

// Plot breakout shapes (must be in global scope)
plotshape(asianHighBreakout, style=shape.triangleup, location=location.belowbar, color=breakoutColor, size=size.small, title="Asian High Breakout")
plotshape(asianLowBreakdown, style=shape.triangledown, location=location.abovebar, color=breakoutColor, size=size.small, title="Asian Low Breakdown")
plotshape(londonHighBreakout, style=shape.triangleup, location=location.belowbar, color=breakoutColor, size=size.small, title="London High Breakout")
plotshape(londonLowBreakdown, style=shape.triangledown, location=location.abovebar, color=breakoutColor, size=size.small, title="London Low Breakdown")
