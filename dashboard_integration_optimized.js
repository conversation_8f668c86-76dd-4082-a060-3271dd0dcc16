/**
 * Dashboard Integration for Optimized Trading Bot
 * 
 * This script provides integration between the optimized trading bot and the dashboard.
 */

// Configuration
const REFRESH_INTERVAL = 5000; // 5 seconds
const API_ENDPOINT = 'http://localhost:3000/api';
const SYMBOLS = ['MNQ', 'MES', 'MGC'];
const COLORS = {
    MNQ: '#00ccff',
    MES: '#ff00aa',
    MGC: '#ffcc00'
};

// State
let botStatus = {
    isConnected: false,
    isInitialized: false,
    activePositions: 0,
    activeOrders: 0,
    circuitBreakers: {
        isTripped: false,
        consecutiveLossCount: 0,
        dailyLoss: 0,
        lastResetTime: null
    },
    cacheStats: {
        contractCache: {
            size: 0,
            hits: 0,
            misses: 0,
            hitRate: 0
        },
        chartDataCache: {
            size: 0,
            hits: 0,
            misses: 0,
            hitRate: 0
        }
    }
};

let tradingData = {
    MNQ: {
        positions: [],
        orders: [],
        performance: {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            totalProfit: 0,
            totalLoss: 0,
            netPnL: 0,
            averageWin: 0,
            averageLoss: 0,
            largestWin: 0,
            largestLoss: 0,
            profitFactor: 0
        },
        candles: []
    },
    MES: {
        positions: [],
        orders: [],
        performance: {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            totalProfit: 0,
            totalLoss: 0,
            netPnL: 0,
            averageWin: 0,
            averageLoss: 0,
            largestWin: 0,
            largestLoss: 0,
            profitFactor: 0
        },
        candles: []
    },
    MGC: {
        positions: [],
        orders: [],
        performance: {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            totalProfit: 0,
            totalLoss: 0,
            netPnL: 0,
            averageWin: 0,
            averageLoss: 0,
            largestWin: 0,
            largestLoss: 0,
            profitFactor: 0
        },
        candles: []
    }
};

// DOM Elements
let statusIndicator;
let statusText;
let activePositionsElement;
let activeOrdersElement;
let circuitBreakerStatusElement;
let cacheStatsElement;
let performanceTableElement;
let symbolTabsElement;
let symbolContentElement;
let chartContainerElement;
let startBotButton;
let stopBotButton;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements
    statusIndicator = document.getElementById('status-indicator');
    statusText = document.getElementById('status-text');
    activePositionsElement = document.getElementById('active-positions');
    activeOrdersElement = document.getElementById('active-orders');
    circuitBreakerStatusElement = document.getElementById('circuit-breaker-status');
    cacheStatsElement = document.getElementById('cache-stats');
    performanceTableElement = document.getElementById('performance-table');
    symbolTabsElement = document.getElementById('symbol-tabs');
    symbolContentElement = document.getElementById('symbol-content');
    chartContainerElement = document.getElementById('chart-container');
    startBotButton = document.getElementById('start-bot');
    stopBotButton = document.getElementById('stop-bot');
    
    // Initialize tabs
    initializeTabs();
    
    // Initialize charts
    initializeCharts();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Start data refresh
    startDataRefresh();
    
    // Initial data load
    loadDashboardData();
});

// Initialize tabs
function initializeTabs() {
    // Create tabs for each symbol
    SYMBOLS.forEach((symbol, index) => {
        // Create tab
        const tab = document.createElement('div');
        tab.className = `symbol-tab ${index === 0 ? 'active' : ''}`;
        tab.setAttribute('data-symbol', symbol);
        tab.innerHTML = `
            <div class="symbol-icon" style="background-color: ${COLORS[symbol]}"></div>
            <div class="symbol-name">${symbol}</div>
        `;
        symbolTabsElement.appendChild(tab);
        
        // Create content
        const content = document.createElement('div');
        content.className = `symbol-content ${index === 0 ? 'active' : ''}`;
        content.setAttribute('data-symbol', symbol);
        content.innerHTML = `
            <div class="symbol-header">
                <h2>${symbol} Trading Data</h2>
                <div class="symbol-stats">
                    <div class="stat">
                        <div class="stat-label">Win Rate</div>
                        <div class="stat-value" id="${symbol}-win-rate">0%</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Net P&L</div>
                        <div class="stat-value" id="${symbol}-net-pnl">$0.00</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">Profit Factor</div>
                        <div class="stat-value" id="${symbol}-profit-factor">0.00</div>
                    </div>
                </div>
            </div>
            <div class="symbol-chart" id="${symbol}-chart"></div>
            <div class="symbol-positions">
                <h3>Active Positions</h3>
                <div class="positions-table" id="${symbol}-positions">
                    <div class="table-header">
                        <div class="table-cell">ID</div>
                        <div class="table-cell">Action</div>
                        <div class="table-cell">Quantity</div>
                        <div class="table-cell">Entry Price</div>
                        <div class="table-cell">Stop Loss</div>
                        <div class="table-cell">Take Profit</div>
                    </div>
                    <div class="table-body" id="${symbol}-positions-body">
                        <div class="table-row empty">
                            <div class="table-cell" colspan="6">No active positions</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="symbol-orders">
                <h3>Active Orders</h3>
                <div class="orders-table" id="${symbol}-orders">
                    <div class="table-header">
                        <div class="table-cell">ID</div>
                        <div class="table-cell">Action</div>
                        <div class="table-cell">Quantity</div>
                        <div class="table-cell">Price</div>
                        <div class="table-cell">Status</div>
                    </div>
                    <div class="table-body" id="${symbol}-orders-body">
                        <div class="table-row empty">
                            <div class="table-cell" colspan="5">No active orders</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        symbolContentElement.appendChild(content);
    });
    
    // Add tab click event listeners
    document.querySelectorAll('.symbol-tab').forEach(tab => {
        tab.addEventListener('click', () => {
            const symbol = tab.getAttribute('data-symbol');
            
            // Update active tab
            document.querySelectorAll('.symbol-tab').forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Update active content
            document.querySelectorAll('.symbol-content').forEach(c => c.classList.remove('active'));
            document.querySelector(`.symbol-content[data-symbol="${symbol}"]`).classList.add('active');
        });
    });
}

// Initialize charts
function initializeCharts() {
    // Initialize charts for each symbol
    SYMBOLS.forEach(symbol => {
        // Create chart
        const chartElement = document.getElementById(`${symbol}-chart`);
        
        // In a real implementation, you would create a chart here
        chartElement.innerHTML = `<div class="chart-placeholder">Chart for ${symbol} will be displayed here</div>`;
    });
}

// Initialize event listeners
function initializeEventListeners() {
    // Start bot button
    startBotButton.addEventListener('click', () => {
        startBot();
    });
    
    // Stop bot button
    stopBotButton.addEventListener('click', () => {
        stopBot();
    });
}

// Start data refresh
function startDataRefresh() {
    setInterval(() => {
        loadDashboardData();
    }, REFRESH_INTERVAL);
}

// Load dashboard data
function loadDashboardData() {
    // In a real implementation, you would fetch data from the API
    // For now, we'll use mock data
    
    // Update bot status
    updateBotStatus({
        isConnected: Math.random() > 0.2,
        isInitialized: Math.random() > 0.2,
        activePositions: Math.floor(Math.random() * 5),
        activeOrders: Math.floor(Math.random() * 10),
        circuitBreakers: {
            isTripped: Math.random() > 0.8,
            consecutiveLossCount: Math.floor(Math.random() * 3),
            dailyLoss: Math.random() * 500,
            lastResetTime: new Date().toISOString()
        },
        cacheStats: {
            contractCache: {
                size: Math.floor(Math.random() * 100),
                hits: Math.floor(Math.random() * 1000),
                misses: Math.floor(Math.random() * 200),
                hitRate: Math.random()
            },
            chartDataCache: {
                size: Math.floor(Math.random() * 500),
                hits: Math.floor(Math.random() * 5000),
                misses: Math.floor(Math.random() * 1000),
                hitRate: Math.random()
            }
        }
    });
    
    // Update trading data for each symbol
    SYMBOLS.forEach(symbol => {
        updateTradingData(symbol, {
            positions: generateMockPositions(symbol),
            orders: generateMockOrders(symbol),
            performance: generateMockPerformance(symbol),
            candles: generateMockCandles(symbol)
        });
    });
}

// Update bot status
function updateBotStatus(status) {
    botStatus = status;
    
    // Update status indicator
    if (statusIndicator) {
        statusIndicator.className = `status-dot ${status.isConnected ? 'active' : ''}`;
    }
    
    // Update status text
    if (statusText) {
        statusText.textContent = status.isConnected ? 'Connected' : 'Disconnected';
        statusText.className = `status-text ${status.isConnected ? 'connected' : 'disconnected'}`;
    }
    
    // Update active positions
    if (activePositionsElement) {
        activePositionsElement.textContent = status.activePositions;
    }
    
    // Update active orders
    if (activeOrdersElement) {
        activeOrdersElement.textContent = status.activeOrders;
    }
    
    // Update circuit breaker status
    if (circuitBreakerStatusElement) {
        circuitBreakerStatusElement.textContent = status.circuitBreakers.isTripped ? 'Tripped' : 'Normal';
        circuitBreakerStatusElement.className = `circuit-breaker-status ${status.circuitBreakers.isTripped ? 'tripped' : 'normal'}`;
    }
    
    // Update cache stats
    if (cacheStatsElement) {
        cacheStatsElement.innerHTML = `
            <div class="cache-stat">
                <div class="cache-stat-label">Contract Cache</div>
                <div class="cache-stat-value">${status.cacheStats.contractCache.size} items</div>
                <div class="cache-stat-value">${(status.cacheStats.contractCache.hitRate * 100).toFixed(2)}% hit rate</div>
            </div>
            <div class="cache-stat">
                <div class="cache-stat-label">Chart Data Cache</div>
                <div class="cache-stat-value">${status.cacheStats.chartDataCache.size} items</div>
                <div class="cache-stat-value">${(status.cacheStats.chartDataCache.hitRate * 100).toFixed(2)}% hit rate</div>
            </div>
        `;
    }
}

// Update trading data
function updateTradingData(symbol, data) {
    tradingData[symbol] = data;
    
    // Update win rate
    const winRateElement = document.getElementById(`${symbol}-win-rate`);
    if (winRateElement) {
        winRateElement.textContent = `${(data.performance.winRate * 100).toFixed(2)}%`;
    }
    
    // Update net P&L
    const netPnLElement = document.getElementById(`${symbol}-net-pnl`);
    if (netPnLElement) {
        netPnLElement.textContent = `$${data.performance.netPnL.toFixed(2)}`;
        netPnLElement.className = `stat-value ${data.performance.netPnL >= 0 ? 'positive' : 'negative'}`;
    }
    
    // Update profit factor
    const profitFactorElement = document.getElementById(`${symbol}-profit-factor`);
    if (profitFactorElement) {
        profitFactorElement.textContent = data.performance.profitFactor.toFixed(2);
    }
    
    // Update positions
    const positionsBodyElement = document.getElementById(`${symbol}-positions-body`);
    if (positionsBodyElement) {
        if (data.positions.length === 0) {
            positionsBodyElement.innerHTML = `
                <div class="table-row empty">
                    <div class="table-cell" colspan="6">No active positions</div>
                </div>
            `;
        } else {
            positionsBodyElement.innerHTML = data.positions.map(position => `
                <div class="table-row">
                    <div class="table-cell">${position.id}</div>
                    <div class="table-cell ${position.action === 'Buy' ? 'buy' : 'sell'}">${position.action}</div>
                    <div class="table-cell">${position.quantity}</div>
                    <div class="table-cell">${position.entryPrice.toFixed(2)}</div>
                    <div class="table-cell">${position.stopLossPrice.toFixed(2)}</div>
                    <div class="table-cell">${position.takeProfitPrice.toFixed(2)}</div>
                </div>
            `).join('');
        }
    }
    
    // Update orders
    const ordersBodyElement = document.getElementById(`${symbol}-orders-body`);
    if (ordersBodyElement) {
        if (data.orders.length === 0) {
            ordersBodyElement.innerHTML = `
                <div class="table-row empty">
                    <div class="table-cell" colspan="5">No active orders</div>
                </div>
            `;
        } else {
            ordersBodyElement.innerHTML = data.orders.map(order => `
                <div class="table-row">
                    <div class="table-cell">${order.id}</div>
                    <div class="table-cell ${order.action === 'Buy' ? 'buy' : 'sell'}">${order.action}</div>
                    <div class="table-cell">${order.quantity}</div>
                    <div class="table-cell">${order.price.toFixed(2)}</div>
                    <div class="table-cell">${order.status}</div>
                </div>
            `).join('');
        }
    }
    
    // Update chart
    // In a real implementation, you would update the chart with the candles data
}

// Start bot
function startBot() {
    // In a real implementation, you would call the API to start the bot
    console.log('Starting bot...');
    
    // Update UI
    startBotButton.disabled = true;
    stopBotButton.disabled = false;
    
    // Update status
    updateBotStatus({
        ...botStatus,
        isConnected: true,
        isInitialized: true
    });
}

// Stop bot
function stopBot() {
    // In a real implementation, you would call the API to stop the bot
    console.log('Stopping bot...');
    
    // Update UI
    startBotButton.disabled = false;
    stopBotButton.disabled = true;
    
    // Update status
    updateBotStatus({
        ...botStatus,
        isConnected: false,
        isInitialized: false
    });
}

// Generate mock positions
function generateMockPositions(symbol) {
    const count = Math.floor(Math.random() * 3);
    const positions = [];
    
    for (let i = 0; i < count; i++) {
        const action = Math.random() > 0.5 ? 'Buy' : 'Sell';
        const entryPrice = Math.random() * 1000 + 1000;
        
        positions.push({
            id: `${symbol}-${Math.floor(Math.random() * 1000)}`,
            action,
            quantity: 10,
            entryPrice,
            stopLossPrice: action === 'Buy' ? entryPrice * 0.99 : entryPrice * 1.01,
            takeProfitPrice: action === 'Buy' ? entryPrice * 1.01 : entryPrice * 0.99,
            timestamp: new Date().toISOString()
        });
    }
    
    return positions;
}

// Generate mock orders
function generateMockOrders(symbol) {
    const count = Math.floor(Math.random() * 5);
    const orders = [];
    
    for (let i = 0; i < count; i++) {
        const action = Math.random() > 0.5 ? 'Buy' : 'Sell';
        const price = Math.random() * 1000 + 1000;
        
        orders.push({
            id: `${symbol}-${Math.floor(Math.random() * 1000)}`,
            action,
            quantity: 10,
            price,
            status: ['Pending', 'Filled', 'Cancelled'][Math.floor(Math.random() * 3)],
            timestamp: new Date().toISOString()
        });
    }
    
    return orders;
}

// Generate mock performance
function generateMockPerformance(symbol) {
    const totalTrades = Math.floor(Math.random() * 1000) + 100;
    const winRate = Math.random() * 0.5 + 0.3;
    const winningTrades = Math.floor(totalTrades * winRate);
    const losingTrades = totalTrades - winningTrades;
    const averageWin = Math.random() * 100 + 50;
    const averageLoss = Math.random() * 50 + 25;
    const totalProfit = winningTrades * averageWin;
    const totalLoss = losingTrades * averageLoss;
    const netPnL = totalProfit - totalLoss;
    
    return {
        totalTrades,
        winningTrades,
        losingTrades,
        winRate,
        totalProfit,
        totalLoss,
        netPnL,
        averageWin,
        averageLoss,
        largestWin: averageWin * (Math.random() * 2 + 1),
        largestLoss: averageLoss * (Math.random() * 2 + 1),
        profitFactor: totalLoss === 0 ? totalProfit : totalProfit / totalLoss
    };
}

// Generate mock candles
function generateMockCandles(symbol) {
    const count = 100;
    const candles = [];
    
    let price = Math.random() * 1000 + 1000;
    
    for (let i = 0; i < count; i++) {
        const change = (Math.random() - 0.5) * 10;
        price += change;
        
        candles.push({
            timestamp: new Date(Date.now() - (count - i) * 60000).toISOString(),
            open: price - change,
            high: Math.max(price, price - change) + Math.random() * 5,
            low: Math.min(price, price - change) - Math.random() * 5,
            close: price,
            volume: Math.floor(Math.random() * 1000) + 100
        });
    }
    
    return candles;
}
