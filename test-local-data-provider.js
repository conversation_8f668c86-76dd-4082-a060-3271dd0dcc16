/**
 * Test script for local data provider
 */

require('dotenv').config();
const marketDataService = require('./market-data-service');
const logger = require('./logger');

// Test functions
async function testHistorical() {
  try {
    console.log('Testing historical data...');
    
    // Get historical data for all symbols
    const symbols = ['MNQ', 'MES', 'MGC', 'M2K'];
    const endDate = new Date('2025-05-01T00:00:00Z');
    const startDate = new Date('2025-04-30T00:00:00Z');
    
    for (const symbol of symbols) {
      console.log(`\nGetting historical data for ${symbol}...`);
      
      const historicalData = await marketDataService.getHistoricalData(
        symbol,
        startDate,
        endDate,
        '1m'
      );
      
      console.log(`Got ${historicalData.length} bars`);
      if (historicalData.length > 0) {
        console.log('Sample data:');
        console.log(historicalData.slice(0, 3));
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error testing historical data:', error);
    return false;
  }
}

async function testSimulation() {
  try {
    console.log('\nTesting data simulation...');
    
    // Subscribe to all symbols
    const symbols = ['MNQ', 'MES', 'MGC', 'M2K'];
    const callbacks = new Map();
    const messageCounts = new Map();
    
    for (const symbol of symbols) {
      console.log(`Subscribing to ${symbol}...`);
      
      messageCounts.set(symbol, 0);
      
      const dataHandler = (data) => {
        const count = messageCounts.get(symbol) + 1;
        messageCounts.set(symbol, count);
        
        if (count <= 3) {
          console.log(`Received data for ${symbol}:`, data);
        }
      };
      
      callbacks.set(symbol, dataHandler);
      
      await marketDataService.subscribeToMarketData(symbol, dataHandler);
    }
    
    // Simulate data for a specific time
    console.log('Simulating data for 2025-05-01...');
    const simulationTime = new Date('2025-05-01T00:00:00Z');
    
    // Simulate data 10 times
    for (let i = 0; i < 10; i++) {
      // Add 1 minute to the simulation time
      simulationTime.setMinutes(simulationTime.getMinutes() + 1);
      
      // Simulate data
      marketDataService.simulateRealTimeData(simulationTime);
      
      // Wait a short time
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Print message counts
    console.log('\nMessage counts:');
    for (const [symbol, count] of messageCounts.entries()) {
      console.log(`- ${symbol}: ${count} messages`);
    }
    
    // Unsubscribe from all symbols
    console.log('\nUnsubscribing...');
    for (const symbol of symbols) {
      await marketDataService.unsubscribeFromMarketData(symbol);
    }
    
    return true;
  } catch (error) {
    console.error('Error testing simulation:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('Starting local data provider tests...');
  
  // Initialize market data service
  console.log('Initializing market data service...');
  await marketDataService.initialize();
  
  let success = true;
  
  // Test historical data
  if (await testHistorical()) {
    console.log('\n✅ Historical data test passed');
  } else {
    console.log('\n❌ Historical data test failed');
    success = false;
  }
  
  // Test simulation
  if (await testSimulation()) {
    console.log('\n✅ Simulation test passed');
  } else {
    console.log('\n❌ Simulation test failed');
    success = false;
  }
  
  // Disconnect
  console.log('\nDisconnecting...');
  await marketDataService.disconnect();
  
  console.log('\nTests completed.');
  if (success) {
    console.log('All tests passed! 🎉');
  } else {
    console.log('Some tests failed. 😢');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
