// ========================
// backtest_m2k.js
// V10.50: Optimized for Micro Russell 2000 (M2K) Futures
// ========================

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
// Configuration will be required dynamically, e.g., const config = require('./config_m2k_grid_test');
let config; // Will be set when runWeeklyBacktests is called with a specific config
const spreadUtils = require('./spread_volatility_utils'); // Assuming this exists
const enhancedPM = require('./enhanced_position_management'); // Assuming this exists

// Import and initialize ML filter
let M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AdvancedMLFilter, mlFilter;
// Import and initialize time analysis
let AdvancedTimeAnalysis, timeAnalyzer;
// Import and initialize dynamic position sizing
let DynamicPositionSizing, positionSizer;


// --- VERY EARLY LOG ---
// console.log("Script started. Attempting to require config..."); // Config will be passed
// --- END EARLY LOG ---


// --- *** Define Output Directory *** ---
// const outputDir = './output/M2K_Optimized_2020_2025'; // This will be set from config
// --- *********************************** ---

// --- 1) Setup & Initialization ---
// Output directory creation will happen based on config.outputDir
const allCandles = []; let allRunResults = [];
// Use ATR thresholds from config if available, otherwise use defaults
let atrThresholds; // = config.atrThresholds || { low_medium: 1.5, medium_high: 3.0 };
let adaptiveParams; /* = {
    Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
    Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
    High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
}; */

function initializeModules(currentConfig) {
    config = currentConfig; // Set global config for this run

    MLEntryFilter = config.useMLEntryFilter && !config.useAdvancedMLFilter ? require('./ml_entry_filter') : null;
    AdvancedMLFilter = config.useMLEntryFilter && config.useAdvancedMLFilter ? require('./advanced_ml_filter') : null;
    mlFilter = config.useMLEntryFilter ?
        (config.useAdvancedMLFilter ? new AdvancedMLFilter(config) : new MLEntryFilter(config)) :
        null;

    AdvancedTimeAnalysis = config.useTimeAnalysis ? require('./advanced_time_analysis') : null;
    timeAnalyzer = config.useTimeAnalysis ? new AdvancedTimeAnalysis(config) : null;

    DynamicPositionSizing = config.useDynamicPositionSizing ? require('./dynamic_position_sizing') : null;
    positionSizer = config.useDynamicPositionSizing ? new DynamicPositionSizing(config) : null;

    atrThresholds = config.atrThresholds || { low_medium: 1.5, medium_high: 3.0 };
    adaptiveParams = config.adaptiveParams || { // Provide defaults if not in config
        Low: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        Medium: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 },
        High: { slFactor: 8.0, tpFactor: 7.0, trailFactor: 0.02 }
    };

    if (!fs.existsSync(config.outputDir)) {
        try {
            fs.mkdirSync(config.outputDir, { recursive: true });
            console.log(`Created output directory: ${config.outputDir}`);
        } catch (err) {
            console.error(`Error creating output directory '${config.outputDir}':`, err);
            process.exit(1);
        }
    } else {
        console.log(`Output directory exists: ${config.outputDir}`);
    }
}


// --- Data Loading & Parsing ---
function loadDataAndRun(currentConfig) {
    initializeModules(currentConfig);
    allCandles.length = 0; // Clear previous data if any

    if (!config) { console.error("FATAL: Config object is undefined."); process.exit(1);}
    if (!config.inputFile || typeof config.inputFile !== 'string') { console.error("Error: 'inputFile' not defined or invalid in config.js"); process.exit(1); }
    if (!fs.existsSync(config.inputFile)) { console.error(`Error: Input file not found at path: ${config.inputFile}`); process.exit(1); }
    console.log(`Attempting to read: ${config.inputFile}`);

    fs.createReadStream(config.inputFile)
        .pipe(csv({ separator: ';', mapHeaders: ({ header }) => header.trim() }))
        .on('data', d => {
            const open = +d['Open'];
            const high = +d['High'];
            const low = +d['Low'];
            const close = +d['Close'];
            const timeString = d['Time'] || d['Date'] || d['Time left']; /* Add other potential headers */
            let timestampSeconds = NaN;
            if (timeString) {
                let parsedDate;
                try { parsedDate = new Date(timeString); } catch (e) {}
                if (parsedDate && !isNaN(parsedDate.getTime())) {
                    timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                } else if (timeString && !isNaN(Number(timeString))) {
                    const tsNum = Number(timeString);
                    timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                }
            }
            if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
                /* console.warn("Skipping row with invalid data:", d); */ return;
            }
            allCandles.push({ timestamp: timestampSeconds, open, high, low, close });
        })
        .on('end', () => {
            console.log("CSV parsing finished ('end' event received).");
            if (allCandles.length === 0) {
                console.error(`Error: No valid candle data parsed from ${config.inputFile}. Check headers (Open,High,Low,Close,Time/Date/'Time left').`);
                process.exit(1);
            }
            console.log(`Parsed ${allCandles.length} initial candles...`);
            allCandles.sort((a, b) => a.timestamp - b.timestamp);
            console.log(`Data time range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);
            console.log("Calling runWeeklyBacktests...");
            runWeeklyBacktests(); // config is now globally set via initializeModules
            console.log(">>> Finished calling runWeeklyBacktests from 'end' handler.");
        })
        .on('error', (err) => {
            console.error(`Error reading CSV file ${config.inputFile}:`, err);
            process.exit(1);
        });
}

// --- Indicator Helper Functions ---
function SMA(arr, period, index) { if (!arr || index < period - 1 || arr.length <= index) return NaN; let sum = 0; let validCount = 0; for (let j = index - period + 1; j <= index; j++) { if (typeof arr[j] === 'number' && !isNaN(arr[j])) { sum += arr[j]; validCount++; } else { return NaN; } } return validCount === period ? sum / period : NaN; }
function RSI(arr, period, index) { if (!arr || index < period || arr.length <= index) return NaN; let gains = 0, losses = 0; let validDeltas = 0; for (let j = index - period + 1; j <= index; j++) { if (j > 0 && typeof arr[j - 1] === 'number' && !isNaN(arr[j - 1]) && typeof arr[j] === 'number' && !isNaN(arr[j])) { const delta = arr[j] - arr[j - 1]; if (delta > 0) { gains += delta; } else { losses -= delta; } validDeltas++; } else { return NaN; } } if (validDeltas < period) return NaN; if (losses === 0) return 100; if (gains === 0) return 0; const avgGain = gains / period, avgLoss = losses / period, rs = avgGain / avgLoss; return 100 - (100 / (1 + rs)); }
function WMA(arr, period, index) { if (!arr || index < period - 1 || arr.length <= index) return NaN; let weightedSum = 0, weightSum = 0, validCount = 0; for (let j = 0; j < period; j++) { const idx = index - j, weight = period - j; if (idx < 0) return NaN; if (typeof arr[idx] === 'number' && !isNaN(arr[idx])) { weightedSum += arr[idx] * weight; weightSum += weight; validCount++; } else { return NaN; } } return validCount === period ? weightedSum / weightSum : NaN; }

// --- Other Helper Functions ---
function candlestickColor(candle) { if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) return 'invalid'; return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji'; }
function getWeekIdentifier(timestamp) { const date = new Date(timestamp * 1000); if (isNaN(date.getTime())) { return "InvalidDate"; } const year = date.getUTCFullYear(); const startOfYear = new Date(Date.UTC(year, 0, 1)); if (isNaN(startOfYear.getTime())) { return "InvalidYearStartDate"; } const timeDiff = date.getTime() - startOfYear.getTime(); if (isNaN(timeDiff)) { return "InvalidTimeDiff"; } const daysPassed = Math.floor(timeDiff / (1000 * 60 * 60 * 24)); const firstDayOfYear = startOfYear.getUTCDay(); const weekNo = Math.floor((daysPassed + firstDayOfYear) / 7) + 1; if (isNaN(weekNo) || weekNo < 1 || weekNo > 53) { return "InvalidWeekNo"; } return `${year}-W${weekNo.toString().padStart(2, '0')}`; }
function getDayIdentifier(timestamp) { const date = new Date(timestamp * 1000); return date.toISOString().slice(0, 10); }

// --- 2) Global Indicator Calculation ---
function computeGlobalIndicators(candles) { console.log("Computing global indicators..."); if (!candles || !Array.isArray(candles) || candles.length === 0) { throw new Error("computeGlobalIndicators invalid candle data."); } if (!candles[0]?.close) { throw new Error("computeGlobalIndicators invalid candle structure."); } const closes = candles.map(c => c?.close); const sma200Arr = config.sma200Period > 0 ? closes.map((_, i) => SMA(closes, config.sma200Period, i)) : new Array(candles.length).fill(NaN); const wma50Arr = config.wma50Period > 0 ? closes.map((_, i) => WMA(closes, config.wma50Period, i)) : new Array(candles.length).fill(NaN); const rsiArr = config.rsiPeriod > 0 ? closes.map((_, i) => RSI(closes, config.rsiPeriod, i)) : new Array(candles.length).fill(NaN); const rsiMaArr = config.rsiMaPeriod > 0 && config.rsiPeriod > 0 ? rsiArr.map((_, i) => SMA(rsiArr, config.rsiMaPeriod, i)) : new Array(candles.length).fill(NaN); const trs = []; for (let i = 0; i < candles.length; i++) { const cur = candles[i]; if (!cur) { trs.push(NaN); continue; } if (i === 0) { if (!isNaN(cur.high) && !isNaN(cur.low)) { trs.push(cur.high - cur.low); } else { trs.push(NaN); } continue; } const prev = candles[i - 1]; if (!prev || isNaN(prev.close) || isNaN(cur.high) || isNaN(cur.low)) { trs.push(NaN); continue; } trs.push(Math.max(cur.high - cur.low, Math.abs(cur.high - prev.close), Math.abs(cur.low - prev.close))); } const atrs = config.atrPeriod > 0 ? trs.map((_, i) => SMA(trs, config.atrPeriod, i)) : new Array(trs.length).fill(NaN); candles.forEach((c, i) => { if (c) { c.sma200 = sma200Arr[i]; c.wma50 = wma50Arr[i]; c.rsi = rsiArr[i]; c.rsiMa = rsiMaArr[i]; c.atr = (i >= config.atrPeriod - 1 && i < atrs.length) ? atrs[i] : NaN; } }); console.log("Global indicators computed."); }

// --- 3) Weekly Data Segmentation ---
function segmentDataIntoWeeks(candlesWithIndicators) { console.log("Segmenting data into weeks (using simple Week definition)..."); if (!candlesWithIndicators || !Array.isArray(candlesWithIndicators)) { console.error("Error: segmentDataIntoWeeks received invalid input."); return {}; } const weeklyData = {}; let invalidWeekIdCount = 0; let validWeekIdCount = 0; for (const candle of candlesWithIndicators) { if (!candle || isNaN(candle.timestamp)) { continue; } const weekId = getWeekIdentifier(candle.timestamp); if (!weekId || typeof weekId !== 'string' || weekId.includes("Invalid") || weekId.includes("NaN")) { invalidWeekIdCount++; continue; } else { validWeekIdCount++; if (!weeklyData[weekId]) { weeklyData[weekId] = []; } weeklyData[weekId].push(candle); } } if (invalidWeekIdCount > 0) { console.warn(`Warning: Skipped ${invalidWeekIdCount} candles due to invalid week ID generation.`); } if (validWeekIdCount === 0 && candlesWithIndicators.length > 0) { console.error("Error: No valid week IDs generated for any candles!"); } console.log(`Segmented data into ${Object.keys(weeklyData).length} weeks.`); const sortedWeeks = Object.keys(weeklyData).sort(); const sortedWeeklyData = {}; for(const weekId of sortedWeeks){ if (weeklyData[weekId]?.length > 0) sortedWeeklyData[weekId] = weeklyData[weekId]; } return sortedWeeklyData; }

// --- 4) Main Workflow: Run Backtests Weekly + Overall Metrics ---
function runWeeklyBacktests() { // config is now global for this execution context
    console.log(">>> runWeeklyBacktests function entered.");
    try {
        console.log("Starting runWeeklyBacktests try block...");
        computeGlobalIndicators(allCandles);
        console.log("Finished computeGlobalIndicators.");
        const weeklySegmentedData = segmentDataIntoWeeks(allCandles);
        console.log("Finished segmentDataIntoWeeks.");

        if (typeof weeklySegmentedData !== 'object' || weeklySegmentedData === null) { console.error("Error: segmentDataIntoWeeks did not return a valid object."); return; }
        const totalWeeksAllData = Object.keys(weeklySegmentedData).length;
        console.log(`Total weeks identified: ${totalWeeksAllData}`);
        if (totalWeeksAllData === 0) { console.error("Error: No weeks identified for backtesting. Exiting."); return; }

        allRunResults = []; // Reset for this config run
        let runFullTradeLog = []; // This will hold the trade log for the *last parameter set only* for detailed inspection

        console.log("Determining run type and parameters...");
        const isAdaptive = config.isAdaptiveRun === true;
        const isCostGridTest = config.costGrid?.length > 0; // Usually for advanced specific tests
        const isRiskGridTest = config.riskPercentGrid?.length > 0;
        const isFixedContractsGridTest = config.fixedContractsGrid?.length > 0;
        const isParameterGridTest = !isAdaptive && ( (Array.isArray(config.slFactors) && config.slFactors.length > 0) || (Array.isArray(config.tpFactors) && config.tpFactors.length > 0) || (Array.isArray(config.trailFactors) && config.trailFactors.length > 0) );
        let parameterSets = [];
        console.log(` - Flags: isAdaptive=${isAdaptive}, isCostGrid=${isCostGridTest}, isRiskGrid=${isRiskGridTest}, isFixedContractsGrid=${isFixedContractsGridTest}, isParameterGrid=${isParameterGridTest}`);

        console.log("Attempting to build parameter sets...");

        if (isParameterGridTest) {
            console.log(" -> Detected Parameter Grid Test condition.");
            parameterSets = getParameterCombinations(); // This uses global config
            if (!Array.isArray(parameterSets)) { console.error("FATAL: Param gen failed!"); parameterSets = [];}
            else if (parameterSets.length === 0) { console.error("PARAM GRID ERROR: No valid combinations generated from config.slFactors, config.tpFactors, config.trailFactors."); }
            else { console.log(` -> Running FIXED Parameter Grid Test (${parameterSets.length} combinations)...`); }
        } else if (isFixedContractsGridTest) {
            console.log(" -> Building Fixed Contracts Grid Sets...");
            const baseParamsList = getParameterCombinations();
            if (isAdaptive || baseParamsList.length !== 1) { console.error("GRID ERROR: Contracts Grid needs single base params (SL/TP/Trail). Ensure config.slFactors etc. define only one value for this mode."); return; }
            const baseParams = baseParamsList[0];
            parameterSets = config.fixedContractsGrid.map(c => typeof c==='number'&&c>0 ? {...baseParams, fixedContracts:c, isAdaptive:false, riskPercent:0, commission:config.commissionPerContract, slippage:config.slippagePoints, latencyDelayBars:config.latencyDelayBars, fixedTp:config.fixedTpPoints} : null).filter(p=>p);
            if (parameterSets.length === 0) { console.error("GRID ERROR: No valid contract sizes in config.fixedContractsGrid."); }
        } else if (isCostGridTest) {
            // ... (cost grid logic - less common for initial broad tests)
            console.warn("Cost Grid Test is less common for initial broad grid testing. Ensure config is set appropriately.");
        } else if (isRiskGridTest) {
            // ... (risk grid logic)
            console.warn("Risk Grid Test is less common for initial broad grid testing. Ensure config is set appropriately.");
        } else { // Single Run
            console.log(" -> Building Single Run Set...");
            let baseParams;
            if (isAdaptive) {
                baseParams = { isAdaptive: true, SL: 'Adaptive', TP: 'Adaptive', Trail: 'Adaptive' }; // Use symbolic names for adaptive
                console.log(" -> Mode: SINGLE ADAPTIVE");
            } else {
                const bpl = getParameterCombinations(); // Will get SL/TP/Trail from config
                if (!Array.isArray(bpl) || bpl.length === 0) { console.error("RUN ERROR: No valid SL/TP/Trail params found for Single Run. Check config.slFactors etc."); return; }
                if (bpl.length > 1) console.warn("WARN: Multiple base params (SL/TP/Trail combinations) found for Single Run. Using the first one.");
                baseParams = bpl[0];
                console.log(" -> Mode: SINGLE FIXED with SL=", baseParams.slFactor, "TP=", baseParams.tpFactor, "Trail=", baseParams.trailFactor);
            }
            const riskP = config.riskPercent||0;
            const contracts = riskP > 0 ? 0 : (config.fixedContracts || 1); // If risk % is set, fixedContracts is effectively 0 unless risk calc fails
            parameterSets = [{
                ...baseParams,
                fixedTp: config.fixedTpPoints||0,
                riskPercent: riskP,
                fixedContracts: contracts,
                commission: config.commissionPerContract,
                slippage: config.slippagePoints,
                latencyDelayBars: config.latencyDelayBars,
                isAdaptive: isAdaptive // Explicitly carry adaptive flag
            }];
        }

        console.log(` -> Generated ${parameterSets ? parameterSets.length : 'undefined'} parameter sets (final assignment).`);
        parameterSets = Array.isArray(parameterSets) ? parameterSets : [];
        if (parameterSets.length === 0) { console.error("Error: No parameter sets to process after setup. Check your config for factor arrays or single run settings."); return; }


        console.log(`Starting parameter set loop (processing ${parameterSets.length} sets)...`);
        for (const params of parameterSets) {
             // Set current run parameters within the global config for access by other functions
             config.currentRunParams = { ...params }; // Make a copy
             config.fixedTpPoints = params.fixedTp !== undefined ? params.fixedTp : (config.fixedTpPoints || 0);
             config.currentRiskPercent = params.riskPercent !== undefined ? params.riskPercent : (config.riskPercent || 0);
             config.currentFixedContracts = params.fixedContracts !== undefined ? params.fixedContracts : (config.fixedContracts || 1);
             config.commissionPerContract = params.commission !== undefined ? params.commission : config.commissionPerContract;
             config.slippagePoints = params.slippage !== undefined ? params.slippage : config.slippagePoints;
             config.latencyDelayBars = params.latencyDelayBars !== undefined ? params.latencyDelayBars : (config.latencyDelayBars || 0);

             const isCurrentRunAdaptive = params.isAdaptive ?? false;
             const trailStepSizeMultiplier = params.trailStepSizeMultiplier || config.trailStepSizeMultiplier; // For stepped trail

             const baseLabel = isCurrentRunAdaptive ? 'Adaptive' : `SL=${params.slFactor},TP=${params.tpFactor},Tr=${params.trailFactor}` + (config.useSteppedTrail && trailStepSizeMultiplier ? `,TrStep=${trailStepSizeMultiplier}` : "");
             let additions = [];
             if (config.fixedTpPoints > 0) additions.push(`MinTP=${config.fixedTpPoints}`);

             if (config.currentRiskPercent > 0) {
                 additions.push(`Risk=${(config.currentRiskPercent * 100).toFixed(1)}%`);
                 if(config.maxContracts < Infinity && config.maxContracts > 0) additions.push(`Cap=${config.maxContracts}`);
             } else if (config.currentFixedContracts >= 1) {
                 additions.push(`Contracts=${config.currentFixedContracts}`);
             } else { // Fallback if neither risk nor fixed contracts properly set
                 config.currentFixedContracts = 1;
                 additions.push(`Contracts=1 (fallback)`);
             }

             if (config.latencyDelayBars > 0) additions.push(`Latency=${config.latencyDelayBars}bar`);

             // Only add cost info if it's a cost grid or differs from typical defaults
             const defaultComm = 1.00; // Example default
             const defaultSlip = 0.25; // Example default
             if (isCostGridTest || config.commissionPerContract !== defaultComm || config.slippagePoints !== defaultSlip) {
                 const rtSlipCost = config.slippagePoints * config.pointValue;
                 const totalRT = config.commissionPerContract + rtSlipCost;
                 additions.push(`Costs(C=${config.commissionPerContract.toFixed(2)}/S=${config.slippagePoints.toFixed(config.pricePrecision || 2)}pt=$${totalRT.toFixed(2)}RT)`);
             }
             const runLabel = baseLabel + (additions.length > 0 ? ', ' + additions.join(', ') : '');
             console.log(`\nTesting Params: ${runLabel}`);

             let currentEquity = config.initialBalance, overallPeakBalance = config.initialBalance, overallMaxDrawdown = 0;
             let weeklyReturns = [], weeklyResultsForParamSet = [];
             runFullTradeLog = []; // Clear for this specific parameter set's detailed log
             let dailyPnLMap = new Map(), weekCounter = 0;

             const weeklyDataCopy = JSON.parse(JSON.stringify(weeklySegmentedData)); // Deep copy for each param set
             const weeksToProcess = Object.entries(weeklyDataCopy);
             const totalWeeksToRun = weeksToProcess.length;
             if (totalWeeksToRun === 0) { console.warn(`Warning: No weeks in copied data. Skipping ${runLabel}.`); continue; }

             for (const [weekIdentifier, weekCandles] of weeksToProcess) {
                weekCounter++;
                process.stdout.write(` Processing Week ${weekCounter}/${totalWeeksToRun}: ${weekIdentifier}\r`);
                const indPeriods = [config.sma200Period,config.wma50Period,config.rsiPeriod,config.rsiMaPeriod,config.atrPeriod].map(Number).filter(p=>p>0&&!isNaN(p));
                const maxP = indPeriods.length > 0 ? Math.max(...indPeriods) : 0;
                const minLookback = Math.max(maxP, 4); // Max indicator period or min pattern lookback
                if (weekCandles.length < minLookback) { /* console.log(`Skipping week ${weekIdentifier}, not enough data: ${weekCandles.length}/${minLookback}`); */ continue; }

                const startBalance = currentEquity;
                const backtestResult = backtest(weekCandles, {
                    initialBalance: currentEquity,
                    overallPeakBalance: overallPeakBalance,
                    overallMaxDrawdown: overallMaxDrawdown,
                    slFactor: params.slFactor, // from the current loop's parameter set
                    tpFactor: params.tpFactor,
                    trailFactor: params.trailFactor,
                    isAdaptiveRun: isCurrentRunAdaptive, // from params
                    dailyPnLMap: dailyPnLMap
                });

                currentEquity = backtestResult.finalBalance;
                overallPeakBalance = backtestResult.peakBalance;
                overallMaxDrawdown = backtestResult.maxDrawdown;
                dailyPnLMap = backtestResult.dailyPnLMap;

                if (startBalance > 0 && !isNaN(currentEquity) && !isNaN(startBalance)) {
                    weeklyReturns.push((currentEquity - startBalance) / startBalance);
                } else if (startBalance <= 0) {
                    weeklyReturns.push(0); // Or handle appropriately
                }
                runFullTradeLog.push(...backtestResult.tradeLog); // Accumulate trades for this param set

                let atrSum = 0, atrCt = 0;
                for(const c of weekCandles) if(c && typeof c.atr === 'number' && !isNaN(c.atr) && c.atr > 0) { atrSum += c.atr; atrCt++; }
                const wkAvgAtr = atrCt > 0 ? (atrSum / atrCt) : NaN;
                const wkRegime = isNaN(wkAvgAtr) ? 'N/A' : (wkAvgAtr < atrThresholds.low_medium ? 'Low' : (wkAvgAtr > atrThresholds.medium_high ? 'High' : 'Medium'));

                weeklyResultsForParamSet.push({
                    Week: weekIdentifier,
                    ParamsSpecific: runLabel, // Save the full label for this run
                    SL: params.slFactor, TP: params.tpFactor, Trail: params.trailFactor,
                    MinTP: config.fixedTpPoints, RiskPct: config.currentRiskPercent, FixedContracts: config.currentFixedContracts,
                    Commission: config.commissionPerContract, Slippage: config.slippagePoints, LatencyBars: config.latencyDelayBars,
                    AvgATR: isNaN(wkAvgAtr)?NaN:parseFloat(wkAvgAtr.toFixed(4)), Regime: wkRegime,
                    Trades: backtestResult.tradesTaken, Wins: backtestResult.wins, Losses: backtestResult.losses,
                    TotalPnL: backtestResult.totalPnL, WinRate: backtestResult.winRate, ProfitFactor: backtestResult.profitFactor,
                    AvgWin: backtestResult.avgWin, AvgLoss: backtestResult.avgLoss,
                    MaxDDWeekly: parseFloat(backtestResult.maxDrawdownWeekly.replace('-','')) || 0,
                    ExitCounts: backtestResult.exitCounts
                });
             }
             process.stdout.write('\n'); // Newline after week processing progress

             if (weeklyResultsForParamSet.length > 0) {
                const aggResArray = aggregateResults(weeklyResultsForParamSet, runLabel); // Returns an array
                if (aggResArray && aggResArray.length > 0) {
                    const aggRes = aggResArray[0]; // Assuming single aggregated result per param set
                    aggRes.MaxDD_Overall = `-${overallMaxDrawdown.toFixed(config.pricePrecision)}`;
                    aggRes.Sharpe_Ann = calculateSharpe(weeklyReturns); // Pass weekly returns for this param set
                    const tradeMetrics = calculateTradeMetrics(runFullTradeLog); // Pass trade log for this param set
                    aggRes.AvgWinDollar = tradeMetrics.avgWinDollar;
                    aggRes.AvgLossDollar = tradeMetrics.avgLossDollar;
                    aggRes.AvgTradeDur_Bars = tradeMetrics.avgDurationBars;
                    aggRes.MaxContractsHit = tradeMetrics.maxContractsHit;

                    const dailyStats = calculateDailyStats(dailyPnLMap);
                    aggRes.AvgDailyPnL = dailyStats.avgDailyPnL;
                    aggRes.StdDevDailyPnL = dailyStats.stdDevDailyPnL;
                    aggRes.WinDayRate = dailyStats.winDayRate;

                    // Ensure these are consistently named for display
                    aggRes.FixedContracts_Run = aggRes.FixedContracts_Tested; // Use the name expected by display
                    aggRes.LatencyTested = aggRes.Latency_Tested;

                    allRunResults.push(aggRes); // Add this param set's aggregated result
                } else { console.warn(`Warning: No aggregated result for ${runLabel}.`); }
             } else { console.warn(`Warning: No weekly results for ${runLabel}.`); }
        } // End of parameter set loop
        console.log("Finished parameter set loop.");

        console.log("\n\n================ ALL BACKTESTS COMPLETE ================");
        processAndDisplayAggregatedResults(allRunResults); // Display all collected results

        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const resultsFilename = `aggregated_results_${timestamp}.json`;
            fs.writeFileSync(path.join(config.outputDir, resultsFilename), JSON.stringify(allRunResults, null, 2));
            console.log(`\nAggregated results saved to: ${path.join(config.outputDir, resultsFilename)}`);

            // Save the trade log of the *last completed parameter set*
            if (allRunResults.length > 0 && runFullTradeLog?.length > 0) {
                const lastRunLabel = allRunResults[allRunResults.length-1]?.Params || 'UnknownParams';
                const safeLastRunLabel = lastRunLabel.replace(/[^a-zA-Z0-9_,-]/g, '_').substring(0, 60);
                const tradeLogFilename = `trade_log_LastRun_${safeLastRunLabel}_${timestamp}.csv`;
                saveTradeLog(runFullTradeLog, tradeLogFilename); // runFullTradeLog has trades from the last param set
            } else { console.log("Skipping trade log save (no trades in last run or no runs)."); }
        } catch (error) { console.error("\nError saving results files:", error); }

    } catch (error) { console.error("Critical error during backtest run:", error); process.exit(1); }
} // --- End runWeeklyBacktests ---


// --- 8) Backtest Function ---
function backtest(candlesForPeriod, { initialBalance, overallPeakBalance, overallMaxDrawdown, slFactor, tpFactor, trailFactor, isAdaptiveRun, dailyPnLMap }) {
    const riskPercent = config.currentRiskPercent, fixedContracts = config.currentFixedContracts,
          maxContractsCap = config.maxContracts || Infinity, latencyDelay = config.latencyDelayBars || 0;
    let balance = initialBalance, wins = 0, losses = 0, trades = 0, pos = null, tradeIdCounter = 0;
    const completedTrades = [];
    let peakBalanceWeekly = initialBalance, maxDrawdownWeekly = 0,
        currentOverallPeak = overallPeakBalance, currentOverallMaxDD = overallMaxDrawdown;
    let grossProfit = 0, grossLoss = 0;
    let exitCounts = { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, hold_expired: 0, end_of_period: 0 };
    let currentDailyPnLMap = dailyPnLMap || new Map();

    const requiredLookback = 3; // For 4-candle pattern (c0, c1, c2, c3) needs index 3 for c3
    const startIdx = requiredLookback;

    if (candlesForPeriod.length <= startIdx) {
        return {
            tradesTaken: 0, wins: 0, losses: 0, totalPnL: 0.0, winRate: 0.0,
            maxDrawdownWeekly: '-0.00', profitFactor: 0.0, avgWin: 0.0, avgLoss: 0.0,
            tradeLog: [], exitCounts, finalBalance: balance,
            peakBalance: currentOverallPeak, maxDrawdown: currentOverallMaxDD, dailyPnLMap: currentDailyPnLMap
        };
    }

    for (let i = startIdx; i < candlesForPeriod.length; i++) {
        const c0 = candlesForPeriod[i-3], c1 = candlesForPeriod[i-2],
              c2 = candlesForPeriod[i-1], c3 = candlesForPeriod[i];

        if (!c3) continue; // Should not happen if loop condition is correct

        if (config.timeFilterEnabled) {
          const timestamp = new Date(c3.timestamp * 1000);
          const hour = timestamp.getUTCHours();
          if (!config.activeHours || !config.activeHours.includes(hour)) {
            if (pos) {
              // Manage existing position (even outside active hours for entry)
            } else {
              continue; // Skip entry consideration
            }
          }
        }

        if (isNaN(c3.wma50)||isNaN(c3.sma200)||isNaN(c3.rsi)||isNaN(c3.rsiMa)||isNaN(c3.atr)||c3.atr<=0) {
            if (pos) { /* ... manage pos ... if indicators are vital for exit, might need to close */ }
            continue;
        }

        let curSL, curTP, curTr, curReg;
        if (isAdaptiveRun) {
            const atr = c3.atr;
            curReg = (atr < atrThresholds.low_medium) ? 'Low' : (atr > atrThresholds.medium_high ? 'High' : 'Medium');
            curSL = adaptiveParams[curReg].slFactor;
            curTP = adaptiveParams[curReg].tpFactor;
            curTr = adaptiveParams[curReg].trailFactor;
        } else {
            curSL = slFactor; curTP = tpFactor; curTr = trailFactor; curReg = 'Fixed';
        }

        const p3 = detect3(c1,c2,c3), p4 = detect4(c0,c1,c2,c3);
        let pat = null, pType = null;
        if (p4) { pat = p4; pType = 'four'; }
        else if (p3) { pat = p3; pType = 'three'; }

        if (!pos && pat && entryOK(pat, pType, c3, i, candlesForPeriod)) {
            tradeIdCounter++; trades++;
            let entryP, atrE, entryTs, entryIdx = i;
            atrE = c3.atr; entryP = c3.close; entryTs = new Date(c3.timestamp * 1000);

            if(latencyDelay > 0 && (i + latencyDelay) < candlesForPeriod.length){
                const entryExecCandleIndex = i + latencyDelay;
                const entryExecCandle = candlesForPeriod[entryExecCandleIndex];
                if (entryExecCandle && !isNaN(entryExecCandle.open)) { // Enter on open of latency-delayed candle
                    entryP = entryExecCandle.open;
                    entryTs = new Date(entryExecCandle.timestamp * 1000);
                    entryIdx = entryExecCandleIndex;
                } else { trades--; tradeIdCounter--; continue; } // Invalid candle for execution
            } else if(latencyDelay > 0){ trades--; tradeIdCounter--; continue; } // Not enough candles for latency

            if (typeof curSL !== 'number' || isNaN(curSL) || typeof curTP !== 'number' || isNaN(curTP) || typeof curTr !== 'number' || isNaN(curTr)) {
                console.error(`Invalid factors: SL=${curSL}, TP=${curTP}, Trail=${curTr} for pattern ${pat}`);
                trades--; tradeIdCounter--; continue;
            }

            const slDist = atrE * curSL;
            const atrTpDist = atrE * curTP;
            const fixTpDistPts = config.fixedTpPoints || 0; // fixedTpPoints is in points
            const tpDist = Math.max(fixTpDistPts, atrTpDist); // tpDist is in points (ATR based or fixed)

            let cont;
            if (config.useDynamicPositionSizing && positionSizer) {
                const timestamp = new Date(c3.timestamp * 1000);
                const atrRegime = c3.atr < atrThresholds.low_medium ? 'Low' : (c3.atr > atrThresholds.medium_high ? 'High' : 'Medium');
                let timeScore = 0.5;
                if (config.useTimeAnalysis && timeAnalyzer) { timeScore = timeAnalyzer.getTimeScore(timestamp).combinedScore; }
                let entryScore = 0.7;
                if (config.useMLEntryFilter && mlFilter && config.useAdvancedMLFilter) {
                    const features = { direction: pat, atrRegime: atrRegime, hourOfDay: timestamp.getUTCHours(), dayOfWeek: timestamp.getUTCDay(), rsi: c3.rsi, rsiMa: c3.rsiMa, close: c3.close, wma50: c3.wma50, patternType: pType };
                    entryScore = mlFilter.calculateEntryScore(features) / (mlFilter.entryThreshold || 0.5); // Normalize score
                }
                positionSizer.updateBalance(balance);
                cont = positionSizer.calculatePositionSize({ currentATR: c3.atr, regime: atrRegime, timeScore: timeScore, entryScore: entryScore });
            } else if (config.useVolatilityPositionSizing && riskPercent > 0 && balance > 0 && spreadUtils) { // Check spreadUtils
                cont = spreadUtils.calculateVolatilityBasedPositionSize(atrE, balance, riskPercent, config);
            } else if (riskPercent > 0 && balance > 0) {
                const pv = config.pointValue;
                const slCostPerContractPts = slDist; // SL distance in points
                const slippageCostPerContractPts = (config.slippagePoints || 0);
                const totalRiskPts = slCostPerContractPts + slippageCostPerContractPts;
                const rpcDollar = (totalRiskPts * pv) + config.commissionPerContract; // Total risk in dollars per contract
                cont = (rpcDollar > 0) ? Math.min(maxContractsCap, Math.max(1, Math.floor((balance * riskPercent) / rpcDollar))) : 1;
            } else {
                cont = fixedContracts;
            }
            cont = Math.max(1, cont); // Ensure at least 1 contract

            const entryCandleForTrail = candlesForPeriod[entryIdx]; // Use the actual entry candle for initial trail high/low
            if (!entryCandleForTrail) { trades--; tradeIdCounter--; continue; }

            pos = {
                tradeId: tradeIdCounter, entryTimestamp: entryTs, dir: pat, entry: entryP,
                atr: atrE, // ATR at entry
                tpDistance: tpDist, // TP distance in points
                slDistance: slDist, // SL distance in points
                stopLossPrice: (pat === 'bullish') ? entryP - slDist : entryP + slDist,
                trailStopPrice: (pat === 'bullish') ? entryP - (atrE * curTr) : entryP + (atrE * curTr), // Initial trail based on entry ATR
                trailFactor: curTr,
                entryAtrRegime: curReg,
                trailHigh: entryCandleForTrail.high, // Initial high for trailing stop adjustment
                trailLow: entryCandleForTrail.low,   // Initial low for trailing stop adjustment
                entryBarIndex: entryIdx,
                tpType: tpDist === fixTpDistPts ? 'Fixed' : 'ATR',
                contracts: cont
            };
        }

        if (pos) {
            if (i > pos.entryBarIndex) { // Only manage position on candles after entry execution
                const trailStepSizeMultiplier = Array.isArray(config.trailStepSizeMultiplier) ?
                    (params.trailStepSizeMultiplier || config.trailStepSizeMultiplier[0]) : // params is from the loop, config.trailStep is from main config
                    config.trailStepSizeMultiplier;

                const modifiedConfig = { ...config, trailStepSizeMultiplier: trailStepSizeMultiplier };

                const exitInfo = (config.useSteppedTrail || config.useAdaptiveSlippage) && enhancedPM ? // Check enhancedPM
                    enhancedPM.enhancedManagePosition(pos, c3, i, candlesForPeriod, completedTrades, modifiedConfig, exitCounts, spreadUtils) :
                    managePosition(pos, c3, i, candlesForPeriod, completedTrades, modifiedConfig, exitCounts); // Pass modified config to standard managePosition

                if (exitInfo) {
                    balance += exitInfo.pnlNetTotal;
                    const day = getDayIdentifier(exitInfo.exitTimestamp.getTime()/1000);
                    currentDailyPnLMap.set(day, (currentDailyPnLMap.get(day)||0) + exitInfo.pnlNetTotal);
                    if (exitInfo.pnlNetTotal > 0) { wins++; grossProfit += exitInfo.pnlNetTotal; }
                    else { losses++; grossLoss += Math.abs(exitInfo.pnlNetTotal); }

                    peakBalanceWeekly = Math.max(peakBalanceWeekly, balance);
                    maxDrawdownWeekly = Math.max(maxDrawdownWeekly, peakBalanceWeekly - balance);
                    currentOverallPeak = Math.max(currentOverallPeak, balance);
                    currentOverallMaxDD = Math.max(currentOverallMaxDD, currentOverallPeak - balance);
                    pos = null;
                }
            } else { // On the entry bar itself, only update trailHigh/Low if not handled by managePosition for the first bar
                 pos.trailHigh = Math.max(pos.trailHigh, c3.high);
                 pos.trailLow = Math.min(pos.trailLow, c3.low);
            }
        }
    } // End candle loop

    if (pos) { // If position still open at end of period
        const lastC = candlesForPeriod[candlesForPeriod.length-1];
        if (!lastC) { console.warn("End of period check: Last candle missing!"); pos = null; }
        else {
            const exitP = lastC.close, exitTs = new Date(lastC.timestamp * 1000);
            pos.currentBarIndex = candlesForPeriod.length - 1;
            let exitInfo;
            const trailStepSizeMultiplier = Array.isArray(config.trailStepSizeMultiplier) ?
                (params.trailStepSizeMultiplier || config.trailStepSizeMultiplier[0]) :
                config.trailStepSizeMultiplier;
            const modifiedConfig = { ...config, trailStepSizeMultiplier: trailStepSizeMultiplier };

            if ((config.useSteppedTrail || config.useAdaptiveSlippage) && enhancedPM && spreadUtils) {
                const lookbackStart = Math.max(0, candlesForPeriod.length - 11);
                const recentCandles = candlesForPeriod.slice(lookbackStart);
                const estimatedSpread = spreadUtils.estimateSpread(recentCandles, modifiedConfig);
                const adaptiveSlippage = modifiedConfig.useAdaptiveSlippage ?
                    spreadUtils.calculateAdaptiveSlippage(recentCandles, modifiedConfig) :
                    (modifiedConfig.slippagePoints || 0.5);
                exitInfo = enhancedPM.enhancedLogAndStoreExit(pos, 'end_of_period', 0, exitP, exitTs, completedTrades, modifiedConfig, exitCounts, adaptiveSlippage, estimatedSpread);
            } else {
                exitInfo = logAndStoreExit(pos, 'end_of_period', 0, exitP, exitTs, completedTrades, modifiedConfig, exitCounts);
            }

            balance += exitInfo.pnlNetTotal;
            const day = getDayIdentifier(exitTs.getTime()/1000);
            currentDailyPnLMap.set(day, (currentDailyPnLMap.get(day) || 0) + exitInfo.pnlNetTotal);
            if (exitInfo.pnlNetTotal > 0) { wins++; grossProfit += exitInfo.pnlNetTotal; }
            else { losses++; grossLoss += Math.abs(exitInfo.pnlNetTotal); }
            peakBalanceWeekly = Math.max(peakBalanceWeekly, balance);
            maxDrawdownWeekly = Math.max(maxDrawdownWeekly, peakBalanceWeekly - balance);
            currentOverallPeak = Math.max(currentOverallPeak, balance);
            currentOverallMaxDD = Math.max(currentOverallMaxDD, currentOverallPeak - balance);
            pos = null;
        }
    }
    const wkPnl = balance - initialBalance;
    const winRate = (wins + losses) > 0 ? (wins / (wins + losses) * 100) : 0;
    const pf = grossLoss > 0 ? (grossProfit / grossLoss) : (grossProfit > 0 ? 99999 : 0);
    const avgW = wins > 0 ? (grossProfit / wins) : 0;
    const avgL = losses > 0 ? (grossLoss / losses) : 0;

    return {
        tradesTaken: trades, wins: wins, losses: losses, totalPnL: wkPnl, winRate: winRate,
        profitFactor: pf, avgWin: avgW, avgLoss: avgL, tradeLog: completedTrades, exitCounts: exitCounts,
        finalBalance: balance, peakBalance: currentOverallPeak, maxDrawdown: currentOverallMaxDD,
        maxDrawdownWeekly: `-${maxDrawdownWeekly.toFixed(config.pricePrecision)}`, dailyPnLMap: currentDailyPnLMap
    };
}


// --- 9) Position Management --- (Uses Exit@Close Latency Model)
function managePosition(currentPos, currentCandle, currentIndex, candlesForPeriod, completedTrades, currentConfig, exitCounts) { // Use currentConfig
    const trailF = currentPos.trailFactor;
    // Use ATR from current candle for trailing, or fall back to entry ATR if current is bad
    const atrTrail = (typeof currentCandle.atr === 'number' && !isNaN(currentCandle.atr) && currentCandle.atr > 0) ? currentCandle.atr : currentPos.atr;

    if (currentPos.dir === 'bullish') {
        currentPos.trailHigh = Math.max(currentPos.trailHigh, currentCandle.high);
        currentPos.trailStopPrice = Math.max(currentPos.trailStopPrice, currentPos.trailHigh - (atrTrail * trailF));
    } else { // bearish
        currentPos.trailLow = Math.min(currentPos.trailLow, currentCandle.low);
        currentPos.trailStopPrice = Math.min(currentPos.trailStopPrice, currentPos.trailLow + (atrTrail * trailF));
    }

    let exitR = null, exitSigP = null; // exitReason, exitSignalPrice
    if (currentPos.dir === 'bullish') {
        if (currentCandle.low <= currentPos.stopLossPrice) { exitR = 'sl'; exitSigP = currentPos.stopLossPrice; }
        else if (currentCandle.high >= currentPos.entry + currentPos.tpDistance) { exitR = 'tp'; exitSigP = currentPos.entry + currentPos.tpDistance; }
        else if (currentCandle.low <= currentPos.trailStopPrice) { exitR = 'trail'; exitSigP = currentPos.trailStopPrice; }
    } else { // bearish
        if (currentCandle.high >= currentPos.stopLossPrice) { exitR = 'sl'; exitSigP = currentPos.stopLossPrice; }
        else if (currentCandle.low <= currentPos.entry - currentPos.tpDistance) { exitR = 'tp'; exitSigP = currentPos.entry - currentPos.tpDistance; }
        else if (currentCandle.high >= currentPos.trailStopPrice) { exitR = 'trail'; exitSigP = currentPos.trailStopPrice; }
    }

    if (!exitR && currentConfig.useTwoBarColorExit && currentIndex > currentPos.entryBarIndex + 1) {
        const prevC = candlesForPeriod[currentIndex-1], prevCol = candlestickColor(prevC), curCol = candlestickColor(currentCandle);
        if (currentPos.dir === 'bullish' && prevCol === 'red' && curCol === 'red') { exitR = 'color_flow_2bar'; exitSigP = currentCandle.close; }
        else if (currentPos.dir === 'bearish' && prevCol === 'green' && curCol === 'green') { exitR = 'color_flow_2bar'; exitSigP = currentCandle.close; }
    }

    if (exitR && exitSigP !== null) {
        let finalExitP = exitSigP, exitIdx = currentIndex;
        const lat = currentConfig.latencyDelayBars || 0; // Use currentConfig
        if (lat > 0 && (currentIndex + lat) < candlesForPeriod.length) {
            const idx = currentIndex + lat;
            const c = candlesForPeriod[idx];
            if (c && !isNaN(c.open) && !isNaN(c.close)) {
                finalExitP = c.close; // Exit on close of latency-delayed candle for exits
                exitIdx = idx;
            } else { // Invalid future candle, execute on current
                finalExitP = currentCandle.close; exitIdx = currentIndex;
            }
        } else if (lat > 0) { // Not enough candles for latency, execute on last available
            finalExitP = candlesForPeriod[candlesForPeriod.length-1].close;
            exitIdx = candlesForPeriod.length-1;
        }
        // If no latency, finalExitP remains exitSigP, exitIdx remains currentIndex

        const exitTs = new Date(candlesForPeriod[exitIdx].timestamp * 1000);
        currentPos.currentBarIndex = exitIdx; // Record actual exit bar index
        const exitInfo = logAndStoreExit(currentPos, exitR, 0, finalExitP, exitTs, completedTrades, currentConfig, exitCounts); // Pass currentConfig
        return exitInfo;
    }
    return null;
}

// --- 10) Trade Logging ---
function logAndStoreExit(posData, reason, pnlPointsTheoretical, exitSignalPrice, exitTimestamp, completedTradesArray, currentConfig, exitCounts) { // Use currentConfig
    if (!posData || typeof posData.entry !== 'number' || !posData.dir || !exitTimestamp) {
        console.error("logAndStoreExit: Invalid data.", posData); return { pnlNetTotal: 0, exitTimestamp: exitTimestamp || new Date() };
    }
    const contracts = posData.contracts || 1, ptVal = currentConfig.pointValue;
    let adjExitP = exitSignalPrice;
    const slipPts = currentConfig.slippagePoints || 0;
    const applySlip = (reason==='sl'||reason==='trail'||reason==='end_of_period'||reason==='hold_expired'||reason==='color_flow_2bar');

    if (applySlip && slipPts > 0) {
        adjExitP = (posData.dir === 'bullish') ? exitSignalPrice - slipPts : exitSignalPrice + slipPts;
    }

    const pnlPtsAdj = (posData.dir === 'bullish') ? adjExitP - posData.entry : posData.entry - adjExitP;
    const pnlGross = pnlPtsAdj * ptVal * contracts;
    const commCost = currentConfig.commissionPerContract * contracts;
    const pnlNet = pnlGross - commCost;
    const durBars = (typeof posData.currentBarIndex === 'number' && typeof posData.entryBarIndex === 'number' && posData.currentBarIndex >= posData.entryBarIndex) ? posData.currentBarIndex - posData.entryBarIndex + 1 : 'N/A';

    if (exitCounts) { exitCounts[reason] = (exitCounts[reason] || 0) + 1; }

    const tradeData = {
        ID: posData.tradeId || 'N/A',
        EntryTime: posData.entryTimestamp?.toISOString() || 'N/A',
        ExitTime: exitTimestamp.toISOString(),
        Dir: posData.dir,
        Entry: posData.entry.toFixed(currentConfig.pricePrecision),
        ExitSignal: exitSignalPrice.toFixed(currentConfig.pricePrecision), // Price that triggered exit logic
        ExitFill: adjExitP.toFixed(currentConfig.pricePrecision),      // Price after slippage
        Reason: reason,
        PnL_Pts_Gross: pnlPtsAdj.toFixed(currentConfig.pricePrecision), // Points after slippage, before commission
        PnL_Net: pnlNet.toFixed(2),
        SlippagePts: applySlip ? slipPts : 0,
        CommissionCost: commCost.toFixed(2),
        Contracts: contracts,
        Duration: durBars,
        EntryATRRegime: posData.entryAtrRegime || 'N/A',
        TP_Type: posData.tpType || 'N/A'
    };
    completedTradesArray.push(tradeData);
    return { pnlNetTotal: pnlNet, exitTimestamp: exitTimestamp };
}

// --- 11) Param Combinations ---
function getParameterCombinations() { // Uses global config object
    const combos = [];
    // Ensure factors are arrays, even if single values are in config
    const slArray = Array.isArray(config.slFactors) ? config.slFactors : (typeof config.slFactors === 'number' ? [config.slFactors] : []);
    const tpArray = Array.isArray(config.tpFactors) ? config.tpFactors : (typeof config.tpFactors === 'number' ? [config.tpFactors] : []);
    const trailArray = Array.isArray(config.trailFactors) ? config.trailFactors : (typeof config.trailFactors === 'number' ? [config.trailFactors] : []);

    if (slArray.length === 0 || tpArray.length === 0 || trailArray.length === 0) {
        console.warn("Warning: One or more factor arrays (sl, tp, trail) are empty or not properly defined in config. Returning empty combos.");
        return [];
    }
    
    // Optional: Grid for trailStepSizeMultiplier if useSteppedTrail is active
    const trailStepMultArray = (config.useSteppedTrail && Array.isArray(config.trailStepSizeMultiplier))
        ? config.trailStepSizeMultiplier
        : (config.useSteppedTrail && typeof config.trailStepSizeMultiplier === 'number' ? [config.trailStepSizeMultiplier] : [null]); // [null] if not used or not array

    for (let sl of slArray) {
        for (let tp of tpArray) {
            for (let trail of trailArray) {
                for (let trailStepMult of trailStepMultArray) {
                    if (typeof sl === 'number' && !isNaN(sl) &&
                        typeof tp === 'number' && !isNaN(tp) &&
                        typeof trail === 'number' && !isNaN(trail) &&
                        (trailStepMult === null || (typeof trailStepMult === 'number' && !isNaN(trailStepMult)))) {
                        
                        let combo = { slFactor: sl, tpFactor: tp, trailFactor: trail };
                        if (trailStepMult !== null) {
                            combo.trailStepSizeMultiplier = trailStepMult;
                        }
                        combos.push(combo);
                    } else {
                        console.warn(`Skipping invalid combo: SL=${sl}, TP=${tp}, Trail=${trail}, TrailStepMult=${trailStepMult}`);
                    }
                }
            }
        }
    }
    console.log(`Generated ${combos.length} parameter combinations.`);
    return combos;
}

// --- 12) Metrics ---
function calculateSharpe(weeklyReturns, riskFreeRate = 0) { const validReturns = weeklyReturns.filter(r => typeof r === 'number' && isFinite(r)); if (!validReturns || validReturns.length < 2) return 'N/A'; const numWeeks = validReturns.length; const meanWeeklyReturn = validReturns.reduce((a, b) => a + b, 0) / numWeeks; if (isNaN(meanWeeklyReturn)) return 'N/A'; const variance = validReturns.map(r => Math.pow(r - meanWeeklyReturn, 2)).reduce((a, b) => a + b, 0) / (numWeeks - 1); const stdDevWeeklyReturn = Math.sqrt(variance); if (isNaN(stdDevWeeklyReturn) || stdDevWeeklyReturn === 0) return 'N/A'; const annualizedMeanReturn = meanWeeklyReturn * 52, annualizedStdDev = stdDevWeeklyReturn * Math.sqrt(52); const sharpeRatio = (annualizedMeanReturn - (riskFreeRate || 0)) / annualizedStdDev; return isNaN(sharpeRatio) ? 'N/A' : sharpeRatio.toFixed(2); }
function calculateDailyStats(dailyPnLMap) { if (!dailyPnLMap || dailyPnLMap.size === 0) { return { avgDailyPnL: '0.00', stdDevDailyPnL: '0.00', winDayRate: 'N/A' }; } const values = Array.from(dailyPnLMap.values()), n = values.length; const totalPnl = values.reduce((s, p) => s + p, 0), avg = totalPnl / n; const winDays = values.filter(p => p > 0).length, winRate = `${(winDays / n * 100).toFixed(1)}%`; const variance = values.map(p => Math.pow(p - avg, 2)).reduce((s, sq) => s + sq, 0) / n; return { avgDailyPnL: avg.toFixed(2), stdDevDailyPnL: Math.sqrt(variance).toFixed(2), winDayRate: winRate }; }
function calculateTradeMetrics(tradeLog) { let winAmt=0, lossAmt=0, winCt=0, lossCt=0, duration=0, tradeCt=0, maxCont=0; if (!tradeLog || tradeLog.length === 0) { return { avgWinDollar: '0.00', avgLossDollar: '0.00', avgDurationBars: 'N/A', maxContractsHit: 0 }; } for (const trade of tradeLog) { const pnl=parseFloat(trade.PnL_Net), dur=parseInt(trade.Duration), cont=parseInt(trade.Contracts)||1; if(!isNaN(pnl)){ if(pnl>0){winAmt+=pnl;winCt++;} else if(pnl<0){lossAmt+=Math.abs(pnl);lossCt++;} } if(!isNaN(dur)&&dur>0){duration+=dur;} tradeCt++; maxCont=Math.max(maxCont,cont); } return { avgWinDollar:winCt>0?(winAmt/winCt).toFixed(2):'0.00', avgLossDollar:lossCt>0?(lossAmt/lossCt).toFixed(2):'0.00', avgDurationBars:tradeCt>0&&duration>0?(duration/tradeCt).toFixed(1):'N/A', maxContractsHit:maxCont }; }

// --- 13) Results Aggregation ---
function aggregateResults(weeklyResults, runLabel) {
    if (!weeklyResults || weeklyResults.length === 0) return [];
    const firstResult = weeklyResults[0];
    const aggData = {
        ParamsKey: runLabel,
        SL: firstResult?.SL, TP: firstResult?.TP, Trail: firstResult?.Trail,
        MinTP_Tested: firstResult?.MinTP, RiskPct_Tested: firstResult?.RiskPct,
        FixedContracts_Tested: firstResult?.FixedContracts, Latency_Tested: firstResult?.LatencyBars,
        Commission_Tested: firstResult?.Commission, Slippage_Tested: firstResult?.Slippage,
        TotalPnL: 0, TotalTrades: 0, TotalWins: 0, TotalLosses: 0,
        TotalGrossProfit: 0, TotalGrossLoss: 0, MaxWeeklyDD: 0, WeeksRun: 0,
        ProfitableWeeks: 0, SumAvgATR: 0, AtrValidWeeks: 0,
        TotalExitCounts: { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, hold_expired: 0, end_of_period: 0 }
    };

    for (const result of weeklyResults) {
        if (!result) continue;
        aggData.TotalPnL += result.TotalPnL||0;
        aggData.TotalTrades += result.Trades||0;
        aggData.TotalWins += result.Wins||0;
        aggData.TotalLosses += result.Losses||0;
        aggData.TotalGrossProfit += (result.AvgWin||0)*(result.Wins||0); // Calculate from avg and count
        aggData.TotalGrossLoss += (result.AvgLoss||0)*(result.Losses||0); // Calculate from avg and count
        aggData.MaxWeeklyDD = Math.max(aggData.MaxWeeklyDD, result.MaxDDWeekly || 0);
        aggData.WeeksRun++;
        if (typeof result.AvgATR === 'number' && !isNaN(result.AvgATR)) {
            aggData.SumAvgATR += result.AvgATR;
            aggData.AtrValidWeeks++;
        }
        if (result.TotalPnL > 0) { aggData.ProfitableWeeks++; }
        if (result.ExitCounts) {
            for (const r in result.ExitCounts) {
                aggData.TotalExitCounts[r] = (aggData.TotalExitCounts[r] || 0) + result.ExitCounts[r];
            }
        }
    }
    const totalTradesCalc = aggData.TotalWins + aggData.TotalLosses; // Use calculated wins/losses
    const winRate = totalTradesCalc > 0 ? (aggData.TotalWins / totalTradesCalc * 100) : 0;
    const pf = aggData.TotalGrossLoss > 0 ? (aggData.TotalGrossProfit / aggData.TotalGrossLoss) : (aggData.TotalGrossProfit > 0 ? 99999 : 0);
    const avgPnlWk = aggData.WeeksRun > 0 ? aggData.TotalPnL / aggData.WeeksRun : 0;
    const avgAtr = aggData.AtrValidWeeks > 0 ? aggData.SumAvgATR / aggData.AtrValidWeeks : NaN;
    let exitSum = "N/A";
    if (totalTradesCalc > 0) {
        exitSum = Object.entries(aggData.TotalExitCounts)
            .filter(([_, ct]) => ct > 0)
            .map(([r, ct]) => `${r}: ${(ct / totalTradesCalc * 100).toFixed(1)}%`)
            .join(', ');
    }

    return [{
        Params: aggData.ParamsKey, // This is the full runLabel
        TotalPnL: aggData.TotalPnL, AvgPnL_Week: avgPnlWk,
        WinRate: `${winRate.toFixed(2)}%`,
        ProfitFactor: pf === 99999 ? 'Infinity' : pf.toFixed(2),
        Trades: aggData.TotalTrades, // Use the sum from weekly results
        Weeks: aggData.WeeksRun,
        WinWeeks: `${aggData.ProfitableWeeks}/${aggData.WeeksRun} (${aggData.WeeksRun > 0 ? (aggData.ProfitableWeeks/aggData.WeeksRun*100).toFixed(1) : '0.0'}%)`,
        AvgATR: avgAtr, // This is the overall average ATR for this param set
        MaxWeeklyDD: `-${aggData.MaxWeeklyDD.toFixed(config.pricePrecision)}`, // Max DD observed in any single week
        Exits: exitSum,
        // Pass through tested parameters for clarity in results table
        SL_Run: aggData.SL, TP_Run: aggData.TP, Trail_Run: aggData.Trail,
        MinTP_Run: aggData.MinTP_Tested, RiskPct_Run: aggData.RiskPct_Tested,
        FixedContracts_Tested: aggData.FixedContracts_Tested, // Ensure this is correctly populated
        Latency_Tested: aggData.Latency_Tested,
        Commission_Run: aggData.Commission_Tested, Slippage_Run: aggData.Slippage_Tested
    }];
}


// --- 14) Results Display ---
function processAndDisplayAggregatedResults(resultsWithOverallMetrics) {
    if (!resultsWithOverallMetrics || resultsWithOverallMetrics.length === 0) { console.log("No aggregated results to display."); return; }
    console.log("\n--- AGGREGATED RESULTS SUMMARY ---");
    const columns = [
        "Params", "TotalPnL", "AvgPnL_Week", "WinRate", "ProfitFactor", "Trades",
        "AvgWinDollar", "AvgLossDollar", "MaxDD_Overall", "Sharpe_Ann",
        "AvgTradeDur_Bars", "MaxContractsHit", "WinDayRate", "AvgDailyPnL", "StdDevDailyPnL",
        "Weeks", "WinWeeks", "AvgATR", "Exits",
        "FixedContracts_Run", "LatencyTested", "Commission_Run", "Slippage_Run" // Ensure these match keys in aggRes
    ];
    const displayData = resultsWithOverallMetrics.map(row => {
        const newRow = {};
        columns.forEach(col => {
            let value = row[col];
            if (typeof value === 'number') {
                if (['TotalPnL', 'AvgPnL_Week', 'AvgWinDollar', 'AvgLossDollar', 'AvgDailyPnL', 'StdDevDailyPnL'].includes(col)) {
                    value = value.toFixed(2);
                } else if (col === 'ProfitFactor' && value !== Infinity && value !== 99999) {
                    value = value.toFixed(2);
                } else if (col === 'AvgATR') {
                    value = isNaN(value) ? 'N/A' : value.toFixed(4);
                } else if (col === 'Sharpe_Ann') {
                    value = (isNaN(value) || !isFinite(value)) ? 'N/A' : value; // Already toFixed(2)
                }
            }
            if (col === 'MaxDD_Overall' && typeof value === 'string' && value !== 'N/A' && !value.startsWith('-')) {
                const num = parseFloat(value); value = isNaN(num) ? 'N/A' : `-${num.toFixed(config.pricePrecision)}`;
            }
            if (col === 'ProfitFactor' && (value === 99999 || value === Infinity)) { value = 'Infinity'; }

            // Explicit mapping for potentially confusing names
            if (col === 'FixedContracts_Run') value = row['FixedContracts_Tested'];
            if (col === 'LatencyTested') value = row['Latency_Tested'];

            newRow[col] = (value !== undefined && value !== null) ? value : 'N/A';
        });
        return newRow;
    });

    if (displayData.length > 1) {
        displayData.sort((a, b) => {
            try {
                const pnlA = Number(String(a.TotalPnL).replace(/[^0-9.-]+/g,"")) || -Infinity;
                const pnlB = Number(String(b.TotalPnL).replace(/[^0-9.-]+/g,"")) || -Infinity;
                return pnlB - pnlA;
            } catch (e) { return 0; }
        });
    }
    console.table(displayData, columns);
}

// --- 15) Save Trade Log ---
function saveTradeLog(tradeLog, filename) {
    if (!tradeLog || tradeLog.length === 0) { console.log(`Skipping save for ${filename}: No trades.`); return; }
    try {
        const headers = Object.keys(tradeLog[0]);
        if (!headers || headers.length === 0) { console.warn(`Cannot save ${filename}: No headers.`); return; }
        const headerString = headers.join(';');
        const rows = tradeLog.map(trade => headers.map(h => String(trade[h] ?? '').replace(/;/g, ',').replace(/\n|\r/g, ' ')).join(';')); // Sanitize rows
        fs.writeFileSync(path.join(config.outputDir, filename), [headerString, ...rows].join('\n'));
        console.log(`Trade log saved to: ${path.join(config.outputDir, filename)}`);
    } catch (error) { console.error(`\nError saving trade log ${filename}:`, error); }
}

// --- 16) Pattern Detection & Entry Filters ---
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;
    const col1 = candlestickColor(c1), col2 = candlestickColor(c2), col3 = candlestickColor(c3);
    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish: Green, Red, Green (engulfing with larger lower wick on c3)
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        const engulf = c3.close > c2.open && c3.open < c2.close; // c3 body engulfs c2 body
        const body3 = Math.abs(c3.close - c3.open);
        const body2 = Math.abs(c2.close - c2.open);
        // Ensure c3 is a reasonably strong candle and c2 is not excessively large
        if (engulf && body3 > body2 * 0.5 && body2 < c3.atr * 2.5) { // Example conditions
             const wick3 = Math.min(c3.open,c3.close)-c3.low;
             const wick2 = Math.min(c2.open,c2.close)-c2.low;
             if (wick3 > wick2 * 0.5) return 'bullish'; // c3 lower wick is significant
        }
    }
    // Bearish: Red, Green, Red (engulfing with larger upper wick on c3)
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        const engulf = c3.close < c2.open && c3.open > c2.close; // c3 body engulfs c2 body
        const body3 = Math.abs(c3.close - c3.open);
        const body2 = Math.abs(c2.close - c2.open);
        if (engulf && body3 > body2 * 0.5 && body2 < c3.atr * 2.5) {
            const wick3 = c3.high-Math.max(c3.open,c3.close);
            const wick2 = c2.high-Math.max(c2.open,c2.close);
            if (wick3 > wick2 * 0.5) return 'bearish'; // c3 upper wick is significant
        }
    }
    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;
    const col0 = candlestickColor(c0), col1 = candlestickColor(c1),
          col2 = candlestickColor(c2), col3 = candlestickColor(c3);
    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish: Green, Red, Red, Green (c3 closes above the high of the two red candles' bodies)
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c1.close, c2.open, c2.close) && c3.close > c0.close * 0.998) { // c3 clears the red candles and near/above c0 close
             // Add more specific conditions if needed, e.g. size of c3 body
            if (Math.abs(c3.close-c3.open) > c3.atr * 0.3) return 'bullish';
        }
    }
    // Bearish: Red, Green, Green, Red (c3 closes below the low of the two green candles' bodies)
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c1.close, c2.open, c2.close) && c3.close < c0.close * 1.002) { // c3 clears green candles and near/below c0 close
            if (Math.abs(c3.close-c3.open) > c3.atr * 0.3) return 'bearish';
        }
    }
    return null;
}

function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod) {
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
        return false; // Basic data check
    }

    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // Apply RSI filter more generally, or specifically for 'three' if desired
    if (config.useRsiFilter) { // Added a general config flag for RSI filter
        if ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa)) {
             return false;
        }
    }


    const minAtrEntry = config.minAtrEntry || 0;
    if (c3.atr < minAtrEntry) {
        return false;
    }

    const minRsiMaSeparation = config.minRsiMaSeparation || 0;
    if (Math.abs(c3.rsi - c3.rsiMa) < minRsiMaSeparation) {
        return false;
    }

    if (config.useMLEntryFilter && mlFilter) { // mlFilter is initialized based on config
        const timestamp = new Date(c3.timestamp * 1000);
        const hourOfDay = timestamp.getUTCHours();
        const dayOfWeek = timestamp.getUTCDay();
        const atrRegime = c3.atr < atrThresholds.low_medium ? 'Low' : (c3.atr > atrThresholds.medium_high ? 'High' : 'Medium');
        let recentCandles = [];
        if (candlesForPeriod && Array.isArray(candlesForPeriod) && currentIndex !== undefined) {
            const lookbackStart = Math.max(0, currentIndex - (config.mlConfig?.patternLookback || 5)); // Use lookback from mlConfig
            recentCandles = candlesForPeriod.slice(lookbackStart, currentIndex + 1);
        }
        const features = {
            direction: dir, atrRegime: atrRegime, hourOfDay: hourOfDay, dayOfWeek: dayOfWeek,
            rsi: c3.rsi, rsiMa: c3.rsiMa, close: c3.close, wma50: c3.wma50,
            patternType: patternType, recentCandles: recentCandles, timestamp: timestamp, currentAtr: c3.atr
        };
        if (config.useTimeAnalysis && timeAnalyzer) {
            features.timeScore = timeAnalyzer.getTimeScore(timestamp).combinedScore;
        }
        return mlFilter.shouldEnter(features); // mlFilter.shouldEnter directly uses its internal threshold
    }
    return true;
}

// ======================== End of file ========================

// To run this, you would typically have a separate runner script like:
// const backtester = require('./backtest_m2k.js');
// const myGridConfig = require('./config_m2k_grid_test.js');
// backtester.loadDataAndRun(myGridConfig);
//
// Make sure spread_volatility_utils.js and enhanced_position_management.js exist
// and export the functions used if useVolatilityPositionSizing or useSteppedTrail/useAdaptiveSlippage are true.
// For now, I've added checks (&& spreadUtils), (&& enhancedPM) to prevent errors if they are missing and features are off.

module.exports = { loadDataAndRun }; // Export the main function to be called by a runner