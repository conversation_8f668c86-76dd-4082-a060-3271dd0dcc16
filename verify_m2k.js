/**
 * Verify M2K Backtest
 * Run a single backtest with parameters from m2k_config.js
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');
const m2kConfig = require('./m2k_config');

// Set symbol and data path
const symbol = 'M2K';
const dataPath = 'C:\\backtest-bot\\input\\MiniRussell2000_2020_2025.csv';

// Set output directory
const outputDir = './output/verify_m2k';
m2kConfig.outputDir = outputDir;

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Extract parameters from m2k_config.js
const params = {
    slFactors: m2kConfig.slFactors,
    tpFactors: m2kConfig.tpFactors,
    trailFactors: m2kConfig.trailFactors,
    fixedTpPoints: m2kConfig.fixedTpPoints
};

// Date range options (from Jan 1, 2024 to present)
const dateRange = {
    useCustomRange: true,
    startDate: '2024-01-01',
    endDate: ''  // Empty string means up to the latest available data
};

/**
 * Run verification test for M2K
 */
async function runVerification() {
    console.log(`\n===== VERIFYING M2K BACKTEST =====`);
    console.log(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
    console.log(`Using parameters from m2k_config.js: ${JSON.stringify(params, null, 2)}`);

    // Create backtest instance with configuration
    const backtest = new GridBacktest(m2kConfig);

    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { dateRange });

    // Run backtest with parameters
    console.log(`Running backtest with parameters...`);
    const result = backtest.runBacktest(params);

    if (!result.success) {
        console.error(`Error running backtest: ${result.error}`);
        return;
    }

    // Save results to file
    const resultsPath = path.join(outputDir, 'verification_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(result, null, 2));

    // Generate HTML report
    generateHtmlReport(result, outputDir);

    // Print summary
    console.log(`\n===== VERIFICATION RESULTS =====`);
    console.log(`Total PnL: $${result.stats.totalPnl.toFixed(2)}`);
    console.log(`Return: ${result.stats.totalReturn.toFixed(2)}%`);
    console.log(`Win Rate: ${result.stats.winRate.toFixed(2)}%`);
    console.log(`Win Day Rate: ${result.stats.winDayRate.toFixed(2)}%`);
    console.log(`Profit Factor: ${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}`);
    console.log(`Max Drawdown: ${result.stats.maxDrawdown.toFixed(2)}%`);
    console.log(`Total Trades: ${result.stats.totalTrades}`);
    console.log(`\nResults saved to ${resultsPath}`);
}

/**
 * Generate HTML report for verification results
 * @param {Object} result - Backtest result
 * @param {string} outputDir - Output directory
 */
function generateHtmlReport(result, outputDir) {
    const reportPath = path.join(outputDir, 'verification_report.html');

    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M2K Verification Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
        .chart {
            margin-top: 30px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>M2K Verification Report</h1>
        <p>Date range: ${dateRange.startDate} to ${dateRange.endDate}</p>
        <p>Parameters: SL=${params.slFactors}, TP=${params.tpFactors}, Trail=${params.trailFactors}, Fixed TP=${params.fixedTpPoints}</p>

        <h2>Performance Summary</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Initial Balance</td>
                <td>$${result.stats.initialBalance.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Final Balance</td>
                <td>$${result.stats.finalBalance.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Total PnL</td>
                <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Return</td>
                <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Trades</td>
                <td>${result.stats.totalTrades}</td>
            </tr>
            <tr>
                <td>Winning Trades</td>
                <td>${result.stats.wins}</td>
            </tr>
            <tr>
                <td>Losing Trades</td>
                <td>${result.stats.losses}</td>
            </tr>
            <tr>
                <td>Win Rate</td>
                <td>${result.stats.winRate.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Profit Factor</td>
                <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
            </tr>
            <tr>
                <td>Average Win</td>
                <td class="positive">$${result.stats.avgWin.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Average Loss</td>
                <td class="negative">$${result.stats.avgLoss.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Max Drawdown</td>
                <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Days</td>
                <td>${result.stats.totalDays}</td>
            </tr>
            <tr>
                <td>Profitable Days</td>
                <td>${result.stats.profitDays}</td>
            </tr>
            <tr>
                <td>Losing Days</td>
                <td>${result.stats.lossDays}</td>
            </tr>
            <tr>
                <td>Win Day Rate</td>
                <td>${result.stats.winDayRate.toFixed(2)}%</td>
            </tr>
        </table>

        <h2>Daily Performance</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>PnL</th>
                    <th>Trades</th>
                    <th>Wins</th>
                    <th>Losses</th>
                    <th>Win Rate</th>
                </tr>
            </thead>
            <tbody>
                ${Object.entries(result.dailyStats).map(([date, stats]) => `
                <tr>
                    <td>${date}</td>
                    <td class="${stats.pnl >= 0 ? 'positive' : 'negative'}">$${stats.pnl.toFixed(2)}</td>
                    <td>${stats.trades}</td>
                    <td>${stats.wins}</td>
                    <td>${stats.losses}</td>
                    <td>${stats.trades > 0 ? ((stats.wins / stats.trades) * 100).toFixed(2) : 0}%</td>
                </tr>
                `).join('')}
            </tbody>
        </table>

        <h2>Trade Exit Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Exit Reason</th>
                    <th>Count</th>
                    <th>Win Count</th>
                    <th>Loss Count</th>
                    <th>Win Rate</th>
                    <th>Avg PnL</th>
                </tr>
            </thead>
            <tbody>
                ${(() => {
                    const exitStats = {};

                    // Group trades by exit reason
                    for (const trade of result.trades) {
                        if (!exitStats[trade.exitReason]) {
                            exitStats[trade.exitReason] = {
                                count: 0,
                                winCount: 0,
                                lossCount: 0,
                                totalPnl: 0
                            };
                        }

                        exitStats[trade.exitReason].count++;
                        exitStats[trade.exitReason].totalPnl += trade.pnl;

                        if (trade.pnl > 0) {
                            exitStats[trade.exitReason].winCount++;
                        } else {
                            exitStats[trade.exitReason].lossCount++;
                        }
                    }

                    // Generate HTML for each exit reason
                    return Object.entries(exitStats).map(([reason, stats]) => `
                    <tr>
                        <td>${reason}</td>
                        <td>${stats.count}</td>
                        <td>${stats.winCount}</td>
                        <td>${stats.lossCount}</td>
                        <td>${stats.count > 0 ? ((stats.winCount / stats.count) * 100).toFixed(2) : 0}%</td>
                        <td class="${stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${(stats.totalPnl / stats.count).toFixed(2)}</td>
                    </tr>
                    `).join('');
                })()}
            </tbody>
        </table>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`HTML report generated at ${reportPath}`);
}

// Run verification
runVerification().catch(error => {
    console.error(`Error running verification:`, error);
});
