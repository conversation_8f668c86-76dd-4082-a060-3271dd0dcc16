import os
import json
import shutil
import pandas as pd
from datetime import datetime
import glob
import re

# Directories
DATA_DIR = "C:/backtest-bot/market_data"
INPUT_DIR = "C:/backtest-bot/input"
OUTPUT_DIR = "C:/backtest-bot/output"
DASHBOARD_DIR = "C:/backtest-bot"

# Instrument configurations
INSTRUMENTS = {
    "MNQ": {
        "name": "Micro Nasdaq",
        "input_file": "input/MNQ2020_2025430.csv",
        "config_file": "output/MNQ/config_mnq.js",
        "color": "#00ccff",
        "params": {
            "stop_loss_factor": 4.5,
            "take_profit_factor": 3.0,
            "trail_factor": 0.11,
            "fixed_tp_points": 40,
            "contracts": 10,
            "commission_per_contract": 0.40,
            "slippage_per_contract": 0.75,
            "adaptive_mode": True
        },
        "metrics": {
            "total_pnl": 3337467.82,
            "win_rate": 0.7884,
            "win_day_rate": 0.9810,
            "max_drawdown": 1470.43,
            "profit_factor": 31.24
        }
    },
    "MGC": {
        "name": "Micro Gold",
        "input_file": "input/MGC10years.csv",
        "config_file": "output/MGC/config_mgc.js",
        "color": "#FFD700",
        "params": {
            "stop_loss_factor": 8.0,
            "take_profit_factor": 7.0,
            "trail_factor": 0.02,
            "contracts": 10,
            "commission_per_contract": 0.40,
            "slippage_per_contract": 0.10,
            "adaptive_mode": True
        },
        "metrics": {
            "total_pnl": 890734.73,
            "win_rate": 0.7049,
            "win_day_rate": 0.9320,
            "max_drawdown": 994.70
        }
    },
    "MES": {
        "name": "Micro E-mini S&P 500",
        "input_file": "input/MES_2020-2025.csv",
        "config_file": "output/MES/config_mes.js",
        "color": "#ff3366",
        "params": {
            "stop_loss_factor": 3.0,
            "take_profit_factor": 3.0,
            "trail_factor": 0.01,
            "contracts": 10,
            "commission_per_contract": 0.40,
            "slippage_per_contract": 0.25,
            "adaptive_mode": True
        },
        "metrics": {
            "total_pnl": 1010762.70,
            "win_rate": 0.7455,
            "win_day_rate": 0.9470,
            "max_drawdown": 1159.36,
            "profit_factor": 18.97
        }
    }
}

# Dashboard files to update
DASHBOARD_FILES = [
    "hedge_dashboard.html",
    "mnq_dashboard.html",
    "mgc_dashboard.html",
    "mes_dashboard.html",
    "performance_comparison_dashboard.html",
    "market_intelligence.html",
    "correlation_analysis_dashboard.html",
    "monte_carlo_dashboard.html",
    "drawdown_analysis_dashboard.html",
    "portfolio_manager.html",
    "portfolio_overview.html",
    "performance_forecast.html",
    "market_volatility.html",
    "daily_pnl_dashboard.html",
    "premium_dashboard.html",
    "custom_alerts_dashboard.html",
    "investor_reporting.html"
]

def ensure_directory(directory):
    """Ensure the directory exists"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def import_market_data():
    """Import all market data into the centralized repository"""
    # Ensure the market_data directory exists
    ensure_directory(DATA_DIR)

    # Import data for each instrument
    for instrument_code, instrument in INSTRUMENTS.items():
        print(f"Importing data for {instrument_code}...")

        # Create instrument directory in market_data
        instrument_dir = os.path.join(DATA_DIR, instrument_code.lower())
        ensure_directory(instrument_dir)

        # Copy input file to market_data directory
        input_file = os.path.join(DASHBOARD_DIR, instrument["input_file"])
        if os.path.exists(input_file):
            dest_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_data.csv")
            shutil.copy2(input_file, dest_file)
            print(f"Copied {input_file} to {dest_file}")
        else:
            print(f"Warning: Input file not found: {input_file}")

        # Copy config file to market_data directory
        config_file = os.path.join(DASHBOARD_DIR, instrument["config_file"])
        if os.path.exists(config_file):
            dest_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_config.js")
            shutil.copy2(config_file, dest_file)
            print(f"Copied {config_file} to {dest_file}")
        else:
            print(f"Warning: Config file not found: {config_file}")

        # Create instrument metadata file
        metadata = {
            "instrumentCode": instrument_code,
            "name": instrument["name"],
            "color": instrument["color"],
            "params": instrument["params"],
            "metrics": instrument["metrics"],
            "lastUpdated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        metadata_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_metadata.json")
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=4)

        print(f"Created metadata file: {metadata_file}")

        # Find and copy trade log files
        trade_log_pattern = os.path.join(OUTPUT_DIR, f"{instrument_code}_*", f"{instrument_code}_trades.csv")
        trade_log_files = glob.glob(trade_log_pattern)

        if trade_log_files:
            # Use the most recent trade log file
            trade_log_file = max(trade_log_files, key=os.path.getmtime)
            dest_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_trades.csv")
            shutil.copy2(trade_log_file, dest_file)
            print(f"Copied {trade_log_file} to {dest_file}")
        else:
            print(f"Warning: No trade log files found for {instrument_code}")

            # Create a sample trade log file
            sample_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_trades.csv")
            with open(sample_file, 'w') as f:
                f.write("EntryTime;ExitTime;Direction;EntryPrice;ExitPrice;Contracts;PnL_Gross;Commission;Slippage;PnL_Net\n")
                entry_time = (datetime.now().replace(hour=9, minute=30, second=0)).strftime('%Y-%m-%d %H:%M:%S')
                exit_time = (datetime.now().replace(hour=10, minute=15, second=0)).strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"{entry_time};{exit_time};LONG;15000.00;15010.00;1;100.00;0.40;0.10;99.50\n")

            print(f"Created sample trade log file: {sample_file}")

        # Find and copy daily PnL files
        daily_pnl_pattern = os.path.join(OUTPUT_DIR, f"{instrument_code}_*", f"{instrument_code}_daily_pnl.csv")
        daily_pnl_files = glob.glob(daily_pnl_pattern)

        if daily_pnl_files:
            # Use the most recent daily PnL file
            daily_pnl_file = max(daily_pnl_files, key=os.path.getmtime)
            dest_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_daily_pnl.csv")
            shutil.copy2(daily_pnl_file, dest_file)
            print(f"Copied {daily_pnl_file} to {dest_file}")
        else:
            print(f"Warning: No daily PnL files found for {instrument_code}")

            # Create a sample daily PnL file
            sample_file = os.path.join(instrument_dir, f"{instrument_code.lower()}_daily_pnl.csv")
            with open(sample_file, 'w') as f:
                f.write("date,PnL_Net\n")
                f.write(f"{datetime.now().strftime('%Y-%m-%d')},{instrument['metrics']['total_pnl'] / 365:.2f}\n")

            print(f"Created sample daily PnL file: {sample_file}")

    # Create combined market data file
    create_combined_market_data()

    print("Market data import complete!")

def create_combined_market_data():
    """Create a combined market data file with data from all instruments"""
    combined_data = {
        "instruments": {},
        "lastUpdated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    # Combine data from all instruments
    for instrument_code, instrument in INSTRUMENTS.items():
        # Load metadata
        metadata_file = os.path.join(DATA_DIR, instrument_code.lower(), f"{instrument_code.lower()}_metadata.json")
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)

            combined_data["instruments"][instrument_code] = metadata

    # Save combined data to file
    combined_file = os.path.join(DATA_DIR, "all_instruments_data.json")
    with open(combined_file, 'w') as f:
        json.dump(combined_data, f, indent=4)

    print(f"Created combined market data file: {combined_file}")

def update_dashboard_config():
    """Update the dashboard_config.js file to use the centralized data repository"""
    config_file = os.path.join(DASHBOARD_DIR, "dashboard_config.js")

    if not os.path.exists(config_file):
        print(f"Warning: Dashboard config file not found: {config_file}")
        return

    # Create a backup of the original file
    backup_file = f"{config_file}.bak"
    shutil.copy2(config_file, backup_file)
    print(f"Created backup of {config_file} to {backup_file}")

    try:
        with open(config_file, 'r') as f:
            content = f.read()

        # Update the dataSources section to use the centralized data repository
        data_sources_section = """    // Data sources configuration
    dataSources: {
        // Trading instruments
        instruments: {
            MNQ: {
                name: "Micro Nasdaq",
                dataFile: "market_data/mnq/mnq_data.csv",
                color: "#00ccff",
                backtest: {
                    resultsFile: "market_data/mnq/mnq_metadata.json",
                    tradesFile: "market_data/mnq/mnq_trades.csv",
                    dailyPnlFile: "market_data/mnq/mnq_daily_pnl.csv",
                    params: {
                        stopLossFactor: 4.5,
                        takeProfitFactor: 3.0,
                        trailFactor: 0.11,
                        fixedTpPoints: 40,
                        contracts: 10,
                        commission: 0.40,
                        slippage: 0.75,
                        adaptiveMode: true
                    },
                    metrics: {
                        totalPnL: 3337467.82,
                        winRate: 0.7884,
                        winDayRate: 0.9810,
                        maxDrawdown: 1470.43,
                        profitFactor: 31.24
                    }
                }
            },
            MGC: {
                name: "Micro Gold",
                dataFile: "market_data/mgc/mgc_data.csv",
                color: "#FFD700",
                backtest: {
                    resultsFile: "market_data/mgc/mgc_metadata.json",
                    tradesFile: "market_data/mgc/mgc_trades.csv",
                    dailyPnlFile: "market_data/mgc/mgc_daily_pnl.csv",
                    params: {
                        stopLossFactor: 8.0,
                        takeProfitFactor: 7.0,
                        trailFactor: 0.02,
                        contracts: 10,
                        commission: 0.40,
                        slippage: 0.10,
                        adaptiveMode: true
                    },
                    metrics: {
                        totalPnL: 890734.73,
                        winRate: 0.7049,
                        winDayRate: 0.9320,
                        maxDrawdown: 994.70
                    }
                }
            },
            MES: {
                name: "Micro E-mini S&P 500",
                dataFile: "market_data/mes/mes_data.csv",
                color: "#ff3366",
                backtest: {
                    resultsFile: "market_data/mes/mes_metadata.json",
                    tradesFile: "market_data/mes/mes_trades.csv",
                    dailyPnlFile: "market_data/mes/mes_daily_pnl.csv",
                    params: {
                        stopLossFactor: 3.0,
                        takeProfitFactor: 3.0,
                        trailFactor: 0.01,
                        contracts: 10,
                        commission: 0.40,
                        slippage: 0.25,
                        adaptiveMode: true
                    },
                    metrics: {
                        totalPnL: 1010762.70,
                        winRate: 0.7455,
                        winDayRate: 0.9470,
                        maxDrawdown: 1159.36,
                        profitFactor: 18.97
                    }
                }
            }
        },

        // Market data
        marketData: {
            allInstruments: "market_data/all_instruments_data.json",
            volatilityIndex: "market_data/market_data/vix_data.csv",
            correlationData: "market_data/market_data/correlation_data.csv",
            economicCalendar: "market_data/market_data/economic_calendar.json",
            marketNews: "market_data/market_data/market_news.json"
        }
    },"""

        # Replace the dataSources section in the content
        pattern = r"    // Data sources configuration\s+dataSources: \{.*?// Market data.*?\},\s+"
        new_content = re.sub(pattern, data_sources_section, content, flags=re.DOTALL)

        # Write the updated content back to the file
        with open(config_file, 'w') as f:
            f.write(new_content)

        print(f"Updated dashboard config file: {config_file}")

    except Exception as e:
        print(f"Error updating dashboard config file: {e}")

        # Restore the backup
        shutil.copy2(backup_file, config_file)
        print(f"Restored {config_file} from backup due to error")

def main():
    """Main function"""
    print("Starting market data import...")

    # Import all market data
    import_market_data()

    # Update dashboard configuration
    update_dashboard_config()

    print("Market data import complete!")

if __name__ == "__main__":
    main()
