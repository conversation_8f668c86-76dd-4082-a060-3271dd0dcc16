<!DOCTYPE html>

<html>
<head>
<title>Market Intelligence</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        /* Original styles for this dashboard */
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 2rem;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .main-panel {
            display: grid;
            grid-template-rows: auto 1fr;
            gap: 1.5rem;
        }

        .side-panel {
            display: grid;
            grid-template-rows: auto 1fr auto;
            gap: 1.5rem;
        }

        .market-summary {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .market-summary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .market-summary h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .market-card {
            display: flex;
            flex-direction: column;
            padding: 1rem;
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            background-color: rgba(0, 204, 255, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .market-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        }

        .market-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .market-value {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            font-family: 'Orbitron', sans-serif;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .market-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            font-family: 'Rajdhani', sans-serif;
        }

        .market-change.positive {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        .market-change.negative {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        .news-feed {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            overflow-y: auto;
            max-height: 600px;
            position: relative;
            scrollbar-width: thin;
            scrollbar-color: var(--primary) var(--card-bg);
        }

        .news-feed::-webkit-scrollbar {
            width: 8px;
        }

        .news-feed::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        .news-feed::-webkit-scrollbar-thumb {
            background-color: var(--primary);
            border-radius: 20px;
            border: 2px solid var(--card-bg);
        }

        .news-feed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .news-feed h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .news-item {
            padding: 1rem;
            margin-bottom: 1.25rem;
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            background-color: rgba(0, 204, 255, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .news-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        }

        .news-item:last-child {
            margin-bottom: 0;
        }

        .news-time {
            font-size: 0.75rem;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .news-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
        }

        .news-source {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-family: 'Rajdhani', sans-serif;
        }

        .news-impact {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 0.5rem;
            font-family: 'Rajdhani', sans-serif;
            border: 1px solid;
        }

        .news-impact.high {
            background-color: rgba(255, 51, 102, 0.1);
            color: var(--danger);
            border-color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
            box-shadow: 0 0 5px var(--danger-glow);
        }

        .news-impact.medium {
            background-color: rgba(255, 204, 0, 0.1);
            color: var(--warning);
            border-color: var(--warning);
            text-shadow: 0 0 5px var(--warning-glow);
            box-shadow: 0 0 5px var(--warning-glow);
        }

        .news-impact.low {
            background-color: rgba(0, 255, 136, 0.1);
            color: var(--success);
            border-color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
            box-shadow: 0 0 5px var(--success-glow);
        }

        .economic-calendar {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .economic-calendar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .economic-calendar h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .calendar-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            background-color: rgba(0, 204, 255, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .calendar-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        }

        .calendar-item:last-child {
            margin-bottom: 0;
        }

        .calendar-time {
            width: 80px;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .calendar-event {
            flex: 1;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .calendar-importance {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-left: 0.75rem;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .calendar-importance.high {
            background-color: var(--danger);
            box-shadow: 0 0 10px var(--danger-glow);
        }

        .calendar-importance.medium {
            background-color: var(--warning);
            box-shadow: 0 0 10px var(--warning-glow);
        }

        .calendar-importance.low {
            background-color: var(--success);
            box-shadow: 0 0 10px var(--success-glow);
        }

        .sentiment-indicators {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sentiment-indicators::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .sentiment-indicators h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .sentiment-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .sentiment-card {
            text-align: center;
            padding: 1rem;
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            background-color: rgba(0, 204, 255, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .sentiment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        }

        .sentiment-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .sentiment-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            font-family: 'Orbitron', sans-serif;
        }

        .sentiment-card.fear .sentiment-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }
        .sentiment-card.greed .sentiment-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .sentiment-card.vix .sentiment-value {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }
        .sentiment-card.put-call .sentiment-value {
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .sentiment-desc {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-family: 'Rajdhani', sans-serif;
        }
    </style>
</head>
<body>
<div class="container">
<div class="header">
<h1>Market Intelligence</h1>
<p>Real-time market data, news, and economic events</p>
</div>
<div class="dashboard-grid">
<div class="main-panel">
<div class="market-summary">
<h3>Market Summary</h3>
<div class="market-grid">
<div class="market-card">
<div class="market-name">S&amp;P 500</div>
<div class="market-value">5,248.36</div>
<div class="market-change positive">+12.45 (+0.24%)</div>
</div>
<div class="market-card">
<div class="market-name">NASDAQ</div>
<div class="market-value">16,782.59</div>
<div class="market-change positive">+58.21 (+0.35%)</div>
</div>
<div class="market-card">
<div class="market-name">Dow Jones</div>
<div class="market-value">38,675.24</div>
<div class="market-change negative">-32.18 (-0.08%)</div>
</div>
<div class="market-card">
<div class="market-name">Russell 2000</div>
<div class="market-value">2,187.45</div>
<div class="market-change positive">+15.32 (+0.71%)</div>
</div>
<div class="market-card">
<div class="market-name">10Y Treasury</div>
<div class="market-value">3.85%</div>
<div class="market-change positive">+0.03</div>
</div>
<div class="market-card">
<div class="market-name">VIX</div>
<div class="market-value">18.24</div>
<div class="market-change negative">-0.87 (-4.56%)</div>
</div>
<div class="market-card">
<div class="market-name">Gold</div>
<div class="market-value">$2,345.60</div>
<div class="market-change positive">+12.40 (+0.53%)</div>
</div>
<div class="market-card">
<div class="market-name">WTI Crude</div>
<div class="market-value">$78.45</div>
<div class="market-change positive">+1.23 (+1.59%)</div>
</div>
</div>
</div>
<div class="chart-container">
<h3>Market Indices (Last 5 Days)</h3>
<div class="chart-wrapper">
<canvas id="marketIndicesChart"></canvas>
</div>
</div>
<div class="chart-container">
<h3>Sector Performance (Today)</h3>
<div class="chart-wrapper">
<canvas id="sectorPerformanceChart"></canvas>
</div>
</div>
</div>
<div class="side-panel">
<div class="sentiment-indicators">
<h3>Market Sentiment</h3>
<div class="sentiment-grid">
<div class="sentiment-card fear">
<div class="sentiment-name">Fear &amp; Greed Index</div>
<div class="sentiment-value">62</div>
<div class="sentiment-desc">Greed</div>
</div>
<div class="sentiment-card vix">
<div class="sentiment-name">VIX</div>
<div class="sentiment-value">18.24</div>
<div class="sentiment-desc">Moderate Volatility</div>
</div>
<div class="sentiment-card put-call">
<div class="sentiment-name">Put/Call Ratio</div>
<div class="sentiment-value">0.85</div>
<div class="sentiment-desc">Neutral</div>
</div>
<div class="sentiment-card">
<div class="sentiment-name">AAII Bull/Bear</div>
<div class="sentiment-value">1.25</div>
<div class="sentiment-desc">Slightly Bullish</div>
</div>
</div>
</div>
<div class="chart-container">
<h3>Market Breadth Indicators</h3>
<div class="chart-wrapper">
<canvas id="marketBreadthChart"></canvas>
</div>
</div>
<div class="news-feed">
<h3>Market News</h3>
<div class="news-item">
<div class="news-time">10:45 AM</div>
<div class="news-title">Fed Minutes Show Officials Discussed Potential Rate Cuts</div>
<div class="news-source">Bloomberg <span class="news-impact high">High Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">9:32 AM</div>
<div class="news-title">Retail Sales Beat Expectations, Rising 0.7% in April</div>
<div class="news-source">CNBC <span class="news-impact medium">Medium Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">9:15 AM</div>
<div class="news-title">Tech Stocks Lead Market Higher as AI Optimism Continues</div>
<div class="news-source">Wall Street Journal <span class="news-impact medium">Medium Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">8:45 AM</div>
<div class="news-title">Oil Prices Rise on Supply Concerns Amid Middle East Tensions</div>
<div class="news-source">Reuters <span class="news-impact low">Low Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">8:30 AM</div>
<div class="news-title">Initial Jobless Claims Fall to 232K, Below Expectations</div>
<div class="news-source">MarketWatch <span class="news-impact medium">Medium Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">8:00 AM</div>
<div class="news-title">European Markets Mixed as ECB Signals Potential Rate Hold</div>
<div class="news-source">Financial Times <span class="news-impact low">Low Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">7:45 AM</div>
<div class="news-title">Major Bank Upgrades Tech Sector, Cites AI Revenue Growth</div>
<div class="news-source">Barron's <span class="news-impact medium">Medium Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">7:30 AM</div>
<div class="news-title">Housing Starts Decline 3.2% in April, Missing Forecasts</div>
<div class="news-source">Bloomberg <span class="news-impact medium">Medium Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">7:15 AM</div>
<div class="news-title">Dollar Weakens Against Major Currencies on Rate Cut Bets</div>
<div class="news-source">Reuters <span class="news-impact low">Low Impact</span></div>
</div>
<div class="news-item">
<div class="news-time">7:00 AM</div>
<div class="news-title">Asian Markets Close Higher, Led by Japanese Stocks</div>
<div class="news-source">CNBC <span class="news-impact low">Low Impact</span></div>
</div>
</div>
<div class="economic-calendar">
<h3>Economic Calendar</h3>
<div class="calendar-item">
<div class="calendar-time">8:30 AM</div>
<div class="calendar-event">Initial Jobless Claims</div>
<div class="calendar-importance medium"></div>
</div>
<div class="calendar-item">
<div class="calendar-time">9:45 AM</div>
<div class="calendar-event">Manufacturing PMI</div>
<div class="calendar-importance high"></div>
</div>
<div class="calendar-item">
<div class="calendar-time">10:00 AM</div>
<div class="calendar-event">Existing Home Sales</div>
<div class="calendar-importance medium"></div>
</div>
<div class="calendar-item">
<div class="calendar-time">10:30 AM</div>
<div class="calendar-event">EIA Crude Oil Inventories</div>
<div class="calendar-importance low"></div>
</div>
<div class="calendar-item">
<div class="calendar-time">2:00 PM</div>
<div class="calendar-event">FOMC Meeting Minutes</div>
<div class="calendar-importance high"></div>
</div>
</div>
</div>
</div>
</div>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Market Indices Chart
            const marketIndicesCtx = document.getElementById('marketIndicesChart').getContext('2d');

            // Generate dates for the last 5 days
            const dates = [];
            const today = new Date();
            for (let i = 4; i >= 0; i--) {
                const date = new Date();
                date.setDate(today.getDate() - i);
                dates.push(date.toISOString().split('T')[0]);
            }

            // Generate market indices data
            const spxData = [5210.45, 5225.78, 5218.92, 5235.91, 5248.36];
            const nasdaqData = [16650.23, 16705.47, 16692.18, 16724.38, 16782.59];
            const dowData = [38725.42, 38692.15, 38705.63, 38707.42, 38675.24];
            const russellData = [2155.23, 2162.47, 2168.92, 2172.13, 2187.45];

            // Normalize data to percentage change from first day
            const normalizeData = (data) => {
                const firstValue = data[0];
                return data.map(value => ((value - firstValue) / firstValue) * 100);
            };

            const normalizedSpx = normalizeData(spxData);
            const normalizedNasdaq = normalizeData(nasdaqData);
            const normalizedDow = normalizeData(dowData);
            const normalizedRussell = normalizeData(russellData);

            new Chart(marketIndicesCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'S&P 500',
                            data: normalizedSpx,
                            borderColor: '#ef4444',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            tension: 0.4
                        },
                        {
                            label: 'NASDAQ',
                            data: normalizedNasdaq,
                            borderColor: '#4f46e5',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            tension: 0.4
                        },
                        {
                            label: 'Dow Jones',
                            data: normalizedDow,
                            borderColor: '#10b981',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            tension: 0.4
                        },
                        {
                            label: 'Russell 2000',
                            data: normalizedRussell,
                            borderColor: '#f59e0b',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Percentage Change (%)'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`;
                                }
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // Sector Performance Chart
            const sectorPerformanceCtx = document.getElementById('sectorPerformanceChart').getContext('2d');

            // Sector performance data
            const sectors = ['Technology', 'Healthcare', 'Financials', 'Consumer Discretionary', 'Industrials', 'Communication Services', 'Consumer Staples', 'Energy', 'Utilities', 'Materials', 'Real Estate'];
            const sectorPerformance = [1.8, 0.9, -0.3, 0.7, 0.4, 1.2, -0.1, 1.5, -0.5, 0.2, -0.8];

            // Create color array based on performance
            const sectorColors = sectorPerformance.map(value =>
                value > 0 ? 'rgba(16, 185, 129, 0.7)' : 'rgba(239, 68, 68, 0.7)'
            );

            const sectorBorderColors = sectorPerformance.map(value =>
                value > 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
            );

            new Chart(sectorPerformanceCtx, {
                type: 'bar',
                data: {
                    labels: sectors,
                    datasets: [{
                        label: 'Sector Performance (%)',
                        data: sectorPerformance,
                        backgroundColor: sectorColors,
                        borderColor: sectorBorderColors,
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.parsed.x > 0 ? '+' : ''}${context.parsed.x.toFixed(2)}%`;
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Market Breadth Chart
            const marketBreadthCtx = document.getElementById('marketBreadthChart').getContext('2d');

            // Generate dates for the last 10 days
            const breadthDates = [];
            for (let i = 9; i >= 0; i--) {
                const date = new Date();
                date.setDate(today.getDate() - i);
                breadthDates.push(date.toISOString().split('T')[0]);
            }

            // Market breadth data
            const advancingIssues = [1850, 1920, 1750, 1680, 2100, 2250, 1950, 1820, 1980, 2050];
            const decliningIssues = [1650, 1580, 1750, 1820, 1400, 1250, 1550, 1680, 1520, 1450];
            const advDecRatio = advancingIssues.map((adv, i) => adv / decliningIssues[i]);

            new Chart(marketBreadthCtx, {
                type: 'line',
                data: {
                    labels: breadthDates,
                    datasets: [
                        {
                            label: 'Adv/Dec Ratio',
                            data: advDecRatio,
                            borderColor: '#4f46e5',
                            backgroundColor: 'rgba(79, 70, 229, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            yAxisID: 'y',
                            fill: true
                        },
                        {
                            label: 'Advancing Issues',
                            data: advancingIssues,
                            borderColor: '#10b981',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            tension: 0.4,
                            yAxisID: 'y1',
                            hidden: true
                        },
                        {
                            label: 'Declining Issues',
                            data: decliningIssues,
                            borderColor: '#ef4444',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            tension: 0.4,
                            yAxisID: 'y1',
                            hidden: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Adv/Dec Ratio'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Number of Issues'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        });
    </script>
<!-- Dashboard Integration -->
<script>
        // Wait for dashboard to initialize
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Market Intelligence`;
            }

            // Update market summary with instrument data
            updateMarketSummary(data);

            // Update charts with data
            updateCharts(data);

            console.log('Market intelligence dashboard updated with data for:', data.instrumentCode);
        }

        // Update market summary with instrument data
        function updateMarketSummary(data) {
            // This would be implemented based on your specific market data structure
            console.log('Market summary would be updated with:', data);
        }

        // Update charts with data
        function updateCharts(data) {
            // Update market chart
            updateMarketChart(data);

            // Update correlation chart
            updateCorrelationChart(data);
        }

        // Update market chart
        function updateMarketChart(data) {
            const dailyPnL = data.dailyPnL || [];

            if (dailyPnL.length === 0) {
                console.warn('No daily PnL data available');
                return;
            }

            // Prepare data
            const labels = dailyPnL.map(day => day.date);
            const prices = dailyPnL.map(day => day.close || 0);

            // Create chart config using the chart utilities
            const chartConfig = dashboardChartUtils.createLineChartConfig(
                labels,
                [{
                    label: `${data.instrumentConfig.name} Price`,
                    data: prices,
                    color: data.instrumentConfig.color
                }]
            );

            // Get the chart context
            const ctx = document.getElementById('market-chart').getContext('2d');

            // Create or update chart
            if (window.marketChart) {
                window.marketChart.data = chartConfig.data;
                window.marketChart.update();
            } else {
                window.marketChart = new Chart(ctx, chartConfig);
            }
        }

        // Update correlation chart
        function updateCorrelationChart(data) {
            // This would be implemented based on your specific correlation data structure
            console.log('Correlation chart would be updated with:', data);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
