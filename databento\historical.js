/**
 * Databento Historical API Client
 * For accessing historical market data
 */

const axios = require('axios');
const { formatDate, handleError } = require('./utils');

class HistoricalClient {
  /**
   * Create a new Historical API client
   * @param {string} apiKey - Your Databento API key
   * @param {string} gateway - Site of historical gateway to connect to
   */
  constructor(apiKey, gateway = 'bo1') {
    this.apiKey = apiKey;
    this.baseUrl = `https://hist.databento.com/v0`;
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Basic ${Buffer.from(`${apiKey}:`).toString('base64')}`,
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Get historical time series data
   * @param {string} dataset - Dataset code (e.g., 'GLBX.MDP3')
   * @param {string|Array} symbols - Symbol or array of symbols
   * @param {string|Date} start - Start date/time
   * @param {string|Date} end - End date/time
   * @param {string} schema - Data schema (e.g., 'trades', 'mbp-1')
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Historical data
   */
  async getTimeseries(dataset, symbols, start, end, schema, options = {}) {
    try {
      const params = {
        dataset,
        symbols: Array.isArray(symbols) ? symbols.join(',') : symbols,
        start: formatDate(start),
        end: formatDate(end),
        schema,
        ...options
      };

      console.log(`Getting timeseries data for ${symbols} from ${start} to ${end}`);

      // The API returns a DBNStore object which we need to handle differently
      const response = await this.client.get('/timeseries.get', {
        params,
        responseType: 'arraybuffer' // Request binary data
      });

      // For now, just return the raw data
      // In a production environment, we would parse the DBN format
      return {
        success: true,
        message: 'Data retrieved successfully',
        rawData: response.data,
        params
      };
    } catch (error) {
      throw handleError(error);
    }
  }

  /**
   * Get historical OHLCV data (bars/candles)
   * @param {string} dataset - Dataset code
   * @param {string|Array} symbols - Symbol or array of symbols
   * @param {string|Date} start - Start date/time
   * @param {string|Date} end - End date/time
   * @param {string} interval - Bar interval (e.g., '1m', '1h', '1d')
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - OHLCV data
   */
  async getBars(dataset, symbols, start, end, interval = '1m', options = {}) {
    try {
      const params = {
        dataset,
        symbols: Array.isArray(symbols) ? symbols.join(',') : symbols,
        start: formatDate(start),
        end: formatDate(end),
        interval,
        ...options
      };

      console.log(`Getting bars for ${symbols} from ${start} to ${end} with interval ${interval}`);

      // The API returns a DBNStore object which we need to handle differently
      const response = await this.client.get('/timeseries.get', {
        params: {
          ...params,
          schema: 'ohlcv-1' // Use OHLCV schema
        },
        responseType: 'arraybuffer' // Request binary data
      });

      // For now, just return the raw data
      // In a production environment, we would parse the DBN format
      return {
        success: true,
        message: 'Data retrieved successfully',
        rawData: response.data,
        params: {
          ...params,
          schema: 'ohlcv-1'
        }
      };
    } catch (error) {
      throw handleError(error);
    }
  }

  /**
   * Get available datasets
   * @returns {Promise<Object>} - Available datasets
   */
  async getDatasets() {
    try {
      console.log('Getting available datasets');
      const response = await this.client.get('/metadata.list_datasets');

      // Based on the Python test, the response is an array of strings
      return response.data;
    } catch (error) {
      throw handleError(error);
    }
  }

  /**
   * Get available symbols for a dataset
   * @param {string} dataset - Dataset code
   * @returns {Promise<Object>} - Available symbols
   */
  async getSymbols(dataset) {
    try {
      console.log(`Getting symbols for dataset ${dataset}`);

      // Based on the Python test, we need to use a different approach
      // The list_symbols method doesn't exist in the current API version
      // Instead, we'll use timeseries.get_range with a small time window to get symbols

      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const params = {
        dataset,
        symbols: 'ALL_SYMBOLS', // Request all symbols
        start: formatDate(yesterday),
        end: formatDate(now),
        schema: 'trades',
        limit: 1000 // Limit to 1000 symbols
      };

      const response = await this.client.get('/timeseries.get', {
        params,
        responseType: 'arraybuffer' // Request binary data
      });

      // For now, just return a message
      // In a production environment, we would parse the DBN format to extract symbols
      return {
        success: true,
        message: 'Symbol data retrieved successfully',
        rawData: response.data,
        params
      };
    } catch (error) {
      throw handleError(error);
    }
  }
}

module.exports = HistoricalClient;
