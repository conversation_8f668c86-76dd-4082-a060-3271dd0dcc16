/**
 * MGC Backtest Script
 *
 * Dedicated backtest script for Micro Gold Futures (MGC)
 * Optimized for the parameters: SL=8.5, TP=7.0, Trail=0.02
 */

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
const config = require('./config');

// --- Output Directory ---
const outputDir = './output/MGC_Backtest_Results';

// --- Setup & Initialization ---
console.log("Starting MGC Backtest...");
if (!fs.existsSync(outputDir)) {
    try {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`Created output directory: ${outputDir}`);
    } catch (err) {
        console.error(`Error creating output directory '${outputDir}':`, err);
        process.exit(1);
    }
} else {
    console.log(`Output directory exists: ${outputDir}`);
}

const allCandles = [];
let allRunResults = [];

// --- Data Loading & Parsing ---
console.log(`Loading data from: ${config.inputFile}`);
fs.createReadStream(config.inputFile)
    .pipe(csv({ separator: ';', mapHeaders: ({ header }) => header.trim() }))
    .on('data', d => {
        const open = +d['Open'];
        const high = +d['High'];
        const low = +d['Low'];
        const close = +d['Close'];
        const timeString = d['Time'] || d['Date'] || d['Time left'];

        let timestampSeconds = NaN;
        if (timeString) {
            let parsedDate;
            try {
                parsedDate = new Date(timeString);
            } catch (e) {}

            if (parsedDate && !isNaN(parsedDate.getTime())) {
                timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
            } else if (timeString && !isNaN(Number(timeString))) {
                const tsNum = Number(timeString);
                timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
            }
        }

        if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            return;
        }

        allCandles.push({ timestamp: timestampSeconds, open, high, low, close });
    })
    .on('end', () => {
        console.log("CSV parsing finished.");
        if (allCandles.length === 0) {
            console.error(`Error: No valid candle data parsed from ${config.inputFile}.`);
            process.exit(1);
        }

        console.log(`Parsed ${allCandles.length} candles.`);
        allCandles.sort((a, b) => a.timestamp - b.timestamp);

        console.log(`Data time range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);

        // Calculate indicators and run backtest
        computeIndicators(allCandles);
        runBacktest();
    })
    .on('error', (err) => {
        console.error(`Error reading CSV file ${config.inputFile}:`, err);
        process.exit(1);
    });

// --- Indicator Calculation ---
function computeIndicators(candles) {
    console.log("Computing indicators...");

    // Calculate SMA200
    if (config.sma200Period > 0) {
        const closes = candles.map(c => c.close);
        for (let i = 0; i < candles.length; i++) {
            if (i >= config.sma200Period - 1) {
                let sum = 0;
                for (let j = i - config.sma200Period + 1; j <= i; j++) {
                    sum += closes[j];
                }
                candles[i].sma200 = sum / config.sma200Period;
            } else {
                candles[i].sma200 = NaN;
            }
        }
    }

    // Calculate WMA50
    if (config.wma50Period > 0) {
        const closes = candles.map(c => c.close);
        for (let i = 0; i < candles.length; i++) {
            if (i >= config.wma50Period - 1) {
                let weightedSum = 0;
                let weightSum = 0;
                for (let j = 0; j < config.wma50Period; j++) {
                    const weight = config.wma50Period - j;
                    weightedSum += closes[i - j] * weight;
                    weightSum += weight;
                }
                candles[i].wma50 = weightedSum / weightSum;
            } else {
                candles[i].wma50 = NaN;
            }
        }
    }

    // Calculate RSI
    if (config.rsiPeriod > 0) {
        const closes = candles.map(c => c.close);
        for (let i = 0; i < candles.length; i++) {
            if (i >= config.rsiPeriod) {
                let gains = 0, losses = 0;
                for (let j = i - config.rsiPeriod + 1; j <= i; j++) {
                    if (j > 0) {
                        const delta = closes[j] - closes[j - 1];
                        if (delta > 0) {
                            gains += delta;
                        } else {
                            losses -= delta;
                        }
                    }
                }

                const avgGain = gains / config.rsiPeriod;
                const avgLoss = losses / config.rsiPeriod;

                if (avgLoss === 0) {
                    candles[i].rsi = 100;
                } else if (avgGain === 0) {
                    candles[i].rsi = 0;
                } else {
                    const rs = avgGain / avgLoss;
                    candles[i].rsi = 100 - (100 / (1 + rs));
                }
            } else {
                candles[i].rsi = NaN;
            }
        }

        // Calculate RSI MA
        if (config.rsiMaPeriod > 0) {
            const rsiValues = candles.map(c => c.rsi);
            for (let i = 0; i < candles.length; i++) {
                if (i >= config.rsiPeriod + config.rsiMaPeriod - 1) {
                    let sum = 0;
                    for (let j = i - config.rsiMaPeriod + 1; j <= i; j++) {
                        sum += rsiValues[j];
                    }
                    candles[i].rsiMa = sum / config.rsiMaPeriod;
                } else {
                    candles[i].rsiMa = NaN;
                }
            }
        }
    }

    // Calculate ATR
    if (config.atrPeriod > 0) {
        const trs = [];
        for (let i = 0; i < candles.length; i++) {
            if (i === 0) {
                trs.push(candles[i].high - candles[i].low);
            } else {
                const tr = Math.max(
                    candles[i].high - candles[i].low,
                    Math.abs(candles[i].high - candles[i-1].close),
                    Math.abs(candles[i].low - candles[i-1].close)
                );
                trs.push(tr);
            }
        }

        for (let i = 0; i < candles.length; i++) {
            if (i >= config.atrPeriod - 1) {
                let sum = 0;
                for (let j = i - config.atrPeriod + 1; j <= i; j++) {
                    sum += trs[j];
                }
                candles[i].atr = sum / config.atrPeriod;
            } else {
                candles[i].atr = NaN;
            }
        }
    }

    console.log("Indicators computed.");
}

// --- Helper Functions ---
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji';
}

function getDayIdentifier(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toISOString().slice(0, 10);
}

// --- Pattern Detection ---
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;

        if (engulf && wick3 > wick2) return 'bullish';
    }

    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);

        if (engulf && wick3 > wick2) return 'bearish';
    }

    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c2.open)) return 'bullish';
    }

    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c2.open)) return 'bearish';
    }

    return null;
}

// --- Entry Filter ---
function entryOK(dir, patternType, c3) {
    // Basic validation
    if (isNaN(c3.wma50) || isNaN(c3.sma200) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
        return false;
    }

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // SMA filter (if SMA200 is enabled)
    if (config.sma200Period > 0 && ((dir === 'bullish' && c3.close <= c3.sma200) || (dir === 'bearish' && c3.close >= c3.sma200))) {
        return false;
    }

    // RSI filter for all patterns
    if ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa)) {
        return false;
    }

    // Minimum ATR filter
    if (c3.atr < (config.minAtrEntry || 0)) {
        return false;
    }

    // RSI-MA separation filter
    if (Math.abs(c3.rsi - c3.rsiMa) < (config.minRsiMaSeparation || 0)) {
        return false;
    }

    return true;
}

// --- Main Backtest Function ---
function runBacktest() {
    console.log("Running MGC backtest with parameters:");

    if (config.isAdaptiveRun) {
        console.log("Mode: ADAPTIVE");
        console.log("Adaptive Parameters:");
        console.log(`  Low Volatility: SL=${config.adaptiveParams.Low.slFactor}, TP=${config.adaptiveParams.Low.tpFactor}, Trail=${config.adaptiveParams.Low.trailFactor}`);
        console.log(`  Medium Volatility: SL=${config.adaptiveParams.Medium.slFactor}, TP=${config.adaptiveParams.Medium.tpFactor}, Trail=${config.adaptiveParams.Medium.trailFactor}`);
        console.log(`  High Volatility: SL=${config.adaptiveParams.High.slFactor}, TP=${config.adaptiveParams.High.tpFactor}, Trail=${config.adaptiveParams.High.trailFactor}`);
    } else {
        console.log("Mode: FIXED");
        console.log(`SL Factor: ${config.slFactors}`);
        console.log(`TP Factor: ${config.tpFactors}`);
        console.log(`Trail Factor: ${config.trailFactors}`);
    }

    console.log(`Fixed Contracts: ${config.fixedContracts}`);
    console.log(`Commission: $${config.commissionPerContract} per contract`);
    console.log(`Slippage: ${config.slippagePoints} points`);

    // Initialize variables
    let balance = config.initialBalance;
    let peakBalance = balance;
    let maxDrawdown = 0;
    let trades = 0;
    let wins = 0;
    let losses = 0;
    let position = null;
    let tradeLog = [];
    let dailyPnL = new Map();
    let exitCounts = { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, end_of_period: 0 };

    // Start from the 4th candle to have enough lookback
    const startIdx = 3;

    // Loop through candles
    for (let i = startIdx; i < allCandles.length; i++) {
        const c0 = allCandles[i-3];
        const c1 = allCandles[i-2];
        const c2 = allCandles[i-1];
        const c3 = allCandles[i];

        // Skip if missing data
        if (!c3) continue;

        // Skip if time filter is enabled
        if (config.timeFilterEnabled) {
            // Time filtering logic would go here
            continue;
        }

        // Skip if indicators are not valid
        if (isNaN(c3.wma50) || isNaN(c3.sma200) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
            continue;
        }

        // Check for entry patterns if no position
        if (!position) {
            const p3 = detect3(c1, c2, c3);
            const p4 = detect4(c0, c1, c2, c3);

            let pattern = null;
            let patternType = null;

            if (p4) {
                pattern = p4;
                patternType = 'four';
            } else if (p3) {
                pattern = p3;
                patternType = 'three';
            }

            // If pattern found and entry filter passes
            if (pattern && entryOK(pattern, patternType, c3)) {
                trades++;

                // Calculate entry parameters
                const entryPrice = c3.close;
                const atr = c3.atr;

                // Determine parameters based on mode (adaptive or fixed)
                let slFactor, tpFactor, trailFactor;

                if (config.isAdaptiveRun) {
                    // Determine ATR regime
                    const atrRegime = atr < config.atrThresholds.low_medium ? 'Low' :
                                     (atr > config.atrThresholds.medium_high ? 'High' : 'Medium');

                    // Get parameters for current regime
                    slFactor = config.adaptiveParams[atrRegime].slFactor;
                    tpFactor = config.adaptiveParams[atrRegime].tpFactor;
                    trailFactor = config.adaptiveParams[atrRegime].trailFactor;
                } else {
                    slFactor = config.slFactors;
                    tpFactor = config.tpFactors;
                    trailFactor = config.trailFactors;
                }

                const slDistance = atr * slFactor;
                const tpDistance = Math.max(config.fixedTpPoints || 0, atr * tpFactor);
                const contracts = config.fixedContracts;

                // Create position object
                position = {
                    entryTime: new Date(c3.timestamp * 1000),
                    direction: pattern,
                    entry: entryPrice,
                    atr: atr,
                    stopLoss: pattern === 'bullish' ? entryPrice - slDistance : entryPrice + slDistance,
                    takeProfit: pattern === 'bullish' ? entryPrice + tpDistance : entryPrice - tpDistance,
                    trailStop: pattern === 'bullish' ? entryPrice - (atr * trailFactor) : entryPrice + (atr * trailFactor),
                    trailFactor: trailFactor,
                    trailHigh: c3.high,
                    trailLow: c3.low,
                    contracts: contracts,
                    entryBar: i,
                    atrRegime: config.isAdaptiveRun ?
                        (atr < config.atrThresholds.low_medium ? 'Low' :
                        (atr > config.atrThresholds.medium_high ? 'High' : 'Medium')) :
                        'Fixed'
                };
            }
        }

        // Manage existing position
        if (position) {
            // Update trail values
            position.trailHigh = Math.max(position.trailHigh, c3.high);
            position.trailLow = Math.min(position.trailLow, c3.low);

            // Update trailing stop
            if (position.direction === 'bullish') {
                position.trailStop = Math.max(position.trailStop, position.trailHigh - (c3.atr * position.trailFactor));
            } else {
                position.trailStop = Math.min(position.trailStop, position.trailLow + (c3.atr * position.trailFactor));
            }

            // Check for exit conditions
            let exitReason = null;
            let exitPrice = null;

            if (position.direction === 'bullish') {
                // Stop loss hit
                if (c3.low <= position.stopLoss) {
                    exitReason = 'sl';
                    exitPrice = position.stopLoss;
                }
                // Take profit hit
                else if (c3.high >= position.takeProfit) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfit;
                }
                // Trailing stop hit
                else if (c3.low <= position.trailStop) {
                    exitReason = 'trail';
                    exitPrice = position.trailStop;
                }
                // Two-bar color exit
                else if (config.useTwoBarColorExit && i > position.entryBar + 1) {
                    const prevColor = candlestickColor(c2);
                    const curColor = candlestickColor(c3);
                    if (prevColor === 'red' && curColor === 'red') {
                        exitReason = 'color_flow_2bar';
                        exitPrice = c3.close;
                    }
                }
            } else { // Bearish position
                // Stop loss hit
                if (c3.high >= position.stopLoss) {
                    exitReason = 'sl';
                    exitPrice = position.stopLoss;
                }
                // Take profit hit
                else if (c3.low <= position.takeProfit) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfit;
                }
                // Trailing stop hit
                else if (c3.high >= position.trailStop) {
                    exitReason = 'trail';
                    exitPrice = position.trailStop;
                }
                // Two-bar color exit
                else if (config.useTwoBarColorExit && i > position.entryBar + 1) {
                    const prevColor = candlestickColor(c2);
                    const curColor = candlestickColor(c3);
                    if (prevColor === 'green' && curColor === 'green') {
                        exitReason = 'color_flow_2bar';
                        exitPrice = c3.close;
                    }
                }
            }

            // Process exit if triggered
            if (exitReason && exitPrice !== null) {
                // Apply slippage to exit price
                let adjustedExitPrice = exitPrice;
                if (['sl', 'trail', 'color_flow_2bar'].includes(exitReason) && config.slippagePoints > 0) {
                    adjustedExitPrice = position.direction === 'bullish' ?
                        exitPrice - config.slippagePoints :
                        exitPrice + config.slippagePoints;
                }

                // Calculate P&L
                const pnlPoints = position.direction === 'bullish' ?
                    adjustedExitPrice - position.entry :
                    position.entry - adjustedExitPrice;

                const pnlGross = pnlPoints * config.pointValue * position.contracts;
                const commissionCost = config.commissionPerContract * position.contracts;
                const pnlNet = pnlGross - commissionCost;

                // Update statistics
                balance += pnlNet;
                peakBalance = Math.max(peakBalance, balance);
                maxDrawdown = Math.max(maxDrawdown, peakBalance - balance);

                if (pnlNet > 0) {
                    wins++;
                } else {
                    losses++;
                }

                // Update exit counts
                exitCounts[exitReason] = (exitCounts[exitReason] || 0) + 1;

                // Update daily P&L
                const day = getDayIdentifier(c3.timestamp);
                dailyPnL.set(day, (dailyPnL.get(day) || 0) + pnlNet);

                // Log trade
                tradeLog.push({
                    EntryTime: position.entryTime.toISOString(),
                    ExitTime: new Date(c3.timestamp * 1000).toISOString(),
                    Direction: position.direction,
                    Entry: position.entry.toFixed(config.pricePrecision),
                    Exit: adjustedExitPrice.toFixed(config.pricePrecision),
                    Reason: exitReason,
                    PnL_Points: pnlPoints.toFixed(config.pricePrecision),
                    PnL_Net: pnlNet.toFixed(2),
                    Contracts: position.contracts,
                    Duration: i - position.entryBar + 1
                });

                // Clear position
                position = null;
            }
        }
    }

    // Close any open position at the end
    if (position) {
        const lastCandle = allCandles[allCandles.length - 1];
        const exitPrice = lastCandle.close;

        // Apply slippage
        let adjustedExitPrice = position.direction === 'bullish' ?
            exitPrice - config.slippagePoints :
            exitPrice + config.slippagePoints;

        // Calculate P&L
        const pnlPoints = position.direction === 'bullish' ?
            adjustedExitPrice - position.entry :
            position.entry - adjustedExitPrice;

        const pnlGross = pnlPoints * config.pointValue * position.contracts;
        const commissionCost = config.commissionPerContract * position.contracts;
        const pnlNet = pnlGross - commissionCost;

        // Update statistics
        balance += pnlNet;
        peakBalance = Math.max(peakBalance, balance);
        maxDrawdown = Math.max(maxDrawdown, peakBalance - balance);

        if (pnlNet > 0) {
            wins++;
        } else {
            losses++;
        }

        // Update exit counts
        exitCounts.end_of_period = (exitCounts.end_of_period || 0) + 1;

        // Update daily P&L
        const day = getDayIdentifier(lastCandle.timestamp);
        dailyPnL.set(day, (dailyPnL.get(day) || 0) + pnlNet);

        // Log trade
        tradeLog.push({
            EntryTime: position.entryTime.toISOString(),
            ExitTime: new Date(lastCandle.timestamp * 1000).toISOString(),
            Direction: position.direction,
            Entry: position.entry.toFixed(config.pricePrecision),
            Exit: adjustedExitPrice.toFixed(config.pricePrecision),
            Reason: 'end_of_period',
            PnL_Points: pnlPoints.toFixed(config.pricePrecision),
            PnL_Net: pnlNet.toFixed(2),
            Contracts: position.contracts,
            Duration: allCandles.length - position.entryBar
        });
    }

    // Calculate performance metrics
    const totalPnL = balance - config.initialBalance;
    const winRate = trades > 0 ? (wins / trades * 100) : 0;
    const profitFactor = losses > 0 ? (wins / losses) : (wins > 0 ? Infinity : 0);

    // Calculate daily statistics
    const dailyValues = Array.from(dailyPnL.values());
    const tradingDays = dailyValues.length;
    const winDays = dailyValues.filter(p => p > 0).length;
    const winDayRate = tradingDays > 0 ? (winDays / tradingDays * 100) : 0;

    // Display results
    console.log("\n=== MGC BACKTEST RESULTS ===");
    console.log(`Total P&L: $${totalPnL.toFixed(2)}`);
    console.log(`Final Balance: $${balance.toFixed(2)}`);
    console.log(`Maximum Drawdown: $${maxDrawdown.toFixed(2)}`);
    console.log(`Total Trades: ${trades}`);
    console.log(`Wins: ${wins}`);
    console.log(`Losses: ${losses}`);
    console.log(`Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`Profit Factor: ${profitFactor === Infinity ? "∞" : profitFactor.toFixed(2)}`);
    console.log(`Trading Days: ${tradingDays}`);
    console.log(`Win Days: ${winDays}`);
    console.log(`Win Day Rate: ${winDayRate.toFixed(2)}%`);
    console.log("\nExit Distribution:");
    for (const [reason, count] of Object.entries(exitCounts)) {
        console.log(`  ${reason}: ${count} (${(count / trades * 100).toFixed(2)}%)`);
    }

    // Save results to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Save trade log
    const tradeLogFile = path.join(outputDir, `mgc_trade_log_${timestamp}.csv`);
    if (tradeLog.length > 0) {
        const headers = Object.keys(tradeLog[0]);
        const csvContent = [
            headers.join(','),
            ...tradeLog.map(trade => headers.map(h => trade[h]).join(','))
        ].join('\n');

        fs.writeFileSync(tradeLogFile, csvContent);
        console.log(`\nTrade log saved to: ${tradeLogFile}`);
    }

    // Save summary
    const summaryFile = path.join(outputDir, `mgc_summary_${timestamp}.json`);
    const summary = {
        parameters: {
            slFactor: config.slFactors,
            tpFactor: config.tpFactors,
            trailFactor: config.trailFactors,
            fixedContracts: config.fixedContracts,
            commission: config.commissionPerContract,
            slippage: config.slippagePoints
        },
        performance: {
            initialBalance: config.initialBalance,
            finalBalance: balance,
            totalPnL: totalPnL,
            maxDrawdown: maxDrawdown,
            totalTrades: trades,
            wins: wins,
            losses: losses,
            winRate: winRate,
            profitFactor: profitFactor,
            tradingDays: tradingDays,
            winDays: winDays,
            winDayRate: winDayRate,
            exitCounts: exitCounts
        }
    };

    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    console.log(`Summary saved to: ${summaryFile}`);
}
