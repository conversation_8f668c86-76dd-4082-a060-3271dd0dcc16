/**
 * Start Trading Bot with Enhanced Logging
 * 
 * This script starts the trading bot with enhanced logging enabled,
 * ensuring that detailed indicator values and trade decisions are logged.
 */

const main = require('./main');
const enhancedLogger = require('./enhanced_logger');
const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
    console.log(`Created logs directory at ${logsDir}`);
}

// Configure enhanced logging
enhancedLogger.setConfig({
    detailedLogging: true,
    logToConsole: true,
    logToFile: true
});

// Log startup
enhancedLogger.logStatus('Starting trading bot with enhanced logging', {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform
});

console.log('='.repeat(80));
console.log('STARTING TRADING BOT WITH ENHANCED LOGGING');
console.log('='.repeat(80));
console.log('This script starts the trading bot with detailed logging enabled.');
console.log('You will be able to see WMA, RSI, RSI-MA values and detailed trade decision logs.');
console.log('-'.repeat(80));
console.log('Log files will be saved in the ./logs directory:');
console.log('- indicators.log: Contains all indicator values');
console.log('- decisions.log: Contains detailed trade decision information');
console.log('- status.log: Contains bot status information');
console.log('- system.log: Contains general system logs');
console.log('-'.repeat(80));
console.log('To check the bot status, run: node check_bot_status.js');
console.log('='.repeat(80));

// Start the trading bot
console.log('Initializing trading bot...');
main.initialize().then(success => {
    if (success) {
        console.log('Trading bot initialized successfully!');
        enhancedLogger.logStatus('Trading bot initialized successfully', {
            timestamp: new Date().toISOString()
        });
        
        // Keep the process running
        console.log('Bot is now running with enhanced logging. Press Ctrl+C to stop.');
        
        // Handle process signals
        process.on('SIGINT', async () => {
            console.log('\nReceived SIGINT. Shutting down...');
            enhancedLogger.logStatus('Received SIGINT signal, shutting down', {
                timestamp: new Date().toISOString()
            });
            await main.shutdown();
            process.exit(0);
        });
        
        process.on('SIGTERM', async () => {
            console.log('\nReceived SIGTERM. Shutting down...');
            enhancedLogger.logStatus('Received SIGTERM signal, shutting down', {
                timestamp: new Date().toISOString()
            });
            await main.shutdown();
            process.exit(0);
        });
    } else {
        console.error('Failed to initialize trading bot');
        enhancedLogger.logStatus('Failed to initialize trading bot', {
            timestamp: new Date().toISOString(),
            error: 'Initialization failed'
        });
        process.exit(1);
    }
}).catch(error => {
    console.error(`Unhandled error during initialization: ${error.message}`);
    enhancedLogger.logStatus('Unhandled error during initialization', {
        timestamp: new Date().toISOString(),
        error: error.message,
        stack: error.stack
    });
    process.exit(1);
});
