// node-example.js
// Node.js version of the Tradovate API example

const fetch = require('node-fetch');
const { waitForMs } = require('./node-utils');

// API URLs
const URLS = {
    DEMO_URL: 'https://demo.tradovateapi.com/v1',
    LIVE_URL: 'https://live.tradovateapi.com/v1',
    DEMO_WS_URL: 'wss://demo.tradovateapi.com/v1/websocket',
    LIVE_WS_URL: 'wss://live.tradovateapi.com/v1/websocket',
    MD_DEMO_WS_URL: 'wss://md.tradovateapi.com/v1/websocket',
    MD_LIVE_WS_URL: 'wss://md-live.tradovateapi.com/v1/websocket'
};

// API credentials
const credentials = {
    name: "bravesbeatmets",
    password: "Braves12$",
    appId: "Trading Bot",
    appVersion: "1.0",
    cid: "6186",
    sec: "69311dad-75a7-49c6-9958-00ab2c4f1ab6"
};

// In-memory storage for tokens and other data
const storage = {
    accessToken: null,
    tokenExpiration: null,
    deviceId: null,
    userData: null
};

/**
 * Store the access token and its expiration
 * @param {string} token - Access token
 * @param {number} expiration - Expiration timestamp
 */
const setAccessToken = (token, expiration) => {
    storage.accessToken = token;
    storage.tokenExpiration = expiration;
};

/**
 * Get the stored access token and expiration
 * @returns {Object} Object containing token and expiration
 */
const getAccessToken = () => {
    return {
        token: storage.accessToken,
        expiration: storage.tokenExpiration
    };
};

/**
 * Check if the token is valid (not expired)
 * @param {number} expiration - Token expiration timestamp
 * @returns {boolean} Whether the token is valid
 */
const tokenIsValid = (expiration) => {
    if (!expiration) {
        return false;
    }

    // Check if token is expired (with 5 minute buffer)
    const now = Date.now();
    return now < (expiration - (5 * 60 * 1000));
};

/**
 * Helper function for POST requests to Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {boolean} auth - Whether to include auth header (default: true)
 * @returns {Promise<Object>} JSON response
 */
const tvPost = async (endpoint, data = {}, auth = true) => {
    // Build request options
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    };

    // Add auth header if needed
    if (auth) {
        const { token } = getAccessToken();
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Helper function for GET requests to Tradovate API
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {boolean} auth - Whether to include auth header (default: true)
 * @returns {Promise<Object>} JSON response
 */
const tvGet = async (endpoint, params = {}, auth = true) => {
    // Build query string from params
    const queryString = Object.keys(params).length
        ? '?' + Object.entries(params)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&')
        : '';

    // Build request options
    const options = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    };

    // Add auth header if needed
    if (auth) {
        const { token } = getAccessToken();
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }
    }

    // Make the request
    const response = await fetch(`${URLS.DEMO_URL}${endpoint}${queryString}`, options);

    // Parse and return JSON response
    return await response.json();
};

/**
 * Handle time penalty retry logic
 * @param {Object} data - Original request data
 * @param {Object} json - Time penalty response
 * @returns {Promise<Object>} Result of retry attempt
 */
const handleRetry = async (data, json) => {
    const ticket = json['p-ticket'];
    const time = json['p-time'];
    const captcha = json['p-captcha'];

    if (captcha) {
        console.error('Captcha present, cannot retry auth request via third party application. Please try again in an hour.');
        return null;
    }

    console.log(`Time Penalty present. Retrying operation in ${time}s`);

    // Wait for the specified time
    await waitForMs(time * 1000);

    // Retry the request with the ticket
    return await connect({ ...data, 'p-ticket': ticket });
};

/**
 * Connect to Tradovate API and get access token
 * @param {Object} data - Authentication data
 * @returns {Promise<Object>} Authentication response
 */
const connect = async (data) => {
    console.log('Connecting to Tradovate API...');

    try {
        // Check if we already have a valid token
        const { token, expiration } = getAccessToken();
        if (token && tokenIsValid(expiration)) {
            console.log('Already have an access token. Using existing token.');
            return { accessToken: token, expirationTime: expiration };
        }

        // No valid token, request a new one
        console.log('Requesting new access token...');

        // Use tvPost to make the access token request
        // The third parameter is false because we don't need auth for this request
        const response = await tvPost('/auth/accesstokenrequest', data, false);

        console.log('Authentication response:', response);

        if (response.accessToken) {
            console.log('Successfully authenticated!');
            console.log('Access Token:', response.accessToken);
            console.log('Expires at:', new Date(response.expirationTime).toLocaleString());

            // Store the access token for future requests
            setAccessToken(response.accessToken, response.expirationTime);

            return response;
        } else {
            // Check for time penalty response
            if (response['p-ticket']) {
                console.warn('Received time penalty response.');
                console.warn('Penalty time:', response['p-time']);
                console.warn('Captcha required:', response['p-captcha'] ? 'Yes' : 'No');

                // Try to handle the retry
                return await handleRetry(data, response);
            }

            console.error('Authentication failed:', response.errorText || 'Unknown error');
            return null;
        }
    } catch (error) {
        console.error('Error connecting to Tradovate API:', error);
        return null;
    }
};

// Generate a device ID for 2FA
const generateDeviceId = () => {
    return 'example-' + Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15);
};

// Add device ID to credentials
const authData = {
    ...credentials,
    deviceId: generateDeviceId()
};

// Main function to run our application
const main = async () => {
    console.log('Starting Tradovate API example...');

    // Connect to Tradovate API with our credentials
    const authResponse = await connect(authData);

    if (authResponse) {
        console.log('Connection successful!');

        // Example of making an authenticated request
        try {
            console.log('Fetching account list...');
            const accounts = await tvGet('/account/list');
            console.log('Accounts:', accounts);
        } catch (error) {
            console.error('Error fetching accounts:', error);
        }
    } else {
        console.log('Connection failed!');
    }
};

// Run the main function
main();
