/**
 * exact_position_management.js
 * Exact implementation of position management from the original backtest code
 */

const { candlestickColor } = require('./exact_pattern_detection');

/**
 * Manage position (simplified implementation)
 * @param {Object} currentPos - Current position
 * @param {Object} currentCandle - Current candle
 * @param {number} currentIndex - Current index
 * @param {Array} candlesForPeriod - Array of candles
 * @param {Array} completedTrades - Array of completed trades
 * @param {Object} config - Configuration object
 * @param {Object} exitCounts - Exit counts object
 * @returns {Object|null} - Exit info or null
 */
function managePosition(currentPos, currentCandle, currentIndex, candlesForPeriod, completedTrades, config, exitCounts) {
    // Update trail high/low for all candles
    currentPos.trailHigh = Math.max(currentPos.trailHigh, currentCandle.high);
    currentPos.trailLow = Math.min(currentPos.trailLow, currentCandle.low);

    // Only process exits if we're past the entry candle
    if (currentIndex <= currentPos.entryBarIndex) {
        return null;
    }

    const trailF = currentPos.trailFactor;
    const atrTrail = (typeof currentCandle.atr === 'number' && !isNaN(currentCandle.atr) && currentCandle.atr > 0) ?
                    currentCandle.atr : currentPos.atr;

    // Update trail stop price (only moves in one direction)
    if (currentPos.dir === 'bullish') {
        currentPos.trailStopPrice = Math.max(currentPos.trailStopPrice, currentPos.trailHigh - (atrTrail * trailF));
    } else {
        currentPos.trailStopPrice = Math.min(currentPos.trailStopPrice, currentPos.trailLow + (atrTrail * trailF));
    }

    let exitR = null, exitSigP = null;

    if (currentPos.dir === 'bullish') {
        if (currentCandle.low <= currentPos.stopLossPrice) {
            exitR = 'sl';
            exitSigP = currentPos.stopLossPrice;
        } else if (currentCandle.high >= currentPos.entry + currentPos.tpDistance) {
            exitR = 'tp';
            exitSigP = currentPos.entry + currentPos.tpDistance;
        } else if (currentCandle.low <= currentPos.trailStopPrice) {
            exitR = 'trail';
            exitSigP = currentPos.trailStopPrice;
        }
    } else {
        if (currentCandle.high >= currentPos.stopLossPrice) {
            exitR = 'sl';
            exitSigP = currentPos.stopLossPrice;
        } else if (currentCandle.low <= currentPos.entry - currentPos.tpDistance) {
            exitR = 'tp';
            exitSigP = currentPos.entry - currentPos.tpDistance;
        } else if (currentCandle.high >= currentPos.trailStopPrice) {
            exitR = 'trail';
            exitSigP = currentPos.trailStopPrice;
        }
    }

    if (!exitR && config.useTwoBarColorExit && currentIndex > currentPos.entryBarIndex + 1) {
        const prevC = candlesForPeriod[currentIndex - 1];
        const prevCol = candlestickColor(prevC);
        const curCol = candlestickColor(currentCandle);

        if (currentPos.dir === 'bullish' && prevCol === 'red' && curCol === 'red') {
            exitR = 'color_flow_2bar';
            exitSigP = currentCandle.close;
        } else if (currentPos.dir === 'bearish' && prevCol === 'green' && curCol === 'green') {
            exitR = 'color_flow_2bar';
            exitSigP = currentCandle.close;
        }
    }

    if (exitR && exitSigP !== null) {
        // Simplified exit processing - no complex latency handling
        const finalExitP = exitSigP;
        const exitTs = new Date(currentCandle.timestamp * 1000);
        currentPos.currentBarIndex = currentIndex;

        const exitInfo = logAndStoreExit(currentPos, exitR, 0, finalExitP, exitTs, completedTrades, config, exitCounts);
        return exitInfo;
    }

    return null;
}

/**
 * Log and store exit (exact implementation from original backtest)
 * @param {Object} posData - Position data
 * @param {string} reason - Exit reason
 * @param {number} pnlPointsTheoretical - Theoretical PnL in points
 * @param {number} exitSignalPrice - Exit signal price
 * @param {Object} exitTimestamp - Exit timestamp
 * @param {Array} completedTradesArray - Array of completed trades
 * @param {Object} config - Configuration object
 * @param {Object} exitCounts - Exit counts object
 * @returns {Object} - Exit info
 */
function logAndStoreExit(posData, reason, pnlPointsTheoretical, exitSignalPrice, exitTimestamp, completedTradesArray, config, exitCounts) {
    if (!posData || typeof posData.entry !== 'number' || !posData.dir || !exitTimestamp) {
        console.error("logAndStoreExit: Invalid data.");
        return { pnlNetTotal: 0, exitTimestamp: exitTimestamp || new Date() };
    }

    const contracts = posData.contracts || 1;
    const ptVal = config.pointValue;
    let adjExitP = exitSignalPrice;
    const slipPts = config.slippagePoints || 0;

    // Determine if slippage should be applied based on exit reason
    // This is exactly how the original backtest code handles it
    const applySlip = (reason === 'sl' || reason === 'trail' || reason === 'end_of_period' ||
                      reason === 'hold_expired' || reason === 'color_flow_2bar');

    // Apply slippage in the direction that worsens the price
    if (applySlip && slipPts > 0) {
        adjExitP = (posData.dir === 'bullish') ? exitSignalPrice - slipPts : exitSignalPrice + slipPts;
    }

    // Calculate P&L with adjusted exit price
    const pnlPtsAdj = (posData.dir === 'bullish') ? adjExitP - posData.entry : posData.entry - adjExitP;
    const pnlGross = pnlPtsAdj * ptVal * contracts;
    const commCost = config.commissionPerContract * contracts;
    const pnlNet = pnlGross - commCost;

    const durBars = (typeof posData.currentBarIndex === 'number' &&
                    typeof posData.entryBarIndex === 'number' &&
                    posData.currentBarIndex >= posData.entryBarIndex) ?
                    posData.currentBarIndex - posData.entryBarIndex + 1 : 'N/A';

    if (exitCounts) {
        exitCounts[reason] = (exitCounts[reason] || 0) + 1;
    }

    const tradeData = {
        ID: posData.tradeId || 'N/A',
        EntryTime: posData.entryTimestamp?.toISOString() || 'N/A',
        ExitTime: exitTimestamp.toISOString(),
        Dir: posData.dir,
        Entry: posData.entry.toFixed(config.pricePrecision),
        ExitSignal: exitSignalPrice.toFixed(config.pricePrecision),
        ExitFill: adjExitP.toFixed(config.pricePrecision),
        Reason: reason,
        PnL_Pts_Gross: pnlPtsAdj.toFixed(config.pricePrecision),
        PnL_Net: pnlNet.toFixed(2),
        SlippagePts: applySlip ? slipPts : 0,
        CommissionCost: commCost.toFixed(2),
        Contracts: contracts,
        Duration: durBars,
        EntryATRRegime: posData.entryAtrRegime || 'N/A',
        TP_Type: posData.tpType || 'N/A'
    };

    completedTradesArray.push(tradeData);

    return { pnlNetTotal: pnlNet, exitTimestamp: exitTimestamp };
}

/**
 * Handle end of period exit (exact implementation from original backtest)
 * @param {Object} pos - Position
 * @param {Array} candlesForPeriod - Array of candles
 * @param {Array} completedTrades - Array of completed trades
 * @param {Object} config - Configuration object
 * @param {Object} exitCounts - Exit counts object
 * @returns {Object} - Exit info
 */
function handleEndOfPeriodExit(pos, candlesForPeriod, completedTrades, config, exitCounts) {
    const lastC = candlesForPeriod[candlesForPeriod.length - 1];

    if (!lastC) {
        console.warn("End of period check: Last candle missing!");
        return null;
    }

    const exitP = lastC.close;
    const exitTs = new Date(lastC.timestamp * 1000);
    pos.currentBarIndex = candlesForPeriod.length - 1;

    const exitInfo = logAndStoreExit(pos, 'end_of_period', 0, exitP, exitTs, completedTrades, config, exitCounts);
    return exitInfo;
}

module.exports = {
    managePosition,
    logAndStoreExit,
    handleEndOfPeriodExit
};
