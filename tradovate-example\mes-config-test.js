/**
 * mes-config-test.js
 * Test the updated MES configuration
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');
const { mesConfig } = require('../multi_symbol_config');

// Function to run test with MES configuration
async function runMesConfigTest() {
    console.log(`\n=== RUNNING TEST FOR MES WITH UPDATED CONFIGURATION ===\n`);
    
    console.log("Configuration:");
    console.log(`Symbol: ${mesConfig.symbol}`);
    console.log(`SL Factor: ${mesConfig.slFactors}`);
    console.log(`TP Factor: ${mesConfig.tpFactors}`);
    console.log(`Trail Factor: ${mesConfig.trailFactors}`);
    console.log(`Fixed TP Points: ${mesConfig.fixedTpPoints}`);
    console.log(`Commission: ${mesConfig.commissionPerContract}`);
    console.log(`Slippage: ${mesConfig.slippagePoints}`);
    console.log(`Fixed Contracts: ${mesConfig.fixedContracts}`);
    console.log(`ATR Low-Medium: ${mesConfig.atrThresholds.low_medium}`);
    console.log(`ATR Medium-High: ${mesConfig.atrThresholds.medium_high}`);
    console.log(`WMA Filter: ${mesConfig.useWmaFilter}`);
    console.log(`SMA200 Filter: ${mesConfig.useSma200Filter}`);
    console.log(`RSI Filter: ${mesConfig.useRsiFilter}`);
    
    // Run backtest
    const result = await runBacktest(mesConfig);
    
    // Calculate win rate
    const winRate = (result.wins / result.totalTrades * 100).toFixed(2);
    
    // Calculate P&L
    const pnl = result.finalBalance - mesConfig.initialBalance;
    
    // Display results
    console.log(`\nResults:`);
    console.log(`Total trades: ${result.totalTrades}`);
    console.log(`Wins: ${result.wins} (${winRate}%)`);
    console.log(`Losses: ${result.losses} (${(100 - parseFloat(winRate)).toFixed(2)}%)`);
    console.log(`P&L: $${pnl.toFixed(2)}`);
    console.log(`Max drawdown: $${result.maxDrawdown.toFixed(2)}`);
    
    return {
        symbol: mesConfig.symbol,
        totalTrades: result.totalTrades,
        wins: result.wins,
        losses: result.losses,
        winRate: `${winRate}%`,
        pnl: pnl.toFixed(2),
        maxDrawdown: result.maxDrawdown.toFixed(2)
    };
}

// Run the test
runMesConfigTest()
    .then(result => {
        // Save result to file
        const resultsFile = path.join(__dirname, 'mes_config_test_results.json');
        fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
        console.log(`\nResults saved to ${resultsFile}`);
    })
    .catch(error => {
        console.error('Error running MES config test:', error);
    });
