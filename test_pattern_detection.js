/**
 * Test Pattern Detection
 *
 * This script tests the pattern detection functions with the simulated market data.
 */

const fs = require('fs');
const path = require('path');
const logger = require('./data_logger');

// Load the simulated candle data
const dataDir = path.join(__dirname, 'data');
const SYMBOLS = ['MNQM5', 'MESM5', 'MGCM5', 'M2KM5'];

// Helper function to determine candlestick color
function candlestickColor(candle) {
    if (!candle) {
        console.log(`candlestickColor: Candle is null or undefined`);
        return 'invalid';
    }

    if (typeof candle.open !== 'number' || typeof candle.close !== 'number') {
        console.log(`candlestickColor: Open or close is not a number - Open: ${candle.open}, Close: ${candle.close}`);
        return 'invalid';
    }

    if (isNaN(candle.open) || isNaN(candle.close)) {
        console.log(`candlestickColor: Open or close is NaN - Open: ${candle.open}, Close: ${candle.close}`);
        return 'invalid';
    }

    if (candle.close > candle.open) {
        return 'green';
    } else if (candle.close < candle.open) {
        return 'red';
    } else {
        return 'doji';
    }
}

// Detect 3-candle pattern
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) {
        console.log(`detect3: Missing candle data`);
        return null;
    }

    // Log the raw candle data for debugging
    console.log(`detect3: Raw candle data -
        C1: O=${c1.open.toFixed(2)}, H=${c1.high.toFixed(2)}, L=${c1.low.toFixed(2)}, C=${c1.close.toFixed(2)}
        C2: O=${c2.open.toFixed(2)}, H=${c2.high.toFixed(2)}, L=${c2.low.toFixed(2)}, C=${c2.close.toFixed(2)}
        C3: O=${c3.open.toFixed(2)}, H=${c3.high.toFixed(2)}, L=${c3.low.toFixed(2)}, C=${c3.close.toFixed(2)}`);

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    console.log(`detect3: Candle colors - C1: ${col1}, C2: ${col2}, C3: ${col3}`);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') {
        console.log(`detect3: Invalid candle color detected`);
        return null;
    }

    // Log the pattern we're checking
    console.log(`Checking 3-candle pattern: ${col1}-${col2}-${col3}`);

    // Bullish pattern: green-red-green (RELAXED version - no wick comparison)
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        // Relaxed engulfing condition: close can be equal to open
        const engulf = c3.close >= c2.open && c3.open <= c2.close;

        console.log(`detect3: Potential bullish pattern - Engulf: ${engulf} (c3.close=${c3.close.toFixed(2)} >= c2.open=${c2.open.toFixed(2)} && c3.open=${c3.open.toFixed(2)} <= c2.close=${c2.close.toFixed(2)})`);

        if (engulf) {
            console.log(`Detected bullish 3-candle pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close.toFixed(2)} >= c2.open=${c2.open.toFixed(2)} && c3.open=${c3.open.toFixed(2)} <= c2.close=${c2.close.toFixed(2)})`);
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-red (RELAXED version - no wick comparison)
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        // Relaxed engulfing condition: close can be equal to open
        const engulf = c3.close <= c2.open && c3.open >= c2.close;

        console.log(`detect3: Potential bearish pattern - Engulf: ${engulf} (c3.close=${c3.close.toFixed(2)} <= c2.open=${c2.open.toFixed(2)} && c3.open=${c3.open.toFixed(2)} >= c2.close=${c2.close.toFixed(2)})`);

        if (engulf) {
            console.log(`Detected bearish 3-candle pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close.toFixed(2)} <= c2.open=${c2.open.toFixed(2)} && c3.open=${c3.open.toFixed(2)} >= c2.close=${c2.close.toFixed(2)})`);
            return 'bearish';
        }
    }

    return null;
}

// Detect 4-candle pattern
function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) {
        console.log(`detect4: Missing candle data`);
        return null;
    }

    // Log the raw candle data for debugging
    console.log(`detect4: Raw candle data -
        C0: O=${c0.open.toFixed(2)}, H=${c0.high.toFixed(2)}, L=${c0.low.toFixed(2)}, C=${c0.close.toFixed(2)}
        C1: O=${c1.open.toFixed(2)}, H=${c1.high.toFixed(2)}, L=${c1.low.toFixed(2)}, C=${c1.close.toFixed(2)}
        C2: O=${c2.open.toFixed(2)}, H=${c2.high.toFixed(2)}, L=${c2.low.toFixed(2)}, C=${c2.close.toFixed(2)}
        C3: O=${c3.open.toFixed(2)}, H=${c3.high.toFixed(2)}, L=${c3.low.toFixed(2)}, C=${c3.close.toFixed(2)}`);

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    console.log(`detect4: Candle colors - C0: ${col0}, C1: ${col1}, C2: ${col2}, C3: ${col3}`);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') {
        console.log(`detect4: Invalid candle color detected`);
        return null;
    }

    // Log the pattern we're checking
    console.log(`Checking 4-candle pattern: ${col0}-${col1}-${col2}-${col3}`);

    // Bullish pattern: green-red-red-green (SLIGHTLY RELAXED version)
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        const maxOpen = Math.max(c1.open, c2.open);
        // Relaxed condition: close can be equal to the max open
        const condition = c3.close >= maxOpen;
        console.log(`detect4: Potential bullish pattern - Condition: ${condition} (c3.close=${c3.close.toFixed(2)} >= max(c1.open=${c1.open.toFixed(2)}, c2.open=${c2.open.toFixed(2)})=${maxOpen.toFixed(2)})`);

        if (condition) {
            console.log(`Detected bullish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close.toFixed(2)} >= max(c1.open=${c1.open.toFixed(2)}, c2.open=${c2.open.toFixed(2)})=${maxOpen.toFixed(2)})`);
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-green-red (SLIGHTLY RELAXED version)
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        const minOpen = Math.min(c1.open, c2.open);
        // Relaxed condition: close can be equal to the min open
        const condition = c3.close <= minOpen;
        console.log(`detect4: Potential bearish pattern - Condition: ${condition} (c3.close=${c3.close.toFixed(2)} <= min(c1.open=${c1.open.toFixed(2)}, c2.open=${c2.open.toFixed(2)})=${minOpen.toFixed(2)})`);

        if (condition) {
            console.log(`Detected bearish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close.toFixed(2)} <= min(c1.open=${c1.open.toFixed(2)}, c2.open=${c2.open.toFixed(2)})=${minOpen.toFixed(2)})`);
            return 'bearish';
        }
    }

    return null;
}

// Test pattern detection for each symbol
for (const symbol of SYMBOLS) {
    console.log(`\n=== Testing pattern detection for ${symbol} ===\n`);

    // Load the candle data
    const candleDataPath = path.join(dataDir, `${symbol}_candles.json`);
    if (!fs.existsSync(candleDataPath)) {
        console.log(`No candle data found for ${symbol}`);
        continue;
    }

    const candles = JSON.parse(fs.readFileSync(candleDataPath, 'utf8'));
    console.log(`Loaded ${candles.length} candles for ${symbol}`);

    // Test 3-candle pattern detection
    console.log(`\n--- Testing 3-candle pattern detection ---\n`);
    for (let i = 2; i < candles.length; i++) {
        const c1 = candles[i - 2];
        const c2 = candles[i - 1];
        const c3 = candles[i];

        console.log(`\nChecking candles at indices ${i-2}, ${i-1}, ${i} (timestamps: ${new Date(c1.timestamp).toISOString()}, ${new Date(c2.timestamp).toISOString()}, ${new Date(c3.timestamp).toISOString()})`);

        const pattern = detect3(c1, c2, c3);
        if (pattern) {
            console.log(`\n!!! DETECTED ${pattern.toUpperCase()} 3-CANDLE PATTERN !!!\n`);
        }
    }

    // Test 4-candle pattern detection
    console.log(`\n--- Testing 4-candle pattern detection ---\n`);
    for (let i = 3; i < candles.length; i++) {
        const c0 = candles[i - 3];
        const c1 = candles[i - 2];
        const c2 = candles[i - 1];
        const c3 = candles[i];

        console.log(`\nChecking candles at indices ${i-3}, ${i-2}, ${i-1}, ${i} (timestamps: ${new Date(c0.timestamp).toISOString()}, ${new Date(c1.timestamp).toISOString()}, ${new Date(c2.timestamp).toISOString()}, ${new Date(c3.timestamp).toISOString()})`);

        const pattern = detect4(c0, c1, c2, c3);
        if (pattern) {
            console.log(`\n!!! DETECTED ${pattern.toUpperCase()} 4-CANDLE PATTERN !!!\n`);
        }
    }
}

console.log('\nPattern detection testing completed');
