/**
 * Run EURUSD Grid Test
 * 
 * This script runs a grid test for EURUSD forex pair to find optimal parameters.
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');

console.log("Starting EURUSD Grid Test...");

// Parse command line arguments
const args = process.argv.slice(2);
const useFocused = args.includes('--focused');

// Load configuration
const config = require('./config_eurusd_grid_test.js');
const dataPath = config.inputFile;

// Date range for testing
const dateRange = {
    startDate: '2020-01-01',
    endDate: '2025-01-01'
};

// Define grid parameters
const fullGridParams = {
    slFactors: config.slFactors,
    tpFactors: config.tpFactors,
    trailFactors: config.trailFactors,
    fixedTpPoints: config.fixedTpPoints
};

// Define focused grid parameters (fewer combinations for faster testing)
const focusedGridParams = {
    slFactors: [4.0, 5.0, 6.0],
    tpFactors: [3.0, 4.0, 5.0],
    trailFactors: [0.02, 0.03, 0.05],
    fixedTpPoints: [0, 0.0010]
};

// Select grid parameters based on command line argument
const selectedParams = useFocused ? focusedGridParams : fullGridParams;

/**
 * Run grid test for EURUSD
 */
async function runGridTest() {
    console.log(`\n===== RUNNING EURUSD GRID TEST =====`);
    console.log(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
    console.log(`Using ${useFocused ? 'focused' : 'full'} grid test mode`);
    console.log(`Grid parameters: ${JSON.stringify(selectedParams, null, 2)}`);
    
    // Create backtest instance with configuration
    const backtest = new GridBacktest(config);
    
    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { dateRange });
    
    // Run grid test
    console.log(`Running grid test with ${useFocused ? 'focused' : 'full'} parameters...`);
    const results = backtest.runGridTest(selectedParams);
    
    // Sort results by profit factor
    results.sort((a, b) => b.stats.profitFactor - a.stats.profitFactor);
    
    // Create output directory if it doesn't exist
    if (!fs.existsSync(config.outputDir)) {
        fs.mkdirSync(config.outputDir, { recursive: true });
    }
    
    // Save results to file
    const resultsPath = path.join(config.outputDir, 'grid_test_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`Results saved to ${resultsPath}`);
    
    // Display top 10 results
    console.log("\n===== TOP 10 PARAMETER COMBINATIONS =====");
    for (let i = 0; i < Math.min(10, results.length); i++) {
        const result = results[i];
        console.log(`\n#${i + 1}: ${JSON.stringify(result.params)}`);
        console.log(`  Total PnL: $${result.stats.totalPnL.toFixed(2)}`);
        console.log(`  Win Rate: ${(result.stats.winRate * 100).toFixed(2)}%`);
        console.log(`  Win Day Rate: ${(result.stats.winDayRate * 100).toFixed(2)}%`);
        console.log(`  Profit Factor: ${result.stats.profitFactor.toFixed(2)}`);
        console.log(`  Max Drawdown: $${result.stats.maxDrawdown.toFixed(2)}`);
    }
    
    // Generate HTML report
    const reportPath = path.join(config.outputDir, 'grid_test_report.html');
    generateHtmlReport(results, reportPath);
    console.log(`HTML report saved to ${reportPath}`);
}

/**
 * Generate HTML report
 * @param {Array} results - Grid test results
 * @param {string} reportPath - Path to save the report
 */
function generateHtmlReport(results, reportPath) {
    // Create HTML report
    let html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>EURUSD Grid Test Results</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1, h2 { color: #333; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            tr:hover { background-color: #f5f5f5; }
            .positive { color: green; }
            .negative { color: red; }
        </style>
    </head>
    <body>
        <h1>EURUSD Grid Test Results</h1>
        <p>Total combinations tested: ${results.length}</p>
        
        <h2>Top 20 Parameter Combinations</h2>
        <table>
            <tr>
                <th>Rank</th>
                <th>SL Factor</th>
                <th>TP Factor</th>
                <th>Trail Factor</th>
                <th>Fixed TP Points</th>
                <th>Total PnL</th>
                <th>Win Rate</th>
                <th>Win Day Rate</th>
                <th>Profit Factor</th>
                <th>Max Drawdown</th>
                <th>Total Trades</th>
            </tr>
    `;
    
    // Add top 20 results to table
    for (let i = 0; i < Math.min(20, results.length); i++) {
        const result = results[i];
        const params = result.params;
        const stats = result.stats;
        
        html += `
            <tr>
                <td>${i + 1}</td>
                <td>${params.slFactors}</td>
                <td>${params.tpFactors}</td>
                <td>${params.trailFactors}</td>
                <td>${params.fixedTpPoints}</td>
                <td class="${stats.totalPnL >= 0 ? 'positive' : 'negative'}">$${stats.totalPnL.toFixed(2)}</td>
                <td>${(stats.winRate * 100).toFixed(2)}%</td>
                <td>${(stats.winDayRate * 100).toFixed(2)}%</td>
                <td>${stats.profitFactor.toFixed(2)}</td>
                <td>$${stats.maxDrawdown.toFixed(2)}</td>
                <td>${stats.totalTrades}</td>
            </tr>
        `;
    }
    
    html += `
        </table>
    </body>
    </html>
    `;
    
    // Save HTML report
    fs.writeFileSync(reportPath, html);
}

// Run the grid test
runGridTest().catch(err => {
    console.error('Error running grid test:', err);
});
