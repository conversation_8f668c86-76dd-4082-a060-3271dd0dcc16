/**
 * Enhanced Logging Module for Trading Bot
 * 
 * This module provides detailed logging for indicator values and trade decisions,
 * making it easier to debug and understand the bot's behavior.
 */

const fs = require('fs');
const path = require('path');
const logger = require('./data_logger');

// Configuration
const config = {
    logDir: './logs',
    indicatorLogFile: 'indicators.log',
    decisionLogFile: 'decisions.log',
    statusLogFile: 'status.log',
    detailedLogging: true,
    logToConsole: true,
    logToFile: true
};

// Ensure log directory exists
if (!fs.existsSync(config.logDir)) {
    fs.mkdirSync(config.logDir, { recursive: true });
}

// Create log file streams
const indicatorLogStream = fs.createWriteStream(
    path.join(config.logDir, config.indicatorLogFile),
    { flags: 'a' }
);

const decisionLogStream = fs.createWriteStream(
    path.join(config.logDir, config.decisionLogFile),
    { flags: 'a' }
);

const statusLogStream = fs.createWriteStream(
    path.join(config.logDir, config.statusLogFile),
    { flags: 'a' }
);

/**
 * Log indicator values
 * @param {string} symbol - Trading symbol
 * @param {Object} indicators - Indicator values
 * @param {Object} candle - Current candle data
 */
function logIndicators(symbol, indicators, candle) {
    if (!config.detailedLogging) return;

    const timestamp = new Date().toISOString();
    const candleTime = candle ? new Date(candle.timestamp).toISOString() : 'N/A';
    
    // Format indicator values
    const rsi = indicators.rsi && indicators.rsi.length > 0 ? 
        indicators.rsi[indicators.rsi.length - 1].toFixed(2) : 'N/A';
    
    const rsiMa = indicators.rsiMa && indicators.rsiMa.length > 0 ? 
        indicators.rsiMa[indicators.rsiMa.length - 1].toFixed(2) : 'N/A';
    
    const wma50 = indicators.wma50 && indicators.wma50.length > 0 ? 
        indicators.wma50[indicators.wma50.length - 1].toFixed(2) : 'N/A';
    
    const atr = indicators.atr && indicators.atr.length > 0 ? 
        indicators.atr[indicators.atr.length - 1].toFixed(4) : 'N/A';
    
    const sma200 = indicators.sma200 && indicators.sma200.length > 0 ? 
        indicators.sma200[indicators.sma200.length - 1].toFixed(2) : 'N/A';
    
    // Create log entry
    const logEntry = `[${timestamp}] [${symbol}] [CANDLE: ${candleTime}] RSI: ${rsi}, RSI-MA: ${rsiMa}, WMA50: ${wma50}, ATR: ${atr}, SMA200: ${sma200}, Close: ${candle ? candle.close.toFixed(2) : 'N/A'}\n`;
    
    // Log to file
    if (config.logToFile) {
        indicatorLogStream.write(logEntry);
    }
    
    // Log to console
    if (config.logToConsole) {
        console.log(`INDICATORS [${symbol}]: RSI: ${rsi}, RSI-MA: ${rsiMa}, WMA50: ${wma50}, ATR: ${atr}`);
    }
    
    // Also log to main system log
    logger.logSystem(`${symbol} Indicators - RSI: ${rsi}, RSI-MA: ${rsiMa}, WMA50: ${wma50}, ATR: ${atr}`, 'info');
    
    return {
        timestamp,
        symbol,
        candleTime,
        rsi,
        rsiMa,
        wma50,
        atr,
        sma200,
        close: candle ? candle.close : null
    };
}

/**
 * Log trade decision details
 * @param {string} symbol - Trading symbol
 * @param {string} decision - Decision type (e.g., 'NO_TRADE', 'ENTRY', 'EXIT')
 * @param {string} reason - Reason for the decision
 * @param {Object} details - Additional details
 */
function logDecision(symbol, decision, reason, details = {}) {
    const timestamp = new Date().toISOString();
    
    // Format details
    const formattedDetails = Object.entries(details)
        .map(([key, value]) => `${key}: ${typeof value === 'number' ? value.toFixed(2) : value}`)
        .join(', ');
    
    // Create log entry
    const logEntry = `[${timestamp}] [${symbol}] [${decision}] ${reason} | ${formattedDetails}\n`;
    
    // Log to file
    if (config.logToFile) {
        decisionLogStream.write(logEntry);
    }
    
    // Log to console
    if (config.logToConsole) {
        console.log(`DECISION [${symbol}]: ${decision} - ${reason} | ${formattedDetails}`);
    }
    
    // Also log to main system log
    logger.logSystem(`${symbol} ${decision} - ${reason} | ${formattedDetails}`, 'info');
    
    return {
        timestamp,
        symbol,
        decision,
        reason,
        details
    };
}

/**
 * Log bot status
 * @param {string} status - Status message
 * @param {Object} details - Status details
 */
function logStatus(status, details = {}) {
    const timestamp = new Date().toISOString();
    
    // Format details
    const formattedDetails = Object.entries(details)
        .map(([key, value]) => `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
        .join(', ');
    
    // Create log entry
    const logEntry = `[${timestamp}] [STATUS] ${status} | ${formattedDetails}\n`;
    
    // Log to file
    if (config.logToFile) {
        statusLogStream.write(logEntry);
    }
    
    // Log to console
    if (config.logToConsole) {
        console.log(`STATUS: ${status} | ${formattedDetails}`);
    }
    
    // Also log to main system log
    logger.logSystem(`Bot Status: ${status} | ${formattedDetails}`, 'info');
    
    return {
        timestamp,
        status,
        details
    };
}

/**
 * Set logging configuration
 * @param {Object} newConfig - New configuration
 */
function setConfig(newConfig) {
    Object.assign(config, newConfig);
}

module.exports = {
    logIndicators,
    logDecision,
    logStatus,
    setConfig
};
