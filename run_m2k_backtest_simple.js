// run_m2k_backtest_simple.js - Simple script to run Micro Russell 2000 backtest

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('=== Running Micro Russell 2000 (M2K) Backtest ===');
console.log('Using config file: ./m2k_config.js');

// Create a modified version of backtest.js for the M2K backtest
const backtestContent = fs.readFileSync('backtest.js', 'utf8');

// Create a modified version that uses the M2K config
const modifiedContent = `
// Modified backtest for M2K
const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

// Import M2K config
const config = require('./m2k_config');

// Set input file path
config.inputFile = 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv';

// Set output directory
const MY_OUTPUT_DIR = './output/M2K_Backtest_Results';
if (!fs.existsSync(MY_OUTPUT_DIR)) {
  fs.mkdirSync(MY_OUTPUT_DIR, { recursive: true });
  console.log(\`Created output directory: \${MY_OUTPUT_DIR}\`);
} else {
  console.log(\`Output directory exists: \${MY_OUTPUT_DIR}\`);
}

${backtestContent.split('const config = require(\'./config\');')[1].replace('./output/MNQ_AdvancedML_2020_2025', 'MY_OUTPUT_DIR')}
`;

// Write the modified file
const modifiedBacktestFile = 'm2k_backtest_simple.js';
fs.writeFileSync(modifiedBacktestFile, modifiedContent);
console.log(`Created modified backtest file: ${modifiedBacktestFile}`);

// Run the backtest
console.log('Starting backtest...');
try {
  execSync(`node ${modifiedBacktestFile}`, { stdio: 'inherit' });
  console.log('Backtest completed successfully!');
} catch (error) {
  console.error('Error running backtest:', error.message);
}

console.log('=== Backtest Process Complete ===');
console.log('Results should be available in: ./output/M2K_Backtest_Results');
