// config.js - GOATED Optimized Configuration with 0 bar latency
// Using the best parameters from the grid test

module.exports = {
  // --- General Settings ---
  inputFile: 'C:/backtest-bot/input/MNQ_Databento_2025.csv', // MNQ (Micro NASDAQ) data from Databento Jan-May 2025
  initialBalance: 10000,

  // --- Instrument Specifics (MNQ Specs) ---
  pointValue: 2.00, // MNQ point value ($2 per point)
  tickSize: 0.25, // MNQ tick size (0.25 points)
  pricePrecision: 2,

  // --- Costs & Slippage ---
  commissionPerContract: 0.40, // Realistic commission costs
  slippagePoints: 0.75,       // Realistic slippage

  // --- Indicator Periods ---
  atrPeriod: 14, // Still calculated for compatibility but not used for entry
  rsiPeriod: 14, // RSI(14) - ENABLED
  rsiMaPeriod: 8, // SMA(8) smoothing - ENABLED
  wma200Period: 200, // 200-period WMA
  wma50Period: 50, // 50-period WMA

  // --- Strategy Parameters ---
  fixedTpPoints: 10, // MNQ take profit: 10 points = $20 per contract
  useWmaFilter: true,
  use200SmaFilter: true, // Enable 200 WMA + 50 WMA confluence filter (both are WMA)
  useTwoBarColorExit: false,
  minAtrEntry: 0, // REMOVED: No ATR requirement for entry
  minRsiMaSeparation: 0,
  disableTrailingStop: true, // Disable trailing stops completely

  // --- RSI Bands ---
  rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

  // --- Run Mode: FIXED POINTS GRID TEST CONFIGURATION ---
  isAdaptiveRun: false,
  useFixedPointsForSLTP: true, // Enable fixed points mode

  // Disable ATR-based factors
  slFactors: [0], // Not using ATR-based SL
  tpFactors: [0], // Not using ATR-based TP
  trailFactors: [0], // No trailing stops

  // MGC PROFIT TARGET: 3 points for gold
  fixedSlPointsGrid: [0], // NO STOP LOSS (hold until profitable)
  fixedTpPointsGrid: [3], // 3-point target ($30 profit) for MGC

  // PROFIT-BASED EXITS - Hold until profitable (like real trading)
  useImmediateExit: false, // Disable time-based exits
  immediateCandleExitGrid: [], // No fixed time exits
  maxTimeExitMinutes: 180, // Max hold time for risk management (3 hours to let trades breathe)

  // 0 bar latency
  latencyDelayBars: 0,

  // Keep these for compatibility
  costGrid: null,
  riskPercentGrid: null,
  fixedContractsGrid: null,

  // Fixed parameters
  riskPercent: 0,
  fixedContracts: 10,// Using 10 contracts for MNQ trading
  maxContracts: 10,

  // For compatibility with backtest.js
  currentRiskPercent: 0,
  currentFixedContracts: 10,

  // --- Time Filter Settings ---
  timeFilterEnabled: true, // ENABLED: Only trade during specified hours

  // Expanded Hours: 6 AM - 2 PM CST = 12:00-20:00 UTC (8 hours)
  // Covers pre-market, market open, and early afternoon
  allowedTradingHours: [12, 13, 14, 15, 16, 17, 18, 19, 20], // UTC hours when trading is allowed

  // --- Enhanced Features Configuration ---
  // All enhanced features disabled
  useAdaptiveSlippage: false,
  minSpreadPoints: 0.25,
  defaultSpreadPoints: 0.5,
  useVolatilityPositionSizing: false,
  useDynamicPositionSizing: false,
  basePositionSize: 3,
  maxPositionSize: 3,
  minPositionSize: 3,
  useMLEntryFilter: false,
  useAdvancedMLFilter: false,
  mlEntryThreshold: 0.5,
  useTimeAnalysis: false,
  timeScoreThreshold: 0.5,
  useCircuitBreakers: false,
  maxSlippageMultiplier: 5,
  pauseTradingOnExcessiveSlippage: false,
  useSteppedTrail: false,
  trailStepSizeMultiplier: 0.1,
  spreadBufferMultiplier: 2.0
};