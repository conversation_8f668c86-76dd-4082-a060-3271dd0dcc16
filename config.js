// config.js - GOATED Optimized Configuration with 0 bar latency
// Using the best parameters from the grid test

module.exports = {
  // --- General Settings ---
  inputFile: 'C:/backtest-bot/input/MNQ_lastyear_clean.csv', // Clean data without spread contamination
  initialBalance: 10000,

  // --- Instrument Specifics (MNQ Specs) ---
  pointValue: 2.00,
  tickSize: 0.25,
  pricePrecision: 2,

  // --- Costs & Slippage ---
  commissionPerContract: 0.40, // Realistic commission costs
  slippagePoints: 0.75,       // Realistic slippage

  // --- Indicator Periods ---
  atrPeriod: 14, // Still calculated for compatibility but not used for entry
  rsiPeriod: 14, // RSI(14) - ENABLED
  rsiMaPeriod: 8, // SMA(8) smoothing - ENABLED
  wma200Period: 200, // 200-period WMA
  wma50Period: 50, // 50-period WMA

  // --- Strategy Parameters ---
  fixedTpPoints: 40,
  useWmaFilter: true,
  use200SmaFilter: true, // Enable 200 WMA + 50 WMA confluence filter (both are WMA)
  useTwoBarColorExit: false,
  minAtrEntry: 0, // REMOVED: No ATR requirement for entry
  minRsiMaSeparation: 0,
  disableTrailingStop: true, // Disable trailing stops completely

  // --- RSI Bands ---
  rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

  // --- Run Mode: FIXED POINTS GRID TEST CONFIGURATION ---
  isAdaptiveRun: false,
  useFixedPointsForSLTP: true, // Enable fixed points mode

  // Disable ATR-based factors
  slFactors: [0], // Not using ATR-based SL
  tpFactors: [0], // Not using ATR-based TP
  trailFactors: [0], // No trailing stops

  // 1-CANDLE EXIT STRATEGY: Exit after exactly 1 candle
  fixedSlPointsGrid: [0], // NO STOP LOSS
  fixedTpPointsGrid: [0], // NO TAKE PROFIT

  // TESTING MULTIPLE EXIT STRATEGIES
  useImmediateExit: true, // Enable immediate candle exits
  immediateCandleExitGrid: [1, 2, 3], // Test 1, 2, and 3 candle exits
  maxTimeExitMinutes: 5, // Backup time limit

  // 0 bar latency
  latencyDelayBars: 0,

  // Keep these for compatibility
  costGrid: null,
  riskPercentGrid: null,
  fixedContractsGrid: null,

  // Fixed parameters
  riskPercent: 0,
  fixedContracts: 3,// Using 3 contracts for initial live trading
  maxContracts: 3,

  // For compatibility with backtest.js
  currentRiskPercent: 0,
  currentFixedContracts: 3,

  // --- Time Filter Settings ---
  timeFilterEnabled: true, // ENABLED: Only trade during profitable hours

  // Trading Hours (UTC) - Focus on early morning and NY open
  // Early Morning: 4-8 AM EST = 9-13 UTC
  // NY Open: 9-11 AM EST = 14-16 UTC
  allowedTradingHours: [9, 10, 11, 12, 13, 14, 15, 16], // UTC hours when trading is allowed

  // --- Enhanced Features Configuration ---
  // All enhanced features disabled
  useAdaptiveSlippage: false,
  minSpreadPoints: 0.25,
  defaultSpreadPoints: 0.5,
  useVolatilityPositionSizing: false,
  useDynamicPositionSizing: false,
  basePositionSize: 3,
  maxPositionSize: 3,
  minPositionSize: 3,
  useMLEntryFilter: false,
  useAdvancedMLFilter: false,
  mlEntryThreshold: 0.5,
  useTimeAnalysis: false,
  timeScoreThreshold: 0.5,
  useCircuitBreakers: false,
  maxSlippageMultiplier: 5,
  pauseTradingOnExcessiveSlippage: false,
  useSteppedTrail: false,
  trailStepSizeMultiplier: 0.1,
  spreadBufferMultiplier: 2.0
};