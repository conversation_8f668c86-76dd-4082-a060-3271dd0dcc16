/**
 * Run Simulation
 *
 * This script runs a simulation of the trading strategy in real-time mode.
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const marketDataService = require('./market-data-service');
const SimpleCache = require('./simple_cache');

// Configuration
const mnqConfig = require('./paper_trading_config');
const mgcConfig = require('./mgc_paper_trading_config');
const m2kConfig = require('./m2k_paper_trading_config');
const mesConfig = {
    ...mnqConfig,
    symbol: 'MES',
    pointValue: 5.00,
    slFactors: 3.0,
    tpFactors: 3.0,
    trailFactors: 0.01,
    fixedTpPoints: 0
};

// Parse command line arguments
const args = process.argv.slice(2);
const symbolArg = args.find(arg => arg.startsWith('--symbol='));
const startDateArg = args.find(arg => arg.startsWith('--start='));
const endDateArg = args.find(arg => arg.startsWith('--end='));
const positionSizeArg = args.find(arg => arg.startsWith('--size='));
const speedArg = args.find(arg => arg.startsWith('--speed='));

// Default values
const defaultSymbols = ['MNQ', 'MES', 'MGC', 'M2K'];
const defaultStartDate = new Date('2025-04-30T00:00:00Z');
const defaultEndDate = new Date('2025-05-01T23:59:59Z');
const defaultPositionSize = 2;
const defaultSpeed = 1000; // 1 second per candle

// Parse symbols
let symbols = defaultSymbols;
if (symbolArg) {
    const symbolValue = symbolArg.split('=')[1];
    if (symbolValue === 'all') {
        symbols = defaultSymbols;
    } else {
        symbols = symbolValue.split(',');
    }
}

// Parse dates
let startDate = defaultStartDate;
if (startDateArg) {
    const startDateValue = startDateArg.split('=')[1];
    startDate = new Date(startDateValue);
}

let endDate = defaultEndDate;
if (endDateArg) {
    const endDateValue = endDateArg.split('=')[1];
    endDate = new Date(endDateValue);
}

// Parse position size
let positionSize = defaultPositionSize;
if (positionSizeArg) {
    const positionSizeValue = positionSizeArg.split('=')[1];
    positionSize = parseInt(positionSizeValue, 10);
}

// Parse speed
let speed = defaultSpeed;
if (speedArg) {
    const speedValue = speedArg.split('=')[1];
    speed = parseInt(speedValue, 10);
}

// Print configuration
console.log('Simulation Configuration:');
console.log(`Symbols: ${symbols.join(', ')}`);
console.log(`Start Date: ${startDate.toISOString()}`);
console.log(`End Date: ${endDate.toISOString()}`);
console.log(`Position Size: ${positionSize} contracts`);
console.log(`Speed: ${speed}ms per candle`);

// Caches
const indicatorCache = new SimpleCache({
    maxSize: 100,
    ttl: 60000, // 1 minute
    debug: false
});

// Simulation class
class Simulator {
    constructor(options = {}) {
        this.options = {
            startDate,
            endDate,
            symbols,
            positionSize,
            speed,
            ...options
        };

        // State
        this.marketData = {};
        this.positions = {};
        this.trades = {};
        this.balance = 10000;
        this.equity = 10000;
        this.dailyStats = {};
        this.currentDate = null;
        this.dailyPnL = 0;
        this.dailyTrades = 0;
        this.dailyWins = 0;
        this.dailyLosses = 0;
        this.totalTrades = 0;
        this.totalWins = 0;
        this.totalLosses = 0;
        this.totalPnL = 0;
        this.maxDrawdown = 0;
        this.peakEquity = 10000;
        this.currentTime = null;
        this.isRunning = false;
        this.simulationInterval = null;

        // Initialize market data
        for (const symbol of this.options.symbols) {
            this.marketData[symbol] = {
                candles: [],
                currentCandle: null,
                indicators: {},
                config: symbol === 'MNQ' ? mnqConfig :
                       symbol === 'MES' ? mesConfig :
                       symbol === 'MGC' ? mgcConfig :
                       m2kConfig
            };

            this.trades[symbol] = [];
        }

        // Data handlers
        this.dataHandlers = new Map();

        console.log(`Simulator initialized with ${this.options.symbols.length} symbols`);
    }

    /**
     * Initialize the simulator
     */
    async initialize() {
        try {
            // Initialize market data service
            await marketDataService.initialize();
            console.log('Market data service initialized');

            // Load historical data for each symbol
            for (const symbol of this.options.symbols) {
                console.log(`Loading historical data for ${symbol}...`);

                const historicalData = await marketDataService.getHistoricalData(
                    symbol,
                    this.options.startDate,
                    this.options.endDate,
                    '1m'
                );

                console.log(`Loaded ${historicalData.length} candles for ${symbol}`);

                // Store candles
                this.marketData[symbol].candles = historicalData;

                // Subscribe to market data
                const dataHandler = (data) => {
                    this.processCandle(symbol, data);
                };

                this.dataHandlers.set(symbol, dataHandler);

                await marketDataService.subscribeToMarketData(symbol, dataHandler);
            }

            return true;
        } catch (error) {
            console.error(`Initialization error: ${error.message}`);
            return false;
        }
    }

    /**
     * Start the simulation
     */
    start() {
        if (this.isRunning) {
            console.log('Simulation is already running');
            return;
        }

        console.log('Starting simulation...');

        this.isRunning = true;
        this.currentTime = new Date(this.options.startDate);

        // Start simulation interval
        this.simulationInterval = setInterval(() => {
            this.step();
        }, this.options.speed);
    }

    /**
     * Stop the simulation
     */
    stop() {
        if (!this.isRunning) {
            console.log('Simulation is not running');
            return;
        }

        console.log('Stopping simulation...');

        clearInterval(this.simulationInterval);
        this.isRunning = false;

        // Print final statistics
        this.printStatistics();
    }

    /**
     * Step the simulation forward
     */
    step() {
        try {
            // Check if we've reached the end date
            if (this.currentTime >= this.options.endDate) {
                console.log('Simulation complete');
                this.stop();
                return;
            }

            // Simulate real-time data
            marketDataService.simulateRealTimeData(this.currentTime);

            // Increment time
            this.currentTime = new Date(this.currentTime.getTime() + 60000); // 1 minute

            // Check for new day
            const dateString = this.currentTime.toISOString().split('T')[0];
            if (this.currentDate !== dateString) {
                if (this.currentDate) {
                    // Save daily stats
                    this.dailyStats[this.currentDate] = {
                        pnl: this.dailyPnL,
                        trades: this.dailyTrades,
                        wins: this.dailyWins,
                        losses: this.dailyLosses,
                        winRate: this.dailyTrades > 0 ? (this.dailyWins / this.dailyTrades) * 100 : 0
                    };

                    console.log(`\nDay complete: ${this.currentDate}`);
                    console.log(`Daily P&L: $${this.dailyPnL.toFixed(2)}`);
                    console.log(`Daily Trades: ${this.dailyTrades}`);
                    console.log(`Daily Win Rate: ${this.dailyStats[this.currentDate].winRate.toFixed(2)}%`);
                }

                // Reset daily stats
                this.currentDate = dateString;
                this.dailyPnL = 0;
                this.dailyTrades = 0;
                this.dailyWins = 0;
                this.dailyLosses = 0;

                console.log(`\nProcessing new day: ${dateString}`);
            }
        } catch (error) {
            console.error(`Error in simulation step: ${error.message}`);
        }
    }

    /**
     * Process a candle
     * @param {string} symbol - Symbol
     * @param {Object} candle - Candle
     */
    processCandle(symbol, candle) {
        try {
            // Store current candle
            this.marketData[symbol].currentCandle = candle;

            // Calculate indicators
            this.calculateIndicators(symbol);

            // Check for signals
            this.checkForSignals(symbol, candle);

            // Update positions
            this.updatePositions(symbol, candle);

            // Update equity
            this.updateEquity();
        } catch (error) {
            console.error(`Error processing candle for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Calculate indicators for a symbol
     * @param {string} symbol - Symbol
     */
    calculateIndicators(symbol) {
        try {
            const candles = this.marketData[symbol].candles;
            const currentCandle = this.marketData[symbol].currentCandle;
            const config = this.marketData[symbol].config;

            if (!currentCandle) {
                return;
            }

            // Find candles up to the current time
            const validCandles = candles.filter(c => c.timestamp <= currentCandle.timestamp);

            // Need enough candles for calculations
            if (validCandles.length < Math.max(config.rsiPeriod, config.wma50Period, config.atrPeriod)) {
                return;
            }

            // Get the last N candles
            const lastCandles = validCandles.slice(-100);

            // Calculate RSI
            const rsiValues = this.calculateRSI(lastCandles, config.rsiPeriod);

            // Calculate RSI MA
            const rsiMaValues = this.calculateSMA(rsiValues, config.rsiMaPeriod);

            // Calculate WMA
            const wmaValues = this.calculateWMA(lastCandles, config.wma50Period);

            // Calculate ATR
            const atrValues = this.calculateATR(lastCandles, config.atrPeriod);

            // Store indicators
            this.marketData[symbol].indicators = {
                rsi: rsiValues.length > 0 ? rsiValues[rsiValues.length - 1] : null,
                rsiMa: rsiMaValues.length > 0 ? rsiMaValues[rsiMaValues.length - 1] : null,
                wma: wmaValues.length > 0 ? wmaValues[wmaValues.length - 1] : null,
                atr: atrValues.length > 0 ? atrValues[atrValues.length - 1] : null,
                atrVolatilityRegime: this.getVolatilityRegime(symbol, atrValues[atrValues.length - 1])
            };
        } catch (error) {
            console.error(`Error calculating indicators for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Calculate RSI
     * @param {Array} candles - Candles
     * @param {number} period - RSI period
     * @returns {Array} - RSI values
     */
    calculateRSI(candles, period) {
        try {
            if (candles.length < period + 1) {
                return [];
            }

            const cacheKey = `rsi_${period}_${candles[candles.length - 1].timestamp.getTime()}`;
            const cachedResult = indicatorCache.get(cacheKey);
            if (cachedResult) {
                return cachedResult;
            }

            const closes = candles.map(c => c.close);
            const gains = [];
            const losses = [];

            // First value is 0
            gains.push(0);
            losses.push(0);

            // Calculate gains and losses
            for (let i = 1; i < closes.length; i++) {
                const diff = closes[i] - closes[i - 1];
                gains.push(diff > 0 ? diff : 0);
                losses.push(diff < 0 ? Math.abs(diff) : 0);
            }

            // Calculate average gains and losses
            const avgGains = [];
            const avgLosses = [];

            // First average is simple average
            let gainSum = 0;
            let lossSum = 0;

            for (let i = 0; i < period; i++) {
                gainSum += gains[i];
                lossSum += losses[i];
            }

            avgGains.push(gainSum / period);
            avgLosses.push(lossSum / period);

            // Rest of averages are smoothed
            for (let i = period; i < closes.length; i++) {
                avgGains.push((avgGains[avgGains.length - 1] * (period - 1) + gains[i]) / period);
                avgLosses.push((avgLosses[avgLosses.length - 1] * (period - 1) + losses[i]) / period);
            }

            // Calculate RS and RSI
            const rsiValues = [];

            for (let i = 0; i < avgGains.length; i++) {
                const rs = avgLosses[i] === 0 ? 100 : avgGains[i] / avgLosses[i];
                const rsi = avgLosses[i] === 0 ? 100 : 100 - (100 / (1 + rs));
                rsiValues.push(rsi);
            }

            indicatorCache.set(cacheKey, rsiValues);
            return rsiValues;
        } catch (error) {
            console.error(`Error calculating RSI: ${error.message}`);
            return [];
        }
    }

    /**
     * Calculate SMA
     * @param {Array} values - Values
     * @param {number} period - SMA period
     * @returns {Array} - SMA values
     */
    calculateSMA(values, period) {
        try {
            if (values.length < period) {
                return [];
            }

            const cacheKey = `sma_${period}_${values.length}`;
            const cachedResult = indicatorCache.get(cacheKey);
            if (cachedResult) {
                return cachedResult;
            }

            const smaValues = [];

            for (let i = period - 1; i < values.length; i++) {
                let sum = 0;
                for (let j = 0; j < period; j++) {
                    sum += values[i - j];
                }
                smaValues.push(sum / period);
            }

            indicatorCache.set(cacheKey, smaValues);
            return smaValues;
        } catch (error) {
            console.error(`Error calculating SMA: ${error.message}`);
            return [];
        }
    }

    /**
     * Calculate WMA
     * @param {Array} candles - Candles
     * @param {number} period - WMA period
     * @returns {Array} - WMA values
     */
    calculateWMA(candles, period) {
        try {
            if (period === 0 || candles.length < period) {
                return [];
            }

            const cacheKey = `wma_${period}_${candles[candles.length - 1].timestamp.getTime()}`;
            const cachedResult = indicatorCache.get(cacheKey);
            if (cachedResult) {
                return cachedResult;
            }

            const closes = candles.map(c => c.close);
            const wmaValues = [];

            for (let i = period - 1; i < closes.length; i++) {
                let sum = 0;
                let weightSum = 0;

                for (let j = 0; j < period; j++) {
                    const weight = period - j;
                    sum += closes[i - j] * weight;
                    weightSum += weight;
                }

                wmaValues.push(sum / weightSum);
            }

            indicatorCache.set(cacheKey, wmaValues);
            return wmaValues;
        } catch (error) {
            console.error(`Error calculating WMA: ${error.message}`);
            return [];
        }
    }

    /**
     * Calculate ATR
     * @param {Array} candles - Candles
     * @param {number} period - ATR period
     * @returns {Array} - ATR values
     */
    calculateATR(candles, period) {
        try {
            if (candles.length < period + 1) {
                return [];
            }

            const cacheKey = `atr_${period}_${candles[candles.length - 1].timestamp.getTime()}`;
            const cachedResult = indicatorCache.get(cacheKey);
            if (cachedResult) {
                return cachedResult;
            }

            const trValues = [];

            // Calculate true ranges
            for (let i = 1; i < candles.length; i++) {
                const high = candles[i].high;
                const low = candles[i].low;
                const prevClose = candles[i - 1].close;

                const tr1 = high - low;
                const tr2 = Math.abs(high - prevClose);
                const tr3 = Math.abs(low - prevClose);

                trValues.push(Math.max(tr1, tr2, tr3));
            }

            // Calculate ATR
            const atrValues = [];

            // First ATR is simple average of TR
            let sum = 0;
            for (let i = 0; i < period; i++) {
                sum += trValues[i];
            }
            atrValues.push(sum / period);

            // Rest of ATRs are smoothed
            for (let i = period; i < trValues.length; i++) {
                atrValues.push((atrValues[atrValues.length - 1] * (period - 1) + trValues[i]) / period);
            }

            indicatorCache.set(cacheKey, atrValues);
            return atrValues;
        } catch (error) {
            console.error(`Error calculating ATR: ${error.message}`);
            return [];
        }
    }

    /**
     * Get volatility regime based on ATR
     * @param {string} symbol - Symbol
     * @param {number} atr - ATR value
     * @returns {string} - Volatility regime ('Low', 'Medium', or 'High')
     */
    getVolatilityRegime(symbol, atr) {
        if (!atr) return 'Medium';

        const config = this.marketData[symbol].config;
        const thresholds = config.atrThresholds || { low_medium: 1.5, medium_high: 3.0 };

        if (atr < thresholds.low_medium) {
            return 'Low';
        } else if (atr < thresholds.medium_high) {
            return 'Medium';
        } else {
            return 'High';
        }
    }

    /**
     * Check for trading signals
     * @param {string} symbol - Symbol
     * @param {Object} candle - Current candle
     */
    checkForSignals(symbol, candle) {
        try {
            const indicators = this.marketData[symbol].indicators;
            const config = this.marketData[symbol].config;

            // Need all indicators
            if (!indicators.rsi || !indicators.rsiMa || (config.useWmaFilter && !indicators.wma) || !indicators.atr) {
                return;
            }

            // Check if we already have a position
            const position = this.positions[symbol];
            if (position && position.size !== 0) {
                return;
            }

            // Get adaptive parameters based on volatility regime
            const adaptiveParams = this.getAdaptiveParams(symbol, indicators.atrVolatilityRegime);

            // Check for long signal
            if (this.isLongSignal(symbol, indicators, config)) {
                this.enterLong(symbol, candle, adaptiveParams);
            }
            // Check for short signal
            else if (this.isShortSignal(symbol, indicators, config)) {
                this.enterShort(symbol, candle, adaptiveParams);
            }
        } catch (error) {
            console.error(`Error checking for signals for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Check for long signal
     * @param {string} symbol - Symbol
     * @param {Object} indicators - Indicators
     * @param {Object} config - Configuration
     * @returns {boolean} - True if long signal
     */
    isLongSignal(symbol, indicators, config) {
        // RSI must be above RSI MA
        if (indicators.rsi <= indicators.rsiMa) {
            return false;
        }

        // RSI MA separation
        if (config.minRsiMaSeparation > 0 && (indicators.rsi - indicators.rsiMa) < config.minRsiMaSeparation) {
            return false;
        }

        // WMA filter
        if (config.useWmaFilter && indicators.wma && this.marketData[symbol].currentCandle.close <= indicators.wma) {
            return false;
        }

        // ATR filter
        if (config.minAtrEntry > 0 && indicators.atr < config.minAtrEntry) {
            return false;
        }

        return true;
    }

    /**
     * Check for short signal
     * @param {string} symbol - Symbol
     * @param {Object} indicators - Indicators
     * @param {Object} config - Configuration
     * @returns {boolean} - True if short signal
     */
    isShortSignal(symbol, indicators, config) {
        // RSI must be below RSI MA
        if (indicators.rsi >= indicators.rsiMa) {
            return false;
        }

        // RSI MA separation
        if (config.minRsiMaSeparation > 0 && (indicators.rsiMa - indicators.rsi) < config.minRsiMaSeparation) {
            return false;
        }

        // WMA filter
        if (config.useWmaFilter && indicators.wma && this.marketData[symbol].currentCandle.close >= indicators.wma) {
            return false;
        }

        // ATR filter
        if (config.minAtrEntry > 0 && indicators.atr < config.minAtrEntry) {
            return false;
        }

        return true;
    }

    /**
     * Get adaptive parameters based on volatility regime
     * @param {string} symbol - Symbol
     * @param {string} regime - Volatility regime
     * @returns {Object} - Adaptive parameters
     */
    getAdaptiveParams(symbol, regime) {
        const config = this.marketData[symbol].config;

        // If not in adaptive mode, use fixed parameters
        if (!config.isAdaptiveRun) {
            return {
                slFactor: config.slFactors,
                tpFactor: config.tpFactors,
                trailFactor: config.trailFactors
            };
        }

        // Default parameters for each regime
        const defaultParams = {
            Low: { slFactor: config.slFactors * 0.8, tpFactor: config.tpFactors * 0.8, trailFactor: config.trailFactors * 0.8 },
            Medium: { slFactor: config.slFactors, tpFactor: config.tpFactors, trailFactor: config.trailFactors },
            High: { slFactor: config.slFactors * 1.2, tpFactor: config.tpFactors * 1.2, trailFactor: config.trailFactors * 1.2 }
        };

        return defaultParams[regime] || defaultParams.Medium;
    }

    /**
     * Enter long position
     * @param {string} symbol - Symbol
     * @param {Object} candle - Current candle
     * @param {Object} adaptiveParams - Adaptive parameters
     */
    enterLong(symbol, candle, adaptiveParams) {
        try {
            const config = this.marketData[symbol].config;
            const indicators = this.marketData[symbol].indicators;

            // Calculate stop loss and take profit
            const atr = indicators.atr || 1.0;
            const slPoints = adaptiveParams.slFactor * atr;
            const tpPoints = adaptiveParams.tpFactor * atr;
            const trailPoints = adaptiveParams.trailFactor * atr;

            // Entry price
            const entryPrice = candle.close;

            // Stop loss and take profit
            const stopLoss = entryPrice - slPoints;
            const takeProfit = config.fixedTpPoints > 0 ? entryPrice + config.fixedTpPoints : entryPrice + tpPoints;

            // Position size
            const positionSize = config.fixedContracts || this.options.positionSize;

            // Create position
            this.positions[symbol] = {
                direction: 'long',
                size: positionSize,
                entryPrice: entryPrice,
                stopLoss: stopLoss,
                takeProfit: takeProfit,
                trailAmount: trailPoints,
                trailPrice: entryPrice - trailPoints,
                entryTime: candle.timestamp,
                atr: atr
            };

            console.log(`${candle.timestamp.toISOString()} - ${symbol} - LONG Entry at ${entryPrice.toFixed(2)}, SL: ${stopLoss.toFixed(2)}, TP: ${takeProfit.toFixed(2)}, Trail: ${trailPoints.toFixed(2)}`);
        } catch (error) {
            console.error(`Error entering long for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Enter short position
     * @param {string} symbol - Symbol
     * @param {Object} candle - Current candle
     * @param {Object} adaptiveParams - Adaptive parameters
     */
    enterShort(symbol, candle, adaptiveParams) {
        try {
            const config = this.marketData[symbol].config;
            const indicators = this.marketData[symbol].indicators;

            // Calculate stop loss and take profit
            const atr = indicators.atr || 1.0;
            const slPoints = adaptiveParams.slFactor * atr;
            const tpPoints = adaptiveParams.tpFactor * atr;
            const trailPoints = adaptiveParams.trailFactor * atr;

            // Entry price
            const entryPrice = candle.close;

            // Stop loss and take profit
            const stopLoss = entryPrice + slPoints;
            const takeProfit = config.fixedTpPoints > 0 ? entryPrice - config.fixedTpPoints : entryPrice - tpPoints;

            // Position size
            const positionSize = config.fixedContracts || this.options.positionSize;

            // Create position
            this.positions[symbol] = {
                direction: 'short',
                size: positionSize,
                entryPrice: entryPrice,
                stopLoss: stopLoss,
                takeProfit: takeProfit,
                trailAmount: trailPoints,
                trailPrice: entryPrice + trailPoints,
                entryTime: candle.timestamp,
                atr: atr
            };

            console.log(`${candle.timestamp.toISOString()} - ${symbol} - SHORT Entry at ${entryPrice.toFixed(2)}, SL: ${stopLoss.toFixed(2)}, TP: ${takeProfit.toFixed(2)}, Trail: ${trailPoints.toFixed(2)}`);
        } catch (error) {
            console.error(`Error entering short for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Update positions
     * @param {string} symbol - Symbol
     * @param {Object} candle - Current candle
     */
    updatePositions(symbol, candle) {
        try {
            const position = this.positions[symbol];
            if (!position || position.size === 0) {
                return;
            }

            let exitReason = null;
            let exitPrice = candle.close;

            // Long position
            if (position.direction === 'long') {
                // Update trail price
                if (candle.high > position.entryPrice + position.trailAmount) {
                    const newTrailPrice = candle.high - position.trailAmount;
                    if (newTrailPrice > position.trailPrice) {
                        position.trailPrice = newTrailPrice;
                    }
                }

                // Check stop loss
                if (candle.low <= position.stopLoss) {
                    exitReason = 'Stop Loss';
                    exitPrice = position.stopLoss;
                }
                // Check take profit
                else if (candle.high >= position.takeProfit) {
                    exitReason = 'Take Profit';
                    exitPrice = position.takeProfit;
                }
                // Check trailing stop
                else if (candle.low <= position.trailPrice && candle.high > position.entryPrice + position.trailAmount) {
                    exitReason = 'Trailing Stop';
                    exitPrice = position.trailPrice;
                }
            }
            // Short position
            else if (position.direction === 'short') {
                // Update trail price
                if (candle.low < position.entryPrice - position.trailAmount) {
                    const newTrailPrice = candle.low + position.trailAmount;
                    if (newTrailPrice < position.trailPrice) {
                        position.trailPrice = newTrailPrice;
                    }
                }

                // Check stop loss
                if (candle.high >= position.stopLoss) {
                    exitReason = 'Stop Loss';
                    exitPrice = position.stopLoss;
                }
                // Check take profit
                else if (candle.low <= position.takeProfit) {
                    exitReason = 'Take Profit';
                    exitPrice = position.takeProfit;
                }
                // Check trailing stop
                else if (candle.high >= position.trailPrice && candle.low < position.entryPrice - position.trailAmount) {
                    exitReason = 'Trailing Stop';
                    exitPrice = position.trailPrice;
                }
            }

            // Exit position if we have a reason
            if (exitReason) {
                this.exitPosition(symbol, exitPrice, candle.timestamp, exitReason);
            }
        } catch (error) {
            console.error(`Error updating position for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Exit position
     * @param {string} symbol - Symbol
     * @param {number} exitPrice - Exit price
     * @param {Date} exitTime - Exit time
     * @param {string} reason - Exit reason
     */
    exitPosition(symbol, exitPrice, exitTime, reason) {
        try {
            const position = this.positions[symbol];
            if (!position || position.size === 0) {
                return;
            }

            const config = this.marketData[symbol].config;

            // Calculate P&L
            let pnlPoints = 0;
            if (position.direction === 'long') {
                pnlPoints = exitPrice - position.entryPrice;
            } else if (position.direction === 'short') {
                pnlPoints = position.entryPrice - exitPrice;
            }

            const pointValue = config.pointValue || 1.0;
            const pnl = pnlPoints * position.size * pointValue;

            // Create trade record
            const trade = {
                symbol: symbol,
                direction: position.direction,
                entryPrice: position.entryPrice,
                exitPrice: exitPrice,
                entryTime: position.entryTime,
                exitTime: exitTime,
                size: position.size,
                pnl: pnl,
                pnlPoints: pnlPoints,
                reason: reason,
                atr: position.atr
            };

            // Add to trades list
            this.trades[symbol].push(trade);

            // Update statistics
            this.totalTrades++;
            this.totalPnL += pnl;
            this.balance += pnl;

            if (pnl > 0) {
                this.totalWins++;
                this.dailyWins++;
            } else if (pnl < 0) {
                this.totalLosses++;
                this.dailyLosses++;
            }

            this.dailyPnL += pnl;
            this.dailyTrades++;

            console.log(`${exitTime.toISOString()} - ${symbol} - ${position.direction} Exit at ${exitPrice.toFixed(2)}, Reason: ${reason}, PnL: $${pnl.toFixed(2)}, Points: ${pnlPoints.toFixed(2)}`);

            // Reset position
            delete this.positions[symbol];
        } catch (error) {
            console.error(`Error exiting position for ${symbol}: ${error.message}`);
        }
    }

    /**
     * Update equity
     */
    updateEquity() {
        // Calculate equity = balance + unrealized P&L
        let unrealizedPnL = 0;

        for (const symbol in this.positions) {
            const position = this.positions[symbol];
            if (position && position.size !== 0) {
                const candle = this.marketData[symbol].currentCandle;
                if (candle) {
                    const currentPrice = candle.close;
                    const pointValue = this.marketData[symbol].config.pointValue;

                    if (position.direction === 'long') {
                        unrealizedPnL += (currentPrice - position.entryPrice) * position.size * pointValue;
                    } else if (position.direction === 'short') {
                        unrealizedPnL += (position.entryPrice - currentPrice) * position.size * pointValue;
                    }
                }
            }
        }

        this.equity = this.balance + unrealizedPnL;

        // Check for max drawdown
        const drawdown = (this.peakEquity - this.equity) / this.peakEquity * 100;
        if (drawdown > this.maxDrawdown) {
            this.maxDrawdown = drawdown;
        }

        // Update peak equity
        if (this.equity > this.peakEquity) {
            this.peakEquity = this.equity;
        }
    }

    /**
     * Print statistics
     */
    printStatistics() {
        console.log('\nSimulation Results:');
        console.log('===================');

        console.log(`Initial Balance: $10000.00`);
        console.log(`Final Balance: $${this.balance.toFixed(2)}`);
        console.log(`Final Equity: $${this.equity.toFixed(2)}`);
        console.log(`Total P&L: $${this.totalPnL.toFixed(2)}`);
        console.log(`Return: ${((this.balance / 10000 - 1) * 100).toFixed(2)}%`);
        console.log(`Max Drawdown: ${this.maxDrawdown.toFixed(2)}%`);
        console.log(`Total Trades: ${this.totalTrades}`);
        console.log(`Win Rate: ${this.totalTrades > 0 ? ((this.totalWins / this.totalTrades) * 100).toFixed(2) : 0}%`);

        // Symbol-specific statistics
        for (const symbol of this.options.symbols) {
            const trades = this.trades[symbol];
            const wins = trades.filter(t => t.pnl > 0).length;
            const losses = trades.filter(t => t.pnl < 0).length;
            const pnl = trades.reduce((sum, t) => sum + t.pnl, 0);

            console.log(`\n${symbol} Statistics:`);
            console.log(`Total Trades: ${trades.length}`);
            console.log(`Win Rate: ${trades.length > 0 ? ((wins / trades.length) * 100).toFixed(2) : 0}%`);
            console.log(`P&L: $${pnl.toFixed(2)}`);
        }
    }

    /**
     * Clean up
     */
    async cleanup() {
        try {
            // Unsubscribe from market data
            for (const symbol of this.options.symbols) {
                await marketDataService.unsubscribeFromMarketData(symbol);
            }

            // Disconnect from market data service
            await marketDataService.disconnect();

            console.log('Cleanup complete');
        } catch (error) {
            console.error(`Cleanup error: ${error.message}`);
        }
    }
}

// Run simulation
async function runSimulation() {
    try {
        // Create simulator
        const simulator = new Simulator();

        // Initialize simulator
        console.log('\nInitializing simulator...');
        const initialized = await simulator.initialize();

        if (!initialized) {
            console.error('Failed to initialize simulator');
            return;
        }

        // Start simulation
        simulator.start();

        // Handle process exit
        process.on('SIGINT', async () => {
            console.log('\nReceived SIGINT. Cleaning up...');
            simulator.stop();
            await simulator.cleanup();
            process.exit(0);
        });
    } catch (error) {
        console.error('Error running simulation:', error);
    }
}

// Run the simulation
runSimulation().catch(console.error);
