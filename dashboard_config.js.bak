/**
 * Dashboard Configuration System
 *
 * This file serves as the central configuration for all dashboard components.
 * It manages data sources, styling, and shared parameters across all dashboards.
 */

// Global configuration object
const DASHBOARD_CONFIG = {
    // Theme configuration
    theme: {
        // Color scheme
        colors: {
            primary: '#00ccff',
            primaryDark: '#0099cc',
            primaryLight: '#66e0ff',
            primaryGlow: 'rgba(0, 204, 255, 0.5)',
            success: '#00ff88',
            successGlow: 'rgba(0, 255, 136, 0.5)',
            warning: '#ffcc00',
            warningGlow: 'rgba(255, 204, 0, 0.5)',
            danger: '#ff3366',
            dangerGlow: 'rgba(255, 51, 102, 0.5)',
            dark: '#f8fafc',
            light: '#0a0e17',
            gray: '#94a3b8',
            cardBg: '#141b2d',
            border: '#2a3a5a',
            textPrimary: '#f8fafc',
            textSecondary: '#94a3b8',
            bgMain: '#0a0e17',
            bgSidebar: '#141b2d',
            gold: '#FFD700'
        },

        // Font configuration
        fonts: {
            heading: "'Orbitron', sans-serif",
            body: "'Raj<PERSON><PERSON>', sans-serif"
        },

        // Chart configuration
        chart: {
            gridColor: 'rgba(42, 58, 90, 0.5)',
            tooltipBgColor: 'rgba(20, 27, 45, 0.9)',
            tooltipTextColor: '#f8fafc',
            tooltipBorderColor: '#2a3a5a'
        }
    },

    // Data sources configuration
    dataSources: {
        // Trading instruments
        instruments: {
            MNQ: {
                name: "Micro Nasdaq",
                dataFile: "input/MNQ2020_2025430.csv",
                color: "#00ccff",
                backtest: {
                    resultsFile: "dashboard_data/mnq_data.json",
                    tradesFile: "output/MNQ_Backtest/MNQ_trades.csv",
                    dailyPnlFile: "output/MNQ_Backtest/MNQ_daily_pnl.csv",
                    params: {
                        stopLossFactor: 4.5,
                        takeProfitFactor: 3.0,
                        trailFactor: 0.11,
                        fixedTpPoints: 40,
                        contracts: 10,
                        commission: 0.40,
                        slippage: 0.75,
                        adaptiveMode: true
                    },
                    metrics: {
                        totalPnL: 3337467.82,
                        winRate: 0.7884,
                        winDayRate: 0.9810,
                        maxDrawdown: 1470.43,
                        profitFactor: 31.24
                    }
                }
            },
            MGC: {
                name: "Micro Gold",
                dataFile: "input/MGC10years.csv",
                color: "#FFD700",
                backtest: {
                    resultsFile: "dashboard_data/mgc_data.json",
                    tradesFile: "output/MGC_Backtest/MGC_trades.csv",
                    dailyPnlFile: "output/MGC_Backtest/MGC_daily_pnl.csv",
                    params: {
                        stopLossFactor: 8.0,
                        takeProfitFactor: 7.0,
                        trailFactor: 0.02,
                        contracts: 10,
                        commission: 0.40,
                        slippage: 0.10,
                        adaptiveMode: true
                    },
                    metrics: {
                        totalPnL: 890734.73,
                        winRate: 0.7049,
                        winDayRate: 0.9320,
                        maxDrawdown: 994.70
                    }
                }
            },
            MES: {
                name: "Micro E-mini S&P 500",
                dataFile: "input/MES_2020-2025.csv",
                color: "#ff3366",
                backtest: {
                    resultsFile: "dashboard_data/mes_data.json",
                    tradesFile: "output/MES_Backtest/MES_trades.csv",
                    dailyPnlFile: "output/MES_Backtest/MES_daily_pnl.csv",
                    params: {
                        stopLossFactor: 3.0,
                        takeProfitFactor: 3.0,
                        trailFactor: 0.01,
                        contracts: 10,
                        commission: 0.40,
                        slippage: 0.25,
                        adaptiveMode: true
                    },
                    metrics: {
                        totalPnL: 1010762.70,
                        winRate: 0.7455,
                        winDayRate: 0.9470,
                        maxDrawdown: 1159.36,
                        profitFactor: 18.97
                    }
                }
            }
        },

        // Market data
        marketData: {
            volatilityIndex: "input/market_data/vix_data.csv",
            correlationData: "input/market_data/correlation_data.csv",
            economicCalendar: "input/market_data/economic_calendar.json",
            marketNews: "input/market_data/market_news.json"
        }
    },

    // Dashboard-specific configurations
    dashboards: {
        main: {
            title: "Trading Bot Command Center",
            description: "Comprehensive overview of trading performance",
            defaultInstrument: "MNQ",
            showAllInstruments: true,
            preserveStyle: true
        },
        performance: {
            title: "Performance Analytics",
            description: "Detailed performance metrics and analysis",
            metrics: ["winRate", "profitFactor", "sharpeRatio", "maxDrawdown", "averageTrade"],
            showAllInstruments: true,
            preserveStyle: true
        },
        correlation: {
            title: "Correlation Analysis",
            description: "Market correlation and regime analysis",
            correlationPeriod: 30, // days
            showAllInstruments: true,
            preserveStyle: true
        },
        monteCarlo: {
            title: "Monte Carlo Simulation",
            description: "Risk and performance projections",
            simulations: 1000,
            confidenceIntervals: [0.05, 0.5, 0.95],
            showAllInstruments: true,
            preserveStyle: true
        },
        alerts: {
            title: "Custom Alerts",
            description: "Trading alerts and notifications",
            defaultAlerts: ["entry", "exit", "drawdown", "performance"],
            showAllInstruments: true,
            preserveStyle: true
        },
        hedge: {
            title: "Quantum Capital Dashboard",
            description: "Advanced trading performance and analytics",
            defaultInstrument: "MNQ",
            gameMode: true,
            sections: ["overview", "performance", "trades", "settings"],
            showAllInstruments: true,
            preserveStyle: true
        },
        investor: {
            title: "Investor Reporting",
            description: "Performance metrics and reporting for investors",
            defaultInstrument: "MNQ",
            showAllInstruments: true,
            preserveStyle: true
        },
        market: {
            title: "Market Intelligence",
            description: "Market analysis and intelligence",
            defaultInstrument: "MNQ",
            showAllInstruments: true,
            preserveStyle: true
        }
    }
};

// Function to get theme CSS variables
function getThemeVariables() {
    const theme = DASHBOARD_CONFIG.theme;
    return `
        :root {
            --primary: ${theme.colors.primary};
            --primary-dark: ${theme.colors.primaryDark};
            --primary-light: ${theme.colors.primaryLight};
            --primary-glow: ${theme.colors.primaryGlow};
            --success: ${theme.colors.success};
            --success-glow: ${theme.colors.successGlow};
            --warning: ${theme.colors.warning};
            --warning-glow: ${theme.colors.warningGlow};
            --danger: ${theme.colors.danger};
            --danger-glow: ${theme.colors.dangerGlow};
            --dark: ${theme.colors.dark};
            --light: ${theme.colors.light};
            --gray: ${theme.colors.gray};
            --card-bg: ${theme.colors.cardBg};
            --border: ${theme.colors.border};
            --text-primary: ${theme.colors.textPrimary};
            --text-secondary: ${theme.colors.textSecondary};
            --bg-main: ${theme.colors.bgMain};
            --bg-sidebar: ${theme.colors.bgSidebar};
            --gold: ${theme.colors.gold};
        }
    `;
}

// Function to get instrument data
function getInstrumentData(instrumentCode) {
    return DASHBOARD_CONFIG.dataSources.instruments[instrumentCode] || null;
}

// Function to get all available instruments
function getAllInstruments() {
    return Object.keys(DASHBOARD_CONFIG.dataSources.instruments).map(code => {
        return {
            code: code,
            name: DASHBOARD_CONFIG.dataSources.instruments[code].name,
            color: DASHBOARD_CONFIG.dataSources.instruments[code].color
        };
    });
}

// Function to get dashboard configuration
function getDashboardConfig(dashboardName) {
    return DASHBOARD_CONFIG.dashboards[dashboardName] || null;
}

// Export configuration and utility functions
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DASHBOARD_CONFIG,
        getThemeVariables,
        getInstrumentData,
        getAllInstruments,
        getDashboardConfig
    };
}
