<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Overview</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>

    <!-- Load dashboard integration scripts -->
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <script src="dashboard_integration.js"></script>
    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
            --nasdaq: #6f42c1;
            --nasdaq-glow: rgba(111, 66, 193, 0.5);
            --sp500: #21ce99;
            --sp500-glow: rgba(33, 206, 153, 0.5);
            --gold: #FFD700;
            --gold-glow: rgba(255, 215, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        /* Improve readability */
        .menu-item-text, .stat-label, .card-title, .dashboard-title h1 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .dashboard-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
        }

        .dashboard-title h1 {
            margin-bottom: 0;
        }

        .portfolio-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .dashboard-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            min-width: 150px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-value.positive {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }

        .stat-value.negative {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0;
        }

        .chart-container {
            position: relative;
            height: 250px;
            margin-top: 1rem;
        }

        .dashboard-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .asset-row {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border);
            align-items: center;
        }

        .asset-row:last-child {
            border-bottom: none;
        }

        .asset-name {
            display: flex;
            align-items: center;
            font-weight: 600;
        }

        .asset-icon {
            margin-right: 0.5rem;
            font-size: 1.25rem;
        }

        .asset-value {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
        }

        .asset-value.mnq {
            color: var(--nasdaq);
            text-shadow: 0 0 5px var(--nasdaq-glow);
        }

        .asset-value.mes {
            color: var(--sp500);
            text-shadow: 0 0 5px var(--sp500-glow);
        }

        .asset-value.mgc {
            color: var(--gold);
            text-shadow: 0 0 5px var(--gold-glow);
        }

        .asset-value.total {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }

        .correlation-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .correlation-table th, .correlation-table td {
            padding: 0.75rem;
            text-align: center;
            border: 1px solid var(--border);
        }

        .correlation-table th {
            background-color: rgba(0, 204, 255, 0.1);
            color: var(--primary);
            font-weight: 600;
        }

        .correlation-table td {
            font-weight: 500;
        }

        .high-correlation {
            color: var(--danger);
        }

        .medium-correlation {
            color: var(--warning);
        }

        .low-correlation {
            color: var(--success);
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                flex-wrap: wrap;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="dashboard-title">
            <div class="portfolio-icon">💼</div>
            <h1>PORTFOLIO OVERVIEW</h1>
        </div>
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-label">Total P&L</div>
                <div class="stat-value positive">$5,238,965.25</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Avg Win Rate</div>
                <div class="stat-value">74.63%</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Avg Win Day Rate</div>
                <div class="stat-value positive">95.3%</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Portfolio Sharpe</div>
                <div class="stat-value positive">3.12</div>
            </div>
        </div>
    </div>

    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Combined Performance</h2>
            </div>
            <div class="chart-container">
                <canvas id="combinedPerformanceChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Asset Allocation</h2>
            </div>
            <div class="chart-container">
                <canvas id="assetAllocationChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Portfolio Composition</h2>
            </div>
            <div style="margin-top: 1rem;">
                <div class="asset-row">
                    <div class="asset-name">
                        <span class="asset-icon">🚀</span>
                        <span>MNQ (Nasdaq-100)</span>
                    </div>
                    <div class="asset-value mnq">$3,337,467.82 (63.7%)</div>
                </div>
                <div class="asset-row">
                    <div class="asset-name">
                        <span class="asset-icon">📈</span>
                        <span>MES (S&P 500)</span>
                    </div>
                    <div class="asset-value mes">$1,010,762.70 (19.3%)</div>
                </div>
                <div class="asset-row">
                    <div class="asset-name">
                        <span class="asset-icon">🥇</span>
                        <span>MGC (Gold)</span>
                    </div>
                    <div class="asset-value mgc">$890,734.73 (17.0%)</div>
                </div>
                <div class="asset-row">
                    <div class="asset-name">
                        <span class="asset-icon">💰</span>
                        <span>Total Portfolio</span>
                    </div>
                    <div class="asset-value total">$5,238,965.25 (100%)</div>
                </div>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Correlation Matrix</h2>
            </div>
            <table class="correlation-table">
                <thead>
                    <tr>
                        <th>Asset</th>
                        <th>MNQ</th>
                        <th>MES</th>
                        <th>MGC</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>MNQ</strong></td>
                        <td>1.00</td>
                        <td class="high-correlation">0.82</td>
                        <td class="low-correlation">0.21</td>
                    </tr>
                    <tr>
                        <td><strong>MES</strong></td>
                        <td class="high-correlation">0.82</td>
                        <td>1.00</td>
                        <td class="low-correlation">0.18</td>
                    </tr>
                    <tr>
                        <td><strong>MGC</strong></td>
                        <td class="low-correlation">0.21</td>
                        <td class="low-correlation">0.18</td>
                        <td>1.00</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="dashboard-footer">
        <p>QUANTUM CAPITAL | PORTFOLIO OVERVIEW DASHBOARD | LAST UPDATED: MAY 8, 2025</p>
        <p style="margin-top: 5px; font-weight: bold; color: var(--success);">MULTI-ASSET STRATEGY | 100% PROFITABLE WEEKS</p>
    </div>

    <script>
        // Combined Performance Chart
        const combinedPerformanceCtx = document.getElementById('combinedPerformanceChart').getContext('2d');
        const combinedPerformanceChart = new Chart(combinedPerformanceCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [
                    {
                        label: 'MNQ',
                        data: [250000, 500000, 800000, 1100000, 1400000, 1700000, 2000000, 2300000, 2600000, 2900000, 3100000, 3337468],
                        borderColor: '#6f42c1',
                        backgroundColor: 'rgba(111, 66, 193, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'MES',
                        data: [80000, 160000, 250000, 340000, 430000, 520000, 610000, 700000, 790000, 880000, 950000, 1010763],
                        borderColor: '#21ce99',
                        backgroundColor: 'rgba(33, 206, 153, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'MGC',
                        data: [50000, 120000, 210000, 290000, 380000, 450000, 520000, 590000, 670000, 750000, 820000, 890735],
                        borderColor: '#FFD700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'Total Portfolio',
                        data: [380000, 780000, 1260000, 1730000, 2210000, 2670000, 3130000, 3590000, 4060000, 4530000, 4870000, 5238966],
                        borderColor: '#00ff88',
                        backgroundColor: 'rgba(0, 255, 136, 0.1)',
                        borderWidth: 3,
                        pointRadius: 0,
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f8fafc',
                            font: {
                                family: 'Rajdhani'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    }
                }
            }
        });

        // Asset Allocation Chart
        const assetAllocationCtx = document.getElementById('assetAllocationChart').getContext('2d');
        const assetAllocationChart = new Chart(assetAllocationCtx, {
            type: 'doughnut',
            data: {
                labels: ['MNQ (Nasdaq-100)', 'MES (S&P 500)', 'MGC (Gold)'],
                datasets: [{
                    data: [63.7, 19.3, 17.0],
                    backgroundColor: [
                        '#6f42c1',
                        '#21ce99',
                        '#FFD700'
                    ],
                    borderColor: [
                        '#5a32a3',
                        '#1ba37a',
                        '#d4b700'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f8fafc',
                            font: {
                                family: 'Rajdhani'
                            }
                        }
                    }
                }
            }
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Portfolio Overview`;
            }

            // Update portfolio data
            updatePortfolioData(data);

            console.log('Portfolio overview dashboard updated with data for:', data.instrumentCode);
        }

        // Update portfolio data
        function updatePortfolioData(data) {
            // This would be implemented to update the portfolio data with actual data
            // For now, we'll just log that we would update the portfolio data
            console.log('Portfolio data would be updated with:', data);
        }
    </script>
</body>
</html>
