/**
 * Trading Bot Main Entry Point
 * 
 * This file ties together all components of the trading bot and starts the system.
 * It initializes the API connection, sets up the trading logic, and manages the overall flow.
 */

// Import required modules
const tradovateApi = require('./tradovate_api');
const tradingLogic = require('./trading_logic');
const orderManager = require('./order_manager');
const logger = require('./data_logger');
const alerts = require('./alert_system');

// Import configuration
const mnqConfig = require('./mnq_config');
const mesConfig = require('./mes_config');
const mgcConfig = require('./mgc_config');

// Configuration for the trading bot
const CONFIG = {
    // Environment
    env: 'demo', // 'demo' or 'live'
    
    // Symbols to trade
    symbols: ['MNQ', 'MES', 'MGC'],
    
    // Symbol configurations
    symbolConfigs: {
        MNQ: mnqConfig,
        MES: mesConfig,
        MGC: mgcConfig
    },
    
    // Risk management
    maxDailyLoss: 0.10, // 10% of account
    
    // Logging
    logLevel: 'info', // 'debug', 'info', 'warn', 'error'
    
    // Alert thresholds
    alertThresholds: {
        drawdown: {
            warning: 3,    // % of account
            critical: 5    // % of account
        },
        winRate: {
            warning: 65,   // % win rate
            critical: 60   // % win rate
        },
        profitTarget: 500  // Daily profit target in $
    }
};

// Global state
let isRunning = false;
let dailyStats = {
    startTime: null,
    trades: 0,
    wins: 0,
    losses: 0,
    grossProfit: 0,
    grossLoss: 0,
    startBalance: 0,
    currentBalance: 0,
    maxDrawdown: 0
};

/**
 * Initialize the trading bot
 */
async function initialize() {
    try {
        logger.logSystem('Initializing trading bot...', 'info');
        
        // Authenticate with Tradovate API
        logger.logSystem('Authenticating with Tradovate API...', 'info');
        const authResult = await tradovateApi.authenticate();
        
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }
        
        logger.logSystem('Authentication successful', 'info');
        
        // Initialize order manager
        orderManager.initialize({
            apiUrl: CONFIG.env === 'live' ? 'https://live.tradovateapi.com/v1' : 'https://demo.tradovateapi.com/v1',
            accessToken: authResult.accessToken,
            accountId: authResult.accountId
        });
        
        // Connect to WebSocket
        logger.logSystem('Connecting to WebSocket...', 'info');
        await tradovateApi.connectWebSocket();
        
        // Initialize trading logic for each symbol
        logger.logSystem('Initializing trading logic for each symbol...', 'info');
        for (const symbol of CONFIG.symbols) {
            tradingLogic.initializeMarket(symbol, CONFIG.symbolConfigs[symbol]);
            logger.logSystem(`Initialized trading logic for ${symbol}`, 'info');
        }
        
        // Configure alerts
        alerts.configureAlerts(CONFIG.alertThresholds);
        
        // Get account details
        const accountDetails = await tradovateApi.getAccountDetails();
        if (accountDetails) {
            dailyStats.startBalance = accountDetails.cashBalance;
            dailyStats.currentBalance = accountDetails.cashBalance;
            logger.logSystem(`Account balance: $${accountDetails.cashBalance.toFixed(2)}`, 'info');
        }
        
        // Subscribe to market data
        logger.logSystem('Subscribing to market data...', 'info');
        for (const symbol of CONFIG.symbols) {
            // Get the current contract month (e.g., M4 for June 2024)
            const contractMonth = getContractMonth();
            await tradovateApi.subscribeToChart(`${symbol}${contractMonth}`, '1m');
            logger.logSystem(`Subscribed to 1-minute chart for ${symbol}${contractMonth}`, 'info');
        }
        
        // Set up daily reset
        setupDailyReset();
        
        // Mark as running
        isRunning = true;
        dailyStats.startTime = new Date();
        
        logger.logSystem('Trading bot initialized successfully', 'info');
        alerts.alertSystemStatus('Trading bot started successfully', 'info');
        
        return true;
    } catch (error) {
        logger.logSystem(`Initialization error: ${error.message}`, 'error');
        alerts.alertSystemStatus(`Initialization error: ${error.message}`, 'critical');
        return false;
    }
}

/**
 * Get the current contract month code
 * @returns {string} - Contract month code (e.g., 'M4' for June 2024)
 */
function getContractMonth() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-1); // Last digit of year
    
    // Month codes: F=Jan, G=Feb, H=Mar, J=Apr, K=May, M=Jun, N=Jul, Q=Aug, U=Sep, V=Oct, X=Nov, Z=Dec
    const monthCodes = ['F', 'G', 'H', 'J', 'K', 'M', 'N', 'Q', 'U', 'V', 'X', 'Z'];
    const month = monthCodes[now.getMonth()];
    
    return `${month}${year}`;
}

/**
 * Set up daily reset for statistics and risk management
 */
function setupDailyReset() {
    // Reset daily stats at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const timeUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
        resetDailyStats();
        setupDailyReset(); // Set up for the next day
    }, timeUntilMidnight);
    
    logger.logSystem(`Daily reset scheduled for ${tomorrow.toLocaleString()}`, 'info');
}

/**
 * Reset daily statistics
 */
function resetDailyStats() {
    // Log the previous day's summary
    const summary = {
        date: new Date().toISOString().split('T')[0],
        totalTrades: dailyStats.trades,
        winningTrades: dailyStats.wins,
        losingTrades: dailyStats.losses,
        winRate: dailyStats.trades > 0 ? (dailyStats.wins / dailyStats.trades) * 100 : 0,
        grossProfit: dailyStats.grossProfit,
        grossLoss: dailyStats.grossLoss,
        netPnl: dailyStats.grossProfit - dailyStats.grossLoss,
        profitFactor: dailyStats.grossLoss > 0 ? dailyStats.grossProfit / dailyStats.grossLoss : dailyStats.grossProfit > 0 ? Infinity : 0,
        maxDrawdown: dailyStats.maxDrawdown,
        avgTradeTime: 0, // Not tracked yet
        avgWinAmount: dailyStats.wins > 0 ? dailyStats.grossProfit / dailyStats.wins : 0,
        avgLossAmount: dailyStats.losses > 0 ? dailyStats.grossLoss / dailyStats.losses : 0,
        largestWin: 0, // Not tracked yet
        largestLoss: 0, // Not tracked yet
        totalCommission: 0, // Not tracked yet
        totalSlippage: 0, // Not tracked yet
        startEquity: dailyStats.startBalance,
        endEquity: dailyStats.currentBalance
    };
    
    logger.logDailySummary(summary);
    
    // Reset stats
    dailyStats = {
        startTime: new Date(),
        trades: 0,
        wins: 0,
        losses: 0,
        grossProfit: 0,
        grossLoss: 0,
        startBalance: dailyStats.currentBalance,
        currentBalance: dailyStats.currentBalance,
        maxDrawdown: 0
    };
    
    logger.logSystem('Daily statistics reset', 'info');
    alerts.alertSystemStatus('Daily statistics reset', 'info');
}

/**
 * Shutdown the trading bot
 */
async function shutdown() {
    try {
        logger.logSystem('Shutting down trading bot...', 'info');
        
        // Close all open positions
        for (const symbol of CONFIG.symbols) {
            const market = tradingLogic.marketData[symbol];
            if (market && market.positions && market.positions.length > 0) {
                logger.logSystem(`Closing ${market.positions.length} open positions for ${symbol}...`, 'info');
                // Implementation of position closing would go here
            }
        }
        
        // Disconnect WebSocket
        tradovateApi.disconnectWebSocket();
        
        // Log final stats
        resetDailyStats();
        
        // Mark as not running
        isRunning = false;
        
        logger.logSystem('Trading bot shutdown complete', 'info');
        alerts.alertSystemStatus('Trading bot shutdown complete', 'info');
        
        return true;
    } catch (error) {
        logger.logSystem(`Shutdown error: ${error.message}`, 'error');
        alerts.alertSystemStatus(`Shutdown error: ${error.message}`, 'critical');
        return false;
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    logger.logSystem('Received SIGINT signal', 'info');
    await shutdown();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    logger.logSystem('Received SIGTERM signal', 'info');
    await shutdown();
    process.exit(0);
});

// Start the trading bot
if (require.main === module) {
    initialize().then(success => {
        if (!success) {
            logger.logSystem('Failed to initialize trading bot', 'error');
            process.exit(1);
        }
    }).catch(error => {
        logger.logSystem(`Unhandled error during initialization: ${error.message}`, 'critical');
        process.exit(1);
    });
}

// Export functions for testing
module.exports = {
    initialize,
    shutdown,
    isRunning: () => isRunning,
    getDailyStats: () => ({ ...dailyStats })
};
