// Backtest script for relaxed pattern detection logic
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
    // Input data directory
    inputDir: path.join(__dirname, 'input'),

    // Output directory for results
    outputDir: path.join(__dirname, 'backtest-results'),

    // Symbols to backtest
    symbols: ['MNQ'], // Only testing MNQ for now

    // Position size (contracts)
    positionSize: 10,

    // Initial account balance
    initialBalance: 5000,

    // Commission per contract
    commissionPerContract: 2.10,

    // Risk management
    dailyStopLoss: 250, // $250 daily stop loss

    // Pattern detection settings
    useRelaxedPatterns: true,

    // Trading hours (in UTC)
    tradingHours: {
        start: 13, // 8:00 AM CST = 13:00 UTC
        end: 21,   // 4:00 PM CST = 21:00 UTC
    },

    // Indicator settings
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wmaPeriod: 50,
    atrPeriod: 14,

    // Entry filters
    useWmaFilter: true,
    minAtrEntry: 0,
    minRsiMaSeparation: 0,
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
    fs.mkdirSync(config.outputDir, { recursive: true });
}

// Utility functions
function calculateRSI(candles, period = 14) {
    if (candles.length < period + 1) {
        return NaN;
    }

    let gains = 0;
    let losses = 0;

    // First RSI calculation
    for (let i = 1; i <= period; i++) {
        const change = candles[i].close - candles[i - 1].close;
        if (change >= 0) {
            gains += change;
        } else {
            losses -= change;
        }
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    // Calculate RSI using Wilder's smoothing method
    for (let i = period + 1; i < candles.length; i++) {
        const change = candles[i].close - candles[i - 1].close;

        if (change >= 0) {
            avgGain = (avgGain * (period - 1) + change) / period;
            avgLoss = (avgLoss * (period - 1)) / period;
        } else {
            avgGain = (avgGain * (period - 1)) / period;
            avgLoss = (avgLoss * (period - 1) - change) / period;
        }
    }

    if (avgLoss === 0) {
        return 100;
    }

    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

function calculateSMA(values, period) {
    if (values.length < period) {
        return NaN;
    }

    let sum = 0;
    for (let i = 0; i < period; i++) {
        sum += values[values.length - 1 - i];
    }

    return sum / period;
}

function calculateWMA(candles, period = 50) {
    if (candles.length < period) {
        return NaN;
    }

    let sum = 0;
    let weightSum = 0;

    for (let i = 0; i < period; i++) {
        const weight = period - i;
        sum += candles[candles.length - 1 - i].close * weight;
        weightSum += weight;
    }

    return sum / weightSum;
}

function calculateATR(candles, period = 14) {
    if (candles.length < period + 1) {
        return NaN;
    }

    let trSum = 0;

    // Calculate first TR
    for (let i = 1; i <= period; i++) {
        const high = candles[i].high;
        const low = candles[i].low;
        const prevClose = candles[i - 1].close;

        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);

        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
    }

    // Initial ATR
    let atr = trSum / period;

    // Calculate ATR using Wilder's smoothing method
    for (let i = period + 1; i < candles.length; i++) {
        const high = candles[i].high;
        const low = candles[i].low;
        const prevClose = candles[i - 1].close;

        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);

        const tr = Math.max(tr1, tr2, tr3);

        atr = ((atr * (period - 1)) + tr) / period;
    }

    return atr;
}

function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
        return 'invalid';
    }

    if (Math.abs(candle.open - candle.close) < 0.0001) {
        return 'doji';
    }

    return candle.close > candle.open ? 'green' : 'red';
}

// Pattern detection functions
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: green-red-green
    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        if (config.useRelaxedPatterns) {
            // Check for engulfing pattern with wick (favorite pattern)
            const engulf = c3.close > c2.open && c3.open < c2.close;
            const wick3 = Math.min(c3.open, c3.close) - c3.low;
            const wick2 = Math.min(c2.open, c2.close) - c2.low;

            // Check for simple pattern where c3 just closes above c2's OPEN (not close)
            const simpleClose = c3.close > c2.open;

            // Accept either condition
            if (engulf && wick3 > wick2) {
                return 'bullish';
            } else if (simpleClose) {
                return 'bullish';
            }
        } else {
            // Original strict condition
            const engulf = c3.close > c2.open && c3.open < c2.close;
            const wick3 = Math.min(c3.open, c3.close) - c3.low;
            const wick2 = Math.min(c2.open, c2.close) - c2.low;

            if (engulf && wick3 > wick2) {
                return 'bullish';
            }
        }
    }

    // Bearish pattern: red-green-red
    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        if (config.useRelaxedPatterns) {
            // Check for engulfing pattern with wick (favorite pattern)
            const engulf = c3.close < c2.open && c3.open > c2.close;
            const wick3 = c3.high - Math.max(c3.open, c3.close);
            const wick2 = c2.high - Math.max(c2.open, c2.close);

            // Check for simple pattern where c3 just closes below c2's OPEN (not close)
            const simpleClose = c3.close < c2.open;

            // Accept either condition
            if (engulf && wick3 > wick2) {
                return 'bearish';
            } else if (simpleClose) {
                return 'bearish';
            }
        } else {
            // Original strict condition
            const engulf = c3.close < c2.open && c3.open > c2.close;
            const wick3 = c3.high - Math.max(c3.open, c3.close);
            const wick2 = c2.high - Math.max(c2.open, c2.close);

            if (engulf && wick3 > wick2) {
                return 'bearish';
            }
        }
    }

    // Also check for doji patterns as continuation patterns
    if (col2 === 'doji') {
        if (col1 === 'green' && col3 === 'green') {
            return 'bullish';
        }
        if (col1 === 'red' && col3 === 'red') {
            return 'bearish';
        }
    }

    return null;
}

function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    // Bullish pattern: green-red-red-green (STRICT version - exactly as in backtest)
    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c2.open)) {
            return 'bullish';
        }
    }

    // Bearish pattern: red-green-green-red (STRICT version - exactly as in backtest)
    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c2.open)) {
            return 'bearish';
        }
    }

    return null;
}

// Entry condition check
function entryOK(dir, patternType, c3, currentIndex, candles) {
    // Basic validation
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
        return false;
    }

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // RSI filter for three-candle patterns
    if (patternType === 'three' && ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa))) {
        return false;
    }

    // Minimum ATR filter
    if (config.minAtrEntry && c3.atr < config.minAtrEntry) {
        return false;
    }

    // RSI-MA separation filter
    if (config.minRsiMaSeparation && Math.abs(c3.rsi - c3.rsiMa) < config.minRsiMaSeparation) {
        return false;
    }

    return true;
}

// Main backtest function
async function runBacktest(symbol) {
    console.log(`Starting backtest for ${symbol}...`);

    // Load historical data
    const dataFile = path.join(config.inputDir, `${symbol}_Databento_2025.csv`);
    if (!fs.existsSync(dataFile)) {
        console.error(`Data file not found: ${dataFile}`);
        return null;
    }
    console.log(`Using data file: ${dataFile}`);

    const data = fs.readFileSync(dataFile, 'utf8');
    const lines = data.split('\n');

    // Parse CSV data
    const candles = [];

    // Check the first line to determine the CSV format and separator
    const headerLine = lines[0].trim();
    const separator = headerLine.includes(';') ? ';' : ',';
    const isDatabentoFormat = headerLine.includes('ts_event') || headerLine.includes('timestamp');
    const isTimeFormat = headerLine.includes('Time') || headerLine.includes('time');

    console.log(`Detected CSV format: ${isDatabentoFormat ? 'Databento' : isTimeFormat ? 'Time-based' : 'Standard'}`);
    console.log(`Using separator: "${separator}"`);

    for (let i = 1; i < lines.length; i++) { // Skip header
        const line = lines[i].trim();
        if (!line) continue;

        let timestamp, open, high, low, close, volume;

        if (isDatabentoFormat) {
            // Databento format
            const parts = line.split(separator);

            // Check if this is the OHLCV format
            if (parts.length >= 6) {
                // Assuming format: timestamp,open,high,low,close,volume
                timestamp = new Date(parseInt(parts[0]));
                open = parseFloat(parts[1]);
                high = parseFloat(parts[2]);
                low = parseFloat(parts[3]);
                close = parseFloat(parts[4]);
                volume = parseInt(parts[5], 10);
            } else {
                // Skip invalid lines
                continue;
            }
        } else if (isTimeFormat) {
            // Time-based format (like your MNQ_Databento_2025.csv)
            const parts = line.split(separator);

            if (parts.length >= 6) {
                timestamp = new Date(parts[0]); // ISO timestamp format
                open = parseFloat(parts[1]);
                high = parseFloat(parts[2]);
                low = parseFloat(parts[3]);
                close = parseFloat(parts[4]);
                volume = parseInt(parts[5], 10);
            } else {
                // Skip invalid lines
                continue;
            }
        } else {
            // Standard format
            const parts = line.split(separator);
            if (parts.length >= 6) {
                const [dateStr, timeStr, o, h, l, c, v] = parts;
                timestamp = new Date(`${dateStr} ${timeStr}`);
                open = parseFloat(o);
                high = parseFloat(h);
                low = parseFloat(l);
                close = parseFloat(c);
                volume = parseInt(v, 10);
            } else {
                // Skip invalid lines
                continue;
            }
        }

        // Validate the data
        if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            continue;
        }

        candles.push({
            timestamp: timestamp.getTime(),
            open,
            high,
            low,
            close,
            volume: isNaN(volume) ? 0 : volume
        });
    }

    console.log(`Loaded ${candles.length} candles for ${symbol}`);

    // Calculate indicators
    console.log(`Calculating indicators for ${candles.length} candles...`);

    // Pre-calculate some values to improve performance
    const minRequiredCandles = Math.max(config.rsiPeriod, config.wmaPeriod, config.atrPeriod);
    console.log(`Minimum required candles for indicators: ${minRequiredCandles}`);

    // Process in batches to show progress
    const indicatorBatchSize = 5000;
    const indicatorTotalBatches = Math.ceil(candles.length / indicatorBatchSize);

    for (let batch = 0; batch < indicatorTotalBatches; batch++) {
        const startIdx = batch * indicatorBatchSize;
        const endIdx = Math.min((batch + 1) * indicatorBatchSize, candles.length);

        console.log(`Processing batch ${batch + 1}/${indicatorTotalBatches} (candles ${startIdx} to ${endIdx})...`);

        for (let i = startIdx; i < endIdx; i++) {
            // We need enough previous candles to calculate indicators
            if (i < minRequiredCandles) {
                continue;
            }

            // Calculate RSI
            const rsiStartIdx = Math.max(0, i - config.rsiPeriod * 2); // Only use necessary candles
            const rsiSubset = candles.slice(rsiStartIdx, i + 1);
            candles[i].rsi = calculateRSI(rsiSubset, config.rsiPeriod);

            // Calculate RSI-MA
            let rsiValues = [];
            for (let j = 0; j < config.rsiMaPeriod; j++) {
                if (i - j >= minRequiredCandles) {
                    const rsiSubsetForMA = candles.slice(Math.max(0, i - j - config.rsiPeriod * 2), i - j + 1);
                    const rsiValue = calculateRSI(rsiSubsetForMA, config.rsiPeriod);
                    if (!isNaN(rsiValue)) {
                        rsiValues.push(rsiValue);
                    }
                }
            }
            candles[i].rsiMa = calculateSMA(rsiValues, rsiValues.length);

            // Calculate WMA
            const wmaStartIdx = Math.max(0, i - config.wmaPeriod * 2); // Only use necessary candles
            const wmaSubset = candles.slice(wmaStartIdx, i + 1);
            candles[i].wma50 = calculateWMA(wmaSubset, config.wmaPeriod);

            // Calculate ATR
            const atrStartIdx = Math.max(0, i - config.atrPeriod * 2); // Only use necessary candles
            const atrSubset = candles.slice(atrStartIdx, i + 1);
            candles[i].atr = calculateATR(atrSubset, config.atrPeriod);
        }
    }

    console.log(`Finished calculating indicators.`);

    // Run backtest
    console.log(`Running backtest...`);
    let balance = config.initialBalance;
    let trades = [];
    let dailyResults = {};
    let dailyBalance = config.initialBalance;
    let dailyPnL = 0;
    let currentDay = '';

    // Process in batches to show progress
    const backtestBatchSize = 10000;
    const backtestTotalBatches = Math.ceil(candles.length / backtestBatchSize);

    for (let batch = 0; batch < backtestTotalBatches; batch++) {
        const startIdx = Math.max(3, batch * backtestBatchSize); // Need at least 4 candles
        const endIdx = Math.min((batch + 1) * backtestBatchSize, candles.length);

        console.log(`Processing backtest batch ${batch + 1}/${backtestTotalBatches} (candles ${startIdx} to ${endIdx})...`);

        for (let i = startIdx; i < endIdx; i++) {
            const c0 = candles[i - 3];
            const c1 = candles[i - 2];
            const c2 = candles[i - 1];
            const c3 = candles[i];

            // Check if we're in a new day
            const candleDate = new Date(c3.timestamp).toISOString().split('T')[0];
            if (candleDate !== currentDay) {
                // Save previous day's results
                if (currentDay) {
                    dailyResults[currentDay] = {
                        startBalance: dailyBalance,
                        endBalance: balance,
                        pnl: dailyPnL,
                        trades: trades.filter(t => new Date(t.entryTime).toISOString().split('T')[0] === currentDay)
                    };
                }

                // Reset daily tracking
                dailyBalance = balance;
                dailyPnL = 0;
                currentDay = candleDate;
        }

            // Check trading hours
            const hour = new Date(c3.timestamp).getUTCHours();
            if (hour < config.tradingHours.start || hour >= config.tradingHours.end) {
                continue;
            }

            // Check daily stop loss
            if (dailyPnL <= -config.dailyStopLoss) {
                continue;
            }

            // Check for pattern
            let pattern = detect4(c0, c1, c2, c3);
            let patternType = 'four';

            if (!pattern) {
                pattern = detect3(c1, c2, c3);
                patternType = 'three';
            }

            if (pattern) {
                // Check entry conditions
                if (entryOK(pattern, patternType, c3, i, candles)) {
                    // Calculate position size
                    const posSize = config.positionSize;

                    // Calculate stop loss and take profit levels
                    let slPoints, tpPoints;

                    if (symbol === 'MNQ') {
                        slPoints = 4.5;
                        tpPoints = 3.0;
                    } else if (symbol === 'MGC') {
                        slPoints = 8.0;
                        tpPoints = 7.0;
                    } else if (symbol === 'MES') {
                        slPoints = 3.0;
                        tpPoints = 3.0;
                    } else if (symbol === 'M2K') {
                        slPoints = 3.0;
                        tpPoints = 3.0;
                    }

                    const slPrice = pattern === 'bullish' ? c3.close - slPoints : c3.close + slPoints;
                    const tpPrice = pattern === 'bullish' ? c3.close + tpPoints : c3.close - tpPoints;

                    // Simulate the trade
                    let exitPrice = null;
                    let exitTime = null;
                    let exitReason = null;

                    // Look ahead to find exit
                    for (let j = i + 1; j < candles.length && j < i + 100; j++) {
                        const futureCandle = candles[j];

                        // Check for stop loss
                        if ((pattern === 'bullish' && futureCandle.low <= slPrice) ||
                            (pattern === 'bearish' && futureCandle.high >= slPrice)) {
                            exitPrice = slPrice;
                            exitTime = futureCandle.timestamp;
                            exitReason = 'sl';
                            break;
                        }

                        // Check for take profit
                        if ((pattern === 'bullish' && futureCandle.high >= tpPrice) ||
                            (pattern === 'bearish' && futureCandle.low <= tpPrice)) {
                            exitPrice = tpPrice;
                            exitTime = futureCandle.timestamp;
                            exitReason = 'tp';
                            break;
                        }
                    }

                    // If no exit found, use the last candle
                    if (!exitPrice) {
                        exitPrice = candles[Math.min(i + 100, candles.length - 1)].close;
                        exitTime = candles[Math.min(i + 100, candles.length - 1)].timestamp;
                        exitReason = 'timeout';
                    }

                    // Calculate P&L
                    const pointValue = symbol === 'MGC' ? 10 : 5; // $10 per point for MGC, $5 for others
                    const points = pattern === 'bullish' ? exitPrice - c3.close : c3.close - exitPrice;
                    const pnl = points * pointValue * posSize - (config.commissionPerContract * posSize * 2);

                    // Update balance
                    balance += pnl;
                    dailyPnL += pnl;

                    // Record trade
                    trades.push({
                        symbol,
                        direction: pattern,
                        patternType,
                        entryPrice: c3.close,
                        entryTime: c3.timestamp,
                        exitPrice,
                        exitTime,
                        exitReason,
                        pnl,
                        balance
                    });
                }
            }
        }
    }

    // Save final day's results
    if (currentDay) {
        dailyResults[currentDay] = {
            startBalance: dailyBalance,
            endBalance: balance,
            pnl: dailyPnL,
            trades: trades.filter(t => new Date(t.entryTime).toISOString().split('T')[0] === currentDay)
        };
    }

    // Calculate statistics
    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl <= 0);

    const stats = {
        symbol,
        initialBalance: config.initialBalance,
        finalBalance: balance,
        totalPnL: balance - config.initialBalance,
        totalTrades: trades.length,
        winningTrades: winningTrades.length,
        losingTrades: losingTrades.length,
        winRate: trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0,
        averageWin: winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0,
        averageLoss: losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0,
        profitFactor: losingTrades.length > 0 ?
            Math.abs(winningTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.reduce((sum, t) => sum + t.pnl, 0)) : 0,
        maxDrawdown: calculateMaxDrawdown(trades),
        dailyResults: Object.keys(dailyResults).map(date => ({
            date,
            pnl: dailyResults[date].pnl,
            trades: dailyResults[date].trades.length,
            winRate: dailyResults[date].trades.length > 0 ?
                (dailyResults[date].trades.filter(t => t.pnl > 0).length / dailyResults[date].trades.length) * 100 : 0
        }))
    };

    // Save results
    const resultsFile = path.join(config.outputDir, `${symbol}_results_${config.useRelaxedPatterns ? 'relaxed' : 'strict'}.json`);
    fs.writeFileSync(resultsFile, JSON.stringify({
        config,
        stats,
        trades
    }, null, 2));

    console.log(`Backtest completed for ${symbol}. Results saved to ${resultsFile}`);
    return stats;
}

// Calculate maximum drawdown
function calculateMaxDrawdown(trades) {
    if (trades.length === 0) return 0;

    let peak = trades[0].balance;
    let maxDrawdown = 0;

    for (const trade of trades) {
        if (trade.balance > peak) {
            peak = trade.balance;
        }

        const drawdown = peak - trade.balance;
        if (drawdown > maxDrawdown) {
            maxDrawdown = drawdown;
        }
    }

    return maxDrawdown;
}

// Run backtests for all symbols
async function runAllBacktests() {
    console.log('Starting backtests with ' + (config.useRelaxedPatterns ? 'relaxed' : 'strict') + ' pattern detection...');

    const results = {};

    for (const symbol of config.symbols) {
        results[symbol] = await runBacktest(symbol);
    }

    // Generate summary report
    const summaryFile = path.join(config.outputDir, `summary_${config.useRelaxedPatterns ? 'relaxed' : 'strict'}.json`);
    fs.writeFileSync(summaryFile, JSON.stringify(results, null, 2));

    console.log(`All backtests completed. Summary saved to ${summaryFile}`);

    // Print summary to console
    console.log('\nBacktest Summary:');
    console.log('=================');

    let totalPnL = 0;
    let totalTrades = 0;
    let totalWins = 0;

    for (const symbol in results) {
        const stats = results[symbol];
        if (!stats) continue;

        console.log(`\n${symbol}:`);
        console.log(`  PnL: $${stats.totalPnL.toFixed(2)}`);
        console.log(`  Trades: ${stats.totalTrades}`);
        console.log(`  Win Rate: ${stats.winRate.toFixed(2)}%`);
        console.log(`  Profit Factor: ${stats.profitFactor.toFixed(2)}`);

        totalPnL += stats.totalPnL;
        totalTrades += stats.totalTrades;
        totalWins += stats.winningTrades;
    }

    console.log('\nOverall:');
    console.log(`  Total PnL: $${totalPnL.toFixed(2)}`);
    console.log(`  Total Trades: ${totalTrades}`);
    console.log(`  Overall Win Rate: ${totalTrades > 0 ? (totalWins / totalTrades * 100).toFixed(2) : 0}%`);
}

// Run both strict and relaxed backtests for comparison
async function runComparison() {
    // Run with strict patterns
    config.useRelaxedPatterns = false;
    await runAllBacktests();

    // Run with relaxed patterns
    config.useRelaxedPatterns = true;
    await runAllBacktests();

    console.log('\nBacktest comparison completed. Check the results in the backtest-results directory.');
}

// Start the backtest
runComparison();
