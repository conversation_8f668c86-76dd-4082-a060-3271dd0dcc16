/**
 * filter-test.js
 * Test different filter combinations to see their impact on results
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');
const { mnqConfig, mesConfig, mgcConfig } = require('../multi_symbol_config');

// Define filter combinations to test
const filterCombinations = [
    { name: "No Filters", useWmaFilter: false, useSma200Filter: false },
    { name: "WMA Filter Only", useWmaFilter: true, useSma200Filter: false },
    { name: "SMA200 Filter Only", useWmaFilter: false, useSma200Filter: true },
    { name: "Both Filters", useWmaFilter: true, useSma200Filter: true }
];

// Define the symbols to test
const symbols = ['MNQ', 'MES', 'MGC'];

// Function to run tests for a symbol with different filter combinations
async function runFilterTests(symbol) {
    console.log(`\n=== RUNNING FILTER TESTS FOR ${symbol} ===\n`);
    
    // Get the base config for the symbol
    let baseConfig;
    switch (symbol) {
        case 'MNQ':
            baseConfig = { ...mnqConfig };
            break;
        case 'MES':
            baseConfig = { ...mesConfig };
            break;
        case 'MGC':
            baseConfig = { ...mgcConfig };
            break;
        default:
            throw new Error(`Unknown symbol: ${symbol}`);
    }
    
    // Results array
    const results = [];
    
    // Run tests for each filter combination
    for (const filterCombo of filterCombinations) {
        console.log(`\nTesting ${symbol} with ${filterCombo.name}:`);
        
        // Create config with current filter settings
        const config = { 
            ...baseConfig,
            useWmaFilter: filterCombo.useWmaFilter,
            useSma200Filter: filterCombo.useSma200Filter
        };
        
        // Run backtest
        const result = await runBacktest(config);
        
        // Calculate win rate
        const winRate = (result.wins / result.totalTrades * 100).toFixed(2);
        
        // Calculate P&L
        const pnl = result.finalBalance - config.initialBalance;
        
        // Store results
        results.push({
            symbol,
            filterCombo: filterCombo.name,
            totalTrades: result.totalTrades,
            wins: result.wins,
            losses: result.losses,
            winRate: `${winRate}%`,
            pnl: pnl.toFixed(2),
            maxDrawdown: result.maxDrawdown.toFixed(2)
        });
        
        // Display results
        console.log(`Total trades: ${result.totalTrades}`);
        console.log(`Win rate: ${winRate}%`);
        console.log(`P&L: $${pnl.toFixed(2)}`);
        console.log(`Max drawdown: $${result.maxDrawdown.toFixed(2)}`);
    }
    
    return results;
}

// Main function to run all tests
async function runAllTests() {
    const allResults = [];
    
    for (const symbol of symbols) {
        const symbolResults = await runFilterTests(symbol);
        allResults.push(...symbolResults);
    }
    
    // Display summary table
    console.log("\n=== FILTER TEST SUMMARY ===\n");
    console.table(allResults);
    
    // Save results to file
    const resultsFile = path.join(__dirname, 'filter_test_results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(allResults, null, 2));
    console.log(`\nResults saved to ${resultsFile}`);
}

// Run all tests
runAllTests().catch(console.error);
