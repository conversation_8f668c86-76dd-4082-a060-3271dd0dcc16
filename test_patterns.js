/**
 * Test script for pattern detection functions
 */

// Determine candlestick color
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji';
}

// Detect three-candle pattern
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) {
        console.log(`detect3: Missing candle data`);
        return null;
    }

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    console.log(`detect3: Checking candle colors: ${col1}-${col2}-${col3}`);
    console.log(`detect3: Candle 1: O=${c1.open} H=${c1.high} L=${c1.low} C=${c1.close}`);
    console.log(`detect3: Candle 2: O=${c2.open} H=${c2.high} L=${c2.low} C=${c2.close}`);
    console.log(`detect3: Candle 3: O=${c3.open} H=${c3.high} L=${c3.low} C=${c3.close}`);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') {
        console.log(`detect3: Invalid candle color detected`);
        return null;
    }

    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        console.log(`detect3: Found green-red-green color pattern, checking conditions...`);
        
        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;
        
        console.log(`detect3: Engulfing check: ${engulf} (c3.close=${c3.close} > c2.open=${c2.open} && c3.open=${c3.open} < c2.close=${c2.close})`);
        console.log(`detect3: Wick comparison: wick3=${wick3} > wick2=${wick2} = ${wick3 > wick2}`);

        if (engulf && wick3 > wick2) {
            console.log(`Detected bullish 3-candle pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} > c2.open=${c2.open} && c3.open=${c3.open} < c2.close=${c2.close})`);
            return 'bullish';
        } else {
            console.log(`detect3: Bullish pattern conditions not met`);
        }
    }

    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        console.log(`detect3: Found red-green-red color pattern, checking conditions...`);
        
        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);
        
        console.log(`detect3: Engulfing check: ${engulf} (c3.close=${c3.close} < c2.open=${c2.open} && c3.open=${c3.open} > c2.close=${c2.close})`);
        console.log(`detect3: Wick comparison: wick3=${wick3} > wick2=${wick2} = ${wick3 > wick2}`);

        if (engulf && wick3 > wick2) {
            console.log(`Detected bearish 3-candle pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} < c2.open=${c2.open} && c3.open=${c3.open} > c2.close=${c2.close})`);
            return 'bearish';
        } else {
            console.log(`detect3: Bearish pattern conditions not met`);
        }
    }

    console.log(`detect3: No 3-candle pattern detected`);
    return null;
}

// Detect four-candle pattern
function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) {
        console.log(`detect4: Missing candle data`);
        return null;
    }

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    console.log(`detect4: Checking candle colors: ${col0}-${col1}-${col2}-${col3}`);
    console.log(`detect4: Candle 0: O=${c0.open} H=${c0.high} L=${c0.low} C=${c0.close}`);
    console.log(`detect4: Candle 1: O=${c1.open} H=${c1.high} L=${c1.low} C=${c1.close}`);
    console.log(`detect4: Candle 2: O=${c2.open} H=${c2.high} L=${c2.low} C=${c2.close}`);
    console.log(`detect4: Candle 3: O=${c3.open} H=${c3.high} L=${c3.low} C=${c3.close}`);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') {
        console.log(`detect4: Invalid candle color detected`);
        return null;
    }

    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        console.log(`detect4: Found green-red-red-green color pattern, checking conditions...`);
        
        const maxOpen = Math.max(c1.open, c2.open);
        console.log(`detect4: Max open of middle candles: ${maxOpen} (c1.open=${c1.open}, c2.open=${c2.open})`);
        console.log(`detect4: Checking if c3.close=${c3.close} > ${maxOpen} = ${c3.close > maxOpen}`);
        
        if (c3.close > maxOpen) {
            console.log(`Detected bullish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close} > max(c1.open=${c1.open}, c2.open=${c2.open}))`);
            return 'bullish';
        } else {
            console.log(`detect4: Bullish pattern conditions not met`);
        }
    }

    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        console.log(`detect4: Found red-green-green-red color pattern, checking conditions...`);
        
        const minOpen = Math.min(c1.open, c2.open);
        console.log(`detect4: Min open of middle candles: ${minOpen} (c1.open=${c1.open}, c2.open=${c2.open})`);
        console.log(`detect4: Checking if c3.close=${c3.close} < ${minOpen} = ${c3.close < minOpen}`);
        
        if (c3.close < minOpen) {
            console.log(`Detected bearish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close} < min(c1.open=${c1.open}, c2.open=${c2.open}))`);
            return 'bearish';
        } else {
            console.log(`detect4: Bearish pattern conditions not met`);
        }
    }

    console.log(`detect4: No 4-candle pattern detected`);
    return null;
}

// Test cases
console.log("Testing 3-candle pattern detection...");

// Bullish 3-candle pattern
const bullish3 = [
    { open: 100, high: 110, low: 95, close: 105 },  // green
    { open: 105, high: 108, low: 98, close: 100 },  // red
    { open: 99, high: 112, low: 95, close: 110 }    // green with engulfing and larger wick
];

console.log("\nTesting bullish 3-candle pattern:");
const bullish3Result = detect3(bullish3[0], bullish3[1], bullish3[2]);
console.log(`Result: ${bullish3Result}`);

// Bearish 3-candle pattern
const bearish3 = [
    { open: 105, high: 110, low: 95, close: 100 },  // red
    { open: 100, high: 108, low: 98, close: 105 },  // green
    { open: 106, high: 112, low: 95, close: 95 }    // red with engulfing and larger wick
];

console.log("\nTesting bearish 3-candle pattern:");
const bearish3Result = detect3(bearish3[0], bearish3[1], bearish3[2]);
console.log(`Result: ${bearish3Result}`);

// Test 4-candle pattern detection
console.log("\nTesting 4-candle pattern detection...");

// Bullish 4-candle pattern
const bullish4 = [
    { open: 100, high: 110, low: 95, close: 105 },  // green
    { open: 105, high: 108, low: 98, close: 100 },  // red
    { open: 102, high: 105, low: 97, close: 98 },   // red
    { open: 99, high: 112, low: 95, close: 110 }    // green with close > max(c1.open, c2.open)
];

console.log("\nTesting bullish 4-candle pattern:");
const bullish4Result = detect4(bullish4[0], bullish4[1], bullish4[2], bullish4[3]);
console.log(`Result: ${bullish4Result}`);

// Bearish 4-candle pattern
const bearish4 = [
    { open: 105, high: 110, low: 95, close: 100 },  // red
    { open: 100, high: 108, low: 98, close: 105 },  // green
    { open: 98, high: 105, low: 97, close: 102 },   // green
    { open: 106, high: 112, low: 95, close: 95 }    // red with close < min(c1.open, c2.open)
];

console.log("\nTesting bearish 4-candle pattern:");
const bearish4Result = detect4(bearish4[0], bearish4[1], bearish4[2], bearish4[3]);
console.log(`Result: ${bearish4Result}`);
