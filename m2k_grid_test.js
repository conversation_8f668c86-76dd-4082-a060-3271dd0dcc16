// m2k_grid_test.js - Grid test for M2K parameters

const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

console.log("Starting M2K Grid Test...");

// --- Grid Test Configuration ---
const gridConfig = {
  inputFile: 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv',
  outputDir: './output/M2K_GridTest',
  initialBalance: 10000,
  pointValue: 5.00,
  commissionPerContract: 0.40,
  slippagePoints: 0.20,
  fixedContracts: 10,
  atrPeriod: 14,
  
  // Parameter ranges to test
  slFactors: [4.0, 5.0, 6.0, 7.0, 8.0, 9.0],
  tpFactors: [3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0],
  trailFactors: [0.01, 0.02, 0.03, 0.05, 0.07, 0.10, 0.12]
};

// --- Setup ---
if (!fs.existsSync(gridConfig.outputDir)) {
  fs.mkdirSync(gridConfig.outputDir, { recursive: true });
  console.log(`Created output directory: ${gridConfig.outputDir}`);
} else {
  console.log(`Output directory exists: ${gridConfig.outputDir}`);
}

// --- Data Loading ---
console.log(`Loading data from: ${gridConfig.inputFile}`);
const allCandles = [];

// Check if file exists
if (!fs.existsSync(gridConfig.inputFile)) {
  console.error(`Error: Input file not found at path: ${gridConfig.inputFile}`);
  process.exit(1);
}

// Load data
fs.createReadStream(gridConfig.inputFile)
  .pipe(csv({ separator: ';' }))
  .on('data', (data) => {
    const open = parseFloat(data.Open);
    const high = parseFloat(data.High);
    const low = parseFloat(data.Low);
    const close = parseFloat(data.Close);
    const timeString = data.Time || data.Date || data['Time left'];
    
    let timestamp;
    try {
      timestamp = new Date(timeString).getTime() / 1000;
    } catch (e) {
      return; // Skip invalid dates
    }
    
    if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close) && !isNaN(timestamp)) {
      allCandles.push({ timestamp, open, high, low, close });
    }
  })
  .on('end', () => {
    console.log(`Loaded ${allCandles.length} candles`);
    
    if (allCandles.length === 0) {
      console.error("No valid data loaded. Check your CSV format.");
      process.exit(1);
    }
    
    // Sort candles by timestamp
    allCandles.sort((a, b) => a.timestamp - b.timestamp);
    
    // Calculate ATR
    calculateATR(allCandles, gridConfig.atrPeriod);
    
    // Run grid test
    runGridTest(allCandles, gridConfig);
  })
  .on('error', (err) => {
    console.error("Error reading CSV:", err);
    process.exit(1);
  });

// --- Calculate ATR ---
function calculateATR(candles, period) {
  // First calculate True Range for each candle
  for (let i = 0; i < candles.length; i++) {
    if (i === 0) {
      candles[i].tr = candles[i].high - candles[i].low;
    } else {
      const prev = candles[i-1];
      candles[i].tr = Math.max(
        candles[i].high - candles[i].low,
        Math.abs(candles[i].high - prev.close),
        Math.abs(candles[i].low - prev.close)
      );
    }
  }
  
  // Then calculate ATR as simple moving average of TR
  for (let i = 0; i < candles.length; i++) {
    if (i < period - 1) {
      candles[i].atr = candles[i].tr;
    } else {
      let sum = 0;
      for (let j = i - period + 1; j <= i; j++) {
        sum += candles[j].tr;
      }
      candles[i].atr = sum / period;
    }
  }
}

// --- Run Backtest with Specific Parameters ---
function runBacktest(candles, config) {
  let balance = config.initialBalance;
  let position = null;
  let trades = [];
  let wins = 0;
  let losses = 0;
  let grossProfit = 0;
  let grossLoss = 0;
  
  // Need at least 3 candles for pattern detection
  for (let i = 3; i < candles.length; i++) {
    const c0 = candles[i-3];
    const c1 = candles[i-2];
    const c2 = candles[i-1];
    const c3 = candles[i];
    
    // Skip if ATR is not available
    if (!c3.atr || isNaN(c3.atr) || c3.atr <= 0) continue;
    
    // Manage existing position
    if (position) {
      // Update trail stop
      if (position.direction === 'long') {
        position.trailStop = Math.max(position.trailStop, 
          c3.high - (c3.atr * config.trailFactor));
      } else {
        position.trailStop = Math.min(position.trailStop, 
          c3.low + (c3.atr * config.trailFactor));
      }
      
      // Check for exit conditions
      let exitPrice = null;
      let exitReason = null;
      
      if (position.direction === 'long') {
        // Stop loss hit
        if (c3.low <= position.stopLoss) {
          exitPrice = position.stopLoss;
          exitReason = 'stop_loss';
        }
        // Take profit hit
        else if (c3.high >= position.takeProfit) {
          exitPrice = position.takeProfit;
          exitReason = 'take_profit';
        }
        // Trail stop hit
        else if (c3.low <= position.trailStop) {
          exitPrice = position.trailStop;
          exitReason = 'trail_stop';
        }
      } else { // Short position
        // Stop loss hit
        if (c3.high >= position.stopLoss) {
          exitPrice = position.stopLoss;
          exitReason = 'stop_loss';
        }
        // Take profit hit
        else if (c3.low <= position.takeProfit) {
          exitPrice = position.takeProfit;
          exitReason = 'take_profit';
        }
        // Trail stop hit
        else if (c3.high >= position.trailStop) {
          exitPrice = position.trailStop;
          exitReason = 'trail_stop';
        }
      }
      
      // Process exit if conditions met
      if (exitPrice !== null) {
        const pnlPoints = position.direction === 'long' ? 
          exitPrice - position.entry : position.entry - exitPrice;
        const pnlDollars = pnlPoints * config.pointValue * config.fixedContracts;
        const commission = config.commissionPerContract * config.fixedContracts;
        const slippage = config.slippagePoints * config.pointValue * config.fixedContracts;
        const netPnl = pnlDollars - commission - slippage;
        
        balance += netPnl;
        
        trades.push({
          entry: position.entry,
          exit: exitPrice,
          direction: position.direction,
          pnl: netPnl,
          reason: exitReason
        });
        
        if (netPnl > 0) {
          wins++;
          grossProfit += netPnl;
        } else {
          losses++;
          grossLoss += Math.abs(netPnl);
        }
        
        position = null;
      }
    }
    
    // Look for new entry if no position
    if (!position) {
      // Simple pattern: bullish if previous candle is green and current is green
      const isBullish = c2.close > c2.open && c3.open < c3.close;
      // Simple pattern: bearish if previous candle is red and current is red
      const isBearish = c2.close < c2.open && c3.open > c3.close;
      
      if (isBullish) {
        position = {
          direction: 'long',
          entry: c3.close,
          stopLoss: c3.close - (c3.atr * config.slFactor),
          takeProfit: c3.close + (c3.atr * config.tpFactor),
          trailStop: c3.close - (c3.atr * config.trailFactor),
          entryTime: c3.timestamp
        };
      } else if (isBearish) {
        position = {
          direction: 'short',
          entry: c3.close,
          stopLoss: c3.close + (c3.atr * config.slFactor),
          takeProfit: c3.close - (c3.atr * config.tpFactor),
          trailStop: c3.close + (c3.atr * config.trailFactor),
          entryTime: c3.timestamp
        };
      }
    }
  }
  
  // Close any open position at the end
  if (position) {
    const lastCandle = candles[candles.length - 1];
    const pnlPoints = position.direction === 'long' ? 
      lastCandle.close - position.entry : position.entry - lastCandle.close;
    const pnlDollars = pnlPoints * config.pointValue * config.fixedContracts;
    const commission = config.commissionPerContract * config.fixedContracts;
    const netPnl = pnlDollars - commission;
    
    balance += netPnl;
    
    trades.push({
      entry: position.entry,
      exit: lastCandle.close,
      direction: position.direction,
      pnl: netPnl,
      reason: 'end_of_data'
    });
    
    if (netPnl > 0) {
      wins++;
      grossProfit += netPnl;
    } else {
      losses++;
      grossLoss += Math.abs(netPnl);
    }
  }
  
  // Calculate metrics
  const totalTrades = wins + losses;
  const winRate = totalTrades > 0 ? (wins / totalTrades * 100) : 0;
  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;
  const totalPnL = balance - config.initialBalance;
  
  return {
    totalTrades,
    wins,
    losses,
    winRate,
    totalPnL,
    profitFactor,
    finalBalance: balance
  };
}

// --- Run Grid Test ---
function runGridTest(candles, gridConfig) {
  console.log("Running grid test...");
  console.log(`Testing ${gridConfig.slFactors.length} SL factors × ${gridConfig.tpFactors.length} TP factors × ${gridConfig.trailFactors.length} trail factors = ${gridConfig.slFactors.length * gridConfig.tpFactors.length * gridConfig.trailFactors.length} combinations`);
  
  const results = [];
  let bestResult = null;
  let bestParams = null;
  let completedTests = 0;
  const totalTests = gridConfig.slFactors.length * gridConfig.tpFactors.length * gridConfig.trailFactors.length;
  
  const startTime = Date.now();
  
  // Generate all parameter combinations
  for (const slFactor of gridConfig.slFactors) {
    for (const tpFactor of gridConfig.tpFactors) {
      for (const trailFactor of gridConfig.trailFactors) {
        // Create config for this test
        const testConfig = {
          ...gridConfig,
          slFactor,
          tpFactor,
          trailFactor
        };
        
        // Run backtest with these parameters
        const result = runBacktest(candles, testConfig);
        
        // Add parameters to result
        result.slFactor = slFactor;
        result.tpFactor = tpFactor;
        result.trailFactor = trailFactor;
        
        // Track best result by profit factor
        if (!bestResult || result.profitFactor > bestResult.profitFactor) {
          bestResult = result;
          bestParams = { slFactor, tpFactor, trailFactor };
        }
        
        // Add to results array
        results.push(result);
        
        // Update progress
        completedTests++;
        if (completedTests % 10 === 0 || completedTests === totalTests) {
          const elapsedSeconds = (Date.now() - startTime) / 1000;
          const testsPerSecond = completedTests / elapsedSeconds;
          const remainingTests = totalTests - completedTests;
          const estimatedRemainingSeconds = remainingTests / testsPerSecond;
          
          console.log(`Progress: ${completedTests}/${totalTests} (${(completedTests/totalTests*100).toFixed(1)}%) - ETA: ${formatTime(estimatedRemainingSeconds)}`);
        }
      }
    }
  }
  
  // Sort results by profit factor (descending)
  results.sort((a, b) => b.profitFactor - a.profitFactor);
  
  // Display top 10 results
  console.log("\n--- TOP 10 PARAMETER COMBINATIONS ---");
  for (let i = 0; i < Math.min(10, results.length); i++) {
    const r = results[i];
    console.log(`${i+1}. SL=${r.slFactor.toFixed(2)}, TP=${r.tpFactor.toFixed(2)}, Trail=${r.trailFactor.toFixed(2)} - PF=${r.profitFactor.toFixed(2)}, P&L=$${r.totalPnL.toFixed(2)}, Trades=${r.totalTrades}, Win=${r.winRate.toFixed(1)}%`);
  }
  
  // Save all results to CSV
  const csvRows = [
    "SL Factor,TP Factor,Trail Factor,Profit Factor,Total P&L,Total Trades,Wins,Losses,Win Rate"
  ];
  
  for (const r of results) {
    csvRows.push(`${r.slFactor},${r.tpFactor},${r.trailFactor},${r.profitFactor},${r.totalPnL},${r.totalTrades},${r.wins},${r.losses},${r.winRate}`);
  }
  
  fs.writeFileSync(path.join(gridConfig.outputDir, 'grid_results.csv'), csvRows.join('\n'));
  console.log(`\nAll results saved to: ${path.join(gridConfig.outputDir, 'grid_results.csv')}`);
  
  // Save best parameters
  const bestParamsJson = JSON.stringify(bestParams, null, 2);
  fs.writeFileSync(path.join(gridConfig.outputDir, 'best_params.json'), bestParamsJson);
  console.log(`Best parameters saved to: ${path.join(gridConfig.outputDir, 'best_params.json')}`);
  
  // Display completion message
  const totalTime = (Date.now() - startTime) / 1000;
  console.log(`\nGrid test completed in ${formatTime(totalTime)}`);
  console.log(`Tested ${results.length} parameter combinations`);
  console.log(`Best combination: SL=${bestParams.slFactor.toFixed(2)}, TP=${bestParams.tpFactor.toFixed(2)}, Trail=${bestParams.trailFactor.toFixed(2)}`);
  console.log(`Best profit factor: ${bestResult.profitFactor.toFixed(2)}`);
}

// --- Helper Functions ---
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  return `${hours}h ${minutes}m ${secs}s`;
}