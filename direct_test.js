/**
 * Direct Test Script for Tradovate API
 *
 * This script tests the Tradovate API directly using axios
 * to diagnose issues with order placement.
 */

require('dotenv').config();
const axios = require('axios');
const logger = require('./data_logger');

// Configuration
const config = {
    baseUrl: 'https://demo.tradovateapi.com/v1',
    name: 'bravesbeatmets',
    password: 'Braves12$',
    appId: 'Trading Bot',
    appVersion: '0.0.1',
    cid: '6186',
    sec: '69311dad-75a7-49c6-9958-00ab2c4f1ab6',
    deviceId: '25e3f568-2890-5442-1e40-b9e8983af7e7',
    accountId: '4690440' // Demo account ID without DEMO prefix
};

// Create axios instance
const api = axios.create({
    baseURL: config.baseUrl,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

// Authentication state
let accessToken = null;
let userId = null;

/**
 * Authenticate with Tradovate API
 * @returns {Promise<Object>} - Authentication result
 */
async function authenticate() {
    try {
        console.log('Authenticating with Tradovate API...');

        // Prepare authentication payload
        const payload = {
            name: config.name,
            password: config.password,
            appId: config.appId,
            appVersion: config.appVersion,
            deviceId: config.deviceId,
            cid: config.cid,
            sec: config.sec
        };

        // Send authentication request
        const response = await api.post('/auth/accesstokenrequest', payload);

        if (response.data && response.data.accessToken) {
            accessToken = response.data.accessToken;
            userId = response.data.userId;

            console.log('Authentication successful!');
            console.log(`User ID: ${userId}`);

            return {
                success: true,
                accessToken,
                userId
            };
        } else {
            throw new Error('Authentication response did not contain an access token');
        }
    } catch (error) {
        console.error('Authentication failed:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get product information
 * @returns {Promise<Array>} - List of products
 */
async function getProducts() {
    try {
        console.log('Getting products...');

        // Send request
        const response = await api.get('/product/list', {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.data && Array.isArray(response.data)) {
            console.log(`Found ${response.data.length} products`);

            // Filter for MNQ products
            const mnqProducts = response.data.filter(p => p.name === 'MNQ');

            if (mnqProducts.length > 0) {
                console.log('MNQ products:');
                mnqProducts.forEach(p => {
                    console.log(`- ${p.name} (ID: ${p.id}): ${p.description}`);
                });
            }

            return response.data;
        } else {
            throw new Error('Product response did not contain an array');
        }
    } catch (error) {
        console.error('Failed to get products:', error.message);
        return [];
    }
}

/**
 * Get contracts for a product
 * @param {number} productId - Product ID
 * @returns {Promise<Array>} - List of contracts
 */
async function getContracts(productId) {
    try {
        console.log(`Getting contracts for product ID ${productId}...`);

        // Send request
        const response = await api.get(`/contract/find?productIds=${productId}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.data && Array.isArray(response.data)) {
            console.log(`Found ${response.data.length} contracts`);

            if (response.data.length > 0) {
                console.log('Contracts:');
                response.data.forEach(c => {
                    console.log(`- ${c.name} (ID: ${c.id})`);
                });
            }

            return response.data;
        } else {
            throw new Error('Contract response did not contain an array');
        }
    } catch (error) {
        console.error(`Failed to get contracts for product ID ${productId}:`, error.message);
        return [];
    }
}

/**
 * Place a test order
 * @param {Object} orderParams - Order parameters
 * @returns {Promise<Object>} - Order result
 */
async function placeOrder(orderParams) {
    try {
        console.log('Placing test order...');
        console.log('Order parameters:', orderParams);

        // Send request
        const response = await api.post('/order/placeorder', orderParams, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Order placed successfully!');
        console.log('Response:', response.data);

        return {
            success: true,
            orderId: response.data.orderId || response.data.id,
            orderData: response.data
        };
    } catch (error) {
        console.error('Failed to place order:', error.message);

        if (error.response) {
            console.error('Error response data:', error.response.data);
            console.error('Error response status:', error.response.status);

            return {
                success: false,
                error: error.message,
                status: error.response.status,
                data: error.response.data
            };
        } else {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

/**
 * Run the test
 */
async function runTest() {
    try {
        // Authenticate
        const authResult = await authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }

        // Get products
        const products = await getProducts();

        // Find MNQ product
        const mnqProduct = products.find(p => p.name === 'MNQ');

        if (!mnqProduct) {
            throw new Error('MNQ product not found');
        }

        console.log(`Found MNQ product: ${mnqProduct.name} (ID: ${mnqProduct.id})`);

        // Get contracts for MNQ
        const contracts = await getContracts(mnqProduct.id);

        // Find MNQM5 contract
        const mnqm5Contract = contracts.find(c => c.name === 'MNQM5');

        if (!mnqm5Contract) {
            console.warn('MNQM5 contract not found, will try using product ID directly');

            // Place order using product ID and symbol
            const orderParams = {
                accountId: Number(config.accountId),
                accountSpec: config.accountId,
                action: 'Buy',
                orderQty: 1,
                orderType: 'Market',
                isAutomated: true,
                contractId: mnqProduct.id,
                symbol: 'MNQM5' // Include symbol even when using contractId
            };

            await placeOrder(orderParams);
        } else {
            console.log(`Found MNQM5 contract: ${mnqm5Contract.name} (ID: ${mnqm5Contract.id})`);

            // Place order using contract ID and symbol
            const orderParams = {
                accountId: Number(config.accountId),
                accountSpec: config.accountId,
                action: 'Buy',
                orderQty: 1,
                orderType: 'Market',
                isAutomated: true,
                contractId: mnqm5Contract.id,
                symbol: mnqm5Contract.name // Include symbol from contract
            };

            await placeOrder(orderParams);
        }
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// Run the test
runTest();
