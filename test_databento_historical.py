#!/usr/bin/env python3
"""
Simple test script for Databento Historical API using the official Python client.
"""

import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import databento
try:
    import databento as db
    logger.info(f"Successfully imported databento version: {db.__version__}")
except ImportError:
    logger.error("Failed to import databento. Please install it with: pip install databento")
    exit(1)

# API Key
API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq"
logger.info(f"Using API key: {API_KEY[:8]}...")

# Create a historical client
logger.info("Creating historical client...")
client = db.Historical(key=API_KEY)

# Get available datasets
logger.info("Getting available datasets...")
try:
    datasets = client.metadata.list_datasets()
    logger.info(f"Found {len(datasets)} datasets")

    # Print raw dataset information to understand the format
    logger.info(f"Dataset type: {type(datasets)}")
    logger.info(f"First few datasets: {datasets[:3]}")

    # Handle different possible formats
    if isinstance(datasets, list):
        if len(datasets) > 0:
            if hasattr(datasets[0], 'dataset'):
                # Original expected format
                for dataset in datasets:
                    logger.info(f"- {dataset.dataset}: {dataset.description}")
            elif isinstance(datasets[0], dict):
                # Dictionary format
                for dataset in datasets:
                    logger.info(f"- {dataset.get('dataset', 'unknown')}: {dataset.get('description', 'unknown')}")
            elif isinstance(datasets[0], str):
                # String format
                for dataset in datasets:
                    logger.info(f"- {dataset}")
        else:
            logger.warning("Dataset list is empty")
    else:
        logger.info(f"Unexpected dataset format: {datasets}")
except Exception as e:
    logger.error(f"Failed to get datasets: {str(e)}")
    exit(1)

# Check if GLBX.MDP3 is in the available datasets
try:
    has_glbx = False
    if isinstance(datasets, list):
        if len(datasets) > 0:
            if hasattr(datasets[0], 'dataset'):
                has_glbx = any(d.dataset == "GLBX.MDP3" for d in datasets)
            elif isinstance(datasets[0], dict):
                has_glbx = any(d.get('dataset') == "GLBX.MDP3" for d in datasets)
            elif isinstance(datasets[0], str):
                has_glbx = "GLBX.MDP3" in datasets

    if has_glbx:
        logger.info("GLBX.MDP3 dataset found")
    else:
        logger.warning("GLBX.MDP3 dataset not found in available datasets")
except Exception as e:
    logger.error(f"Error checking for GLBX.MDP3: {str(e)}")

# Try to get symbols for GLBX.MDP3
logger.info("Getting symbols for GLBX.MDP3...")
try:
    symbols = client.metadata.list_symbols(dataset="GLBX.MDP3")
    logger.info(f"Found {len(symbols)} symbols")

    # Print raw symbol information to understand the format
    logger.info(f"Symbol type: {type(symbols)}")
    if len(symbols) > 0:
        logger.info(f"First symbol type: {type(symbols[0])}")
        logger.info(f"First few symbols: {symbols[:3]}")

    # Look for specific symbols
    target_symbols = ["MNQ.FUT", "ES.FUT", "GC.FUT", "RTY.FUT"]
    found_symbols = []

    # Handle different possible formats
    if isinstance(symbols, list):
        if len(symbols) > 0:
            if hasattr(symbols[0], 'symbol'):
                # Original expected format
                found_symbols = [s for s in symbols if s.symbol in target_symbols]
                logger.info(f"Found {len(found_symbols)} target symbols")
                for symbol in found_symbols:
                    logger.info(f"- {symbol.symbol}: {getattr(symbol, 'description', 'N/A')}")
            elif isinstance(symbols[0], dict):
                # Dictionary format
                found_symbols = [s for s in symbols if s.get('symbol') in target_symbols]
                logger.info(f"Found {len(found_symbols)} target symbols")
                for symbol in found_symbols:
                    logger.info(f"- {symbol.get('symbol', 'unknown')}: {symbol.get('description', 'N/A')}")
            elif isinstance(symbols[0], str):
                # String format
                found_symbols = [s for s in symbols if s in target_symbols]
                logger.info(f"Found {len(found_symbols)} target symbols")
                for symbol in found_symbols:
                    logger.info(f"- {symbol}")
        else:
            logger.warning("Symbol list is empty")
    else:
        logger.info(f"Unexpected symbol format: {symbols}")
except Exception as e:
    logger.error(f"Error getting symbols: {str(e)}")

# Try to get historical data
logger.info("Getting historical data for ES.FUT...")
try:
    end_date = datetime.now()
    start_date = end_date - timedelta(days=1)

    logger.info(f"Requesting data from {start_date} to {end_date}")

    try:
        # First try with timeseries.get_range
        logger.info("Trying timeseries.get_range...")
        data = client.timeseries.get_range(
            dataset="GLBX.MDP3",
            symbols="ES.FUT",
            start=start_date,
            end=end_date,
            schema="trades",
            stype_in="parent"
        )

        logger.info(f"Data type: {type(data)}")
        logger.info(f"Got {len(data)} records")
        if len(data) > 0:
            logger.info(f"Sample data: {data[:3]}")
    except Exception as e:
        logger.error(f"Error with timeseries.get_range: {str(e)}")

        # Try alternative method
        logger.info("Trying alternative method...")
        try:
            # Try with ohlcv.get
            logger.info("Trying ohlcv.get...")
            data = client.ohlcv.get(
                dataset="GLBX.MDP3",
                symbols="ES.FUT",
                start=start_date,
                end=end_date,
                interval="1m",
                stype_in="parent"
            )

            logger.info(f"Data type: {type(data)}")
            logger.info(f"Got {len(data)} records")
            if len(data) > 0:
                logger.info(f"Sample data: {data[:3]}")
        except Exception as e2:
            logger.error(f"Error with alternative method: {str(e2)}")
except Exception as e:
    logger.error(f"Error getting historical data: {str(e)}")

logger.info("Test completed")
