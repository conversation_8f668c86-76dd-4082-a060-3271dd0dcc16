/**
 * Connection Pool Manager
 * 
 * This module provides a connection pool for managing multiple WebSocket connections.
 */

const WebSocketManager = require('./websocket_manager');
const EventEmitter = require('events');
const logger = require('./data_logger');

/**
 * Connection Pool Manager Class
 */
class ConnectionPoolManager extends EventEmitter {
    /**
     * Constructor
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
        super();
        
        this.options = {
            maxConnections: options.maxConnections || 5,
            connectionTimeout: options.connectionTimeout || 10000,
            debug: options.debug || false
        };
        
        // State
        this.connections = new Map();
        this.connectionQueue = [];
        
        // Debug logging
        if (this.options.debug) {
            this.on('debug', (message) => {
                logger.logSystem(`[ConnectionPool] ${message}`, 'debug');
            });
        }
    }
    
    /**
     * Create a new connection
     * @param {string} id - Connection ID
     * @param {Object} options - WebSocket options
     * @returns {Promise<WebSocketManager>} - WebSocket manager
     */
    createConnection(id, options) {
        return new Promise((resolve, reject) => {
            if (this.connections.has(id)) {
                this.emit('debug', `Connection ${id} already exists`);
                resolve(this.connections.get(id));
                return;
            }
            
            if (this.connections.size >= this.options.maxConnections) {
                this.emit('debug', `Connection limit reached (${this.options.maxConnections}), queueing connection ${id}`);
                
                // Queue connection
                this.connectionQueue.push({
                    id,
                    options,
                    resolve,
                    reject
                });
                
                return;
            }
            
            this.emit('debug', `Creating connection ${id}`);
            
            try {
                // Create WebSocket manager
                const wsManager = new WebSocketManager({
                    name: id,
                    ...options
                });
                
                // Set up event handlers
                wsManager.on('open', () => {
                    this.emit('connection_open', id);
                });
                
                wsManager.on('close', (code, reason) => {
                    this.emit('connection_close', id, code, reason);
                });
                
                wsManager.on('error', (error) => {
                    this.emit('connection_error', id, error);
                });
                
                wsManager.on('reconnected', () => {
                    this.emit('connection_reconnected', id);
                });
                
                wsManager.on('reconnect_failed', () => {
                    this.emit('connection_reconnect_failed', id);
                    
                    // Remove connection
                    this.removeConnection(id);
                    
                    // Process queue
                    this.processConnectionQueue();
                });
                
                // Store connection
                this.connections.set(id, wsManager);
                
                // Connect
                wsManager.connect()
                    .then((connected) => {
                        if (connected) {
                            this.emit('debug', `Connection ${id} established`);
                            resolve(wsManager);
                        } else {
                            this.emit('debug', `Failed to establish connection ${id}`);
                            reject(new Error(`Failed to establish connection ${id}`));
                        }
                    })
                    .catch((error) => {
                        this.emit('debug', `Error establishing connection ${id}: ${error.message}`);
                        reject(error);
                    });
            } catch (error) {
                this.emit('debug', `Error creating connection ${id}: ${error.message}`);
                reject(error);
            }
        });
    }
    
    /**
     * Get a connection
     * @param {string} id - Connection ID
     * @returns {WebSocketManager|null} - WebSocket manager or null if not found
     */
    getConnection(id) {
        return this.connections.get(id) || null;
    }
    
    /**
     * Remove a connection
     * @param {string} id - Connection ID
     * @returns {boolean} - True if connection was removed
     */
    removeConnection(id) {
        if (!this.connections.has(id)) {
            return false;
        }
        
        this.emit('debug', `Removing connection ${id}`);
        
        // Get connection
        const connection = this.connections.get(id);
        
        // Disconnect
        connection.disconnect();
        
        // Remove connection
        this.connections.delete(id);
        
        // Process queue
        this.processConnectionQueue();
        
        return true;
    }
    
    /**
     * Process connection queue
     */
    processConnectionQueue() {
        if (this.connectionQueue.length === 0) {
            return;
        }
        
        if (this.connections.size >= this.options.maxConnections) {
            this.emit('debug', `Cannot process queue, connection limit reached (${this.options.maxConnections})`);
            return;
        }
        
        this.emit('debug', `Processing connection queue (${this.connectionQueue.length} connections)`);
        
        // Process connections until limit is reached
        while (this.connectionQueue.length > 0 && this.connections.size < this.options.maxConnections) {
            const { id, options, resolve, reject } = this.connectionQueue.shift();
            
            this.createConnection(id, options)
                .then(resolve)
                .catch(reject);
        }
    }
    
    /**
     * Disconnect all connections
     */
    disconnectAll() {
        this.emit('debug', `Disconnecting all connections (${this.connections.size})`);
        
        // Disconnect all connections
        for (const [id, connection] of this.connections) {
            connection.disconnect();
        }
        
        // Clear connections
        this.connections.clear();
        
        // Clear queue
        this.connectionQueue = [];
    }
    
    /**
     * Get connection count
     * @returns {number} - Connection count
     */
    getConnectionCount() {
        return this.connections.size;
    }
    
    /**
     * Get queue length
     * @returns {number} - Queue length
     */
    getQueueLength() {
        return this.connectionQueue.length;
    }
    
    /**
     * Get connection IDs
     * @returns {string[]} - Connection IDs
     */
    getConnectionIds() {
        return Array.from(this.connections.keys());
    }
    
    /**
     * Get connection states
     * @returns {Object} - Connection states
     */
    getConnectionStates() {
        const states = {};
        
        for (const [id, connection] of this.connections) {
            states[id] = connection.getState();
        }
        
        return states;
    }
}

module.exports = ConnectionPoolManager;
