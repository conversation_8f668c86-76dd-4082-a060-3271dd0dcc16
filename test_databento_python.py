#!/usr/bin/env python3
"""
Comprehensive test script for Databento API using the official Python client.
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import databento
try:
    import databento as db
    logger.info(f"Successfully imported databento version: {db.__version__}")
except ImportError:
    logger.error("Failed to import databento. Please install it with: pip install databento")
    sys.exit(1)

# API Key
API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq"
logger.info(f"Using API key: {API_KEY[:8]}...")

def test_historical():
    """Test historical data access."""
    logger.info("Testing historical data access...")
    
    try:
        # Create a historical client
        client = db.Historical(key=API_KEY)
        logger.info("Historical client created successfully")
        
        # Get available datasets
        logger.info("Getting available datasets...")
        datasets = client.metadata.list_datasets()
        logger.info(f"Found {len(datasets)} datasets")
        for dataset in datasets:
            logger.info(f"- {dataset.dataset}: {dataset.description}")
        
        # Check if GLBX.MDP3 is in the available datasets
        glbx_dataset = next((d for d in datasets if d.dataset == "GLBX.MDP3"), None)
        if glbx_dataset:
            logger.info(f"GLBX.MDP3 dataset found: {glbx_dataset.description}")
        else:
            logger.warning("GLBX.MDP3 dataset not found in available datasets")
        
        # Try to get symbols for GLBX.MDP3
        logger.info("Getting symbols for GLBX.MDP3...")
        try:
            symbols = client.metadata.list_symbols(dataset="GLBX.MDP3")
            logger.info(f"Found {len(symbols)} symbols")
            
            # Look for specific symbols
            target_symbols = ["MNQ.FUT", "ES.FUT", "GC.FUT", "RTY.FUT"]
            found_symbols = [s for s in symbols if s.symbol in target_symbols]
            logger.info(f"Found {len(found_symbols)} target symbols")
            for symbol in found_symbols:
                logger.info(f"- {symbol.symbol}: {symbol.description}")
        except Exception as e:
            logger.error(f"Error getting symbols: {str(e)}")
        
        # Try to get historical data
        logger.info("Getting historical data for ES.FUT...")
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1)
            
            data = client.timeseries.get_range(
                dataset="GLBX.MDP3",
                symbols="ES.FUT",
                start=start_date,
                end=end_date,
                schema="trades",
                stype_in="parent"
            )
            
            logger.info(f"Got {len(data)} records")
            if len(data) > 0:
                logger.info(f"Sample data: {data[:3]}")
        except Exception as e:
            logger.error(f"Error getting historical data: {str(e)}")
        
        return True
    except Exception as e:
        logger.error(f"Error in historical test: {str(e)}")
        return False

def test_live():
    """Test live data access."""
    logger.info("Testing live data access...")
    
    try:
        # Create a live client
        client = db.Live(key=API_KEY)
        logger.info("Live client created successfully")
        
        # Define a callback to handle records
        def record_callback(record):
            logger.info(f"Received record: {record}")
        
        # Define a callback to handle exceptions
        def exception_callback(exception):
            logger.error(f"Exception in record callback: {str(exception)}")
        
        # Add the callback to the client
        client.add_callback(record_callback, exception_callback)
        
        # Subscribe to ES.FUT trades
        logger.info("Subscribing to ES.FUT trades...")
        client.subscribe(
            dataset="GLBX.MDP3",
            schema="trades",
            stype_in="parent",
            symbols="ES.FUT"
        )
        
        # Start the streaming session
        logger.info("Starting streaming session...")
        client.start()
        
        # Wait for some data
        logger.info("Waiting for data (10 seconds)...")
        time.sleep(10)
        
        # Stop the streaming session
        logger.info("Stopping streaming session...")
        client.stop()
        
        return True
    except Exception as e:
        logger.error(f"Error in live test: {str(e)}")
        return False

def test_reference():
    """Test reference data access."""
    logger.info("Testing reference data access...")
    
    try:
        # Create a reference client
        client = db.Reference(key=API_KEY)
        logger.info("Reference client created successfully")
        
        # Get security master data for ES.FUT
        logger.info("Getting security master data for ES.FUT...")
        try:
            security_master = client.security_master.get_last(
                symbols="ES.FUT",
                stype_in="parent"
            )
            
            logger.info(f"Got security master data with {len(security_master)} records")
            if len(security_master) > 0:
                logger.info(f"Sample data: {security_master.head()}")
        except Exception as e:
            logger.error(f"Error getting security master data: {str(e)}")
        
        return True
    except Exception as e:
        logger.error(f"Error in reference test: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting Databento API tests...")
    
    # Test historical data access
    if test_historical():
        logger.info("✅ Historical data test passed")
    else:
        logger.error("❌ Historical data test failed")
    
    # Test live data access
    if test_live():
        logger.info("✅ Live data test passed")
    else:
        logger.error("❌ Live data test failed")
    
    # Test reference data access
    if test_reference():
        logger.info("✅ Reference data test passed")
    else:
        logger.error("❌ Reference data test failed")
    
    logger.info("All tests completed")

if __name__ == "__main__":
    main()
