#!/usr/bin/env python3
"""
Simple test script for Databento Live API using the official Python client.
This script follows the example provided by the user.
"""

import time
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import databento
try:
    import databento as db
    logger.info(f"Successfully imported databento version: {db.__version__}")
except ImportError:
    logger.error("Failed to import databento. Please install it with: pip install databento")
    exit(1)

# API Key
API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq"
logger.info(f"Using API key: {API_KEY[:8]}...")

# Create a live client
logger.info("Creating live client...")
try:
    # Print available attributes and methods of the db module
    logger.info("Available attributes and methods of db module:")
    for attr in dir(db):
        if not attr.startswith('_'):
            logger.info(f"- {attr}")

    # Create the client with detailed logging
    logger.info("Creating Live client...")
    client = db.Live(key=API_KEY)
    logger.info("Live client created successfully")

    # Print client information
    logger.info(f"Client type: {type(client)}")
    logger.info(f"Client attributes: {[attr for attr in dir(client) if not attr.startswith('_')]}")
except Exception as e:
    logger.error(f"Error creating Live client: {str(e)}")
    exit(1)

# Define a callback to handle records
def record_callback(record):
    logger.info(f"Received record: {record}")

# Define a callback to handle exceptions
def exception_callback(exception):
    logger.error(f"Exception in record callback: {str(exception)}")

# Add the callback to the client
try:
    logger.info("Adding callback...")
    client.add_callback(record_callback, exception_callback)
    logger.info("Callback added successfully")
except Exception as e:
    logger.error(f"Error adding callback: {str(e)}")
    exit(1)

# Subscribe to ES.FUT trades
logger.info("Subscribing to ES.FUT trades...")
try:
    # Try different symbol formats
    symbols_to_try = ["ES.FUT", "ES", "ESM5"]

    for symbol in symbols_to_try:
        try:
            logger.info(f"Trying to subscribe to {symbol}...")
            client.subscribe(
                dataset="GLBX.MDP3",
                schema="trades",
                stype_in="parent",
                symbols=symbol
            )
            logger.info(f"Subscription to {symbol} successful")
            break
        except Exception as e:
            logger.error(f"Subscription to {symbol} failed: {str(e)}")

    # If all symbols failed, exit
    if symbol == symbols_to_try[-1]:
        logger.error("All subscription attempts failed")
        exit(1)
except Exception as e:
    logger.error(f"Subscription process failed: {str(e)}")
    exit(1)

# Start the streaming session
logger.info("Starting streaming session...")
try:
    client.start()
    logger.info("Streaming session started")
except Exception as e:
    logger.error(f"Failed to start streaming session: {str(e)}")
    exit(1)

# Wait for some data
logger.info("Waiting for data (30 seconds)...")
try:
    time.sleep(30)
except KeyboardInterrupt:
    logger.info("Interrupted by user")

# Stop the streaming session
logger.info("Stopping streaming session...")
try:
    client.stop()
    logger.info("Streaming session stopped")
except Exception as e:
    logger.error(f"Failed to stop streaming session: {str(e)}")

logger.info("Test completed")
