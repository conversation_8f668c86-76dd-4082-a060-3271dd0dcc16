<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <style>
        /* Apply theme variables */
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
            --gold: #FFD700;
        }

        /* Base styles */
        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            margin-top: 0;
        }

        /* Dashboard layout */
        .dashboard-header {
            background-color: var(--bg-sidebar);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .dashboard-icon {
            font-size: 2rem;
        }

        .dashboard-stats {
            display: flex;
            gap: 1.5rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border-radius: 0.5rem;
            padding: 0.75rem 1.25rem;
            min-width: 150px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border);
            display: flex;
            flex-direction: column;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            padding-bottom: 0.75rem;
        }

        .card-title {
            margin: 0;
            font-size: 1.25rem;
            color: var(--primary);
        }

        .chart-container {
            flex: 1;
            position: relative;
            min-height: 300px;
        }

        .dashboard-footer {
            background-color: var(--bg-sidebar);
            padding: 1rem 2rem;
            border-top: 1px solid var(--border);
            text-align: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Table styles */
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .performance-table th, .performance-table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }

        .performance-table th {
            background-color: rgba(42, 58, 90, 0.5);
            color: var(--primary);
            font-weight: 600;
        }

        .performance-table tr:hover {
            background-color: rgba(42, 58, 90, 0.3);
        }

        /* Utility classes */
        .positive {
            color: var(--success);
        }

        .negative {
            color: var(--danger);
        }

        .instrument-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .instrument-button {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            cursor: pointer;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .instrument-button:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary);
        }

        .instrument-button.active {
            background-color: var(--primary);
            border-color: var(--primary-light);
        }

        .mnq-color { color: #00ccff; }
        .mes-color { color: #ff3366; }
        .mgc-color { color: #FFD700; }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="dashboard-title">
            <div class="dashboard-icon">📊</div>
            <h1>Performance Dashboard</h1>
        </div>
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-label">Total P&L</div>
                <div class="stat-value positive" id="total-pnl">$0.00</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Daily Win Rate</div>
                <div class="stat-value" id="daily-win-rate">0.00%</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Weekly Win Rate</div>
                <div class="stat-value" id="weekly-win-rate">0.00%</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Avg Daily P&L</div>
                <div class="stat-value" id="avg-daily-pnl">$0.00</div>
            </div>
        </div>
    </div>

    <div class="instrument-selector" id="instrument-selector">
        <button class="instrument-button active" data-instrument="ALL">ALL SYMBOLS</button>
        <button class="instrument-button" data-instrument="MNQ">MNQ</button>
        <button class="instrument-button" data-instrument="MES">MES</button>
        <button class="instrument-button" data-instrument="MGC">MGC</button>
    </div>

    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Weekly Performance</h2>
            </div>
            <div class="chart-container">
                <canvas id="weeklyPerformanceChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Daily Performance</h2>
            </div>
            <div class="chart-container">
                <canvas id="dailyPerformanceChart"></canvas>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Weekly Performance Breakdown</h2>
            </div>
            <div style="overflow-x: auto;">
                <table class="performance-table" id="weekly-table">
                    <thead>
                        <tr>
                            <th>Week</th>
                            <th>MNQ P&L</th>
                            <th>MES P&L</th>
                            <th>MGC P&L</th>
                            <th>Total P&L</th>
                            <th>Win Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Weekly data will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
        <div class="dashboard-card">
            <div class="card-header">
                <h2 class="card-title">Daily Performance Breakdown</h2>
            </div>
            <div style="overflow-x: auto;">
                <table class="performance-table" id="daily-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>MNQ P&L</th>
                            <th>MES P&L</th>
                            <th>MGC P&L</th>
                            <th>Total P&L</th>
                            <th>Win Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Daily data will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="dashboard-footer" id="dashboard-footer">
        DASHBOARD UPDATED: <span id="update-time"></span> | TOTAL P&L: <span id="footer-pnl">$0.00</span> | DAILY WIN RATE: <span id="footer-daily-win">0.00%</span> | WEEKLY WIN RATE: <span id="footer-weekly-win">0.00%</span>
    </div>

    <script>
        // Initialize the dashboard when the DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Set update time
            const now = new Date();
            document.getElementById('update-time').textContent = now.toLocaleString();

            // Initialize the data loader
            const dataLoader = dashboardDataLoader;

            // Load data for all instruments
            dataLoader.loadAllData()
                .then(data => {
                    console.log('All data loaded successfully');
                    initializeDashboard(data);
                })
                .catch(error => {
                    console.error('Error loading data:', error);
                });
        });

        // Function to initialize the dashboard
        function initializeDashboard(data) {
            // Process the data for each instrument
            const instruments = Object.keys(data);
            let selectedInstrument = 'ALL';

            // Calculate performance metrics
            const performanceData = calculatePerformanceMetrics(data);

            // Update dashboard with initial data
            updateDashboard(performanceData, selectedInstrument);

            // Set up instrument selector
            const selectorButtons = document.querySelectorAll('.instrument-button');
            selectorButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active button
                    selectorButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Update selected instrument
                    selectedInstrument = this.getAttribute('data-instrument');

                    // Update dashboard
                    updateDashboard(performanceData, selectedInstrument);
                });
            });
        }

        // Function to calculate performance metrics
        function calculatePerformanceMetrics(data) {
            const instruments = Object.keys(data);
            const dailyPerformance = {};
            const weeklyPerformance = {};

            // Process daily PnL data for each instrument
            instruments.forEach(instrumentCode => {
                const dailyPnL = data[instrumentCode].dailyPnL || [];

                dailyPnL.forEach(day => {
                    const date = day.Date;
                    const weekKey = getWeekFromDate(new Date(date));

                    // Initialize daily entry if not exists
                    if (!dailyPerformance[date]) {
                        dailyPerformance[date] = {
                            date,
                            totalPnL: 0,
                            instrumentPnL: {},
                            trades: 0,
                            wins: 0
                        };
                    }

                    // Initialize weekly entry if not exists
                    if (!weeklyPerformance[weekKey]) {
                        weeklyPerformance[weekKey] = {
                            week: weekKey,
                            totalPnL: 0,
                            instrumentPnL: {},
                            trades: 0,
                            wins: 0
                        };
                    }

                    // Add PnL for this instrument
                    const pnl = parseFloat(day.PnL) || 0;
                    dailyPerformance[date].instrumentPnL[instrumentCode] = pnl;
                    dailyPerformance[date].totalPnL += pnl;

                    weeklyPerformance[weekKey].instrumentPnL[instrumentCode] =
                        (weeklyPerformance[weekKey].instrumentPnL[instrumentCode] || 0) + pnl;
                    weeklyPerformance[weekKey].totalPnL += pnl;

                    // Add trade counts
                    const trades = parseInt(day.Trades) || 0;
                    const wins = parseInt(day.Wins) || 0;

                    dailyPerformance[date].trades += trades;
                    dailyPerformance[date].wins += wins;

                    weeklyPerformance[weekKey].trades += trades;
                    weeklyPerformance[weekKey].wins += wins;
                });
            });

            // Calculate win rates
            Object.values(dailyPerformance).forEach(day => {
                day.winRate = day.trades > 0 ? (day.wins / day.trades * 100).toFixed(2) : '0.00';
            });

            Object.values(weeklyPerformance).forEach(week => {
                week.winRate = week.trades > 0 ? (week.wins / week.trades * 100).toFixed(2) : '0.00';
            });

            // Convert to arrays and sort
            const dailyArray = Object.values(dailyPerformance).sort((a, b) =>
                new Date(a.date) - new Date(b.date)
            );

            const weeklyArray = Object.values(weeklyPerformance).sort((a, b) =>
                a.week.localeCompare(b.week)
            );

            // Calculate summary metrics
            const totalPnL = dailyArray.reduce((sum, day) => sum + day.totalPnL, 0);
            const profitableDays = dailyArray.filter(day => day.totalPnL > 0).length;
            const dailyWinRate = (profitableDays / dailyArray.length * 100).toFixed(2);

            const profitableWeeks = weeklyArray.filter(week => week.totalPnL > 0).length;
            const weeklyWinRate = (profitableWeeks / weeklyArray.length * 100).toFixed(2);

            const avgDailyPnL = (totalPnL / dailyArray.length).toFixed(2);
            const avgWeeklyPnL = (totalPnL / weeklyArray.length).toFixed(2);

            return {
                daily: dailyArray,
                weekly: weeklyArray,
                summary: {
                    totalPnL,
                    dailyWinRate,
                    weeklyWinRate,
                    avgDailyPnL,
                    avgWeeklyPnL,
                    profitableDays,
                    totalDays: dailyArray.length,
                    profitableWeeks,
                    totalWeeks: weeklyArray.length
                }
            };
        }

        // Function to update the dashboard
        function updateDashboard(performanceData, selectedInstrument) {
            // Update summary stats
            updateSummaryStats(performanceData.summary);

            // Update charts
            updateCharts(performanceData, selectedInstrument);

            // Update tables
            updateTables(performanceData, selectedInstrument);
        }

        // Function to update summary statistics
        function updateSummaryStats(summary) {
            document.getElementById('total-pnl').textContent = formatCurrency(summary.totalPnL);
            document.getElementById('daily-win-rate').textContent = summary.dailyWinRate + '%';
            document.getElementById('weekly-win-rate').textContent = summary.weeklyWinRate + '%';
            document.getElementById('avg-daily-pnl').textContent = formatCurrency(summary.avgDailyPnL);

            // Update footer
            document.getElementById('footer-pnl').textContent = formatCurrency(summary.totalPnL);
            document.getElementById('footer-daily-win').textContent = summary.dailyWinRate + '%';
            document.getElementById('footer-weekly-win').textContent = summary.weeklyWinRate + '%';

            // Set color classes based on values
            setColorClass('total-pnl', summary.totalPnL);
            setColorClass('avg-daily-pnl', summary.avgDailyPnL);
        }

        // Function to update charts
        function updateCharts(performanceData, selectedInstrument) {
            // Weekly performance chart
            updateWeeklyChart(performanceData.weekly, selectedInstrument);

            // Daily performance chart
            updateDailyChart(performanceData.daily, selectedInstrument);
        }

        // Function to update weekly performance chart
        function updateWeeklyChart(weeklyData, selectedInstrument) {
            const ctx = document.getElementById('weeklyPerformanceChart').getContext('2d');

            // Prepare data based on selected instrument
            let labels = [];
            let datasets = [];

            if (selectedInstrument === 'ALL') {
                // Show all instruments
                labels = weeklyData.map(week => week.week);

                // MNQ dataset
                datasets.push({
                    label: 'MNQ',
                    data: weeklyData.map(week => week.instrumentPnL.MNQ || 0),
                    borderColor: '#00ccff',
                    backgroundColor: 'rgba(0, 204, 255, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });

                // MES dataset
                datasets.push({
                    label: 'MES',
                    data: weeklyData.map(week => week.instrumentPnL.MES || 0),
                    borderColor: '#ff3366',
                    backgroundColor: 'rgba(255, 51, 102, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });

                // MGC dataset
                datasets.push({
                    label: 'MGC',
                    data: weeklyData.map(week => week.instrumentPnL.MGC || 0),
                    borderColor: '#FFD700',
                    backgroundColor: 'rgba(255, 215, 0, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });

                // Total dataset
                datasets.push({
                    label: 'Total',
                    data: weeklyData.map(week => week.totalPnL),
                    borderColor: '#00ff88',
                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                });
            } else {
                // Show only selected instrument
                labels = weeklyData.map(week => week.week);

                const color = getInstrumentColor(selectedInstrument);

                datasets.push({
                    label: selectedInstrument,
                    data: weeklyData.map(week => week.instrumentPnL[selectedInstrument] || 0),
                    borderColor: color,
                    backgroundColor: hexToRgba(color, 0.1),
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                });
            }

            // Create or update chart
            if (window.weeklyChart) {
                window.weeklyChart.data.labels = labels;
                window.weeklyChart.data.datasets = datasets;
                window.weeklyChart.update();
            } else {
                window.weeklyChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#f8fafc',
                                    font: {
                                        family: 'Rajdhani'
                                    }
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                backgroundColor: 'rgba(20, 27, 45, 0.9)',
                                titleColor: '#f8fafc',
                                bodyColor: '#f8fafc',
                                borderColor: '#2a3a5a',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += formatCurrency(context.raw);
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                grid: {
                                    color: 'rgba(42, 58, 90, 0.5)'
                                },
                                ticks: {
                                    color: '#94a3b8',
                                    callback: function(value) {
                                        return formatCurrency(value);
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(42, 58, 90, 0.5)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Function to update daily performance chart
        function updateDailyChart(dailyData, selectedInstrument) {
            const ctx = document.getElementById('dailyPerformanceChart').getContext('2d');

            // Prepare data based on selected instrument
            let labels = [];
            let datasets = [];

            // Get the last 30 days of data
            const recentDailyData = dailyData.slice(-30);

            if (selectedInstrument === 'ALL') {
                // Show all instruments
                labels = recentDailyData.map(day => day.date);

                // MNQ dataset
                datasets.push({
                    label: 'MNQ',
                    data: recentDailyData.map(day => day.instrumentPnL.MNQ || 0),
                    borderColor: '#00ccff',
                    backgroundColor: 'rgba(0, 204, 255, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });

                // MES dataset
                datasets.push({
                    label: 'MES',
                    data: recentDailyData.map(day => day.instrumentPnL.MES || 0),
                    borderColor: '#ff3366',
                    backgroundColor: 'rgba(255, 51, 102, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });

                // MGC dataset
                datasets.push({
                    label: 'MGC',
                    data: recentDailyData.map(day => day.instrumentPnL.MGC || 0),
                    borderColor: '#FFD700',
                    backgroundColor: 'rgba(255, 215, 0, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                });

                // Total dataset
                datasets.push({
                    label: 'Total',
                    data: recentDailyData.map(day => day.totalPnL),
                    borderColor: '#00ff88',
                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                });
            } else {
                // Show only selected instrument
                labels = recentDailyData.map(day => day.date);

                const color = getInstrumentColor(selectedInstrument);

                datasets.push({
                    label: selectedInstrument,
                    data: recentDailyData.map(day => day.instrumentPnL[selectedInstrument] || 0),
                    borderColor: color,
                    backgroundColor: hexToRgba(color, 0.1),
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                });
            }

            // Create or update chart
            if (window.dailyChart) {
                window.dailyChart.data.labels = labels;
                window.dailyChart.data.datasets = datasets;
                window.dailyChart.update();
            } else {
                window.dailyChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#f8fafc',
                                    font: {
                                        family: 'Rajdhani'
                                    }
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                backgroundColor: 'rgba(20, 27, 45, 0.9)',
                                titleColor: '#f8fafc',
                                bodyColor: '#f8fafc',
                                borderColor: '#2a3a5a',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += formatCurrency(context.raw);
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                grid: {
                                    color: 'rgba(42, 58, 90, 0.5)'
                                },
                                ticks: {
                                    color: '#94a3b8',
                                    callback: function(value) {
                                        return formatCurrency(value);
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(42, 58, 90, 0.5)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Function to update tables
        function updateTables(performanceData, selectedInstrument) {
            // Update weekly table
            updateWeeklyTable(performanceData.weekly, selectedInstrument);

            // Update daily table
            updateDailyTable(performanceData.daily, selectedInstrument);
        }

        // Function to update weekly table
        function updateWeeklyTable(weeklyData, selectedInstrument) {
            const tableBody = document.querySelector('#weekly-table tbody');
            tableBody.innerHTML = '';

            // Get the last 12 weeks of data
            const recentWeeklyData = weeklyData.slice(-12).reverse();

            recentWeeklyData.forEach(week => {
                const row = document.createElement('tr');

                // Week column
                const weekCell = document.createElement('td');
                weekCell.textContent = week.week;
                row.appendChild(weekCell);

                // MNQ P&L column
                const mnqCell = document.createElement('td');
                mnqCell.textContent = formatCurrency(week.instrumentPnL.MNQ || 0);
                setElementColor(mnqCell, week.instrumentPnL.MNQ || 0);
                row.appendChild(mnqCell);

                // MES P&L column
                const mesCell = document.createElement('td');
                mesCell.textContent = formatCurrency(week.instrumentPnL.MES || 0);
                setElementColor(mesCell, week.instrumentPnL.MES || 0);
                row.appendChild(mesCell);

                // MGC P&L column
                const mgcCell = document.createElement('td');
                mgcCell.textContent = formatCurrency(week.instrumentPnL.MGC || 0);
                setElementColor(mgcCell, week.instrumentPnL.MGC || 0);
                row.appendChild(mgcCell);

                // Total P&L column
                const totalCell = document.createElement('td');
                totalCell.textContent = formatCurrency(week.totalPnL);
                setElementColor(totalCell, week.totalPnL);
                row.appendChild(totalCell);

                // Win Rate column
                const winRateCell = document.createElement('td');
                winRateCell.textContent = week.winRate + '%';
                row.appendChild(winRateCell);

                tableBody.appendChild(row);
            });
        }

        // Function to update daily table
        function updateDailyTable(dailyData, selectedInstrument) {
            const tableBody = document.querySelector('#daily-table tbody');
            tableBody.innerHTML = '';

            // Get the last 14 days of data
            const recentDailyData = dailyData.slice(-14).reverse();

            recentDailyData.forEach(day => {
                const row = document.createElement('tr');

                // Date column
                const dateCell = document.createElement('td');
                dateCell.textContent = formatDate(day.date);
                row.appendChild(dateCell);

                // MNQ P&L column
                const mnqCell = document.createElement('td');
                mnqCell.textContent = formatCurrency(day.instrumentPnL.MNQ || 0);
                setElementColor(mnqCell, day.instrumentPnL.MNQ || 0);
                row.appendChild(mnqCell);

                // MES P&L column
                const mesCell = document.createElement('td');
                mesCell.textContent = formatCurrency(day.instrumentPnL.MES || 0);
                setElementColor(mesCell, day.instrumentPnL.MES || 0);
                row.appendChild(mesCell);

                // MGC P&L column
                const mgcCell = document.createElement('td');
                mgcCell.textContent = formatCurrency(day.instrumentPnL.MGC || 0);
                setElementColor(mgcCell, day.instrumentPnL.MGC || 0);
                row.appendChild(mgcCell);

                // Total P&L column
                const totalCell = document.createElement('td');
                totalCell.textContent = formatCurrency(day.totalPnL);
                setElementColor(totalCell, day.totalPnL);
                row.appendChild(totalCell);

                // Win Rate column
                const winRateCell = document.createElement('td');
                winRateCell.textContent = day.winRate + '%';
                row.appendChild(winRateCell);

                tableBody.appendChild(row);
            });
        }

        // Helper function to get week number from date
        function getWeekFromDate(date) {
            const d = new Date(date);
            d.setHours(0, 0, 0, 0);
            d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
            const week = Math.floor((d.getTime() - new Date(d.getFullYear(), 0, 4).getTime()) / 86400000 / 7) + 1;
            return `${d.getFullYear()}-W${week.toString().padStart(2, '0')}`;
        }

        // Helper function to format currency
        function formatCurrency(value) {
            return '$' + parseFloat(value).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // Helper function to format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Helper function to set color class based on value
        function setColorClass(elementId, value) {
            const element = document.getElementById(elementId);
            if (parseFloat(value) >= 0) {
                element.classList.add('positive');
                element.classList.remove('negative');
            } else {
                element.classList.add('negative');
                element.classList.remove('positive');
            }
        }

        // Helper function to set element color based on value
        function setElementColor(element, value) {
            if (parseFloat(value) >= 0) {
                element.classList.add('positive');
                element.classList.remove('negative');
            } else {
                element.classList.add('negative');
                element.classList.remove('positive');
            }
        }

        // Helper function to get instrument color
        function getInstrumentColor(instrumentCode) {
            switch (instrumentCode) {
                case 'MNQ':
                    return '#00ccff';
                case 'MES':
                    return '#ff3366';
                case 'MGC':
                    return '#FFD700';
                default:
                    return '#00ff88';
            }
        }

        // Helper function to convert hex color to rgba
        function hexToRgba(hex, alpha) {
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
    </script>
</body>
</html>
