/**
 * Test script for Databento integration
 */

// Load environment variables from .env file
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Debug: Check if .env file exists
const envPath = path.resolve(__dirname, '.env');
console.log(`Checking for .env file at: ${envPath}`);
console.log(`File exists: ${fs.existsSync(envPath)}`);

// If .env file exists, show its content (excluding sensitive data)
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const safeContent = envContent.replace(/DATABENTO_API_KEY=.*/g, 'DATABENTO_API_KEY=***');
  console.log('Content of .env file:');
  console.log(safeContent);
}

// Load environment variables
const result = dotenv.config();
console.log('dotenv.config() result:', result.error ? result.error.message : 'Success');

// Debug: Show all environment variables (excluding sensitive data)
console.log('Environment variables:');
Object.keys(process.env).forEach(key => {
  if (key.includes('KEY') || key.includes('PASSWORD') || key.includes('SECRET')) {
    console.log(`${key}=***`);
  } else {
    console.log(`${key}=${process.env[key]}`);
  }
});

const DabentoClient = require('./databento');
const logger = require('./logger');

// Check if API key is available
if (!process.env.DATABENTO_API_KEY || process.env.DATABENTO_API_KEY === 'your_databento_api_key') {
  console.error('DATABENTO_API_KEY environment variable is required. Please update your .env file with your actual Databento API key.');
  process.exit(1);
}

console.log(`Using Databento API key: ${process.env.DATABENTO_API_KEY.substring(0, 5)}...`);

// Create Databento client
const client = new DabentoClient(process.env.DATABENTO_API_KEY);

// Test functions
async function testHistorical() {
  try {
    console.log('Testing historical data...');

    // Get available datasets
    console.log('Getting available datasets...');
    const datasets = await client.historical.getDatasets();
    console.log(`Found ${datasets.length} datasets`);
    datasets.forEach(dataset => {
      console.log(`- ${dataset.dataset}: ${dataset.description}`);
    });

    // Get symbols for GLBX.MDP3 dataset
    console.log('\nGetting symbols for GLBX.MDP3 dataset...');
    const symbols = await client.historical.getSymbols('GLBX.MDP3');
    console.log(`Found ${symbols.length} symbols`);

    // Find our target symbols
    const targetSymbols = ['MNQ.FUT', 'ES.FUT', 'GC.FUT', 'RTY.FUT'];
    const foundSymbols = symbols.filter(s => targetSymbols.includes(s.symbol));
    console.log('\nFound target symbols:');
    foundSymbols.forEach(s => {
      console.log(`- ${s.symbol}: ${s.description}`);
    });

    // Get historical data for MNQ
    console.log('\nGetting historical data for MNQ.FUT...');
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 1); // 1 day ago

    const historicalData = await client.historical.getBars(
      'GLBX.MDP3',
      'MNQ.FUT',
      startDate,
      endDate,
      '1m'
    );

    console.log(`Got ${historicalData.data ? historicalData.data.length : 0} bars`);
    if (historicalData.data && historicalData.data.length > 0) {
      console.log('Sample data:');
      console.log(historicalData.data.slice(0, 3));
    }

    return true;
  } catch (error) {
    console.error('Error testing historical data:', error);
    return false;
  }
}

async function testLiveWebSocket() {
  try {
    console.log('\nTesting live data via WebSocket...');

    // Connect to live data
    console.log('Connecting to live data...');
    client.live.on('connected', () => {
      console.log('Connected to Databento live data');
    });

    client.live.on('error', (error) => {
      console.error('Live data error:', error);
    });

    client.live.on('disconnected', () => {
      console.log('Disconnected from Databento live data');
    });

    client.live.on('heartbeat', () => {
      console.log('Received heartbeat');
    });

    await client.live.connect();

    // Subscribe to MNQ
    console.log('Subscribing to MNQ.FUT...');
    await client.live.subscribe(
      'GLBX.MDP3',
      'MNQ.FUT',
      'trades',
      { stype_in: 'parent' }
    );

    // Set up data handler
    let messageCount = 0;
    const dataHandler = (data) => {
      messageCount++;
      if (messageCount <= 5) {
        console.log('Received data:', data);
      }
    };

    client.live.on('data', dataHandler);

    // Wait for some data
    console.log('Waiting for data (30 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    console.log(`Received ${messageCount} messages`);

    // Unsubscribe and disconnect
    console.log('Unsubscribing and disconnecting...');
    client.live.unsubscribe('MNQ.FUT');
    client.live.removeListener('data', dataHandler);
    client.live.disconnect();

    return true;
  } catch (error) {
    console.error('Error testing live data:', error);
    return false;
  }
}

async function testReference() {
  try {
    console.log('\nTesting reference data...');

    // Get security master data for MNQ
    console.log('Getting security master data for MNQ.FUT...');
    const securityMaster = await client.reference.securityMaster.getLast('MNQ.FUT', {
      stype_in: 'parent'
    });

    console.log('Security master data:');
    console.log(securityMaster);

    return true;
  } catch (error) {
    console.error('Error testing reference data:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('Starting Databento integration tests...');
  console.log('API Key:', process.env.DATABENTO_API_KEY.substring(0, 5) + '...');

  let success = true;

  // Test historical data
  if (await testHistorical()) {
    console.log('\n✅ Historical data test passed');
  } else {
    console.log('\n❌ Historical data test failed');
    success = false;
  }

  // Test live data
  if (await testLiveWebSocket()) {
    console.log('\n✅ Live data test passed');
  } else {
    console.log('\n❌ Live data test failed');
    success = false;
  }

  // Test reference data
  if (await testReference()) {
    console.log('\n✅ Reference data test passed');
  } else {
    console.log('\n❌ Reference data test failed');
    success = false;
  }

  console.log('\nTests completed.');
  if (success) {
    console.log('All tests passed! 🎉');
  } else {
    console.log('Some tests failed. 😢');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
