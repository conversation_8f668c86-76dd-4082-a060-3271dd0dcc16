/**
 * mes-comparison-test.js
 * Compare different MES configurations
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');
const { mesConfig } = require('../multi_symbol_config');

// Original MES configuration
const originalMesConfig = {
    ...mesConfig,
    slFactors: 5.5,
    tpFactors: 4.0,
    trailFactors: 0.08,
    atrThresholds: {
        low_medium: 4.7601,
        medium_high: 7.2605
    },
    adaptiveParams: {
        Low: {
            slFactor: 5.5,
            tpFactor: 4.0,
            trailFactor: 0.08
        },
        Medium: {
            slFactor: 5.5,
            tpFactor: 4.0,
            trailFactor: 0.08
        },
        High: {
            slFactor: 5.5,
            tpFactor: 4.0,
            trailFactor: 0.08
        }
    }
};

// New MES configuration
const newMesConfig = {
    ...mesConfig
};

// Function to run test with a specific configuration
async function runConfigTest(config, name) {
    console.log(`\n=== RUNNING TEST FOR MES WITH ${name} CONFIGURATION ===\n`);
    
    console.log("Configuration:");
    console.log(`Symbol: ${config.symbol}`);
    console.log(`SL Factor: ${config.slFactors}`);
    console.log(`TP Factor: ${config.tpFactors}`);
    console.log(`Trail Factor: ${config.trailFactors}`);
    console.log(`Fixed TP Points: ${config.fixedTpPoints}`);
    console.log(`ATR Low-Medium: ${config.atrThresholds.low_medium}`);
    console.log(`ATR Medium-High: ${config.atrThresholds.medium_high}`);
    
    // Run backtest
    const result = await runBacktest(config);
    
    // Calculate win rate
    const winRate = (result.wins / result.totalTrades * 100).toFixed(2);
    
    // Calculate P&L
    const pnl = result.finalBalance - config.initialBalance;
    
    // Display results
    console.log(`\nResults:`);
    console.log(`Total trades: ${result.totalTrades}`);
    console.log(`Wins: ${result.wins} (${winRate}%)`);
    console.log(`Losses: ${result.losses} (${(100 - parseFloat(winRate)).toFixed(2)}%)`);
    console.log(`P&L: $${pnl.toFixed(2)}`);
    console.log(`Max drawdown: $${result.maxDrawdown.toFixed(2)}`);
    
    return {
        name,
        symbol: config.symbol,
        slFactor: config.slFactors,
        tpFactor: config.tpFactors,
        trailFactor: config.trailFactors,
        atrLowMedium: config.atrThresholds.low_medium,
        atrMediumHigh: config.atrThresholds.medium_high,
        totalTrades: result.totalTrades,
        wins: result.wins,
        losses: result.losses,
        winRate: `${winRate}%`,
        pnl: pnl.toFixed(2),
        maxDrawdown: result.maxDrawdown.toFixed(2)
    };
}

// Main function to run all tests
async function runAllTests() {
    const results = [];
    
    // Run test with original configuration
    const originalResult = await runConfigTest(originalMesConfig, "ORIGINAL");
    results.push(originalResult);
    
    // Run test with new configuration
    const newResult = await runConfigTest(newMesConfig, "NEW");
    results.push(newResult);
    
    // Display summary table
    console.log("\n=== MES CONFIGURATION COMPARISON ===\n");
    console.table(results);
    
    // Save results to file
    const resultsFile = path.join(__dirname, 'mes_comparison_results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    console.log(`\nResults saved to ${resultsFile}`);
    
    return results;
}

// Run all tests
runAllTests().catch(console.error);
