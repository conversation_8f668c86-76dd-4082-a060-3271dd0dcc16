#!/usr/bin/env python3
"""
Extract and analyze Databento data file.
"""

import os
import sys
import zstandard as zstd
import pandas as pd
import csv
import io
from datetime import datetime

# Configure paths
input_file = "C:\\backtest-bot\\input\\glbx-mdp3-20250101-20250511.ohlcv-1m.csv.zst"
output_dir = "C:\\backtest-bot\\input"

def decompress_zst(input_path):
    """
    Decompress a .zst file and return its content as a string.
    """
    try:
        with open(input_path, 'rb') as compressed_file:
            dctx = zstd.ZstdDecompressor()
            decompressed_data = dctx.decompress(compressed_file.read())
            return decompressed_data.decode('utf-8')
    except Exception as e:
        print(f"Error decompressing file: {e}")
        return None

def analyze_csv_content(csv_content):
    """
    Analyze CSV content to understand its structure.
    """
    try:
        # Read the first few lines to understand the structure
        lines = csv_content.split('\n')
        if len(lines) < 2:
            print("CSV file appears to be empty or has only a header.")
            return None
        
        print(f"Total lines in CSV: {len(lines)}")
        print(f"Header: {lines[0]}")
        print(f"First data row: {lines[1]}")
        
        # Try to parse as a DataFrame
        df = pd.read_csv(io.StringIO(csv_content))
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {df.columns.tolist()}")
        
        # Check for symbol column
        if 'symbol' in df.columns:
            symbols = df['symbol'].unique()
            print(f"Unique symbols: {symbols}")
            
            # Count records per symbol
            for symbol in symbols:
                count = len(df[df['symbol'] == symbol])
                print(f"Records for {symbol}: {count}")
        
        return df
    except Exception as e:
        print(f"Error analyzing CSV content: {e}")
        return None

def extract_symbol_data(df, symbol, output_path):
    """
    Extract data for a specific symbol and save to CSV.
    """
    try:
        if 'symbol' not in df.columns:
            print("No 'symbol' column found in DataFrame.")
            return False
        
        # Filter for the symbol
        symbol_df = df[df['symbol'] == symbol].copy()
        
        if len(symbol_df) == 0:
            print(f"No data found for symbol {symbol}")
            return False
        
        print(f"Extracted {len(symbol_df)} records for {symbol}")
        
        # Check if we have timestamp and OHLCV columns
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in symbol_df.columns]
        
        if missing_columns:
            print(f"Missing required columns: {missing_columns}")
            
            # Try to map columns
            column_map = {}
            for col in df.columns:
                col_lower = col.lower()
                if 'time' in col_lower or 'date' in col_lower:
                    column_map[col] = 'timestamp'
                elif 'open' in col_lower:
                    column_map[col] = 'open'
                elif 'high' in col_lower:
                    column_map[col] = 'high'
                elif 'low' in col_lower:
                    column_map[col] = 'low'
                elif 'close' in col_lower or 'last' in col_lower:
                    column_map[col] = 'close'
                elif 'vol' in col_lower:
                    column_map[col] = 'volume'
            
            # Rename columns
            symbol_df = symbol_df.rename(columns=column_map)
            
            # Check again for missing columns
            missing_columns = [col for col in required_columns if col not in symbol_df.columns]
            if missing_columns:
                print(f"Still missing required columns after mapping: {missing_columns}")
                return False
        
        # Ensure timestamp is in the correct format
        if 'timestamp' in symbol_df.columns:
            # Try to parse timestamp
            try:
                if isinstance(symbol_df['timestamp'].iloc[0], (int, float)):
                    # Assume nanoseconds
                    symbol_df['timestamp'] = pd.to_datetime(symbol_df['timestamp'], unit='ns')
                else:
                    symbol_df['timestamp'] = pd.to_datetime(symbol_df['timestamp'])
                
                symbol_df['timestamp'] = symbol_df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')
            except Exception as e:
                print(f"Error parsing timestamp: {e}")
        
        # Save to CSV
        symbol_df.to_csv(output_path, index=False)
        print(f"Saved data to {output_path}")
        
        return True
    except Exception as e:
        print(f"Error extracting symbol data: {e}")
        return False

def main():
    """
    Main function.
    """
    print(f"Extracting and analyzing Databento data file: {input_file}")
    
    # Check if file exists
    if not os.path.exists(input_file):
        print(f"File not found: {input_file}")
        return
    
    # Decompress the file
    print("Decompressing file...")
    csv_content = decompress_zst(input_file)
    if not csv_content:
        print("Failed to decompress file.")
        return
    
    # Analyze the CSV content
    print("\nAnalyzing CSV content...")
    df = analyze_csv_content(csv_content)
    if df is None:
        print("Failed to analyze CSV content.")
        return
    
    # Extract data for each symbol
    symbols_to_extract = ['MNQ', 'MES', 'MGC', 'M2K']
    
    for symbol in symbols_to_extract:
        print(f"\nExtracting data for {symbol}...")
        output_path = os.path.join(output_dir, f"{symbol}_1m.csv")
        success = extract_symbol_data(df, symbol, output_path)
        
        if not success:
            print(f"Failed to extract data for {symbol}")
    
    print("\nExtraction complete!")

if __name__ == "__main__":
    main()
