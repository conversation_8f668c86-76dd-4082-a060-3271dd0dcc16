/**
 * EURUSD Optimized Trading Configuration
 *
 * This file contains the optimized configuration for trading EURUSD forex pair.
 * Parameters are based on experience with other markets and forex trading characteristics.
 */

module.exports = {
    // Symbol information
    symbol: 'EURUSD',
    pointValue: 1.0,     // Each pip is worth $1 per standard lot (100,000 units)
    tickSize: 0.0001,    // Forex typically has 4 or 5 decimal places for major pairs
    
    // Contract information
    contractMonth: null, // Not applicable for forex
    
    // Account information
    accountId: null, // Will be set during initialization
    
    // Commission and slippage
    commission: 0.0,     // Forex typically has no commission but uses spread
    commissionPerContract: 0.0,
    slippage: 0.0002,    // 2 pips of slippage (typical for EURUSD)
    slippagePoints: 0.0002,
    
    // Position sizing
    fixedContracts: 10,  // Will represent mini lots (10,000 units each)
    
    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    sma200Period: 0, // Not used
    atrPeriod: 14,
    
    // Entry/exit parameters - Optimized values based on experience
    slFactors: 5.0,      // 5x ATR for stop loss
    tpFactors: 4.0,      // 4x ATR for take profit
    trailFactors: 0.03,  // 3% trail factor
    fixedTpPoints: 0,    // No fixed take profit points
    
    // Risk management
    maxDailyLoss: 0.10, // 10% of account
    
    // Filters
    minAtrEntry: 0.0005, // Minimum ATR for entry (5 pips)
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    useTwoBarColorExit: false,
    
    // ATR thresholds for adaptive mode
    isAdaptiveRun: true, // Enable adaptive mode
    atrThresholds: {
        low_medium: 0.0010, // 10 pips
        medium_high: 0.0020 // 20 pips
    },
    
    // Adaptive parameters for different volatility regimes
    adaptiveParams: {
        Low: { 
            slFactor: 4.0, 
            tpFactor: 3.0, 
            trailFactor: 0.02 
        },
        Medium: { 
            slFactor: 5.0, 
            tpFactor: 4.0, 
            trailFactor: 0.03 
        },
        High: { 
            slFactor: 6.0, 
            tpFactor: 5.0, 
            trailFactor: 0.05 
        }
    },
    
    // Formatting
    pricePrecision: 5,
    
    // Logging
    logLevel: 'info',
    
    // Time filter settings
    timeFilterEnabled: false, // Disable time filtering for 24/5 trading
    activeHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
};
