/**
 * Fix for Explicit Test Order Functionality
 * 
 * This script fixes the issue with the explicit test order functionality
 * when pressing 'e' in the trading bot.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');
const explicitTestOrder = require('./test_order_explicit');

// Configure API with demo mode
tradovateApi.setConfig({
    baseUrl: 'https://demo.tradovateapi.com/v1',
    wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
    mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
});

// Run the test
async function runTest() {
    try {
        console.log('Running explicit test order with detailed logging...');
        
        // Call the explicit test order function
        const result = await explicitTestOrder.placeExplicitTestOrder();
        
        if (result) {
            console.log('\nExplicit test order placed successfully!');
        } else {
            console.log('\nExplicit test order failed. See logs above for details.');
        }
        
        return result;
    } catch (error) {
        console.error(`\nUnhandled error: ${error.message}`);
        return false;
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    runTest()
        .then(result => {
            process.exit(result ? 0 : 1);
        })
        .catch(error => {
            console.error(`Unhandled error: ${error.message}`);
            process.exit(1);
        });
}

module.exports = {
    runTest
};
