<!DOCTYPE html>
<html>
<head>
    <title>Create App Icons</title>
</head>
<body>
    <h2>Golf Tracker App Icons</h2>
    <p>Right-click each icon and "Save image as..." to save as PNG files</p>
    
    <h3>192x192 Icon (save as icon-192.png)</h3>
    <canvas id="icon192" width="192" height="192" style="border: 1px solid #ccc;"></canvas>
    
    <h3>512x512 Icon (save as icon-512.png)</h3>
    <canvas id="icon512" width="512" height="512" style="border: 1px solid #ccc;"></canvas>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#2e7d32';
            ctx.fillRect(0, 0, size, size);
            
            // Golf ball
            const ballSize = size * 0.3;
            const ballX = size * 0.5;
            const ballY = size * 0.4;
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(ballX, ballY, ballSize/2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Golf ball dimples
            ctx.fillStyle = '#ddd';
            const dimpleSize = size * 0.02;
            for (let i = 0; i < 8; i++) {
                const angle = (i / 8) * 2 * Math.PI;
                const dimpleX = ballX + Math.cos(angle) * ballSize * 0.2;
                const dimpleY = ballY + Math.sin(angle) * ballSize * 0.2;
                ctx.beginPath();
                ctx.arc(dimpleX, dimpleY, dimpleSize, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // Golf club (simplified)
            ctx.strokeStyle = '#8d6e63';
            ctx.lineWidth = size * 0.02;
            ctx.beginPath();
            ctx.moveTo(size * 0.2, size * 0.8);
            ctx.lineTo(size * 0.45, size * 0.55);
            ctx.stroke();
            
            // Club head
            ctx.fillStyle = '#8d6e63';
            ctx.fillRect(size * 0.42, size * 0.52, size * 0.08, size * 0.06);
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.08}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('GOLF', size * 0.5, size * 0.75);
            ctx.fillText('TRACKER', size * 0.5, size * 0.85);
        }
        
        // Draw both icons
        drawIcon(document.getElementById('icon192'), 192);
        drawIcon(document.getElementById('icon512'), 512);
        
        // Add download functionality
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        document.getElementById('icon192').addEventListener('click', () => {
            downloadCanvas(document.getElementById('icon192'), 'icon-192.png');
        });
        
        document.getElementById('icon512').addEventListener('click', () => {
            downloadCanvas(document.getElementById('icon512'), 'icon-512.png');
        });
    </script>
</body>
</html>
