#!/usr/bin/env python3
"""
Prepare data files for use with the trading bot.
"""

import os
import sys
import glob
import logging
import pandas as pd
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def list_data_files(directory):
    """
    List all data files in the directory.
    
    Args:
        directory (str): Directory to search
    
    Returns:
        dict: Dictionary of file types and counts
    """
    # Get all files
    all_files = os.listdir(directory)
    
    # Count file types
    file_types = {}
    for file in all_files:
        ext = os.path.splitext(file)[1].lower()
        if ext not in file_types:
            file_types[ext] = []
        file_types[ext].append(file)
    
    # Print summary
    logger.info(f"Found {len(all_files)} files in {directory}")
    for ext, files in file_types.items():
        logger.info(f"- {ext}: {len(files)} files")
    
    return file_types

def process_csv_files(directory, symbols):
    """
    Process CSV files for the specified symbols.
    
    Args:
        directory (str): Directory containing CSV files
        symbols (list): List of symbols to process
    
    Returns:
        dict: Dictionary of processed files by symbol
    """
    # Find all CSV files
    csv_files = glob.glob(os.path.join(directory, "*.csv"))
    logger.info(f"Found {len(csv_files)} CSV files")
    
    # Group files by symbol
    symbol_files = {}
    for symbol in symbols:
        symbol_files[symbol] = []
        
        # Find files for this symbol
        for file in csv_files:
            filename = os.path.basename(file)
            if symbol in filename:
                symbol_files[symbol].append(file)
        
        logger.info(f"Found {len(symbol_files[symbol])} files for {symbol}")
    
    # Process each symbol
    processed_files = {}
    for symbol, files in symbol_files.items():
        if not files:
            logger.warning(f"No files found for {symbol}")
            continue
        
        # Find the best file for this symbol
        best_file = find_best_file(files, symbol)
        if not best_file:
            logger.warning(f"No suitable file found for {symbol}")
            continue
        
        # Process the file
        processed_file = process_csv_file(best_file, symbol, directory)
        if processed_file:
            processed_files[symbol] = processed_file
    
    return processed_files

def find_best_file(files, symbol):
    """
    Find the best file for a symbol based on size and name.
    
    Args:
        files (list): List of files
        symbol (str): Symbol to find file for
    
    Returns:
        str: Path to the best file
    """
    if not files:
        return None
    
    # Prefer files with _1m in the name
    minute_files = [f for f in files if "_1m" in os.path.basename(f)]
    if minute_files:
        # Return the largest file
        return max(minute_files, key=os.path.getsize)
    
    # Prefer files with the symbol as a prefix
    prefix_files = [f for f in files if os.path.basename(f).startswith(symbol)]
    if prefix_files:
        # Return the largest file
        return max(prefix_files, key=os.path.getsize)
    
    # Return the largest file
    return max(files, key=os.path.getsize)

def process_csv_file(file_path, symbol, output_dir):
    """
    Process a CSV file for use with the trading bot.
    
    Args:
        file_path (str): Path to the CSV file
        symbol (str): Symbol to process
        output_dir (str): Directory to save the processed file
    
    Returns:
        str: Path to the processed file
    """
    try:
        logger.info(f"Processing {file_path}...")
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        
        # Check if DataFrame is empty
        if df.empty:
            logger.warning(f"No data found in {file_path}")
            return None
        
        logger.info(f"Read {len(df)} rows from {file_path}")
        logger.info(f"Columns: {df.columns.tolist()}")
        
        # Check if we have the required columns
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        
        # Map column names to required columns
        column_map = {}
        for col in df.columns:
            col_lower = col.lower()
            if col_lower == 'date' or col_lower == 'time' or col_lower == 'datetime':
                column_map[col] = 'timestamp'
            elif col_lower == 'open':
                column_map[col] = 'open'
            elif col_lower == 'high':
                column_map[col] = 'high'
            elif col_lower == 'low':
                column_map[col] = 'low'
            elif col_lower == 'close':
                column_map[col] = 'close'
            elif col_lower == 'volume':
                column_map[col] = 'volume'
        
        # Rename columns
        df = df.rename(columns=column_map)
        
        # Check if we have all required columns
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.warning(f"Missing columns: {missing_columns}")
            
            # Add missing columns with default values
            for col in missing_columns:
                if col == 'timestamp':
                    # Try to create timestamp from other columns
                    if 'date' in df.columns and 'time' in df.columns:
                        df['timestamp'] = df['date'] + ' ' + df['time']
                    else:
                        logger.error(f"Cannot create timestamp column")
                        return None
                else:
                    df[col] = 0
        
        # Ensure timestamp is in the correct format
        if 'timestamp' in df.columns:
            # Try to parse timestamp
            try:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')
            except Exception as e:
                logger.warning(f"Error parsing timestamp: {str(e)}")
        
        # Ensure numeric columns are numeric
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        # Save to CSV
        output_file = os.path.join(output_dir, f"{symbol}_1m.csv")
        df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(df)} records to {output_file}")
        
        return output_file
    
    except Exception as e:
        logger.error(f"Error processing {file_path}: {str(e)}")
        return None

def main():
    """Main function."""
    # Get input directory
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    
    # Create input directory if it doesn't exist
    os.makedirs(input_dir, exist_ok=True)
    
    # List data files
    file_types = list_data_files(input_dir)
    
    # Process CSV files for our symbols
    symbols = ['MNQ', 'MES', 'MGC', 'M2K']
    processed_files = process_csv_files(input_dir, symbols)
    
    # Print summary
    logger.info("Processed files:")
    for symbol, file in processed_files.items():
        logger.info(f"- {symbol}: {file}")

if __name__ == "__main__":
    main()
