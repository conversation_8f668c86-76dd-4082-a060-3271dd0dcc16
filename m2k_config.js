/**
 * M2K (Micro Russell 2000) Trading Configuration
 *
 * This file contains the configuration for trading M2K futures.
 * Parameters are based on the backtest results.
 */

module.exports = {
    // Symbol information
    symbol: 'M2K',
    pointValue: 5.0,
    tickSize: 0.10,

    // Contract information
    contractMonth: 'M5', // Will be updated dynamically

    // Account information
    accountId: null, // Will be set during initialization

    // Commission and slippage
    commission: 0.40,
    commissionPerContract: 0.40,
    slippage: 0.0,
    slippagePoints: 0.0,

    // Position sizing
    fixedContracts: 3,

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    sma200Period: 0, // Not used
    atrPeriod: 14,

    // Entry/exit parameters
    slFactors: 5.0,
    tpFactors: 4.0,
    trailFactors: 0.03,
    fixedTpPoints: 0,

    // Risk management
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0.5,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    useTwoBarColorExit: false,

    // ATR thresholds for adaptive mode
    isAdaptiveRun: false,
    atrThresholds: {
        low_medium: 1.5,
        medium_high: 3.0
    },

    // Formatting
    pricePrecision: 1,

    // Logging
    logLevel: 'info'
};
