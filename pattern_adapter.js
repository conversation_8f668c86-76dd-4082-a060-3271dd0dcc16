// pattern_adapter.js - Adapter for pattern detection functions

const patternDetection = require('./pattern_detection');

/**
 * Adapter for detect3Pattern to match the expected detect3 function signature
 * @param {Object} c1 - First candle
 * @param {Object} c2 - Second candle
 * @param {Object} c3 - Third candle
 * @returns {string|null} - 'bullish', 'bearish', or null
 */
function detect3(c1, c2, c3) {
  return patternDetection.detect3Pattern(c1, c2, c3);
}

/**
 * Adapter for detect4Pattern to match the expected detect4 function signature
 * @param {Object} c0 - First candle
 * @param {Object} c1 - Second candle
 * @param {Object} c2 - Third candle
 * @param {Object} c3 - Fourth candle
 * @returns {string|null} - 'bullish', 'bearish', or null
 */
function detect4(c0, c1, c2, c3) {
  return patternDetection.detect4Pattern(c0, c1, c2, c3);
}

/**
 * Adapter for validateEntry to match the expected entryOK function signature
 * @param {string} dir - Direction ('bullish' or 'bearish')
 * @param {string} patternType - Pattern type ('three' or 'four')
 * @param {Object} c3 - Current candle
 * @param {number} currentIndex - Current index in candles array
 * @param {Array} candlesForPeriod - Array of candles
 * @returns {boolean} - Whether entry conditions are met
 */
function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod) {
  return patternDetection.validateEntry(dir, patternType, c3, currentIndex, candlesForPeriod);
}

module.exports = {
  detect3,
  detect4,
  entryOK,
  candlestickColor: patternDetection.candlestickColor
};
