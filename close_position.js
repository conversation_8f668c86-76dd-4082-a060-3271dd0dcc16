/**
 * Standalone Position Closing Script
 * 
 * This script provides a simple way to test position closing through the Tradovate API
 * without running the full trading bot.
 */

require('dotenv').config();
const testClosePosition = require('./test_close_position');

// Run the test
console.log('Running standalone position closing test...');
testClosePosition.runTest()
    .then(() => {
        console.log('Position closing test completed.');
        process.exit(0);
    })
    .catch(error => {
        console.error(`Unhandled error: ${error.message}`);
        process.exit(1);
    });
