/**
 * Paper Trading Configuration for MGC (Micro Gold Futures)
 *
 * This configuration matches the optimized backtest settings
 * that produced the $890,734 profit over 5 years.
 */

module.exports = {
  // --- General Settings ---
  symbol: 'MGC',
  contractMonth: 'M5', // Updated to May 2025 contract
  accountType: 'DEMO',
  initialBalance: 10000,

  // --- Instrument Specifics (MGC Specs) ---
  pointValue: 10.00,      // MGC point value ($10.00 per 1 point move)
  tickSize: 0.10,         // MGC tick size (0.10 points)
  pricePrecision: 1,      // MGC price precision (1 decimal place)

  // --- Costs & Slippage ---
  commissionPerContract: 0.0, // No commission for demo trading
  slippagePoints: 0.0,       // No slippage for demo trading

  // --- Indicator Periods ---
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 0,
  wma50Period: 0,

  // --- Strategy Parameters ---
  fixedTpPoints: 3,       // Fixed take profit of 3 points
  fixedSlPoints: 8,       // Fixed stop loss of 8 points
  useWmaFilter: true,     // IMPROVED: Enable WMA filter for better trade quality
  useTwoBarColorExit: false,
  minAtrEntry: 1.0,       // IMPROVED: Set minimum ATR for entry to filter out low volatility
  minRsiMaSeparation: 1.0, // IMPROVED: Require minimum RSI-MA separation
  requireClosedCandles: true, // Only trade on fully closed candles

  // --- RSI Bands ---
  rsiUpperBand: 60,
  rsiLowerBand: 40,
  rsiMiddleBand: 50,

  // --- Run Mode: OPTIMIZED CONFIGURATION ---
  isAdaptiveRun: true,

  // Using optimal parameters from grid test for MGC
  slFactors: 8.0,     // Optimized SL for MGC
  tpFactors: 7.0,     // Optimized TP for MGC
  trailFactors: 0.02, // Optimized Trail for MGC

  // 0 bar latency
  latencyDelayBars: 0,

  // Position sizing
  riskPercent: 0,
  fixedContracts: 3, // Using 3 contracts as requested
  maxContracts: 3,   // Maximum position size

  // Risk management
  dailyStopLoss: 500,      // $500 daily stop loss (5% of $10k account)
  dailyProfitTarget: 0,    // No profit target (let winners run)
  maxDrawdownPercent: 10,   // 10% maximum drawdown

  // --- Time Filter Settings ---
  timeFilterEnabled: false,

  // --- Enhanced Features Configuration ---
  // All enhanced features disabled
  useAdaptiveSlippage: false,
  minSpreadPoints: 0.10,
  defaultSpreadPoints: 0.20,
  useVolatilityPositionSizing: false,
  useDynamicPositionSizing: false,
  basePositionSize: 3,
  maxPositionSize: 3,
  minPositionSize: 3,
  useMLEntryFilter: false,
  useAdvancedMLFilter: false,
  mlEntryThreshold: 0.5,
  useTimeAnalysis: false,
  timeScoreThreshold: 0.5,
  useCircuitBreakers: false,
  maxSlippageMultiplier: 5,
  pauseTradingOnExcessiveSlippage: false,
  useSteppedTrail: false,
  trailStepSizeMultiplier: 0.1,
  spreadBufferMultiplier: 2.0
};
