/**
 * MES Paper Trading Configuration
 *
 * This module provides configuration for MES paper trading.
 */

module.exports = {
    // Symbol
    symbol: 'MES',

    // Contract month
    contractMonth: 'M5', // May 2025 contract

    // Account ID (will be set at runtime)
    accountId: null,

    // Position sizing
    fixedPositionSize: 3,

    // Costs & Slippage
    commissionPerContract: 0.0, // No commission for demo trading
    slippagePoints: 0.0,       // No slippage for demo trading

    // ATR thresholds
    atrThresholds: {
        lowMedium: 3.0,
        mediumHigh: 5.0
    },

    // Stop loss and take profit factors
    slFactor: 3.0,
    tpFactor: 3.0,

    // Trail factor
    trailFactor: 0.01,

    // Fixed take profit points (optional)
    fixedTpPoints: 40,

    // Filters
    filters: {
        // WMA filter
        wmaEnabled: false,

        // Time filter
        timeFilterEnabled: false,

        // RSI filter
        rsiEnabled: true,
        rsiOverbought: 70,
        rsiOversold: 30
    },

    // Circuit breakers
    circuitBreakers: {
        maxDailyLoss: 0.05, // 5% of account
        maxConsecutiveLosses: 3
    }
};
