// test_trading_bot.js - Test the trading bot's pattern detection and RSI calculation
const fs = require('fs');
const path = require('path');

// Import the indicator functions
const { calculateRSI, calculateSMA, calculateWMA } = require('./indicators');

// Import the pattern detection functions from trading_logic.js
const tradingLogic = require('./trading_logic');
const detect3 = tradingLogic.detect3;
const detect4 = tradingLogic.detect4;
const candlestickColor = tradingLogic.candlestickColor;

// Create sample data
const generateSampleData = () => {
    const candles = [];
    let price = 2100.00;

    // Generate 100 candles with some price movement
    for (let i = 0; i < 100; i++) {
        const change = (Math.random() - 0.5) * 10;
        const open = price;
        const close = price + change;
        price = close;

        const high = Math.max(open, close) + Math.random() * 5;
        const low = Math.min(open, close) - Math.random() * 5;

        const timestamp = new Date(2025, 4, 14, 5, i, 0);

        candles.push({
            timestamp,
            open,
            high,
            low,
            close
        });
    }

    return candles;
};

// Calculate RSI for each candle
const calculateRSIValues = (candles, period) => {
    const rsiValues = [];

    for (let i = period; i < candles.length; i++) {
        const slice = candles.slice(i - period, i + 1);
        const rsi = calculateRSI(slice, period);
        rsiValues.push(rsi);
    }

    return rsiValues;
};

// Calculate SMA for each value
const calculateSMAValues = (values, period) => {
    const smaValues = [];

    for (let i = period - 1; i < values.length; i++) {
        const slice = values.slice(i - period + 1, i + 1);
        const sum = slice.reduce((acc, val) => acc + val, 0);
        const sma = sum / period;
        smaValues.push(sma);
    }

    return smaValues;
};

// Test the trading bot
const testTradingBot = () => {
    console.log("Testing trading bot...");

    // Generate sample data
    const candles = generateSampleData();

    // Calculate RSI with period 14
    const rsiPeriod = 14;
    const rsiValues = calculateRSIValues(candles, rsiPeriod);

    // Calculate SMA of RSI with period 8
    const smaPeriod = 8;
    const rsiSmaValues = calculateSMAValues(rsiValues, smaPeriod);

    // Check for pattern detections
    console.log("\nChecking for pattern detections...");

    let bullish3Count = 0;
    let bearish3Count = 0;
    let bullish4Count = 0;
    let bearish4Count = 0;

    for (let i = 3; i < candles.length; i++) {
        const c1 = candles[i - 3];
        const c2 = candles[i - 2];
        const c3 = candles[i - 1];
        const c4 = candles[i];

        // Check for 3-candle patterns
        if (i >= 3) {
            const pattern3 = detect3(c1, c2, c3);

            if (pattern3 === 'bullish') {
                bullish3Count++;
                console.log(`Detected bullish 3-candle pattern at ${c3.timestamp.toISOString()}`);
            }

            if (pattern3 === 'bearish') {
                bearish3Count++;
                console.log(`Detected bearish 3-candle pattern at ${c3.timestamp.toISOString()}`);
            }
        }

        // Check for 4-candle patterns
        if (i >= 4) {
            const pattern4 = detect4(c1, c2, c3, c4);

            if (pattern4 === 'bullish') {
                bullish4Count++;
                console.log(`Detected bullish 4-candle pattern at ${c4.timestamp.toISOString()}`);
            }

            if (pattern4 === 'bearish') {
                bearish4Count++;
                console.log(`Detected bearish 4-candle pattern at ${c4.timestamp.toISOString()}`);
            }
        }
    }

    console.log(`\nPattern detection summary:`);
    console.log(`Bullish 3-candle patterns: ${bullish3Count}`);
    console.log(`Bearish 3-candle patterns: ${bearish3Count}`);
    console.log(`Bullish 4-candle patterns: ${bullish4Count}`);
    console.log(`Bearish 4-candle patterns: ${bearish4Count}`);

    // Check RSI and RSI-MA crossovers
    console.log("\nChecking for RSI and RSI-MA crossovers...");

    let rsiAboveMaCount = 0;
    let rsiBelowMaCount = 0;

    for (let i = 0; i < rsiValues.length; i++) {
        if (i >= smaPeriod - 1) {
            const rsi = rsiValues[i];
            const rsiMa = rsiSmaValues[i - (smaPeriod - 1)];

            if (rsi > rsiMa) {
                rsiAboveMaCount++;
            } else if (rsi < rsiMa) {
                rsiBelowMaCount++;
            }
        }
    }

    console.log(`RSI above RSI-MA: ${rsiAboveMaCount} times`);
    console.log(`RSI below RSI-MA: ${rsiBelowMaCount} times`);

    // Print the last 10 RSI and RSI-MA values
    console.log("\nLast 10 RSI and RSI-MA values:");
    console.log("Candle #\tClose\t\tRSI\t\tRSI-MA");
    console.log("-".repeat(50));

    const startIndex = Math.max(rsiPeriod, candles.length - 10);
    for (let i = startIndex; i < candles.length; i++) {
        const rsiIndex = i - rsiPeriod;
        const rsiValue = rsiValues[rsiIndex];

        let rsiMaValue = "N/A";
        if (rsiIndex >= smaPeriod - 1) {
            const rsiMaIndex = rsiIndex - (smaPeriod - 1);
            rsiMaValue = rsiSmaValues[rsiMaIndex].toFixed(2);
        }

        console.log(`${i+1}\t\t${candles[i].close.toFixed(2)}\t\t${rsiValue.toFixed(2)}\t\t${rsiMaValue}`);
    }
};

// Run the test
testTradingBot();
