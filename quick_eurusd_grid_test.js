/**
 * Quick EURUSD Grid Test
 * 
 * This script runs a focused grid test for EURUSD forex pair to quickly find promising parameters.
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');

console.log("Starting Quick EURUSD Grid Test...");

// Load configuration
const config = require('./config_eurusd_grid_test.js');
const dataPath = config.inputFile;

// Date range for testing (just use 2023-2024 for quick test)
const dateRange = {
    startDate: '2023-01-01',
    endDate: '2024-01-01'
};

// Define focused grid parameters (fewer combinations for faster testing)
const focusedGridParams = {
    slFactors: [4.0, 5.0, 6.0],
    tpFactors: [3.0, 4.0, 5.0],
    trailFactors: [0.02, 0.03, 0.05],
    fixedTpPoints: [0, 0.0010]
};

/**
 * Run quick grid test for EURUSD
 */
async function runQuickGridTest() {
    console.log(`\n===== RUNNING QUICK EURUSD GRID TEST =====`);
    console.log(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
    console.log(`Grid parameters: ${JSON.stringify(focusedGridParams, null, 2)}`);
    
    // Create backtest instance with configuration
    const backtest = new GridBacktest(config);
    
    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { dateRange });
    
    // Run grid test
    console.log(`Running grid test with focused parameters...`);
    const results = backtest.runGridTest(focusedGridParams);
    
    // Sort results by profit factor
    results.sort((a, b) => b.stats.profitFactor - a.stats.profitFactor);
    
    // Create output directory if it doesn't exist
    if (!fs.existsSync(config.outputDir)) {
        fs.mkdirSync(config.outputDir, { recursive: true });
    }
    
    // Save results to file
    const resultsPath = path.join(config.outputDir, 'quick_grid_test_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`Results saved to ${resultsPath}`);
    
    // Display top 5 results
    console.log("\n===== TOP 5 PARAMETER COMBINATIONS =====");
    for (let i = 0; i < Math.min(5, results.length); i++) {
        const result = results[i];
        console.log(`\n#${i + 1}: ${JSON.stringify(result.params)}`);
        console.log(`  Total PnL: $${result.stats.totalPnL.toFixed(2)}`);
        console.log(`  Win Rate: ${(result.stats.winRate * 100).toFixed(2)}%`);
        console.log(`  Win Day Rate: ${(result.stats.winDayRate * 100).toFixed(2)}%`);
        console.log(`  Profit Factor: ${result.stats.profitFactor.toFixed(2)}`);
        console.log(`  Max Drawdown: $${result.stats.maxDrawdown.toFixed(2)}`);
    }
}

// Run the quick grid test
runQuickGridTest().catch(err => {
    console.error('Error running quick grid test:', err);
});
