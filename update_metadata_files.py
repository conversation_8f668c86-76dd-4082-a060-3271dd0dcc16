import os
import json
import glob

# Directories
DATA_DIR = "C:/backtest-bot/market_data"

def update_metadata_files():
    """Update metadata files to match the expected structure"""
    # Find all metadata files
    metadata_files = glob.glob(os.path.join(DATA_DIR, "*", "*_metadata.json"))
    
    for file_path in metadata_files:
        print(f"Updating {file_path}...")
        
        try:
            # Load the metadata file
            with open(file_path, 'r') as f:
                metadata = json.load(f)
            
            # Create a new metadata object with the expected structure
            new_metadata = {
                "instrumentCode": metadata["instrumentCode"],
                "instrumentConfig": {
                    "name": metadata["name"],
                    "color": metadata["color"]
                },
                "backtestResults": {
                    "params": metadata["params"],
                    "metrics": metadata["metrics"]
                },
                "lastUpdated": metadata["lastUpdated"]
            }
            
            # Write the updated metadata back to the file
            with open(file_path, 'w') as f:
                json.dump(new_metadata, f, indent=4)
            
            print(f"Updated {file_path} successfully")
        
        except Exception as e:
            print(f"Error updating {file_path}: {e}")

def update_all_instruments_data():
    """Update the all_instruments_data.json file to match the expected structure"""
    all_data_file = os.path.join(DATA_DIR, "all_instruments_data.json")
    
    if not os.path.exists(all_data_file):
        print(f"Warning: All instruments data file not found: {all_data_file}")
        return
    
    print(f"Updating {all_data_file}...")
    
    try:
        # Load the all instruments data file
        with open(all_data_file, 'r') as f:
            all_data = json.load(f)
        
        # Create a new all instruments data object with the expected structure
        new_all_data = {}
        
        for instrument_code, instrument_data in all_data["instruments"].items():
            new_all_data[instrument_code] = {
                "instrumentCode": instrument_data["instrumentCode"],
                "instrumentConfig": {
                    "name": instrument_data["name"],
                    "color": instrument_data["color"]
                },
                "backtestResults": {
                    "params": instrument_data["params"],
                    "metrics": instrument_data["metrics"]
                },
                "lastUpdated": instrument_data["lastUpdated"]
            }
        
        # Write the updated all instruments data back to the file
        with open(all_data_file, 'w') as f:
            json.dump(new_all_data, f, indent=4)
        
        print(f"Updated {all_data_file} successfully")
    
    except Exception as e:
        print(f"Error updating {all_data_file}: {e}")

def main():
    """Main function"""
    print("Updating metadata files...")
    update_metadata_files()
    
    print("Updating all instruments data file...")
    update_all_instruments_data()
    
    print("Metadata update complete!")

if __name__ == "__main__":
    main()
