/**
 * Run Grid Backtest
 * Optimized for testing multiple markets and parameters
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');
const gridConfig = require('./grid_config');

// Get configuration from grid_config.js
const OPTIONS = gridConfig.options;
const GRID_PARAMS = process.argv.includes('--focused') ?
    gridConfig.focusedGridParams :
    gridConfig.gridParams;
const CONFIGS = gridConfig.configs;
const DATA_PATHS = gridConfig.dataPaths;

// Add output directory to each config
for (const symbol in CONFIGS) {
    CONFIGS[symbol].outputDir = path.join(OPTIONS.outputDir, symbol);
}

// Ensure output directory exists
if (!fs.existsSync(OPTIONS.outputDir)) {
    fs.mkdirSync(OPTIONS.outputDir, { recursive: true });
}

/**
 * Run grid test for a single instrument
 * @param {string} symbol - Instrument symbol
 * @returns {Promise<Object>} - Grid test results
 */
async function runGridTest(symbol) {
    console.log(`\n===== RUNNING GRID TEST FOR ${symbol} =====`);

    // Create output directory for this symbol
    const symbolOutputDir = path.join(OPTIONS.outputDir, symbol);
    if (!fs.existsSync(symbolOutputDir)) {
        fs.mkdirSync(symbolOutputDir, { recursive: true });
    }

    // Create backtest instance with configuration
    const config = { ...CONFIGS[symbol], outputDir: symbolOutputDir };
    const backtest = new GridBacktest(config);

    // Load historical data with options
    await backtest.loadData(DATA_PATHS[symbol], {
        dateRange: OPTIONS.dateRange
    });

    // Run grid test
    const results = backtest.runGridTest(GRID_PARAMS);

    // Save results to file
    const resultsPath = path.join(symbolOutputDir, 'grid_test_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));

    // Generate HTML report
    generateHtmlReport(symbol, results, symbolOutputDir);

    console.log(`Grid test for ${symbol} completed. Results saved to ${resultsPath}`);

    return results;
}

/**
 * Generate HTML report for grid test results
 * @param {string} symbol - Instrument symbol
 * @param {Array} results - Grid test results
 * @param {string} outputDir - Output directory
 */
function generateHtmlReport(symbol, results, outputDir) {
    const reportPath = path.join(outputDir, 'grid_test_report.html');

    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${symbol} Grid Test Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${symbol} Grid Test Report</h1>

        <h2>Top 20 Parameter Combinations</h2>
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>SL</th>
                    <th>TP</th>
                    <th>Trail</th>
                    <th>Fixed TP</th>
                    <th>Total PnL</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Win Day Rate</th>
                    <th>Profit Factor</th>
                    <th>Max DD</th>
                    <th>Trades</th>
                </tr>
            </thead>
            <tbody>
                ${results.slice(0, 20).map((result, index) => `
                <tr>
                    <td>${index + 1}</td>
                    <td>${result.params.slFactors}</td>
                    <td>${result.params.tpFactors}</td>
                    <td>${result.params.trailFactors}</td>
                    <td>${result.params.fixedTpPoints}</td>
                    <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
                    <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
                    <td>${result.stats.winRate.toFixed(2)}%</td>
                    <td>${result.stats.winDayRate.toFixed(2)}%</td>
                    <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
                    <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
                    <td>${result.stats.totalTrades}</td>
                </tr>
                `).join('')}
            </tbody>
        </table>

        <h2>Parameter Analysis</h2>
        <p>This section shows the average performance for each parameter value.</p>

        <h3>Stop Loss Factors</h3>
        <table>
            <thead>
                <tr>
                    <th>SL Factor</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'slFactors')}
            </tbody>
        </table>

        <h3>Take Profit Factors</h3>
        <table>
            <thead>
                <tr>
                    <th>TP Factor</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'tpFactors')}
            </tbody>
        </table>

        <h3>Trail Factors</h3>
        <table>
            <thead>
                <tr>
                    <th>Trail Factor</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'trailFactors')}
            </tbody>
        </table>

        <h3>Fixed TP Points</h3>
        <table>
            <thead>
                <tr>
                    <th>Fixed TP</th>
                    <th>Avg PnL</th>
                    <th>Avg Win Rate</th>
                    <th>Avg Win Day Rate</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                ${analyzeParameter(results, 'fixedTpPoints')}
            </tbody>
        </table>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`HTML report generated at ${reportPath}`);
}

/**
 * Analyze parameter performance
 * @param {Array} results - Grid test results
 * @param {string} paramName - Parameter name
 * @returns {string} - HTML table rows
 */
function analyzeParameter(results, paramName) {
    // Group results by parameter value
    const paramGroups = {};

    for (const result of results) {
        const paramValue = result.params[paramName];

        if (!paramGroups[paramValue]) {
            paramGroups[paramValue] = {
                totalPnl: 0,
                totalWinRate: 0,
                totalWinDayRate: 0,
                count: 0
            };
        }

        paramGroups[paramValue].totalPnl += result.stats.totalPnl;
        paramGroups[paramValue].totalWinRate += result.stats.winRate;
        paramGroups[paramValue].totalWinDayRate += result.stats.winDayRate;
        paramGroups[paramValue].count++;
    }

    // Calculate averages and generate HTML
    let html = '';

    for (const paramValue in paramGroups) {
        const group = paramGroups[paramValue];
        const avgPnl = group.totalPnl / group.count;
        const avgWinRate = group.totalWinRate / group.count;
        const avgWinDayRate = group.totalWinDayRate / group.count;

        html += `
        <tr>
            <td>${paramValue}</td>
            <td class="${avgPnl >= 0 ? 'positive' : 'negative'}">$${avgPnl.toFixed(2)}</td>
            <td>${avgWinRate.toFixed(2)}%</td>
            <td>${avgWinDayRate.toFixed(2)}%</td>
            <td>${group.count}</td>
        </tr>`;
    }

    return html;
}

/**
 * Run grid test for all instruments
 */
async function runAllGridTests() {
    console.log('\n===== GRID TEST CONFIGURATION =====');
    console.log(`Date range: ${OPTIONS.dateRange.startDate} to ${OPTIONS.dateRange.endDate}`);
    console.log(`Symbols to test: ${OPTIONS.symbols.join(', ')}`);
    console.log(`Grid parameters: ${JSON.stringify(GRID_PARAMS, null, 2)}`);
    console.log(`Using ${process.argv.includes('--focused') ? 'focused' : 'full'} grid test mode`);

    const allResults = {};

    for (const symbol of OPTIONS.symbols) {
        if (!CONFIGS[symbol]) {
            console.error(`Configuration not found for symbol: ${symbol}`);
            continue;
        }

        try {
            allResults[symbol] = await runGridTest(symbol);
        } catch (error) {
            console.error(`Error running grid test for ${symbol}:`, error);
        }
    }

    // Generate combined report
    generateCombinedReport(allResults);
}

/**
 * Generate combined report for all instruments
 * @param {Object} allResults - Results for all instruments
 */
function generateCombinedReport(allResults) {
    const reportPath = path.join(OPTIONS.outputDir, 'combined_grid_test_report.html');

    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Combined Grid Test Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Combined Grid Test Report</h1>

        <h2>Best Parameters by Symbol</h2>
        <table>
            <thead>
                <tr>
                    <th>Symbol</th>
                    <th>SL</th>
                    <th>TP</th>
                    <th>Trail</th>
                    <th>Fixed TP</th>
                    <th>Total PnL</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Win Day Rate</th>
                    <th>Profit Factor</th>
                    <th>Max DD</th>
                    <th>Trades</th>
                </tr>
            </thead>
            <tbody>
                ${Object.entries(allResults).map(([symbol, results]) => {
                    if (!results || results.length === 0) return '';
                    const best = results[0]; // Results are already sorted by PnL
                    return `
                    <tr>
                        <td>${symbol}</td>
                        <td>${best.params.slFactors}</td>
                        <td>${best.params.tpFactors}</td>
                        <td>${best.params.trailFactors}</td>
                        <td>${best.params.fixedTpPoints}</td>
                        <td class="${best.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${best.stats.totalPnl.toFixed(2)}</td>
                        <td class="${best.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${best.stats.totalReturn.toFixed(2)}%</td>
                        <td>${best.stats.winRate.toFixed(2)}%</td>
                        <td>${best.stats.winDayRate.toFixed(2)}%</td>
                        <td>${typeof best.stats.profitFactor === 'number' ? best.stats.profitFactor.toFixed(2) : best.stats.profitFactor}</td>
                        <td>${best.stats.maxDrawdown.toFixed(2)}%</td>
                        <td>${best.stats.totalTrades}</td>
                    </tr>`;
                }).join('')}
            </tbody>
        </table>

        <h2>Links to Individual Reports</h2>
        <ul>
            ${Object.keys(allResults).map(symbol => `
            <li><a href="./${symbol}/grid_test_report.html" style="color: #4caf50;">${symbol} Grid Test Report</a></li>
            `).join('')}
        </ul>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`Combined report generated at ${reportPath}`);
}

// Run all grid tests
runAllGridTests().catch(error => {
    console.error('Error running grid tests:', error);
});
