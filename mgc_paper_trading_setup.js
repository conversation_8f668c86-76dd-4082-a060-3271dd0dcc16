/**
 * Paper Trading Setup for MGC (Micro Gold Futures)
 * 
 * This module configures and initializes paper trading for MGC futures
 * using the Tradovate API, with enhanced infrastructure for reliability.
 * 
 * Using the optimized configuration that produced $890,734 profit over 5 years.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api');
const logger = require('./data_logger');
const alerts = require('./alert_system');
const redundancy = require('./redundancy_system');
const mgcPaperTradingConfig = require('./mgc_paper_trading_config');

// Use the optimized configuration from the backtest
const config = {
    // Trading parameters
    symbol: mgcPaperTradingConfig.symbol || 'MGC',
    contractMonth: mgcPaperTradingConfig.contractMonth || 'M4',
    accountType: mgcPaperTradingConfig.accountType || 'DEMO',
    
    // Strategy parameters (from optimized backtest)
    slFactors: mgcPaperTradingConfig.slFactors || 8.0,
    tpFactors: mgcPaperTradingConfig.tpFactors || 7.0,
    trailFactors: mgcPaperTradingConfig.trailFactors || 0.02,
    fixedTpPoints: mgcPaperTradingConfig.fixedTpPoints || 0,
    
    // Position sizing
    fixedContracts: mgcPaperTradingConfig.fixedContracts || 10,
    maxContracts: mgcPaperTradingConfig.maxContracts || 30,
    
    // Costs & Slippage
    commissionPerContract: mgcPaperTradingConfig.commissionPerContract || 0.40,
    slippagePoints: mgcPaperTradingConfig.slippagePoints || 0.10,
    
    // Risk management
    dailyStopLoss: mgcPaperTradingConfig.dailyStopLoss || 500,
    dailyProfitTarget: mgcPaperTradingConfig.dailyProfitTarget || 0,
    maxDrawdownPercent: mgcPaperTradingConfig.maxDrawdownPercent || 5,
    
    // Trading hours (based on analysis)
    tradingHours: {
        enabled: mgcPaperTradingConfig.timeFilterEnabled || false,
        optimalHours: [
            { start: '10:00', end: '11:00', multiplier: 1.5 },  // Most profitable hour
            { start: '14:00', end: '15:00', multiplier: 1.2 },  // Highest win rate hour
            { start: '09:30', end: '10:30', multiplier: 1.0 }   // Most active hour
        ],
        avoidHours: [
            { start: '15:30', end: '16:00', multiplier: 0.5 }   // Least profitable hour
        ]
    },
    
    // Monitoring and alerts
    monitoringInterval: process.env.MONITORING_INTERVAL || 60000,
    alertThresholds: {
        drawdown: {
            warning: mgcPaperTradingConfig.maxDrawdownPercent * 0.6 || 3,      // Alert at 60% of max drawdown
            critical: mgcPaperTradingConfig.maxDrawdownPercent * 0.9 || 4.5    // Critical alert at 90% of max drawdown
        },
        winRate: {
            warning: 65,     // Alert if win rate drops below 65%
            critical: 60     // Critical alert if win rate drops below 60%
        }
    },
    
    // Logging and data collection
    logLevel: process.env.LOG_LEVEL || 'info',
    dataCollection: {
        tradeDetails: true,  // Collect detailed trade data
        marketData: true,    // Collect market data
        performance: true    // Collect performance metrics
    }
};

// Trading state
let tradingState = {
    isTrading: false,
    openPositions: [],
    lastTradeTime: null,
    equity: config.initialBalance || 10000,
    dailyPnL: 0,
    weeklyPnL: 0,
    monthlyPnL: 0,
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0
};

// Load previous state if available
function loadTradingState() {
    try {
        const loadedState = redundancy.loadState();
        if (loadedState) {
            tradingState = loadedState;
            console.log('Loaded previous system state');
        }
    } catch (error) {
        logger.logSystem(`Error loading trading state: ${error.message}`, 'error');
    }
}

// Initialize Tradovate API connection
async function initializeTradovateConnection() {
    try {
        console.log('Attempting to authenticate...');
        
        // Prepare authentication payload
        const authPayload = {
            name: process.env.TRADOVATE_USERNAME,
            password: process.env.TRADOVATE_PASSWORD,
            appId: process.env.TRADOVATE_APP_ID,
            appVersion: process.env.TRADOVATE_APP_VERSION,
            deviceId: process.env.TRADOVATE_DEVICE_ID,
            cid: process.env.TRADOVATE_CID,
            sec: process.env.TRADOVATE_SEC
        };
        
        console.log('Authentication payload:', authPayload);
        
        // Authenticate with Tradovate
        const authResult = await tradovateApi.authenticate(authPayload);
        
        if (authResult.success) {
            console.log(`Authentication successful! Token expires at: ${new Date(authResult.expirationTime).toLocaleString()}`);
            console.log(`User ID: ${authResult.userId}`);
            
            // Connect to WebSocket
            console.log(`Connecting to WebSocket: ${tradovateApi.getWebSocketUrl()}`);
            await tradovateApi.connectWebSocket();
            
            // Configure alert thresholds
            console.log('Alert thresholds configured:', config.alertThresholds);
            alerts.configureAlerts(config.alertThresholds);
            
            // Send system startup notification
            alerts.sendSystemAlert('MGC paper trading system starting up', 'info');
            
            return true;
        } else {
            console.error('Authentication failed:', authResult.error);
            return false;
        }
    } catch (error) {
        console.error('Error initializing Tradovate connection:', error);
        return false;
    }
}

// Initialize paper trading setup
async function initializePaperTrading() {
    try {
        console.log('INFO: Initializing MGC paper trading setup...');
        
        // Load previous trading state
        loadTradingState();
        
        // Initialize Tradovate API connection
        const connected = await initializeTradovateConnection();
        
        if (!connected) {
            console.error('Failed to connect to Tradovate API. Exiting...');
            return false;
        }
        
        // Subscribe to market data
        const symbol = `${config.symbol}${config.contractMonth}`;
        await tradovateApi.subscribeToChart(symbol, '1m');
        
        // Initialize alert system
        alerts.sendSystemAlert('MGC paper trading setup initialized and ready', 'info');
        
        console.log('INFO: MGC paper trading setup initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing paper trading:', error);
        return false;
    }
}

// Start trading
async function startTrading() {
    if (tradingState.isTrading) {
        console.log('Trading is already active.');
        return;
    }
    
    tradingState.isTrading = true;
    redundancy.saveState(tradingState);
    
    console.log('Trading started.');
    alerts.sendSystemAlert('MGC paper trading started', 'info');
}

// Stop trading
async function stopTrading() {
    if (!tradingState.isTrading) {
        console.log('Trading is already stopped.');
        return;
    }
    
    tradingState.isTrading = false;
    redundancy.saveState(tradingState);
    
    console.log('Trading stopped.');
    alerts.sendSystemAlert('MGC paper trading stopped', 'info');
}

// Show trading status
function showTradingStatus() {
    console.log('\n=== MGC Paper Trading Status ===');
    console.log(`Trading Active: ${tradingState.isTrading ? 'Yes' : 'No'}`);
    console.log(`Current Equity: $${tradingState.equity.toFixed(2)}`);
    console.log(`Open Positions: ${tradingState.openPositions.length}`);
    console.log(`Daily P&L: $${tradingState.dailyPnL.toFixed(2)}`);
    console.log(`Total Trades: ${tradingState.totalTrades}`);
    console.log(`Win Rate: ${tradingState.totalTrades > 0 ? ((tradingState.winningTrades / tradingState.totalTrades) * 100).toFixed(2) : 0}%`);
    console.log('===============================\n');
}

// Show configuration
function showConfiguration() {
    console.log('\n=== MGC Paper Trading Configuration ===');
    console.log(`Symbol: ${config.symbol}${config.contractMonth}`);
    console.log(`Strategy Parameters:`);
    console.log(`  - Stop Loss Factor: ${config.slFactors}`);
    console.log(`  - Take Profit Factor: ${config.tpFactors}`);
    console.log(`  - Trail Factor: ${config.trailFactors}`);
    console.log(`Position Size: ${config.fixedContracts} contracts`);
    console.log(`Risk Management:`);
    console.log(`  - Daily Stop Loss: $${config.dailyStopLoss}`);
    console.log(`  - Max Drawdown: ${config.maxDrawdownPercent}%`);
    console.log('======================================\n');
}

// Backup data
function backupData() {
    try {
        redundancy.saveState(tradingState);
        console.log('Trading data backed up successfully.');
    } catch (error) {
        console.error('Error backing up data:', error);
    }
}

// Command menu
function showCommandMenu() {
    console.log('\nMGC Paper Trading Bot - Command Menu');
    console.log('1. Start Trading');
    console.log('2. Stop Trading');
    console.log('3. Show Trading Status');
    console.log('4. Show Configuration');
    console.log('5. Backup Data');
    console.log('6. Exit');
    console.log('\nEnter command (1-6): ');
}

// Process command
async function processCommand(command) {
    switch (command) {
        case '1':
            await startTrading();
            break;
        case '2':
            await stopTrading();
            break;
        case '3':
            showTradingStatus();
            break;
        case '4':
            showConfiguration();
            break;
        case '5':
            backupData();
            break;
        case '6':
            console.log('Exiting MGC paper trading...');
            process.exit(0);
            break;
        default:
            console.log('Invalid command. Please enter a number between 1 and 6.');
    }
    
    // Show menu again
    showCommandMenu();
}

// Main function
async function main() {
    console.log('================================================================================');
    console.log('                         MGC PAPER TRADING BOT');
    console.log('                    Optimized Configuration');
    console.log('================================================================================\n');
    
    console.log('Initializing trading system...');
    
    // Initialize paper trading
    const initialized = await initializePaperTrading();
    
    if (!initialized) {
        console.error('Failed to initialize paper trading. Exiting...');
        process.exit(1);
    }
    
    console.log('Trading system initialized successfully!\n');
    
    // Show command menu
    showCommandMenu();
    
    // Process user input
    process.stdin.on('data', (data) => {
        const command = data.toString().trim();
        processCommand(command);
    });
}

// Start the application
main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
});

module.exports = {
    startTrading,
    stopTrading,
    showTradingStatus,
    showConfiguration,
    backupData
};
