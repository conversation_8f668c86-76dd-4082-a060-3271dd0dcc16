# Tradovate API Example

This project demonstrates how to connect to the Tradovate API and perform basic operations.

## Project Structure

```
tradovate-example/
├── src/
│   ├── app.js         # Main application file
│   ├── connect.js     # Handles API connection
│   ├── services.js    # API service functions
│   ├── storage.js     # Local storage utilities
│   └── utils/
│       └── isMobile.js # Mobile detection utility
├── tutorialsURLs.js   # API URLs
├── tutorialsCredentials.js # API credentials
├── index.html         # Web interface
├── node-example.js    # Node.js version of the example
├── package.json       # Project dependencies
├── webpack.config.js  # Webpack configuration
└── babel.config.json  # Babel configuration
```

## Setup

1. Make sure you have Node.js and npm installed
2. Install dependencies:
   ```
   npm install
   ```
3. Start the development server:
   ```
   npm start
   ```

## Features

- Connects to Tradovate API
- Authenticates using provided credentials
- Stores access token and user data in sessionStorage
- Handles token validation and reuse
- Handles device ID for 2FA
- Makes authenticated requests
- Provides helper functions for API requests
- Handles time penalty responses with automatic retry
- Implements proper error handling for captcha requirements

## Running the Examples

### Web Example
To run the web version of the example:

```
npm install
npm start
```

Then open your browser to http://localhost:8080 and click the "Get Account List" button to see your account information.

### Node.js Examples

To run the basic Node.js example:

```
npm install node-fetch
node node-example.js
```

To run the account list example:

```
npm install node-fetch
node account-list-example.js
```

To run the order placement example:

```
npm install node-fetch readline
node place-order-example.js
```

To run the WebSocket example:

```
npm install node-fetch ws
node websocket-node-example.js
```

To run the WebSocket web example:

```
cd websocket-example
npm install
npm start
```

## API Helper Functions

The project includes two main helper functions for API requests:

- `tvGet(endpoint, params, auth)` - For making GET requests
- `tvPost(endpoint, data, auth)` - For making POST requests

Example usage:

```javascript
// GET request with parameters
const contract = await tvGet('/contract/item', { id: 2287764 })

// POST request with data
const orderResult = await tvPost('/order/placeorder', {
    accountSpec: 'DEMO123',
    accountId: 12345,
    action: 'Buy',
    symbol: 'MNQM1',
    orderQty: 2,
    orderType: 'Market',
    isAutomated: true
})
```

## Time Penalty Handling

The project includes handling for time penalty responses from the Tradovate API:

1. When too many requests are made in a short period, the API may return a time penalty response
2. The response includes:
   - `p-ticket`: A ticket code needed for retry
   - `p-time`: The time in seconds to wait before retrying
   - `p-captcha`: Whether a captcha is required (cannot be handled programmatically)

3. The code automatically handles retries when appropriate:
   - Waits for the specified time period
   - Retries the request with the ticket included
   - Properly handles captcha requirements with clear error messages

## Account List Example

The project includes an example of fetching and displaying account information:

1. Web Version:
   - Click the "Get Account List" button on the web interface
   - Account information is displayed in a formatted HTML view
   - Includes all account details like ID, name, type, status, etc.

2. Node.js Version:
   - Run `node account-list-example.js`
   - Account information is displayed in the console
   - Formatted for easy reading with clear labels

This example demonstrates:
- How to make authenticated API requests
- How to handle and display API response data
- How to structure API interaction code

## Order Placement Example

The project includes an example of placing orders with the Tradovate API:

1. Web Version:
   - Fill out the order form with symbol, quantity, and order type
   - Select Buy or Sell action
   - Check "Is Automated" if the order is placed by an algorithm
   - Click the Buy or Sell button to place the order
   - Order response is displayed on the page

2. Node.js Version:
   - Run `node place-order-example.js`
   - Follow the interactive prompts to enter order details
   - Confirm the order before it's placed
   - Order response is displayed in the console

Important Notes:
- The `isAutomated` flag must be set to `true` for orders placed by algorithms
- Failing to properly set this flag could violate exchange policies
- Different order types require different parameters (price, stop price, etc.)
- The example includes validation to ensure all required fields are provided

## WebSocket Example

The project includes an example of using the Tradovate WebSocket API:

1. Web Version:
   - Open the WebSocket example page in your browser
   - Click the "Connect WebSocket" button to establish a connection
   - Use the action buttons to send different types of requests
   - WebSocket messages are displayed in real-time on the page
   - Connection status is shown at the top of the page

2. Node.js Version:
   - Run `node websocket-node-example.js`
   - The script automatically connects to the WebSocket
   - Sends example requests (user/accounts, user/syncrequest)
   - Displays all WebSocket messages in the console

WebSocket Message Types:
- `o` - Open frame (initial connection)
- `h` - Heartbeat frame (keeps connection alive)
- `a` - Array of JSON data (main data format)
- `c` - Closed frame (connection closing)

Heartbeat Handling:
- The server sends heartbeat frames to keep the connection alive
- The client must respond with its own heartbeat (`[]`) to maintain the connection
- Our implementation automatically sends heartbeats every 2.5 seconds
- Without proper heartbeat handling, the connection will time out

WebSocket Request Format:
```
operation
id
query
body
```

Example WebSocket Request Types:
1. Request with no query or body:
```
executionReport/list
4

```

2. Request with a query parameter:
```
tradingPermission/ldeps
8
masterids=1

```

3. Request with a body:
```
contract/rollcontract
33

{"name":"YMZ6","forward":true,"ifExpired":true}
```

Example WebSocket Requests:
```javascript
// Using the raw WebSocket API
ws.send(`authorize\n0\n\n${accessToken}`)
ws.send(`user/accounts\n${Date.now()}\n\n`)
ws.send(`user/syncrequest\n${Date.now()}\n\n`)

// Using our TradovateSocket class
tradovateSocket.send({ url: 'authorize', body: accessToken })
tradovateSocket.send({ url: 'user/accounts' })
tradovateSocket.send({ url: 'user/syncrequest' })
```

TradovateSocket Class Features:
- Encapsulates WebSocket connection logic
- Handles authentication automatically
- Manages heartbeats to keep the connection alive
- Provides a simple API for sending requests
- Includes a listener system for handling responses
- Handles reconnection and error scenarios
- Supports real-time subscriptions
- Returns promises for easy async/await usage

## Real-Time Market Data

The Tradovate WebSocket API provides real-time market data through the Market Data WebSocket endpoint. This allows you to subscribe to real-time quotes, depth of market (DOM), and histograms for specific contracts.

### Real-Time Quotes

```javascript
// Subscribe to quotes for a symbol
const unsubscribeQuote = await marketDataSocket.subscribe({
    url: 'md/subscribequote',
    body: { symbol: 'MNQM3' },
    subscription: (data) => {
        if (data && data.quotes && data.quotes.length > 0) {
            const quote = data.quotes[0];
            console.log('Quote update:', quote);
        }
    }
});

// Later, to unsubscribe
unsubscribeQuote();
```

The quote data includes:
- timestamp: When the quote was generated
- contractId: ID of the contract
- entries: Various price and size information including:
  - Bid: Current bid price and size
  - Offer: Current offer price and size
  - Trade: Last trade price and size
  - HighPrice: High price of the day
  - LowPrice: Low price of the day
  - OpenInterest: Open interest
  - OpeningPrice: Opening price
  - SettlementPrice: Settlement price
  - TotalTradeVolume: Total trade volume

### Depth of Market (DOM)

```javascript
// Subscribe to DOM for a symbol
const unsubscribeDOM = await marketDataSocket.subscribe({
    url: 'md/subscribedom',
    body: { symbol: 'MNQM3' },
    subscription: (data) => {
        if (data && data.doms && data.doms.length > 0) {
            const dom = data.doms[0];
            console.log('DOM update:', dom);
        }
    }
});

// Later, to unsubscribe
unsubscribeDOM();
```

The DOM data includes:
- timestamp: When the DOM was generated
- contractId: ID of the contract
- bids: Array of bid prices and sizes, sorted by price (descending)
- offers: Array of offer prices and sizes, sorted by price (ascending)

### Histograms

```javascript
// Subscribe to histogram for a symbol
const unsubscribeHistogram = await marketDataSocket.subscribe({
    url: 'md/subscribehistogram',
    body: { symbol: 'MNQM3' },
    subscription: (data) => {
        if (data && data.histograms && data.histograms.length > 0) {
            const histogram = data.histograms[0];
            console.log('Histogram update:', histogram);
        }
    }
});

// Later, to unsubscribe
unsubscribeHistogram();
```

The histogram data includes:
- timestamp: When the histogram was generated
- contractId: ID of the contract
- tradeDate: Object with year, month, and day
- base: Base price
- items: Object with offsets as keys and values as counts
- refresh: Boolean indicating whether to refresh

### Chart Data

#### Regular Charts (OHLC)

```javascript
// Get regular chart data for a symbol
const unsubscribeChart = await marketDataSocket.subscribe({
    url: 'md/getchart',
    body: {
        symbol: 'MNQM3',
        chartDescription: {
            underlyingType: 'MinuteBar', // MinuteBar, DailyBar
            elementSize: 30, // 30 minutes
            elementSizeUnit: 'UnderlyingUnits',
            withHistogram: false
        },
        timeRange: {
            asMuchAsElements: 100 // Get 100 bars
        }
    },
    subscription: (chart) => {
        // Check for end of history
        if (chart.eoh) {
            console.log('End of chart history reached');
            return;
        }

        // Process chart data
        if (chart.bars && chart.bars.length > 0) {
            // Process the bars
            chart.bars.forEach(bar => {
                const { high, low, open, close, timestamp } = bar;
                // Use the bar data for OHLC chart
            });
        }
    }
});

// Later, to unsubscribe
unsubscribeChart();
```

The regular chart bar data includes:
- timestamp: When the bar was generated
- open: Opening price
- high: Highest price
- low: Lowest price
- close: Closing price
- upVolume: Up volume
- downVolume: Down volume
- upTicks: Up ticks
- downTicks: Down ticks
- bidVolume: Bid volume
- offerVolume: Offer volume

#### Tick Charts

```javascript
// Get tick chart data for a symbol
const unsubscribeTickChart = await marketDataSocket.subscribe({
    url: 'md/getchart',
    body: {
        symbol: 'MNQM3',
        chartDescription: {
            underlyingType: 'Tick', // Must be 'Tick' for tick charts
            elementSize: 1, // Always 1 for Tick charts
            elementSizeUnit: 'UnderlyingUnits',
            withHistogram: false
        },
        timeRange: {
            asMuchAsElements: 100 // Get 100 ticks
        }
    },
    subscription: (chart) => {
        // Check for end of history
        if (chart.eoh) {
            console.log('End of tick chart history reached');
            return;
        }

        // Process tick chart data
        if (chart.tks && chart.tks.length > 0) {
            const { bt: baseTimestamp, bp: basePrice, ts: tickSize } = chart;

            // Process ticks
            chart.tks.forEach(tick => {
                const { t: relativeTime, p: relativePrice } = tick;

                // Calculate actual timestamp and price
                const timestamp = new Date(baseTimestamp + relativeTime);
                const price = (basePrice + relativePrice) * tickSize;

                // Use the tick data for line chart
            });
        }
    }
});

// Later, to unsubscribe
unsubscribeTickChart();
```

The tick chart data structure is different from regular charts:
- bt: Base timestamp of the packet
- bp: Base price of the packet (integer number of contract tick sizes)
- ts: Tick size of the contract
- tks: Array of ticks with the following properties:
  - t: Tick relative timestamp (actual timestamp = bt + t)
  - p: Tick relative price (actual price = (bp + p) * ts)
  - s: Tick size (volume)
  - b: Bid relative price (optional)
  - a: Ask relative price (optional)
  - bs: Bid size (optional)
  - as: Ask size (optional)
  - id: Tick ID

The chart request parameters include:
- symbol: Contract symbol or ID
- chartDescription: Configuration for the chart
  - underlyingType: Type of chart (MinuteBar, Tick, DailyBar, Custom, DOM)
  - elementSize: Size of each element
  - elementSizeUnit: Unit for element size (UnderlyingUnits, Volume, Range, etc.)
  - withHistogram: Whether to include histogram data
- timeRange: Time range for the chart
  - closestTimestamp: Most recent timestamp
  - closestTickId: Most recent tick ID
  - asFarAsTimestamp: Most distant timestamp
  - asMuchAsElements: Number of elements to retrieve

## Real-Time P&L Calculation

The Tradovate WebSocket API allows you to calculate profit and loss (P&L) in real-time by combining position data from user sync requests with real-time market data:

```javascript
// Subscribe to user sync to get positions
const unsubscribeUserSync = await socket.subscribe({
    url: 'user/syncrequest',
    body: { users: [userId] },
    subscription: (data) => {
        if (data.users) {
            const { positions, contracts, products } = data;

            // Process each position
            positions.forEach(async (pos) => {
                // Skip positions with no net position
                if (pos.netPos === 0 && pos.prevPos === 0) return;

                // Get contract name and value per point
                const contract = contracts.find(c => c.id === pos.contractId);
                const product = products.find(p => p.name.startsWith(contract.name.substring(0, 3)));
                const vpp = product.valuePerPoint;

                // Subscribe to quotes for real-time price updates
                await marketDataSocket.subscribe({
                    url: 'md/subscribequote',
                    body: { symbol: contract.name },
                    subscription: (quoteData) => {
                        if (quoteData && quoteData.quotes && quoteData.quotes.length > 0) {
                            const quote = quoteData.quotes[0];

                            // Get buy price and current price
                            const buyPrice = pos.netPrice || pos.prevPrice;
                            const currentPrice = quote.entries.Trade.price;

                            // Calculate P&L
                            // P&L = (Current Price - Buy Price) * Value Per Point * Contract Qty
                            const pl = (currentPrice - buyPrice) * vpp * pos.netPos;

                            // Update UI with P&L information
                            console.log(`P&L for ${contract.name}: $${pl.toFixed(2)}`);
                        }
                    }
                });
            });
        }
    }
});
```

The P&L calculation formula is:
```
P&L = (Current Price - Buy Price) * Value Per Point * Contract Qty
```

Where:
- Current Price: The latest trade price from real-time quotes
- Buy Price: The price at which the position was opened (netPrice or prevPrice)
- Value Per Point: The dollar value of a one-point move in the contract (from product data)
- Contract Qty: The number of contracts in the position (netPos)

## Real-Time User Data Subscriptions

The TradovateSocket class supports real-time subscriptions for continuous user data updates:

```javascript
// Subscribe to user sync updates
socket.subscribe({
    url: 'user/syncrequest',
    body: { users: [userId] },
    subscription: (data) => {
        if (data.users) {
            // Initial response with all user data
            console.log('Initial user data:', data);
        } else {
            // Subsequent updates
            console.log('Update:', data.entityType, data.eventType, data.entity);
        }
    }
});
```

The initial response contains all current user data:
- accounts
- positions
- orders
- fills
- contracts
- products
- and more...

Subsequent updates include:
- entityType (what type of entity changed)
- eventType (what happened - added, updated, removed)
- entity (the actual data that changed)

This subscription mechanism allows for real-time tracking of account changes, order status, positions, and more.

## References

- [Tradovate API Documentation](https://api.tradovate.com/)
- [Tradovate Community](https://community.tradovate.com/)
