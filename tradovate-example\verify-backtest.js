/**
 * verify-backtest.js
 * Script to verify that our new implementation produces the same results as the original backtest
 */

const fs = require('fs');
const path = require('path');
const csvParser = require('csv-parser');
const { Readable } = require('stream');

// Import our modules
const patternDetection = require('../pattern_detection');
const enhancedPM = require('../enhanced_position_management');
const spreadUtils = require('../spread_volatility_utils');
const { mnqConfig, mesConfig, mgcConfig } = require('../multi_symbol_config');

// Import exact implementations from original backtest
const exactPatternDetection = require('../exact_pattern_detection');
const exactPM = require('../exact_position_management');

// Helper function to parse CSV data
async function parseCSV(filePath, separator = ';') {
    return new Promise((resolve, reject) => {
        const results = [];

        if (!fs.existsSync(filePath)) {
            reject(new Error(`File not found: ${filePath}`));
            return;
        }

        const fileContent = fs.readFileSync(filePath, 'utf8');

        const readable = Readable.from([fileContent]);
        readable
            .pipe(csvParser({ separator, mapHeaders: ({ header }) => header.trim() }))
            .on('data', (data) => {
                const open = +data['Open'];
                const high = +data['High'];
                const low = +data['Low'];
                const close = +data['Close'];
                const timeString = data['Time'] || data['Date'] || data['Time left'];

                let timestampSeconds = NaN;
                if (timeString) {
                    let parsedDate;
                    try {
                        parsedDate = new Date(timeString);
                    } catch (e) {}

                    if (parsedDate && !isNaN(parsedDate.getTime())) {
                        timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
                    } else if (timeString && !isNaN(Number(timeString))) {
                        const tsNum = Number(timeString);
                        timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
                    }
                }

                if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
                    return;
                }

                results.push({ timestamp: timestampSeconds, open, high, low, close });
            })
            .on('end', () => {
                resolve(results);
            })
            .on('error', (err) => {
                reject(err);
            });
    });
}

// Helper function to calculate indicators
function calculateIndicators(candles, config) {
    console.log("Calculating indicators...");

    // Extract close prices
    const closes = candles.map(c => c.close);

    // Calculate SMA200
    const sma200Arr = [];
    for (let i = 0; i < candles.length; i++) {
        if (i < config.sma200Period - 1) {
            sma200Arr.push(NaN);
        } else {
            let sum = 0;
            for (let j = i - config.sma200Period + 1; j <= i; j++) {
                sum += closes[j];
            }
            sma200Arr.push(sum / config.sma200Period);
        }
    }

    // Calculate WMA50
    const wma50Arr = [];
    for (let i = 0; i < candles.length; i++) {
        if (i < config.wma50Period - 1) {
            wma50Arr.push(NaN);
        } else {
            let weightedSum = 0;
            let weightSum = 0;

            for (let j = 0; j < config.wma50Period; j++) {
                const idx = i - j;
                const weight = config.wma50Period - j;

                weightedSum += closes[idx] * weight;
                weightSum += weight;
            }

            wma50Arr.push(weightedSum / weightSum);
        }
    }

    // Calculate RSI
    const rsiArr = [];
    for (let i = 0; i < candles.length; i++) {
        if (i < config.rsiPeriod) {
            rsiArr.push(NaN);
        } else {
            let gains = 0;
            let losses = 0;

            for (let j = i - config.rsiPeriod + 1; j <= i; j++) {
                if (j > 0) {
                    const delta = closes[j] - closes[j - 1];

                    if (delta > 0) {
                        gains += delta;
                    } else {
                        losses -= delta;
                    }
                }
            }

            if (losses === 0) {
                rsiArr.push(100);
            } else if (gains === 0) {
                rsiArr.push(0);
            } else {
                const rs = (gains / config.rsiPeriod) / (losses / config.rsiPeriod);
                rsiArr.push(100 - (100 / (1 + rs)));
            }
        }
    }

    // Calculate RSI MA
    const rsiMaArr = [];
    for (let i = 0; i < candles.length; i++) {
        if (i < config.rsiPeriod + config.rsiMaPeriod - 1) {
            rsiMaArr.push(NaN);
        } else {
            let sum = 0;
            for (let j = i - config.rsiMaPeriod + 1; j <= i; j++) {
                sum += rsiArr[j];
            }
            rsiMaArr.push(sum / config.rsiMaPeriod);
        }
    }

    // Calculate ATR
    const atrArr = [];
    for (let i = 0; i < candles.length; i++) {
        if (i === 0) {
            atrArr.push(candles[i].high - candles[i].low);
        } else if (i < config.atrPeriod) {
            const tr = Math.max(
                candles[i].high - candles[i].low,
                Math.abs(candles[i].high - candles[i - 1].close),
                Math.abs(candles[i].low - candles[i - 1].close)
            );
            atrArr.push(tr);
        } else {
            const tr = Math.max(
                candles[i].high - candles[i].low,
                Math.abs(candles[i].high - candles[i - 1].close),
                Math.abs(candles[i].low - candles[i - 1].close)
            );

            // Use smoothed ATR calculation
            atrArr.push(((config.atrPeriod - 1) * atrArr[i - 1] + tr) / config.atrPeriod);
        }
    }

    // Add indicators to candles
    for (let i = 0; i < candles.length; i++) {
        candles[i].sma200 = sma200Arr[i];
        candles[i].wma50 = wma50Arr[i];
        candles[i].rsi = rsiArr[i];
        candles[i].rsiMa = rsiMaArr[i];
        candles[i].atr = atrArr[i];
    }

    console.log("Indicators calculated.");
    return candles;
}

// Main function to run the backtest
async function runBacktest(symbolOrConfig, returnTrades = false) {
    try {
        // Get the appropriate config for the symbol
        let config;
        let symbol;

        if (typeof symbolOrConfig === 'string') {
            symbol = symbolOrConfig;
            switch (symbol) {
                case 'MNQ':
                    config = { ...mnqConfig };
                    break;
                case 'MES':
                    config = { ...mesConfig };
                    break;
                case 'MGC':
                    config = { ...mgcConfig };
                    break;
                default:
                    throw new Error(`Unknown symbol: ${symbol}`);
            }
        } else if (typeof symbolOrConfig === 'object') {
            // Custom config provided
            symbol = symbolOrConfig.symbol;

            // Get base config
            let baseConfig;
            switch (symbol) {
                case 'MNQ':
                    baseConfig = { ...mnqConfig };
                    break;
                case 'MES':
                    baseConfig = { ...mesConfig };
                    break;
                case 'MGC':
                    baseConfig = { ...mgcConfig };
                    break;
                default:
                    throw new Error(`Unknown symbol: ${symbol}`);
            }

            // Merge custom config with base config
            config = { ...baseConfig, ...symbolOrConfig };

            // Update adaptive parameters if needed
            if (config.slFactors !== undefined && config.tpFactors !== undefined && config.trailFactors !== undefined) {
                config.adaptiveParams = {
                    Low: {
                        slFactor: config.slFactors,
                        tpFactor: config.tpFactors,
                        trailFactor: config.trailFactors
                    },
                    Medium: {
                        slFactor: config.slFactors,
                        tpFactor: config.tpFactors,
                        trailFactor: config.trailFactors
                    },
                    High: {
                        slFactor: config.slFactors,
                        tpFactor: config.tpFactors,
                        trailFactor: config.trailFactors
                    }
                };
            }
        } else {
            throw new Error('Invalid argument: expected string or object');
        }

        console.log(`Running backtest for ${symbol} with config:`, config);

        // Load data
        const candles = await parseCSV(config.inputFile);
        console.log(`Loaded ${candles.length} candles from ${config.inputFile}`);

        // Sort candles by timestamp
        candles.sort((a, b) => a.timestamp - b.timestamp);

        // Calculate indicators
        const candlesWithIndicators = calculateIndicators(candles, config);

        // Run backtest
        const results = backtest(candlesWithIndicators, config);

        // Calculate daily statistics
        const dailyStats = calculateDailyStats(results.trades, config);
        results.dailyStats = dailyStats;

        // Display results
        console.log(`\n--- BACKTEST RESULTS FOR ${symbol} ---`);
        console.log(`Total trades: ${results.totalTrades}`);
        console.log(`Wins: ${results.wins} (${(results.wins / results.totalTrades * 100).toFixed(2)}%)`);
        console.log(`Losses: ${results.losses} (${(results.losses / results.totalTrades * 100).toFixed(2)}%)`);
        console.log(`Final balance: $${results.finalBalance.toFixed(2)}`);
        console.log(`Total P&L: $${(results.finalBalance - config.initialBalance).toFixed(2)}`);
        console.log(`Max drawdown: $${results.maxDrawdown.toFixed(2)}`);

        // Add win rate and PnL to results for grid testing
        results.winRate = (results.wins / results.totalTrades) * 100;
        results.pnl = results.finalBalance - config.initialBalance;
        results.trades = results.totalTrades;

        // Include detailed trade data if requested
        if (returnTrades) {
            results.trades = results.completedTradesDetails;
        }

        return results;
    } catch (error) {
        console.error('Error running backtest:', error);
        throw error;
    }
}

// Calculate daily statistics
function calculateDailyStats(trades, config) {
    const dailyStats = {};

    if (!trades || !Array.isArray(trades)) {
        return dailyStats;
    }

    for (const trade of trades) {
        if (!trade.exitTimestamp) continue;

        // Get date string (YYYY-MM-DD)
        const date = new Date(trade.exitTimestamp).toISOString().split('T')[0];

        if (!dailyStats[date]) {
            dailyStats[date] = {
                trades: 0,
                wins: 0,
                losses: 0,
                grossPnL: 0,
                netPnL: 0,
                commissions: 0
            };
        }

        dailyStats[date].trades++;

        if (trade.pnlNetTotal > 0) {
            dailyStats[date].wins++;
        } else {
            dailyStats[date].losses++;
        }

        dailyStats[date].grossPnL += trade.pnlGrossTotal;
        dailyStats[date].netPnL += trade.pnlNetTotal;
        dailyStats[date].commissions += trade.commCost;
    }

    return dailyStats;
}

// Backtest function
function backtest(candles, config) {
    // Set up global config for pattern detection
    global.config = config;

    // Initialize variables
    let balance = config.initialBalance;
    let position = null;
    let trades = 0;
    let wins = 0;
    let losses = 0;
    let peakBalance = balance;
    let maxDrawdown = 0;

    // Track all trades for daily statistics
    const allTrades = [];

    // Track completed trades with detailed information
    const completedTradesDetails = [];

    // Add completedTradesDetails to results object
    const results = {
        totalTrades: 0,
        wins: 0,
        losses: 0,
        finalBalance: 0,
        maxDrawdown: 0,
        completedTradesDetails: completedTradesDetails
    };

    // Loop through candles
    for (let i = 3; i < candles.length; i++) {
        const c0 = candles[i - 3];
        const c1 = candles[i - 2];
        const c2 = candles[i - 1];
        const c3 = candles[i];

        // Skip if indicators are not available
        if (isNaN(c3.wma50) || isNaN(c3.sma200) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
            continue;
        }

        // Check for entry signal if no position
        if (!position) {
            // Use exact pattern detection from original backtest
            const pattern3 = exactPatternDetection.detect3(c1, c2, c3);
            const pattern4 = exactPatternDetection.detect4(c0, c1, c2, c3);

            // Use the pattern that was detected
            let pattern = null;
            let patternType = null;

            if (pattern4) {
                pattern = pattern4;
                patternType = 'four';
            } else if (pattern3) {
                pattern = pattern3;
                patternType = 'three';
            }

            // Validate entry using exact implementation
            if (pattern && exactPatternDetection.entryOK(pattern, patternType, c3, i, candles, config)) {
                // Determine ATR regime
                const atrRegime = c3.atr < config.atrThresholds.low_medium ? 'Low' :
                                 (c3.atr > config.atrThresholds.medium_high ? 'High' : 'Medium');

                // Get adaptive parameters based on ATR regime
                let slFactor, tpFactor, trailFactor;

                if (config.isAdaptiveRun) {
                    slFactor = config.adaptiveParams[atrRegime].slFactor;
                    tpFactor = config.adaptiveParams[atrRegime].tpFactor;
                    trailFactor = config.adaptiveParams[atrRegime].trailFactor;
                } else {
                    slFactor = config.slFactors;
                    tpFactor = config.tpFactors;
                    trailFactor = config.trailFactors;
                }

                // Calculate stop loss and take profit distances
                const slDistance = c3.atr * slFactor;
                const atrTpDistance = c3.atr * tpFactor;
                const fixTpDistance = config.fixedTpPoints || 0;
                const tpDistance = Math.max(fixTpDistance, atrTpDistance);

                // Calculate stop loss and take profit prices
                const stopLossPrice = pattern === 'bullish' ?
                    c3.close - slDistance :
                    c3.close + slDistance;

                const takeProfitPrice = pattern === 'bullish' ?
                    c3.close + tpDistance :
                    c3.close - tpDistance;

                // Calculate trail stop price
                const trailStopPrice = pattern === 'bullish' ?
                    c3.close - (c3.atr * trailFactor) :
                    c3.close + (c3.atr * trailFactor);

                // Simplified entry handling - no complex latency
                const entryP = c3.close;
                const entryTs = new Date(c3.timestamp * 1000);
                const entryIdx = i;

                // Create position exactly as in the original backtest
                position = {
                    dir: pattern,
                    entry: entryP,
                    atr: c3.atr,
                    tpDistance: tpDistance,
                    stopLossPrice: pattern === 'bullish' ? entryP - slDistance : entryP + slDistance,
                    trailStopPrice: pattern === 'bullish' ? entryP - (c3.atr * trailFactor) : entryP + (c3.atr * trailFactor),
                    trailFactor: trailFactor,
                    trailHigh: c3.high,
                    trailLow: c3.low,
                    contracts: config.fixedContracts,
                    entryTimestamp: entryTs,
                    entryBarIndex: entryIdx,
                    currentBarIndex: entryIdx,
                    entryAtrRegime: atrRegime,
                    tpType: fixTpDistance > atrTpDistance ? 'Fixed' : 'ATR'
                };

                trades++;
            }
        } else {
            // Use exact position management from original backtest
            const exitCounts = {};
            const completedTrades = [];

            const exitInfo = exactPM.managePosition(
                position,
                c3,
                i,
                candles,
                completedTrades,
                config,
                exitCounts
            );

            // Store completed trade details if available
            if (completedTrades.length > 0) {
                completedTradesDetails.push(completedTrades[0]);
            }

            if (exitInfo) {
                // Update balance
                balance += exitInfo.pnlNetTotal;

                // Update statistics
                if (exitInfo.pnlNetTotal > 0) {
                    wins++;
                } else {
                    losses++;
                }

                // Update peak balance and max drawdown
                peakBalance = Math.max(peakBalance, balance);
                maxDrawdown = Math.max(maxDrawdown, peakBalance - balance);

                // Add trade to allTrades for daily statistics
                allTrades.push({
                    entryTimestamp: position.entryTimestamp,
                    exitTimestamp: exitInfo.exitTimestamp,
                    pnlGrossTotal: exitInfo.pnlNetTotal + (completedTrades.length > 0 ? parseFloat(completedTrades[0].CommissionCost) : 0),
                    pnlNetTotal: exitInfo.pnlNetTotal,
                    commCost: completedTrades.length > 0 ? parseFloat(completedTrades[0].CommissionCost) : 0,
                    direction: position.dir,
                    exitReason: completedTrades.length > 0 ? completedTrades[0].Reason : 'unknown'
                });

                // Reset position
                position = null;
            }
        }
    }

    // Close any open position at the end using exact implementation
    if (position) {
        const exitCounts = {};
        const completedTrades = [];

        const exitInfo = exactPM.handleEndOfPeriodExit(
            position,
            candles,
            completedTrades,
            config,
            exitCounts
        );

        if (exitInfo) {
            // Update balance
            balance += exitInfo.pnlNetTotal;

            // Update statistics
            if (exitInfo.pnlNetTotal > 0) {
                wins++;
            } else {
                losses++;
            }

            // Update peak balance and max drawdown
            peakBalance = Math.max(peakBalance, balance);
            maxDrawdown = Math.max(maxDrawdown, peakBalance - balance);

            // Add trade to allTrades for daily statistics
            allTrades.push({
                entryTimestamp: position.entryTimestamp,
                exitTimestamp: exitInfo.exitTimestamp,
                pnlGrossTotal: exitInfo.pnlNetTotal + (completedTrades.length > 0 ? parseFloat(completedTrades[0].CommissionCost) : 0),
                pnlNetTotal: exitInfo.pnlNetTotal,
                commCost: completedTrades.length > 0 ? parseFloat(completedTrades[0].CommissionCost) : 0,
                direction: position.dir,
                exitReason: 'end_of_period'
            });
        }
    }

    // Update results object
    results.totalTrades = trades;
    results.wins = wins;
    results.losses = losses;
    results.finalBalance = balance;
    results.maxDrawdown = maxDrawdown;
    results.trades = allTrades;
    results.completedTradesDetails = completedTradesDetails;

    return results;
}

// Run the backtest for each symbol
async function main() {
    try {
        await runBacktest('MNQ');
        await runBacktest('MES');
        await runBacktest('MGC');
    } catch (error) {
        console.error('Error in main function:', error);
    }
}

// Export functions for grid testing
module.exports = { runBacktest };

// Run main if this is the main module
if (require.main === module) {
    main();
}
