// run_m2k_hourly_analysis.js - Run the hourly analysis and visualize results

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('=== M2K Hourly Performance Analysis ===');
console.log('Running analysis script...');

// Run the analysis script
const analyzer = spawn('node', ['analyze_m2k_hourly_performance.js'], {
  stdio: 'inherit'
});

analyzer.on('close', (code) => {
  if (code !== 0) {
    console.error(`Analysis script exited with code ${code}`);
    process.exit(code);
  }
  
  console.log('\nAnalysis complete. Creating visualization...');
  
  // Create HTML visualization
  createHourlyVisualization();
});

function createHourlyVisualization() {
  const outputDir = './output/M2K_HourlyAnalysis';
  const hourlyStatsFile = path.join(outputDir, 'hourly_stats.csv');
  const timeFilterFile = path.join(outputDir, 'time_filter_config.json');
  
  if (!fs.existsSync(hourlyStatsFile)) {
    console.error(`Error: Hourly stats file not found at ${hourlyStatsFile}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(timeFilterFile)) {
    console.error(`Error: Time filter config file not found at ${timeFilterFile}`);
    process.exit(1);
  }
  
  // Read hourly stats
  const hourlyStatsCSV = fs.readFileSync(hourlyStatsFile, 'utf8');
  const timeFilterConfig = JSON.parse(fs.readFileSync(timeFilterFile, 'utf8'));
  
  // Create HTML content
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>M2K Hourly Performance Analysis</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .chart-container {
      position: relative;
      height: 400px;
      margin-bottom: 30px;
    }
    .metrics {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    .metric-card {
      flex: 1;
      min-width: 200px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #0066cc;
    }
    .optimal-hours {
      background-color: #e6f7ff;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .hour-tag {
      display: inline-block;
      background-color: #0066cc;
      color: white;
      padding: 5px 10px;
      border-radius: 15px;
      margin: 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .optimal {
      background-color: #e6f7ff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>M2K Hourly Performance Analysis</h1>
    
   