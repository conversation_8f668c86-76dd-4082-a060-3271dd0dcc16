/**
 * MGC (Micro Gold) Trading Configuration
 *
 * This file contains the configuration for trading MGC futures.
 * Parameters are based on the backtest results.
 */

module.exports = {
    // Symbol information
    symbol: 'MGC',
    pointValue: 10.0,
    tickSize: 0.10,

    // Contract information
    contractMonth: 'M5', // May 2025 contract

    // Account information
    accountId: null, // Will be set during initialization

    // Commission and slippage
    commission: 0.40,
    commissionPerContract: 0.40,
    slippage: 0.0,
    slippagePoints: 0.0,

    // Position sizing
    fixedContracts: 3,

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    sma200Period: 0, // Not used
    atrPeriod: 14,

    // Entry/exit parameters
    slFactors: 8.0,
    tpFactors: 7.0,
    trailFactors: 0.02,
    fixedTpPoints: 0,

    // Risk management
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0.5,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    useTwoBarColorExit: false,

    // ATR thresholds for adaptive mode
    isAdaptiveRun: false,
    atrThresholds: {
        low_medium: 1.5,
        medium_high: 3.0
    },

    // Formatting
    pricePrecision: 2,

    // Logging
    logLevel: 'info'
};
