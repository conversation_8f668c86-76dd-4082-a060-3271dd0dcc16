/**
 * Generate Test Data
 * 
 * This script generates synthetic test data for backtesting.
 */

const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const symbolArg = args.find(arg => arg.startsWith('--symbol='));
const daysArg = args.find(arg => arg.startsWith('--days='));
const startDateArg = args.find(arg => arg.startsWith('--start='));

// Default values
const defaultSymbols = ['MNQ', 'MES', 'MGC', 'M2K'];
const defaultDays = 5;
const defaultStartDate = new Date('2025-01-01T00:00:00Z');

// Parse symbols
let symbols = defaultSymbols;
if (symbolArg) {
    const symbolValue = symbolArg.split('=')[1];
    if (symbolValue === 'all') {
        symbols = defaultSymbols;
    } else {
        symbols = symbolValue.split(',');
    }
}

// Parse days
let days = defaultDays;
if (daysArg) {
    const daysValue = daysArg.split('=')[1];
    days = parseInt(daysValue, 10);
}

// Parse start date
let startDate = defaultStartDate;
if (startDateArg) {
    const startDateValue = startDateArg.split('=')[1];
    startDate = new Date(startDateValue);
}

// Symbol configurations
const symbolConfig = {
    MNQ: {
        basePrice: 17500,
        volatility: 50,
        pointValue: 0.5,
        minTick: 0.25
    },
    MES: {
        basePrice: 5200,
        volatility: 10,
        pointValue: 5.0,
        minTick: 0.25
    },
    MGC: {
        basePrice: 2500,
        volatility: 20,
        pointValue: 10.0,
        minTick: 0.1
    },
    M2K: {
        basePrice: 2000,
        volatility: 15,
        pointValue: 5.0,
        minTick: 0.1
    }
};

// Generate random price data
function generatePriceData(symbol, days, startDate) {
    const config = symbolConfig[symbol] || symbolConfig.MNQ;
    const minutesPerDay = 24 * 60; // 24 hours * 60 minutes
    const totalMinutes = days * minutesPerDay;
    const data = [];
    
    let currentPrice = config.basePrice;
    let currentDate = new Date(startDate);
    
    for (let i = 0; i < totalMinutes; i++) {
        // Generate random price movement
        const movement = (Math.random() - 0.5) * config.volatility * 0.01;
        currentPrice = currentPrice * (1 + movement);
        
        // Round to min tick
        currentPrice = Math.round(currentPrice / config.minTick) * config.minTick;
        
        // Generate OHLC data
        const volatilityFactor = config.volatility * 0.01;
        const open = currentPrice;
        const high = open * (1 + Math.random() * volatilityFactor * 0.5);
        const low = open * (1 - Math.random() * volatilityFactor * 0.5);
        const close = open * (1 + (Math.random() - 0.5) * volatilityFactor);
        
        // Round to min tick
        const roundedOpen = Math.round(open / config.minTick) * config.minTick;
        const roundedHigh = Math.round(Math.max(high, roundedOpen) / config.minTick) * config.minTick;
        const roundedLow = Math.round(Math.min(low, roundedOpen) / config.minTick) * config.minTick;
        const roundedClose = Math.round(close / config.minTick) * config.minTick;
        
        // Generate volume
        const volume = Math.round(Math.random() * 1000);
        
        // Add data point
        data.push({
            timestamp: new Date(currentDate),
            open: roundedOpen,
            high: roundedHigh,
            low: roundedLow,
            close: roundedClose,
            volume
        });
        
        // Increment date by 1 minute
        currentDate = new Date(currentDate.getTime() + 60000);
    }
    
    return data;
}

// Save data to CSV
function saveToCSV(symbol, data) {
    const outputDir = path.join('C:', 'backtest-bot', 'input');
    const outputFile = path.join(outputDir, `${symbol}_test.csv`);
    
    // Create CSV content
    let csvContent = 'timestamp,open,high,low,close,volume\n';
    
    for (const point of data) {
        csvContent += `${point.timestamp.toISOString()},${point.open},${point.high},${point.low},${point.close},${point.volume}\n`;
    }
    
    // Save to file
    fs.writeFileSync(outputFile, csvContent);
    
    console.log(`Saved ${data.length} data points to ${outputFile}`);
}

// Main function
function main() {
    console.log('Generating test data...');
    console.log(`Symbols: ${symbols.join(', ')}`);
    console.log(`Days: ${days}`);
    console.log(`Start Date: ${startDate.toISOString()}`);
    
    for (const symbol of symbols) {
        console.log(`\nGenerating data for ${symbol}...`);
        
        const data = generatePriceData(symbol, days, startDate);
        saveToCSV(symbol, data);
    }
    
    console.log('\nData generation complete!');
}

// Run the main function
main();
