#!/usr/bin/env python3
"""
Manually fix CSV files by reading and writing line by line.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def manual_fix_csv(file_path):
    """
    Manually fix a CSV file by reading and writing line by line.
    
    Args:
        file_path (str): Path to CSV file
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Manually fixing {file_path}...")
        
        # Read the file
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        if not lines:
            logger.error(f"Empty file: {file_path}")
            return False
        
        # Fix header line
        header_line = lines[0].strip()
        logger.info(f"Original header: {header_line}")
        
        # Remove extra columns
        if ',' in header_line:
            parts = header_line.split(',')
            if len(parts) > 6:
                fixed_header = ','.join(parts[:6])
                logger.info(f"Fixed header: {fixed_header}")
            else:
                fixed_header = header_line
                logger.info("Header already fixed")
        else:
            fixed_header = header_line
            logger.info("Header has no commas")
        
        # Fix data lines
        fixed_lines = [fixed_header + '\n']
        for line in lines[1:]:
            line = line.strip()
            if ',' in line:
                parts = line.split(',')
                if len(parts) > 6:
                    fixed_line = ','.join(parts[:6])
                    fixed_lines.append(fixed_line + '\n')
                else:
                    fixed_lines.append(line + '\n')
            else:
                fixed_lines.append(line + '\n')
        
        # Write the fixed file
        with open(file_path, 'w') as f:
            f.writelines(fixed_lines)
        
        logger.info(f"Saved fixed file to {file_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error fixing {file_path}: {str(e)}")
        return False

def main():
    """Main function."""
    # Configure paths
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    
    # Find CSV files
    csv_files = [f for f in os.listdir(input_dir) if f.endswith('_1m.csv')]
    
    if not csv_files:
        logger.error("No CSV files found")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files")
    
    # Process each CSV file
    for csv_file in csv_files:
        file_path = os.path.join(input_dir, csv_file)
        success = manual_fix_csv(file_path)
        
        if not success:
            logger.error(f"Failed to fix {csv_file}")
    
    logger.info("Data fixing complete!")

if __name__ == "__main__":
    main()
