/**
 * Convert Databento Data to Backtest Format
 *
 * This script converts Databento data to the format expected by the backtest system.
 * It handles multiple contract months and creates a continuous contract.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Parse command line arguments
const args = process.argv.slice(2);
const inputFileArg = args.find(arg => arg.startsWith('--input='));
const outputFileArg = args.find(arg => arg.startsWith('--output='));
const symbolArg = args.find(arg => arg.startsWith('--symbol='));

// Default values
const defaultInputFile = 'C:/backtest-bot/input/glbx-mdp3-20250101-20250511.ohlcv-1m.csv';
const defaultOutputFile = 'C:/backtest-bot/input/MNQ_Databento_2025.csv';
const defaultSymbol = 'MNQ';

// Parse input file
let inputFile = defaultInputFile;
if (inputFileArg) {
    inputFile = inputFileArg.split('=')[1];
}

// Parse output file
let outputFile = defaultOutputFile;
if (outputFileArg) {
    outputFile = outputFileArg.split('=')[1];
}

// Parse symbol
let symbol = defaultSymbol;
if (symbolArg) {
    symbol = symbolArg.split('=')[1];
}

// Print configuration
console.log('Convert Databento Data to Backtest Format');
console.log(`Input File: ${inputFile}`);
console.log(`Output File: ${outputFile}`);
console.log(`Symbol: ${symbol}`);

// Create a map to store the latest data for each timestamp
// This handles the case where multiple contract months have data for the same timestamp
const timestampMap = new Map();

// Create a function to process the input file
async function processInputFile() {
    console.log('Processing input file...');

    // Create a read stream
    const fileStream = fs.createReadStream(inputFile);

    // Create a readline interface
    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    // Process each line
    let lineCount = 0;
    let headerProcessed = false;

    for await (const line of rl) {
        lineCount++;

        // Skip empty lines
        if (!line.trim()) {
            continue;
        }

        // Process header
        if (!headerProcessed) {
            headerProcessed = true;
            continue;
        }

        // Parse the line
        const columns = line.split(',');

        // Check if we have enough columns
        if (columns.length < 10) {
            console.warn(`Line ${lineCount} has insufficient columns: ${line}`);
            continue;
        }

        // Extract data
        const timestamp = columns[0];
        const open = parseFloat(columns[4]);
        const high = parseFloat(columns[5]);
        const low = parseFloat(columns[6]);
        const close = parseFloat(columns[7]);
        const volume = parseInt(columns[8], 10);
        const contractSymbol = columns[9];

        // Check if this is the symbol we're interested in
        if (!contractSymbol.startsWith(symbol)) {
            continue;
        }

        // Format timestamp (remove nanoseconds)
        const formattedTimestamp = timestamp.split('.')[0] + 'Z';

        // Store the data in the timestamp map
        // If we already have data for this timestamp, use the one with higher volume
        if (timestampMap.has(formattedTimestamp)) {
            const existingData = timestampMap.get(formattedTimestamp);

            // Use the data with higher volume
            if (volume > existingData.volume) {
                timestampMap.set(formattedTimestamp, {
                    timestamp: formattedTimestamp,
                    open,
                    high,
                    low,
                    close,
                    volume,
                    contractSymbol
                });
            }
        } else {
            timestampMap.set(formattedTimestamp, {
                timestamp: formattedTimestamp,
                open,
                high,
                low,
                close,
                volume,
                contractSymbol
            });
        }
    }

    console.log(`Processed ${lineCount} lines`);
    console.log(`Found ${timestampMap.size} unique timestamps`);

    // Sort the data by timestamp
    const sortedData = Array.from(timestampMap.values()).sort((a, b) => {
        return new Date(a.timestamp) - new Date(b.timestamp);
    });

    return sortedData;
}

// Create a function to write the output file
function writeOutputFile(data) {
    console.log('Writing output file...');

    // Create the output directory if it doesn't exist
    const outputDir = path.dirname(outputFile);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create the CSV content
    let csvContent = 'Time;Open;High;Low;Close;Volume\n';

    // Add each data point
    for (const point of data) {
        csvContent += `${point.timestamp};${point.open};${point.high};${point.low};${point.close};${point.volume}\n`;
    }

    // Write the file
    fs.writeFileSync(outputFile, csvContent);

    console.log(`Wrote ${data.length} data points to ${outputFile}`);
}

// Main function
async function main() {
    try {
        // Process the input file
        const data = await processInputFile();

        // Write the output file
        writeOutputFile(data);

        console.log('Conversion complete!');
    } catch (error) {
        console.error('Error:', error);
    }
}

// Run the main function
main();
