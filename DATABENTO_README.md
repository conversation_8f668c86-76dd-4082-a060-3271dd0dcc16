# Databento Integration for Trading Bot

This document provides instructions on how to use the Databento integration with your trading bot.

## Overview

The trading bot now supports using Databento as a data source for both historical and real-time market data. This integration allows you to:

1. Use Databento as the primary data source instead of Tradovate
2. Use Databento for historical data while using Tradovate for real-time data
3. Use Databento for real-time data while using Tradovate for historical data
4. Use Databento as a backup data source if Tradovate fails

## Setup

1. Make sure you have a Databento account and API key
2. Update the `.env` file with your Databento API key
3. Configure the Databento integration using the environment variables in the `.env` file

## Configuration

The following environment variables can be set in the `.env` file:

- `DATABENTO_API_KEY`: Your Databento API key
- `USE_DATABENTO`: Set to `true` to use Databento as the primary data source
- `USE_DATABENTO_HISTORICAL`: Set to `true` to use Databento for historical data
- `USE_DATABENTO_REALTIME`: Set to `true` to use Databento for real-time data
- `USE_DATABENTO_BACKUP`: Set to `true` to use Databento as a backup data source (default: `true`)
- `DATABENTO_DATASET`: The Databento dataset to use (default: `GLBX.MDP3`)
- `DATABENTO_SCHEMA`: The Databento schema to use (default: `trades`)
- `DATABENTO_SYMBOL_TYPE`: The Databento symbol type to use (default: `parent`)
- `DATABENTO_USE_WEBSOCKET`: Set to `true` to use WebSocket for real-time data (default: `true`)

## Symbol Mapping

The trading bot maps Tradovate symbols to Databento symbols as follows:

- `MNQ` -> `MNQ.FUT`
- `MES` -> `ES.FUT`
- `MGC` -> `GC.FUT`
- `M2K` -> `RTY.FUT`

## Testing

To test the Databento integration, you can run the `test-databento.js` script:

```
node test-databento.js
```

This script will test the following:

1. Historical data retrieval
2. Real-time data streaming
3. Reference data retrieval

## Troubleshooting

If you encounter issues with the Databento integration, check the following:

1. Make sure your Databento API key is correct
2. Verify that you have the necessary permissions for the datasets you're trying to access
3. Check the logs for error messages
4. Make sure you have an active Databento subscription

## Switching Between Data Sources

You can switch between Tradovate and Databento as data sources by updating the `.env` file and restarting the trading bot. No code changes are required.

## Example Configurations

### Using Databento for Everything

```
USE_DATABENTO=true
USE_DATABENTO_HISTORICAL=true
USE_DATABENTO_REALTIME=true
USE_DATABENTO_BACKUP=true
```

### Using Databento for Historical Data Only

```
USE_DATABENTO=false
USE_DATABENTO_HISTORICAL=true
USE_DATABENTO_REALTIME=false
USE_DATABENTO_BACKUP=true
```

### Using Databento for Real-time Data Only

```
USE_DATABENTO=false
USE_DATABENTO_HISTORICAL=false
USE_DATABENTO_REALTIME=true
USE_DATABENTO_BACKUP=true
```

### Using Databento as Backup Only

```
USE_DATABENTO=false
USE_DATABENTO_HISTORICAL=false
USE_DATABENTO_REALTIME=false
USE_DATABENTO_BACKUP=true
```
