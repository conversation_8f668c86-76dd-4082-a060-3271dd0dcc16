<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Performance Forecasting</title>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Press+Start+2P&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
            --nasdaq: #6f42c1;
            --nasdaq-glow: rgba(111, 66, 193, 0.5);
            --sp500: #21ce99;
            --sp500-glow: rgba(33, 206, 153, 0.5);
            --gold: #FFD700;
            --gold-glow: rgba(255, 215, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        /* Improve readability */
        .menu-item-text, .stat-label, .card-title, .dashboard-title h1 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .dashboard-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
        }

        .dashboard-title h1 {
            margin-bottom: 0;
        }

        .forecast-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .dashboard-stats {
            display: flex;
            gap: 1rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            min-width: 150px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-value.positive {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }

        .stat-value.negative {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .card-title {
            font-size: 1.25rem;
            margin-bottom: 0;
        }

        .chart-container {
            position: relative;
            height: 250px;
            margin-top: 1rem;
        }

        .dashboard-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .forecast-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .forecast-table th, .forecast-table td {
            padding: 0.75rem;
            text-align: center;
            border: 1px solid var(--border);
        }

        .forecast-table th {
            background-color: rgba(0, 204, 255, 0.1);
            color: var(--primary);
            font-weight: 600;
        }

        .forecast-table td {
            font-weight: 500;
        }

        .calculator-form {
            margin-top: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            background-color: rgba(0, 204, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 0.25rem;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px var(--primary-glow);
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary);
            color: var(--light);
            border: none;
            border-radius: 0.25rem;
            font-family: 'Orbitron', sans-serif;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background-color: var(--primary-dark);
            box-shadow: 0 0 10px var(--primary-glow);
        }

        .result-card {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: rgba(0, 204, 255, 0.05);
            border: 1px solid var(--border);
            border-radius: 0.25rem;
        }

        .result-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
        }

        .result-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .result-label {
            color: var(--text-secondary);
        }

        .result-value {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            color: var(--primary);
        }

        .result-value.positive {
            color: var(--success);
        }

        .result-value.negative {
            color: var(--danger);
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                flex-wrap: wrap;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<div class="dashboard-header">
<div class="dashboard-title">
<div class="forecast-icon">🔮</div>
<h1>PERFORMANCE FORECASTING</h1>
</div>
<div class="dashboard-stats">
<div class="stat-card">
<div class="stat-label">Projected Annual Return</div>
<div class="stat-value positive">$1,747,988.42</div>
</div>
<div class="stat-card">
<div class="stat-label">Projected Max Drawdown</div>
<div class="stat-value">$3,500.00</div>
</div>
<div class="stat-card">
<div class="stat-label">Confidence Level</div>
<div class="stat-value positive">95%</div>
</div>
</div>
</div>
<div class="dashboard-grid">
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Performance Projection</h2>
</div>
<div class="chart-container">
<canvas id="projectionChart"></canvas>
</div>
</div>
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Position Sizing Calculator</h2>
</div>
<div class="calculator-form">
<div class="form-group">
<label for="account-size">Account Size ($)</label>
<input class="form-control" id="account-size" type="number" value="10000"/>
</div>
<div class="form-group">
<label for="risk-percentage">Risk Percentage (%)</label>
<input class="form-control" id="risk-percentage" max="10" min="0.1" step="0.1" type="number" value="2"/>
</div>
<div class="form-group">
<label for="instrument">Instrument</label>
<select class="form-control" id="instrument">
<option value="mnq">MNQ (Nasdaq-100)</option>
<option value="mes">MES (S&amp;P 500)</option>
<option value="mgc">MGC (Gold)</option>
</select>
</div>
<button class="btn" id="calculate-btn">Calculate</button>
<div class="result-card" id="position-result" style="display: none;">
<div class="result-row">
<span class="result-label">Recommended Position Size:</span>
<span class="result-value" id="position-size">10 contracts</span>
</div>
<div class="result-row">
<span class="result-label">Max Risk Amount:</span>
<span class="result-value" id="risk-amount">$200.00</span>
</div>
<div class="result-row">
<span class="result-label">Projected Daily P&amp;L:</span>
<span class="result-value positive" id="projected-pnl">$203.01</span>
</div>
<div class="result-row">
<span class="result-label">Projected Monthly P&amp;L:</span>
<span class="result-value positive" id="projected-monthly">$4,263.21</span>
</div>
</div>
</div>
</div>
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Forecast Scenarios</h2>
</div>
<table class="forecast-table">
<thead>
<tr>
<th>Scenario</th>
<th>Probability</th>
<th>Annual Return</th>
<th>Max Drawdown</th>
</tr>
</thead>
<tbody>
<tr>
<td>Conservative</td>
<td>90%</td>
<td>$1,310,991.31</td>
<td>$2,625.00</td>
</tr>
<tr>
<td>Base Case</td>
<td>50%</td>
<td>$1,747,988.42</td>
<td>$3,500.00</td>
</tr>
<tr>
<td>Optimistic</td>
<td>10%</td>
<td>$2,184,985.52</td>
<td>$4,375.00</td>
</tr>
</tbody>
</table>
</div>
<div class="dashboard-card">
<div class="card-header">
<h2 class="card-title">Maximum Drawdown Projection</h2>
</div>
<div class="chart-container">
<canvas id="drawdownChart"></canvas>
</div>
</div>
</div>
<div class="dashboard-footer">
<p>QUANTUM CAPITAL | PERFORMANCE FORECASTING DASHBOARD | LAST UPDATED: MAY 8, 2025</p>
<p style="margin-top: 5px; font-weight: bold; color: var(--warning);">PROJECTIONS ARE BASED ON HISTORICAL DATA AND NOT GUARANTEED</p>
</div>
<script>
        // Projection Chart
        const projectionCtx = document.getElementById('projectionChart').getContext('2d');
        const projectionChart = new Chart(projectionCtx, {
            type: 'line',
            data: {
                labels: ['May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'],
                datasets: [
                    {
                        label: 'Conservative',
                        data: [0, 109249, 218498, 327747, 436996, 546245, 655494, 764743, 873992, 983241, 1092490, 1310991],
                        borderColor: 'rgba(0, 204, 255, 0.7)',
                        backgroundColor: 'rgba(0, 204, 255, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        borderDash: [5, 5],
                        fill: false
                    },
                    {
                        label: 'Base Case',
                        data: [0, 145666, 291332, 436998, 582664, 728330, 873996, 1019662, 1165328, 1310994, 1456660, 1747988],
                        borderColor: '#00ff88',
                        backgroundColor: 'rgba(0, 255, 136, 0.1)',
                        borderWidth: 3,
                        pointRadius: 0,
                        fill: false
                    },
                    {
                        label: 'Optimistic',
                        data: [0, 182082, 364164, 546246, 728328, 910410, 1092492, 1274574, 1456656, 1638738, 1820820, 2184986],
                        borderColor: 'rgba(255, 204, 0, 0.7)',
                        backgroundColor: 'rgba(255, 204, 0, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        borderDash: [5, 5],
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f8fafc',
                            font: {
                                family: 'Rajdhani'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    }
                }
            }
        });

        // Drawdown Chart
        const drawdownCtx = document.getElementById('drawdownChart').getContext('2d');
        const drawdownChart = new Chart(drawdownCtx, {
            type: 'bar',
            data: {
                labels: ['MNQ', 'MES', 'MGC', 'Portfolio'],
                datasets: [
                    {
                        label: 'Current Max Drawdown',
                        data: [1470.43, 1159.36, 994.70, 1470.43],
                        backgroundColor: 'rgba(0, 204, 255, 0.7)',
                        borderColor: 'rgba(0, 204, 255, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Projected Max Drawdown',
                        data: [2205.65, 1739.04, 1492.05, 3500.00],
                        backgroundColor: 'rgba(255, 51, 102, 0.7)',
                        borderColor: 'rgba(255, 51, 102, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f8fafc',
                            font: {
                                family: 'Rajdhani'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(42, 58, 90, 0.5)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    }
                }
            }
        });

        // Position Sizing Calculator
        document.getElementById('calculate-btn').addEventListener('click', function() {
            const accountSize = parseFloat(document.getElementById('account-size').value);
            const riskPercentage = parseFloat(document.getElementById('risk-percentage').value);
            const instrument = document.getElementById('instrument').value;

            let positionSize, riskAmount, projectedPnL, projectedMonthly;

            // Calculate based on instrument
            if (instrument === 'mnq') {
                positionSize = Math.floor((accountSize * riskPercentage / 100) / 147);
                positionSize = Math.min(Math.max(positionSize, 1), 20); // Limit between 1 and 20
                riskAmount = (accountSize * riskPercentage / 100).toFixed(2);
                projectedPnL = (203.01 * positionSize / 10).toFixed(2);
                projectedMonthly = (4263.21 * positionSize / 10).toFixed(2);
            } else if (instrument === 'mes') {
                positionSize = Math.floor((accountSize * riskPercentage / 100) / 116);
                positionSize = Math.min(Math.max(positionSize, 1), 20);
                riskAmount = (accountSize * riskPercentage / 100).toFixed(2);
                projectedPnL = (61.26 * positionSize / 10).toFixed(2);
                projectedMonthly = (1286.46 * positionSize / 10).toFixed(2);
            } else { // mgc
                positionSize = Math.floor((accountSize * riskPercentage / 100) / 99);
                positionSize = Math.min(Math.max(positionSize, 1), 20);
                riskAmount = (accountSize * riskPercentage / 100).toFixed(2);
                projectedPnL = (54.00 * positionSize / 10).toFixed(2);
                projectedMonthly = (1134.00 * positionSize / 10).toFixed(2);
            }

            // Update result
            document.getElementById('position-size').textContent = positionSize + ' contracts';
            document.getElementById('risk-amount').textContent = '$' + riskAmount;
            document.getElementById('projected-pnl').textContent = '$' + projectedPnL;
            document.getElementById('projected-monthly').textContent = '$' + projectedMonthly;

            // Show result
            document.getElementById('position-result').style.display = 'block';
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Performance Forecast`;
            }

            // Update forecast data
            updateForecastData(data);

            // Pre-select the instrument in the dropdown
            const instrumentSelect = document.getElementById('instrument');
            if (instrumentSelect) {
                if (data.instrumentCode === 'MNQ') {
                    instrumentSelect.value = 'mnq';
                } else if (data.instrumentCode === 'MES') {
                    instrumentSelect.value = 'mes';
                } else if (data.instrumentCode === 'MGC') {
                    instrumentSelect.value = 'mgc';
                }
            }

            console.log('Performance forecast dashboard updated with data for:', data.instrumentCode);
        }

        // Update forecast data
        function updateForecastData(data) {
            // This would be implemented to update the forecast data with actual data
            // For now, we'll just log that we would update the forecast data
            console.log('Forecast data would be updated with:', data);
        }
    </script>

<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        if (showAllInstruments) {
            instrumentsToDisplay = Object.keys(data);
        } else if (instrumentParam && data[instrumentParam]) {
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to MNQ if no instrument is specified
            instrumentsToDisplay = ['MNQ'];
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
