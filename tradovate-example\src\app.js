// app.js
// Main application file

import { connect } from './connect.js'
import { credentials } from '../tutorialsCredentials.js'
import { getDeviceId, setDeviceId, setUserData } from './storage.js'
import { tvGet } from './services.js'
import { handleAccountList } from './handleAccountList.js'
import { ORDER_ACTION, ORDER_TYPE, placeOrder } from './placeOrder.js'

// Generate or retrieve a device ID for 2FA
const getOrCreateDeviceId = () => {
    // Try to get existing device ID from storage
    let deviceId = getDeviceId()

    // If no device ID exists, create a new one
    if (!deviceId) {
        deviceId = 'example-' + Math.random().toString(36).substring(2, 15) +
                  Math.random().toString(36).substring(2, 15)
        setDeviceId(deviceId)
        console.log('Created new device ID:', deviceId)
    } else {
        console.log('Using existing device ID:', deviceId)
    }

    return deviceId
}

// Set up the order form functionality
const setupOrderForm = () => {
    // Get form elements
    const $symbol = document.getElementById('symbol')
    const $quantity = document.getElementById('quantity')
    const $orderType = document.getElementById('order-type')
    const $price = document.getElementById('price')
    const $stopPrice = document.getElementById('stop-price')
    const $isAutomated = document.getElementById('is-automated')
    const $buyBtn = document.getElementById('buy-btn')
    const $sellBtn = document.getElementById('sell-btn')
    const $orderResponse = document.getElementById('order-response')
    const $priceField = document.querySelector('.price-field')
    const $stopPriceField = document.querySelector('.stop-price-field')

    // Show/hide price fields based on order type
    $orderType.addEventListener('change', () => {
        const orderType = $orderType.value

        // Show/hide price field
        if (orderType === 'Limit' || orderType === 'StopLimit') {
            $priceField.style.display = 'block'
        } else {
            $priceField.style.display = 'none'
        }

        // Show/hide stop price field
        if (orderType === 'Stop' || orderType === 'StopLimit') {
            $stopPriceField.style.display = 'block'
        } else {
            $stopPriceField.style.display = 'none'
        }
    })

    // Handle buy button click
    $buyBtn.addEventListener('click', async () => {
        await placeOrderWithUI(ORDER_ACTION.Buy)
    })

    // Handle sell button click
    $sellBtn.addEventListener('click', async () => {
        await placeOrderWithUI(ORDER_ACTION.Sell)
    })

    // Function to place an order and update UI
    const placeOrderWithUI = async (action) => {
        // Clear previous response
        $orderResponse.textContent = ''
        $orderResponse.className = 'order-response'

        // Validate inputs
        if (!$symbol.value) {
            showOrderResponse('Please enter a symbol', true)
            return
        }

        if (!$quantity.value || $quantity.value < 1) {
            showOrderResponse('Please enter a valid quantity', true)
            return
        }

        const orderType = $orderType.value

        // Validate price for Limit and StopLimit orders
        if ((orderType === 'Limit' || orderType === 'StopLimit') && !$price.value) {
            showOrderResponse('Please enter a price', true)
            return
        }

        // Validate stop price for Stop and StopLimit orders
        if ((orderType === 'Stop' || orderType === 'StopLimit') && !$stopPrice.value) {
            showOrderResponse('Please enter a stop price', true)
            return
        }

        // Prepare order parameters
        const orderParams = {
            action,
            symbol: $symbol.value,
            orderQty: parseInt($quantity.value),
            orderType: ORDER_TYPE[$orderType.value],
            isAutomated: $isAutomated.checked
        }

        // Add price if needed
        if (orderType === 'Limit' || orderType === 'StopLimit') {
            orderParams.price = parseFloat($price.value)
        }

        // Add stop price if needed
        if (orderType === 'Stop' || orderType === 'StopLimit') {
            orderParams.stopPrice = parseFloat($stopPrice.value)
        }

        try {
            // Show loading state
            $buyBtn.disabled = true
            $sellBtn.disabled = true

            // Place the order
            console.log('Placing order with parameters:', orderParams)
            const response = await placeOrder(orderParams)

            // Handle response
            if (response.error) {
                showOrderResponse(`Error: ${response.error}`, true)
            } else if (response.errorText) {
                showOrderResponse(`Error: ${response.errorText}`, true)
            } else {
                showOrderResponse(`Order placed successfully! Order ID: ${response.orderId || 'N/A'}`, false)
            }

            console.log('Order response:', response)
        } catch (error) {
            showOrderResponse(`Error: ${error.message}`, true)
            console.error('Error placing order:', error)
        } finally {
            // Reset button state
            $buyBtn.disabled = false
            $sellBtn.disabled = false
        }
    }

    // Function to show order response message
    const showOrderResponse = (message, isError) => {
        $orderResponse.textContent = message
        $orderResponse.className = `order-response ${isError ? 'error' : 'success'}`
    }
}

// Main function to run our application
const main = async () => {
    console.log('Starting Tradovate API example...')

    // Get or create device ID
    const deviceId = getOrCreateDeviceId()

    // Add device ID to credentials
    const authData = {
        ...credentials,
        deviceId
    }

    // Connect to Tradovate API with our credentials
    const authResponse = await connect(authData)

    if (authResponse) {
        console.log('Connection successful!')

        // Store user data if available
        if (authResponse.user) {
            setUserData(authResponse.user)
            console.log('User data stored:', authResponse.user)
        }

        // Set up event listener for the account list button
        const $accountListBtn = document.querySelector('#get-acct-btn')
        if ($accountListBtn) {
            $accountListBtn.addEventListener('click', async () => {
                try {
                    console.log('Fetching account list...')
                    const accounts = await tvGet('/account/list')
                    console.log('Accounts:', accounts)
                    handleAccountList(accounts)
                } catch (error) {
                    console.error('Error fetching accounts:', error)
                }
            })
            console.log('Account list button event listener added')
        } else {
            console.warn('Account list button not found in the DOM')
        }

        // Set up order form functionality
        setupOrderForm()
    } else {
        console.log('Connection failed!')
    }
}

// Run the main function
main()
