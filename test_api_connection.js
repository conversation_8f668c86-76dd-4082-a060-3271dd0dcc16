/**
 * Test Tradovate API Connection
 *
 * This script tests the connection to the Tradovate API
 * to ensure everything is set up correctly for demo trading.
 */

const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');

// Test authentication and API connection
async function testApiConnection() {
    console.log('Testing Tradovate API connection...');

    try {
        // Test authentication
        console.log('Authenticating with Tradovate API...');
        const authResult = await tradovateApi.authenticate();

        if (!authResult.success) {
            console.error(`Authentication failed: ${authResult.error}`);
            return false;
        }

        console.log('Authentication successful!');
        console.log(`Access Token: ${authResult.accessToken.substring(0, 10)}...`);
        console.log(`User ID: ${authResult.userId}`);
        console.log(`Expiration: ${new Date(authResult.expirationTime).toLocaleString()}`);

        // Test WebSocket connection
        console.log('\nInitializing WebSockets...');
        const wsInitialized = await tradovateApi.initializeWebSockets();

        if (!wsInitialized) {
            console.error('Failed to initialize WebSockets');
            return false;
        }

        console.log('WebSockets initialized successfully');

        // Test contract lookup
        console.log('\nLooking up contracts...');
        for (const symbol of ['MNQ', 'MES', 'MGC']) {
            const contract = await tradovateApi.findContract(symbol);
            if (contract) {
                console.log(`Found contract for ${symbol}: ID=${contract.id}, Name=${contract.name}`);
            } else {
                console.error(`Could not find contract for ${symbol}`);
            }
        }

        // Wait for a moment to see WebSocket messages
        console.log('\nWaiting for WebSocket messages (10 seconds)...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        // Disconnect WebSockets
        console.log('\nDisconnecting WebSockets...');
        tradovateApi.disconnectWebSockets();

        console.log('\nAPI connection test completed successfully!');
        return true;
    } catch (error) {
        console.error(`API connection test failed: ${error.message}`);
        return false;
    }
}

// Run the test
testApiConnection()
    .then(success => {
        if (success) {
            console.log('All API tests passed! Ready for demo trading.');
        } else {
            console.error('API tests failed. Please check the errors above.');
        }

        // Exit after a short delay to allow any pending operations to complete
        setTimeout(() => process.exit(success ? 0 : 1), 1000);
    })
    .catch(error => {
        console.error(`Unexpected error: ${error.message}`);
        process.exit(1);
    });