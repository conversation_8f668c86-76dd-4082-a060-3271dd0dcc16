/**
 * pattern_detection.js
 * Pattern detection functions for trading signals
 */

/**
 * Get the color of a candlestick
 * @param {Object} candle - Candle data
 * @returns {string} - Candle color ('green', 'red', 'doji', or 'invalid')
 */
function candlestickColor(candle) {
    if (!candle ||
        typeof candle.open !== 'number' ||
        typeof candle.close !== 'number' ||
        isNaN(candle.open) ||
        isNaN(candle.close)) {
        return 'invalid';
    }

    if (candle.close > candle.open) {
        return 'green';
    } else if (candle.close < candle.open) {
        return 'red';
    } else {
        return 'doji';
    }
}

/**
 * Detect 3-candle pattern
 * @param {Object} c1 - First candle
 * @param {Object} c2 - Second candle
 * @param {Object} c3 - Third candle
 * @returns {string|null} - Pattern direction or null
 */
function detect3Pattern(c1, c2, c3) {
    if (!c1 || !c2 || !c3) {
        return null;
    }

    // Get candle colors
    const c1Color = candlestickColor(c1);
    const c2Color = candlestickColor(c2);
    const c3Color = candlestickColor(c3);

    // Check for invalid candles
    if (c1Color === 'invalid' || c2Color === 'invalid' || c3Color === 'invalid') {
        return null;
    }

    // SUPER RELAXED VERSION - Just check for color patterns

    // Log candle colors for debugging
    console.log(`detect3: Candle colors - C1: ${c1Color}, C2: ${c2Color}, C3: ${c3Color}`);

    // Bullish pattern: Green, Red, Green (SUPER RELAXED - only check colors)
    if (c1Color === 'green' && c2Color === 'red' && c3Color === 'green') {
        return 'bullish';
    }

    // Bearish pattern: Red, Green, Red (SUPER RELAXED - only check colors)
    if (c1Color === 'red' && c2Color === 'green' && c3Color === 'red') {
        return 'bearish';
    }

    // ADDITIONAL PATTERNS - Even more relaxed

    // Bullish: Any color, Red, Green
    if (c2Color === 'red' && c3Color === 'green') {
        return 'bullish';
    }

    // Bearish: Any color, Green, Red
    if (c2Color === 'green' && c3Color === 'red') {
        return 'bearish';
    }

    return null;
}

/**
 * Detect 4-candle pattern
 * @param {Object} c0 - First candle
 * @param {Object} c1 - Second candle
 * @param {Object} c2 - Third candle
 * @param {Object} c3 - Fourth candle
 * @returns {string|null} - Pattern direction or null
 */
function detect4Pattern(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) {
        return null;
    }

    // Get candle colors
    const c0Color = candlestickColor(c0);
    const c1Color = candlestickColor(c1);
    const c2Color = candlestickColor(c2);
    const c3Color = candlestickColor(c3);

    // Check for invalid candles
    if (c0Color === 'invalid' || c1Color === 'invalid' || c2Color === 'invalid' || c3Color === 'invalid') {
        return null;
    }

    // Log candle colors for debugging
    console.log(`detect4: Candle colors - C0: ${c0Color}, C1: ${c1Color}, C2: ${c2Color}, C3: ${c3Color}`);

    // SUPER RELAXED VERSION - Just check for color patterns

    // Bullish pattern: Green, Red, Red, Green (SUPER RELAXED - only check colors)
    if (c0Color === 'green' && c1Color === 'red' && c2Color === 'red' && c3Color === 'green') {
        return 'bullish';
    }

    // Bearish pattern: Red, Green, Green, Red (SUPER RELAXED - only check colors)
    if (c0Color === 'red' && c1Color === 'green' && c2Color === 'green' && c3Color === 'red') {
        return 'bearish';
    }

    // ADDITIONAL PATTERNS - Even more relaxed

    // Bullish: Any, Any, Red, Green
    if (c2Color === 'red' && c3Color === 'green') {
        return 'bullish';
    }

    // Bearish: Any, Any, Green, Red
    if (c2Color === 'green' && c3Color === 'red') {
        return 'bearish';
    }

    return null;
}

/**
 * Validate entry signal
 * @param {string} pattern - Pattern direction
 * @param {string} patternType - Pattern type
 * @param {Object} candle - Current candle
 * @param {number} index - Current index
 * @param {Array} candles - Array of candles
 * @returns {boolean} - True if entry is valid
 */
function validateEntry(pattern, patternType, candle, index, candles) {
    if (!pattern || !candle) {
        return false;
    }

    // Basic validation - check if indicators are available
    if (isNaN(candle.wma50) || isNaN(candle.rsi) || isNaN(candle.rsiMa) || isNaN(candle.atr) || candle.atr <= 0) {
        return false;
    }

    // Apply SMA200 filter if enabled
    if (global.config.useSma200Filter && !isNaN(candle.sma200)) {
        if (pattern === 'bullish' && candle.close < candle.sma200) {
            return false;
        }
        if (pattern === 'bearish' && candle.close > candle.sma200) {
            return false;
        }
    }

    // Apply WMA filter - TEMPORARILY DISABLED FOR TESTING
    if (false && global.config.useWmaFilter) {
        if ((pattern === 'bullish' && candle.close <= candle.wma50) ||
            (pattern === 'bearish' && candle.close >= candle.wma50)) {
            console.log(`WMA filter failed: ${pattern} position with close ${candle.close} vs WMA50 ${candle.wma50}`);
            return false;
        }
    }

    // RSI filter for three-candle patterns - TEMPORARILY DISABLED FOR TESTING
    if (false && patternType === 'three') {
        if ((pattern === 'bullish' && candle.rsi <= candle.rsiMa) ||
            (pattern === 'bearish' && candle.rsi >= candle.rsiMa)) {
            console.log(`RSI filter failed: ${pattern} position with RSI ${candle.rsi.toFixed(2)} vs RSI-MA ${candle.rsiMa.toFixed(2)}`);
            return false;
        }
    }

    // Apply RSI filter if enabled - TEMPORARILY DISABLED FOR TESTING
    if (false && global.config.useRsiFilter) {
        if (pattern === 'bullish' && candle.rsi < global.config.rsiLowerBand) {
            console.log(`RSI band filter failed: ${pattern} position with RSI ${candle.rsi.toFixed(2)} < lower band ${global.config.rsiLowerBand}`);
            return false;
        }
        if (pattern === 'bearish' && candle.rsi > global.config.rsiUpperBand) {
            console.log(`RSI band filter failed: ${pattern} position with RSI ${candle.rsi.toFixed(2)} > upper band ${global.config.rsiUpperBand}`);
            return false;
        }
    }

    // Apply RSI-MA separation filter if enabled - TEMPORARILY DISABLED FOR TESTING
    if (false && global.config.minRsiMaSeparation > 0) {
        if (Math.abs(candle.rsi - candle.rsiMa) < global.config.minRsiMaSeparation) {
            console.log(`RSI-MA separation filter failed: separation ${Math.abs(candle.rsi - candle.rsiMa).toFixed(2)} < min ${global.config.minRsiMaSeparation}`);
            return false;
        }
    }

    // Apply minimum ATR filter if enabled - TEMPORARILY DISABLED FOR TESTING
    const minAtrEntry = global.config.minAtrEntry || 0;
    if (false && candle.atr < minAtrEntry) {
        console.log(`ATR filter failed: ATR ${candle.atr.toFixed(2)} < min ${minAtrEntry}`);
        return false;
    }

    // Apply time filter if enabled - TEMPORARILY DISABLED FOR TESTING
    if (false && global.config.timeFilterEnabled) {
        const timestamp = new Date(candle.timestamp * 1000);
        const hour = timestamp.getUTCHours() + (global.config.cstOffset || 0);

        // Check if we're in the filtered time range
        // If start < end, we filter during that range (e.g., 8-15)
        // If start > end, we filter outside that range (e.g., 15-8)
        if (global.config.timeFilter.start < global.config.timeFilter.end) {
            // Normal range (e.g., 8-15)
            if (hour >= global.config.timeFilter.start && hour < global.config.timeFilter.end) {
                console.log(`Time filter failed: hour ${hour} is in filtered range ${global.config.timeFilter.start}-${global.config.timeFilter.end}`);
                return false;
            }
        } else {
            // Inverted range (e.g., 15-8)
            if (hour >= global.config.timeFilter.start || hour < global.config.timeFilter.end) {
                console.log(`Time filter failed: hour ${hour} is in filtered range ${global.config.timeFilter.start}-${global.config.timeFilter.end}`);
                return false;
            }
        }
    }

    // ML-based entry filter if enabled - TEMPORARILY DISABLED FOR TESTING
    if (false && global.config.useMLEntryFilter && global.mlFilter) {
        // Get the timestamp
        const timestamp = new Date(candle.timestamp * 1000);

        // Get the hour of day (UTC)
        const hourOfDay = timestamp.getUTCHours();

        // Get the day of week (0 = Sunday, 6 = Saturday)
        const dayOfWeek = timestamp.getUTCDay();

        // Determine ATR regime
        const atrRegime = candle.atr < global.config.atrThresholds.low_medium ? 'Low' :
                         (candle.atr > global.config.atrThresholds.medium_high ? 'High' : 'Medium');

        // Get recent candles for pattern analysis if available
        let recentCandles = [];
        if (candles && Array.isArray(candles) && index !== undefined) {
            const lookbackStart = Math.max(0, index - 5);
            recentCandles = candles.slice(lookbackStart, index + 1);
        }

        // Create feature object for ML filter
        const features = {
            direction: pattern,
            atrRegime: atrRegime,
            hourOfDay: hourOfDay,
            dayOfWeek: dayOfWeek,
            rsi: candle.rsi,
            rsiMa: candle.rsiMa,
            close: candle.close,
            wma50: candle.wma50,
            patternType: patternType,
            recentCandles: recentCandles,
            timestamp: timestamp
        };

        // Get time score if time analysis is enabled
        if (global.config.useTimeAnalysis && global.timeAnalyzer) {
            const timeScoreInfo = global.timeAnalyzer.getTimeScore(timestamp);
            features.timeScore = timeScoreInfo.combinedScore;
        }

        // Use ML filter to decide whether to take the entry
        return global.mlFilter.shouldEnter(features);
    }

    // Log that we're passing all filters
    console.log(`ALL FILTERS PASSED for ${pattern} pattern (${patternType})`);


    // All filters passed
    return true;
}

// Export functions
module.exports = {
    candlestickColor,
    detect3Pattern,
    detect4Pattern,
    validateEntry
};
