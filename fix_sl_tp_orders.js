/**
 * Fix for Stop Loss and Take Profit Orders
 * 
 * This script adds enhanced functionality to ensure SL/TP orders are properly
 * placed and tracked for all positions.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');
const fs = require('fs');
const path = require('path');

// Global variables
const positionCacheFile = path.join(__dirname, 'position_cache.json');
let activePositions = {};
let activeOrders = {};

// Fixed take profit points for each instrument
const FIXED_TP_POINTS = {
    MNQ: 10,  // 10 points for MNQ
    MES: 5,   // 5 points for MES
    MGC: 3,   // 3 points for MGC
    M2K: 5    // 5 points for M2K
};

// Default stop loss points (very wide initially)
const DEFAULT_SL_POINTS = {
    MNQ: 50,  // 50 points for MNQ
    MES: 20,  // 20 points for MES
    MGC: 15,  // 15 points for MGC
    M2K: 25   // 25 points for M2K
};

/**
 * Initialize the fix
 */
async function initialize() {
    try {
        console.log('Initializing SL/TP order fix...');
        
        // Configure API with demo mode
        tradovateApi.setConfig({
            baseUrl: 'https://demo.tradovateapi.com/v1',
            wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
            mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
        });
        
        // Authenticate with Tradovate API
        console.log('Authenticating with Tradovate API...');
        const authResult = await tradovateApi.authenticate();
        
        if (!authResult.success) {
            console.error(`Authentication failed: ${authResult.error}`);
            return false;
        }
        
        // Sync positions with API
        await syncPositionsWithAPI();
        
        console.log('Initialization complete.');
        return true;
    } catch (error) {
        console.error(`Error initializing: ${error.message}`);
        return false;
    }
}

/**
 * Sync positions with API
 */
async function syncPositionsWithAPI() {
    try {
        console.log('Syncing positions with API...');
        
        // Get account ID
        const accountId = ********; // DEMO4690440 account ID
        
        // Get positions from API
        const positions = await tradovateApi.makeApiRequest('GET', `position/list?accountId=${accountId}`);
        console.log(`Found ${positions.length} positions from API`);
        
        // Filter for active positions (netPos != 0)
        const activeApiPositions = positions.filter(position => position.netPos !== 0);
        console.log(`Active positions: ${activeApiPositions.length}`);
        
        // Reset active positions
        activePositions = {};
        
        // Process active positions
        if (activeApiPositions.length > 0) {
            console.log('\nActive Positions:');
            
            for (const position of activeApiPositions) {
                // Get contract details to get symbol
                const contract = await getContractDetails(position.contractId);
                let symbol = contract ? contract.name : `Unknown-${position.contractId}`;
                
                console.log(`Symbol: ${symbol}, Net Position: ${position.netPos}, Entry Price: ${position.netPrice}`);
                
                // Create position object
                const positionId = `pos-${position.contractId}-${Date.now()}`;
                const action = position.netPos > 0 ? 'Buy' : 'Sell';
                
                // Extract base symbol (e.g., MNQ from MNQM5)
                const baseSymbol = symbol.substring(0, 3);
                
                // Calculate take profit price based on fixed points
                const takeProfitPoints = FIXED_TP_POINTS[baseSymbol] || 10;
                const takeProfitPrice = action === 'Buy' ?
                    position.netPrice + takeProfitPoints :
                    position.netPrice - takeProfitPoints;
                
                // Calculate stop loss price based on default points
                const stopLossPoints = DEFAULT_SL_POINTS[baseSymbol] || 50;
                const stopLossPrice = action === 'Buy' ?
                    position.netPrice - stopLossPoints :
                    position.netPrice + stopLossPoints;
                
                // Add to active positions
                activePositions[positionId] = {
                    id: positionId,
                    symbol: symbol,
                    contractId: position.contractId,
                    action: action,
                    quantity: Math.abs(position.netPos),
                    entryPrice: position.netPrice,
                    stopLossPrice: stopLossPrice,
                    takeProfitPrice: takeProfitPrice,
                    takeProfitPoints: takeProfitPoints,
                    stopLossPoints: stopLossPoints,
                    timestamp: Date.now(),
                    apiPositionId: position.id,
                    hasOcoOrders: false, // Will be set to true after placing OCO orders
                    stopLossOrderId: null,
                    takeProfitOrderId: null
                };
            }
            
            // Now check for existing SL/TP orders
            await syncOrdersWithAPI();
            
            // Place missing SL/TP orders
            await placeMissingOcoOrders();
        }
        
        return activeApiPositions;
    } catch (error) {
        console.error(`Error syncing positions with API: ${error.message}`);
        return [];
    }
}

/**
 * Get contract details
 */
async function getContractDetails(contractId) {
    try {
        const response = await tradovateApi.makeApiRequest('GET', `contract/find?id=${contractId}`);
        return response;
    } catch (error) {
        console.error(`Error getting contract details: ${error.message}`);
        return null;
    }
}

/**
 * Sync orders with API
 */
async function syncOrdersWithAPI() {
    try {
        console.log('Syncing orders with API...');
        
        // Get account ID
        const accountId = ********; // DEMO4690440 account ID
        
        // Get orders from API
        const orders = await tradovateApi.makeApiRequest('GET', `order/list?accountId=${accountId}`);
        console.log(`Found ${orders.length} orders from API`);
        
        // Filter for active orders (not filled or canceled)
        const activeApiOrders = orders.filter(order => 
            order.status !== 'Filled' && 
            order.status !== 'Canceled' &&
            order.status !== 'Rejected');
        
        console.log(`Active orders: ${activeApiOrders.length}`);
        
        // Reset active orders
        activeOrders = {};
        
        // Process active orders
        if (activeApiOrders.length > 0) {
            console.log('\nActive Orders:');
            
            for (const order of activeApiOrders) {
                // Get contract details to get symbol
                const contract = await getContractDetails(order.contractId);
                let symbol = contract ? contract.name : `Unknown-${order.contractId}`;
                
                console.log(`Symbol: ${symbol}, Action: ${order.action}, Qty: ${order.orderQty}, Type: ${order.orderType}, Status: ${order.status}`);
                
                // Add to active orders
                activeOrders[order.id] = {
                    id: order.id,
                    symbol: symbol,
                    contractId: order.contractId,
                    action: order.action,
                    quantity: order.orderQty,
                    orderType: order.orderType,
                    price: order.price,
                    stopPrice: order.stopPrice,
                    status: order.status,
                    text: order.text || ''
                };
                
                // Match with positions
                for (const posId in activePositions) {
                    const position = activePositions[posId];
                    
                    // Check if this order belongs to this position
                    if (position.contractId === order.contractId) {
                        // Check if it's a stop loss order
                        if (order.orderType === 'Stop' && 
                            ((position.action === 'Buy' && order.action === 'Sell') ||
                             (position.action === 'Sell' && order.action === 'Buy'))) {
                            
                            position.stopLossOrderId = order.id;
                            position.stopLossPrice = order.stopPrice;
                            console.log(`Matched stop loss order ${order.id} with position ${posId}`);
                        }
                        
                        // Check if it's a take profit order
                        if (order.orderType === 'Limit' && 
                            ((position.action === 'Buy' && order.action === 'Sell') ||
                             (position.action === 'Sell' && order.action === 'Buy'))) {
                            
                            position.takeProfitOrderId = order.id;
                            position.takeProfitPrice = order.price;
                            console.log(`Matched take profit order ${order.id} with position ${posId}`);
                        }
                        
                        // Update OCO status
                        position.hasOcoOrders = !!(position.stopLossOrderId && position.takeProfitOrderId);
                    }
                }
            }
        }
        
        return activeApiOrders;
    } catch (error) {
        console.error(`Error syncing orders with API: ${error.message}`);
        return [];
    }
}

/**
 * Place missing OCO orders
 */
async function placeMissingOcoOrders() {
    try {
        console.log('Checking for missing OCO orders...');
        
        for (const posId in activePositions) {
            const position = activePositions[posId];
            
            // Skip if position already has OCO orders
            if (position.hasOcoOrders) {
                console.log(`Position ${posId} already has OCO orders`);
                continue;
            }
            
            console.log(`Position ${posId} is missing OCO orders, placing them now...`);
            
            // Place stop loss order if missing
            if (!position.stopLossOrderId) {
                await placeStopLossOrder(position);
            }
            
            // Place take profit order if missing
            if (!position.takeProfitOrderId) {
                await placeTakeProfitOrder(position);
            }
            
            // Update OCO status
            position.hasOcoOrders = !!(position.stopLossOrderId && position.takeProfitOrderId);
            
            console.log(`OCO orders for position ${posId}: SL=${position.stopLossOrderId}, TP=${position.takeProfitOrderId}`);
        }
        
        return true;
    } catch (error) {
        console.error(`Error placing missing OCO orders: ${error.message}`);
        return false;
    }
}

/**
 * Place a stop loss order
 */
async function placeStopLossOrder(position) {
    try {
        console.log(`Placing stop loss order for ${position.symbol}...`);
        
        // Prepare stop order
        const orderParams = {
            accountId: ********, // DEMO4690440 account ID
            contractId: position.contractId,
            action: position.action === 'Buy' ? 'Sell' : 'Buy', // Opposite of position action
            orderQty: position.quantity,
            orderType: 'Stop',
            stopPrice: position.stopLossPrice,
            isAutomated: true,
            text: 'Stop Loss'
        };
        
        console.log(`Stop loss order params: ${JSON.stringify(orderParams)}`);
        
        // Place the order
        const orderResult = await tradovateApi.placeOrder(orderParams);
        
        if (orderResult.success) {
            position.stopLossOrderId = orderResult.orderId;
            console.log(`Successfully placed stop loss order: ${orderResult.orderId}`);
            return orderResult;
        } else {
            console.error(`Failed to place stop loss order: ${orderResult.error}`);
            return null;
        }
    } catch (error) {
        console.error(`Error placing stop loss order: ${error.message}`);
        return null;
    }
}

/**
 * Place a take profit order
 */
async function placeTakeProfitOrder(position) {
    try {
        console.log(`Placing take profit order for ${position.symbol}...`);
        
        // Prepare limit order
        const orderParams = {
            accountId: ********, // DEMO4690440 account ID
            contractId: position.contractId,
            action: position.action === 'Buy' ? 'Sell' : 'Buy', // Opposite of position action
            orderQty: position.quantity,
            orderType: 'Limit',
            price: position.takeProfitPrice,
            isAutomated: true,
            text: 'Take Profit'
        };
        
        console.log(`Take profit order params: ${JSON.stringify(orderParams)}`);
        
        // Place the order
        const orderResult = await tradovateApi.placeOrder(orderParams);
        
        if (orderResult.success) {
            position.takeProfitOrderId = orderResult.orderId;
            console.log(`Successfully placed take profit order: ${orderResult.orderId}`);
            return orderResult;
        } else {
            console.error(`Failed to place take profit order: ${orderResult.error}`);
            return null;
        }
    } catch (error) {
        console.error(`Error placing take profit order: ${error.message}`);
        return null;
    }
}

// Run the fix if this file is executed directly
if (require.main === module) {
    initialize()
        .then(() => {
            console.log('\nSL/TP order fix complete.');
            process.exit(0);
        })
        .catch(error => {
            console.error(`Unhandled error: ${error.message}`);
            process.exit(1);
        });
}

module.exports = {
    initialize,
    syncPositionsWithAPI,
    syncOrdersWithAPI,
    placeMissingOcoOrders
};
