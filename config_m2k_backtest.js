// config_m2k_backtest.js - Configuration for Micro Russell 2000 (M2K) Backtest
// Based on the optimized configuration for M2K

module.exports = {
  // --- General Settings ---
  inputFile: 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv',
  initialBalance: 10000,

  // --- Instrument Specifics (M2K Specs) ---
  pointValue: 5.00,      // M2K point value ($5.00 per 1 point move)
  tickSize: 0.10,        // M2K tick size (0.10 points)
  pricePrecision: 1,     // M2K price precision (1 decimal place)

  // --- Costs & Slippage ---
  commissionPerContract: 0.40, // Realistic commission costs
  slippagePoints: 0.20,       // Realistic slippage (2 ticks)

  // --- Indicator Periods ---
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 0,
  wma50Period: 0,

  // --- Strategy Parameters ---
  fixedTpPoints: 0,       // Using ATR-based TP instead of fixed points
  useWmaFilter: false,
  useTwoBarColorExit: false,
  minAtrEntry: 0,
  minRsiMaSeparation: 0,

  // --- RSI Bands ---
  rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

  // --- Run Mode: OPTIMIZED CONFIGURATION ---
  isAdaptiveRun: true,   // Enable adaptive mode for optimal performance

  // Using optimal parameters from grid test
  slFactors: 6.0,        // Stop loss factor
  tpFactors: 5.0,        // Take profit factor
  trailFactors: 0.05,    // Trailing stop factor

  // ATR thresholds for adaptive mode
  atrThresholds: { 
    low_medium: 1.5, 
    medium_high: 3.0 
  },

  // Adaptive parameters for different volatility regimes
  adaptiveParams: {
    Low: { slFactor: 6.0, tpFactor: 5.0, trailFactor: 0.05 },
    Medium: { slFactor: 6.0, tpFactor: 5.0, trailFactor: 0.05 },
    High: { slFactor: 6.0, tpFactor: 5.0, trailFactor: 0.05 }
  },

  // 0 bar latency for optimal performance
  latencyDelayBars: 0,

  // Keep these for compatibility
  costGrid: null,
  riskPercentGrid: null,
  fixedContractsGrid: null,
  fixedTpPointsGrid: null,

  // Fixed parameters
  riskPercent: 0,
  fixedContracts: 10,     // Using 10 contracts for final version
  maxContracts: 10,

  // For compatibility with backtest.js
  currentRiskPercent: 0,
  currentFixedContracts: 10,

  // --- Time Filter Settings ---
  timeFilterEnabled: false,  // Disable time filtering for 24/5 trading

  // --- Enhanced Features Configuration ---
  // All enhanced features disabled
  useAdaptiveSlippage: false,
  minSpreadPoints: 0.10,
  defaultSpreadPoints: 0.20,
  useVolatilityPositionSizing: false,
  useDynamicPositionSizing: false,
  basePositionSize: 10,
  maxPositionSize: 10,
  minPositionSize: 10,
  useMLEntryFilter: false,
  useAdvancedMLFilter: false,
  mlEntryThreshold: 0.5,
  useTimeAnalysis: false,
  timeScoreThreshold: 0.5,
  useCircuitBreakers: false,
  maxSlippageMultiplier: 5,
  pauseTradingOnExcessiveSlippage: false,
  useSteppedTrail: false,
  trailStepSizeMultiplier: 0.1,
  spreadBufferMultiplier: 2.0
};
