/**
 * Backtest Trading Strategy
 * 
 * This script backtests the trading strategy on historical data.
 * Strategy: RSI(14) with SMA(8) smoothing, requiring RSI above RSI-based MA for long positions
 * and below for short positions, with price above 50 WMA for long and below for short.
 */

require('dotenv').config();
const marketDataService = require('./market-data-service');
const logger = require('./logger');
const fs = require('fs');
const path = require('path');
const { RSI, SMA, WMA } = require('technicalindicators');

// Strategy parameters
const DEFAULT_PARAMS = {
  // Indicators
  rsiPeriod: 14,
  rsiSmaPeriod: 8,
  wmaPeriod: 50,
  
  // Entry/Exit
  stopLoss: 4.5,      // Stop loss in points
  takeProfit: 3.0,    // Take profit in points
  trailAmount: 0.11,  // Trail amount in points
  fixedTpPoints: 40,  // Fixed take profit in points
  
  // ATR Thresholds
  atrPeriod: 14,
  lowMediumThreshold: 1.5,
  mediumHighThreshold: 3.0,
  
  // Position sizing
  contracts: 2,       // Number of contracts per trade
  
  // Risk management
  dailyStopLoss: 500, // Daily stop loss in dollars
  dailyTarget: 0,     // Daily profit target in dollars (0 = no target)
  
  // Filters
  useRsiFilter: true,
  usePriceFilter: true,
  useVolatilityFilter: true,
  
  // Symbol-specific multipliers
  multipliers: {
    'MNQ': 0.5,  // Micro Nasdaq
    'MES': 1.0,  // Micro S&P
    'MGC': 1.0,  // Micro Gold
    'M2K': 0.5   // Micro Russell
  },
  
  // Symbol-specific point values
  pointValues: {
    'MNQ': 0.5,   // $0.50 per point
    'MES': 5.0,   // $5.00 per point
    'MGC': 10.0,  // $10.00 per point
    'M2K': 5.0    // $5.00 per point
  }
};

// Trade state
class TradeState {
  constructor(symbol, params = DEFAULT_PARAMS) {
    this.symbol = symbol;
    this.params = { ...DEFAULT_PARAMS, ...params };
    
    // Position state
    this.position = 'flat';  // 'flat', 'long', 'short'
    this.entryPrice = 0;
    this.stopLoss = 0;
    this.takeProfit = 0;
    this.trailPrice = 0;
    this.contracts = this.params.contracts * (this.params.multipliers[symbol] || 1.0);
    
    // Trade tracking
    this.trades = [];
    this.currentTrade = null;
    
    // Daily tracking
    this.dailyPnL = 0;
    this.dailyTrades = 0;
    this.dailyWins = 0;
    this.dailyLosses = 0;
    this.lastTradeDate = null;
    
    // Indicator data
    this.prices = [];
    this.highs = [];
    this.lows = [];
    this.closes = [];
    this.volumes = [];
    this.timestamps = [];
    
    // Indicator values
    this.rsiValues = [];
    this.rsiSmaValues = [];
    this.wmaValues = [];
    this.atrValues = [];
    
    // Pattern detection
    this.pattern3Values = [];
    this.pattern4Values = [];
  }
  
  // Add a new price bar
  addBar(bar) {
    // Add price data
    this.prices.push(bar);
    this.highs.push(bar.high);
    this.lows.push(bar.low);
    this.closes.push(bar.close);
    this.volumes.push(bar.volume);
    this.timestamps.push(bar.timestamp);
    
    // Calculate indicators
    this.calculateIndicators();
    
    // Check for pattern signals
    this.detectPatterns();
    
    // Check for trade entry/exit
    this.checkTradeLogic(bar);
    
    // Reset daily PnL if new day
    this.checkNewDay(bar.timestamp);
  }
  
  // Calculate technical indicators
  calculateIndicators() {
    // Need enough data for calculations
    if (this.closes.length < Math.max(this.params.rsiPeriod, this.params.wmaPeriod)) {
      return;
    }
    
    // Calculate RSI
    const rsiInput = {
      values: this.closes,
      period: this.params.rsiPeriod
    };
    this.rsiValues = RSI.calculate(rsiInput);
    
    // Calculate RSI SMA
    if (this.rsiValues.length >= this.params.rsiSmaPeriod) {
      const rsiSmaInput = {
        values: this.rsiValues.slice(-this.params.rsiSmaPeriod * 2),
        period: this.params.rsiSmaPeriod
      };
      this.rsiSmaValues = SMA.calculate(rsiSmaInput);
    }
    
    // Calculate WMA
    const wmaInput = {
      values: this.closes,
      period: this.params.wmaPeriod
    };
    this.wmaValues = WMA.calculate(wmaInput);
    
    // Calculate ATR
    this.calculateATR();
  }
  
  // Calculate Average True Range
  calculateATR() {
    if (this.highs.length < this.params.atrPeriod + 1) {
      return;
    }
    
    const trueRanges = [];
    
    // Calculate true ranges
    for (let i = 1; i < this.highs.length; i++) {
      const high = this.highs[i];
      const low = this.lows[i];
      const prevClose = this.closes[i - 1];
      
      const tr1 = high - low;
      const tr2 = Math.abs(high - prevClose);
      const tr3 = Math.abs(low - prevClose);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    // Calculate ATR
    let atr = 0;
    if (trueRanges.length >= this.params.atrPeriod) {
      const lastTrueRanges = trueRanges.slice(-this.params.atrPeriod);
      atr = lastTrueRanges.reduce((sum, tr) => sum + tr, 0) / this.params.atrPeriod;
    }
    
    this.atrValues.push(atr);
  }
  
  // Detect trading patterns
  detectPatterns() {
    // Implement pattern detection logic here
    // This is a placeholder for your pattern3 and pattern4 functions
    
    // For now, we'll use simple patterns based on RSI and price
    const hasEnoughData = this.rsiValues.length > 2 && this.rsiSmaValues.length > 2 && this.wmaValues.length > 2;
    
    if (!hasEnoughData) {
      this.pattern3Values.push(0);
      this.pattern4Values.push(0);
      return;
    }
    
    // Pattern 3: RSI crosses above RSI SMA (bullish) or below (bearish)
    const rsi = this.rsiValues[this.rsiValues.length - 1];
    const rsiPrev = this.rsiValues[this.rsiValues.length - 2];
    const rsiSma = this.rsiSmaValues[this.rsiSmaValues.length - 1];
    const rsiSmaPrev = this.rsiSmaValues[this.rsiSmaValues.length - 2];
    
    let pattern3 = 0;
    if (rsiPrev <= rsiSmaPrev && rsi > rsiSma) {
      pattern3 = 1;  // Bullish
    } else if (rsiPrev >= rsiSmaPrev && rsi < rsiSma) {
      pattern3 = -1; // Bearish
    }
    this.pattern3Values.push(pattern3);
    
    // Pattern 4: Price above/below WMA with RSI confirmation
    const close = this.closes[this.closes.length - 1];
    const wma = this.wmaValues[this.wmaValues.length - 1];
    
    let pattern4 = 0;
    if (close > wma && rsi > rsiSma && rsi > 50) {
      pattern4 = 1;  // Bullish
    } else if (close < wma && rsi < rsiSma && rsi < 50) {
      pattern4 = -1; // Bearish
    }
    this.pattern4Values.push(pattern4);
  }
  
  // Check for trade entry/exit
  checkTradeLogic(bar) {
    // Check for exit first
    if (this.position !== 'flat') {
      this.checkExit(bar);
    }
    
    // Then check for entry
    if (this.position === 'flat') {
      this.checkEntry(bar);
    }
  }
  
  // Check for trade entry
  checkEntry(bar) {
    // Need enough data for signals
    if (this.pattern3Values.length < 2 || this.pattern4Values.length < 2) {
      return;
    }
    
    const pattern3 = this.pattern3Values[this.pattern3Values.length - 1];
    const pattern4 = this.pattern4Values[this.pattern4Values.length - 1];
    
    // Check filters
    if (!this.checkFilters(pattern3, pattern4)) {
      return;
    }
    
    // Check daily stop loss
    if (this.dailyPnL <= -this.params.dailyStopLoss) {
      return;
    }
    
    // Check daily target
    if (this.params.dailyTarget > 0 && this.dailyPnL >= this.params.dailyTarget) {
      return;
    }
    
    // Long entry
    if (pattern3 === 1 && pattern4 === 1) {
      this.enterLong(bar);
    }
    // Short entry
    else if (pattern3 === -1 && pattern4 === -1) {
      this.enterShort(bar);
    }
  }
  
  // Check filters
  checkFilters(pattern3, pattern4) {
    // RSI filter
    if (this.params.useRsiFilter) {
      const rsi = this.rsiValues[this.rsiValues.length - 1];
      const rsiSma = this.rsiSmaValues[this.rsiSmaValues.length - 1];
      
      // RSI must be above/below its SMA
      if (pattern3 === 1 && rsi <= rsiSma) {
        return false;
      }
      if (pattern3 === -1 && rsi >= rsiSma) {
        return false;
      }
    }
    
    // Price filter
    if (this.params.usePriceFilter) {
      const close = this.closes[this.closes.length - 1];
      const wma = this.wmaValues[this.wmaValues.length - 1];
      
      // Price must be above/below WMA
      if (pattern4 === 1 && close <= wma) {
        return false;
      }
      if (pattern4 === -1 && close >= wma) {
        return false;
      }
    }
    
    // Volatility filter
    if (this.params.useVolatilityFilter) {
      const atr = this.atrValues[this.atrValues.length - 1];
      
      // ATR must be within thresholds
      if (atr < this.params.lowMediumThreshold || atr > this.params.mediumHighThreshold) {
        return false;
      }
    }
    
    return true;
  }
  
  // Enter long position
  enterLong(bar) {
    this.position = 'long';
    this.entryPrice = bar.close;
    this.stopLoss = this.entryPrice - this.params.stopLoss;
    this.takeProfit = this.entryPrice + this.params.takeProfit;
    this.trailPrice = this.entryPrice - this.params.trailAmount;
    
    this.currentTrade = {
      symbol: this.symbol,
      direction: 'long',
      entryPrice: this.entryPrice,
      entryTime: bar.timestamp,
      stopLoss: this.stopLoss,
      takeProfit: this.takeProfit,
      contracts: this.contracts,
      exitPrice: null,
      exitTime: null,
      pnl: 0,
      pnlPoints: 0,
      reason: null
    };
    
    console.log(`${new Date(bar.timestamp).toISOString()} - ${this.symbol} - LONG Entry at ${this.entryPrice}, SL: ${this.stopLoss}, TP: ${this.takeProfit}, Trail: ${this.trailPrice}`);
  }
  
  // Enter short position
  enterShort(bar) {
    this.position = 'short';
    this.entryPrice = bar.close;
    this.stopLoss = this.entryPrice + this.params.stopLoss;
    this.takeProfit = this.entryPrice - this.params.takeProfit;
    this.trailPrice = this.entryPrice + this.params.trailAmount;
    
    this.currentTrade = {
      symbol: this.symbol,
      direction: 'short',
      entryPrice: this.entryPrice,
      entryTime: bar.timestamp,
      stopLoss: this.stopLoss,
      takeProfit: this.takeProfit,
      contracts: this.contracts,
      exitPrice: null,
      exitTime: null,
      pnl: 0,
      pnlPoints: 0,
      reason: null
    };
    
    console.log(`${new Date(bar.timestamp).toISOString()} - ${this.symbol} - SHORT Entry at ${this.entryPrice}, SL: ${this.stopLoss}, TP: ${this.takeProfit}, Trail: ${this.trailPrice}`);
  }
  
  // Check for trade exit
  checkExit(bar) {
    if (this.position === 'flat') {
      return;
    }
    
    let exitReason = null;
    let exitPrice = bar.close;
    
    // Long position
    if (this.position === 'long') {
      // Update trail price
      if (bar.high > this.entryPrice + this.params.trailAmount) {
        const newTrailPrice = bar.high - this.params.trailAmount;
        if (newTrailPrice > this.trailPrice) {
          this.trailPrice = newTrailPrice;
        }
      }
      
      // Check stop loss
      if (bar.low <= this.stopLoss) {
        exitReason = 'Stop Loss';
        exitPrice = this.stopLoss;
      }
      // Check take profit
      else if (bar.high >= this.takeProfit) {
        exitReason = 'Take Profit';
        exitPrice = this.takeProfit;
      }
      // Check trailing stop
      else if (bar.low <= this.trailPrice && bar.high > this.entryPrice + this.params.trailAmount) {
        exitReason = 'Trailing Stop';
        exitPrice = this.trailPrice;
      }
    }
    // Short position
    else if (this.position === 'short') {
      // Update trail price
      if (bar.low < this.entryPrice - this.params.trailAmount) {
        const newTrailPrice = bar.low + this.params.trailAmount;
        if (newTrailPrice < this.trailPrice) {
          this.trailPrice = newTrailPrice;
        }
      }
      
      // Check stop loss
      if (bar.high >= this.stopLoss) {
        exitReason = 'Stop Loss';
        exitPrice = this.stopLoss;
      }
      // Check take profit
      else if (bar.low <= this.takeProfit) {
        exitReason = 'Take Profit';
        exitPrice = this.takeProfit;
      }
      // Check trailing stop
      else if (bar.high >= this.trailPrice && bar.low < this.entryPrice - this.params.trailAmount) {
        exitReason = 'Trailing Stop';
        exitPrice = this.trailPrice;
      }
    }
    
    // Exit trade if we have a reason
    if (exitReason) {
      this.exitTrade(exitPrice, bar.timestamp, exitReason);
    }
  }
  
  // Exit current trade
  exitTrade(exitPrice, exitTime, reason) {
    if (!this.currentTrade) {
      return;
    }
    
    // Calculate PnL
    let pnlPoints = 0;
    if (this.position === 'long') {
      pnlPoints = exitPrice - this.entryPrice;
    } else if (this.position === 'short') {
      pnlPoints = this.entryPrice - exitPrice;
    }
    
    const pointValue = this.params.pointValues[this.symbol] || 1.0;
    const pnl = pnlPoints * this.contracts * pointValue;
    
    // Update current trade
    this.currentTrade.exitPrice = exitPrice;
    this.currentTrade.exitTime = exitTime;
    this.currentTrade.pnl = pnl;
    this.currentTrade.pnlPoints = pnlPoints;
    this.currentTrade.reason = reason;
    
    // Add to trades list
    this.trades.push(this.currentTrade);
    
    // Update daily stats
    this.dailyPnL += pnl;
    this.dailyTrades++;
    if (pnl > 0) {
      this.dailyWins++;
    } else if (pnl < 0) {
      this.dailyLosses++;
    }
    
    console.log(`${new Date(exitTime).toISOString()} - ${this.symbol} - ${this.position} Exit at ${exitPrice}, Reason: ${reason}, PnL: ${pnl.toFixed(2)}, Points: ${pnlPoints.toFixed(2)}`);
    
    // Reset position
    this.position = 'flat';
    this.entryPrice = 0;
    this.stopLoss = 0;
    this.takeProfit = 0;
    this.trailPrice = 0;
    this.currentTrade = null;
  }
  
  // Check for new day
  checkNewDay(timestamp) {
    const date = new Date(timestamp).toISOString().split('T')[0];
    
    if (this.lastTradeDate && this.lastTradeDate !== date) {
      // Reset daily stats
      this.dailyPnL = 0;
      this.dailyTrades = 0;
      this.dailyWins = 0;
      this.dailyLosses = 0;
    }
    
    this.lastTradeDate = date;
  }
  
  // Get trade statistics
  getStats() {
    const totalTrades = this.trades.length;
    const winningTrades = this.trades.filter(t => t.pnl > 0).length;
    const losingTrades = this.trades.filter(t => t.pnl < 0).length;
    const breakEvenTrades = totalTrades - winningTrades - losingTrades;
    
    const totalPnL = this.trades.reduce((sum, t) => sum + t.pnl, 0);
    const winningPnL = this.trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0);
    const losingPnL = this.trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0);
    
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const avgWin = winningTrades > 0 ? winningPnL / winningTrades : 0;
    const avgLoss = losingTrades > 0 ? losingPnL / losingTrades : 0;
    const profitFactor = losingPnL !== 0 ? Math.abs(winningPnL / losingPnL) : 0;
    
    return {
      symbol: this.symbol,
      totalTrades,
      winningTrades,
      losingTrades,
      breakEvenTrades,
      winRate,
      totalPnL,
      winningPnL,
      losingPnL,
      avgWin,
      avgLoss,
      profitFactor
    };
  }
}

// Backtest function
async function runBacktest(symbols, startDate, endDate, params = {}) {
  // Initialize market data service
  await marketDataService.initialize();
  
  // Create trade state for each symbol
  const tradeStates = {};
  for (const symbol of symbols) {
    tradeStates[symbol] = new TradeState(symbol, params);
  }
  
  // Get historical data for each symbol
  for (const symbol of symbols) {
    console.log(`Getting historical data for ${symbol}...`);
    
    const historicalData = await marketDataService.getHistoricalData(
      symbol,
      startDate,
      endDate,
      '1m'
    );
    
    console.log(`Got ${historicalData.length} bars for ${symbol}`);
    
    // Process each bar
    for (const bar of historicalData) {
      tradeStates[symbol].addBar(bar);
    }
  }
  
  // Calculate and print statistics
  let totalPnL = 0;
  let totalTrades = 0;
  let totalWinningTrades = 0;
  
  console.log('\nBacktest Results:');
  console.log('================');
  
  for (const symbol of symbols) {
    const stats = tradeStates[symbol].getStats();
    totalPnL += stats.totalPnL;
    totalTrades += stats.totalTrades;
    totalWinningTrades += stats.winningTrades;
    
    console.log(`\nSymbol: ${symbol}`);
    console.log(`Total Trades: ${stats.totalTrades}`);
    console.log(`Winning Trades: ${stats.winningTrades} (${stats.winRate.toFixed(2)}%)`);
    console.log(`Losing Trades: ${stats.losingTrades}`);
    console.log(`Total P&L: $${stats.totalPnL.toFixed(2)}`);
    console.log(`Avg Win: $${stats.avgWin.toFixed(2)}`);
    console.log(`Avg Loss: $${stats.avgLoss.toFixed(2)}`);
    console.log(`Profit Factor: ${stats.profitFactor.toFixed(2)}`);
  }
  
  const overallWinRate = totalTrades > 0 ? (totalWinningTrades / totalTrades) * 100 : 0;
  
  console.log('\nOverall Results:');
  console.log(`Total P&L: $${totalPnL.toFixed(2)}`);
  console.log(`Total Trades: ${totalTrades}`);
  console.log(`Win Rate: ${overallWinRate.toFixed(2)}%`);
  
  // Disconnect from market data service
  await marketDataService.disconnect();
  
  return {
    tradeStates,
    totalPnL,
    totalTrades,
    totalWinningTrades,
    overallWinRate
  };
}

// Main function
async function main() {
  try {
    // Symbols to backtest
    const symbols = ['MNQ', 'MES', 'MGC', 'M2K'];
    
    // Date range
    const startDate = new Date('2025-04-30T00:00:00Z');
    const endDate = new Date('2025-05-01T23:59:59Z');
    
    // Run backtest
    const results = await runBacktest(symbols, startDate, endDate);
    
    // Save results to file
    const resultsFile = path.join('C:', 'backtest-bot', 'backtest-results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    
    console.log(`\nResults saved to ${resultsFile}`);
  } catch (error) {
    console.error('Error running backtest:', error);
  }
}

// Run the main function
main().catch(console.error);

module.exports = {
  TradeState,
  runBacktest
};
