/**
 * Utility functions for Databento API Client
 */

/**
 * Format a date for Databento API
 * @param {string|Date} date - Date to format
 * @returns {string} - Formatted date
 */
function formatDate(date) {
  if (typeof date === 'string') {
    // Check if the string is already in ISO format
    if (/^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}(:\d{2}(\.\d+)?)?(Z|[+-]\d{2}:?\d{2})?)?$/.test(date)) {
      return date;
    }
    // Try to parse the string as a date
    date = new Date(date);
  }
  
  if (date instanceof Date) {
    return date.toISOString();
  }
  
  throw new Error('Invalid date format');
}

/**
 * Handle API errors
 * @param {Error} error - Error object
 * @returns {Error} - Processed error
 */
function handleError(error) {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const { status, data } = error.response;
    
    let message = `${status}`;
    if (data && data.error) {
      message += ` ${data.error}`;
    }
    if (data && data.message) {
      message += ` ${data.message}`;
    }
    
    const enhancedError = new Error(message);
    enhancedError.status = status;
    enhancedError.data = data;
    
    // Handle rate limiting
    if (status === 429 && error.response.headers['retry-after']) {
      enhancedError.retryAfter = parseInt(error.response.headers['retry-after'], 10);
    }
    
    return enhancedError;
  } else if (error.request) {
    // The request was made but no response was received
    return new Error('No response received from server');
  } else {
    // Something happened in setting up the request that triggered an Error
    return error;
  }
}

module.exports = {
  formatDate,
  handleError
};
