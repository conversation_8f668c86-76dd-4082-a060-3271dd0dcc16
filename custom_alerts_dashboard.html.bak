<!DOCTYPE html>
<html>
<head>
    <title>Custom Alerts</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Load dashboard integration scripts -->
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <script src="dashboard_integration.js"></script>
    <style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 15px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .alert-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .alert-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .alert-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.2);
        }

        .alert-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .alert-card.entry::after { background: linear-gradient(to right, var(--primary), var(--primary-light)); }
        .alert-card.exit::after { background: linear-gradient(to right, var(--success), rgba(0, 255, 136, 0.7)); }
        .alert-card.drawdown::after { background: linear-gradient(to right, var(--danger), rgba(255, 51, 102, 0.7)); }
        .alert-card.performance::after { background: linear-gradient(to right, var(--warning), rgba(255, 204, 0, 0.7)); }

        .alert-card h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .alert-card p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .alert-settings {
            margin-bottom: 1.5rem;
        }

        .alert-setting {
            margin-bottom: 1rem;
        }

        .alert-setting label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .alert-setting input[type="text"],
        .alert-setting input[type="number"],
        .alert-setting input[type="email"],
        .alert-setting select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            font-family: 'Rajdhani', sans-serif;
            font-size: 0.9rem;
            background-color: rgba(0, 204, 255, 0.05);
            color: var(--text-primary);
        }

        .alert-setting input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .alert-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .alert-toggle label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(42, 58, 90, 0.7);
            transition: .4s;
            border-radius: 34px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: var(--text-primary);
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
        }

        input:checked + .toggle-slider {
            background-color: var(--primary);
            box-shadow: 0 0 10px var(--primary-glow), inset 0 0 5px rgba(0, 0, 0, 0.5);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .alert-button {
            display: inline-block;
            background-color: var(--primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid var(--primary-dark);
            cursor: pointer;
            width: 100%;
            font-family: 'Rajdhani', sans-serif;
            font-size: 1rem;
            box-shadow: 0 0 10px var(--primary-glow);
            text-shadow: 0 0 5px var(--primary-glow);
        }

        .alert-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0 15px var(--primary-glow);
        }

        .alert-history {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2.5rem;
            overflow-x: auto;
            position: relative;
            scrollbar-width: thin;
            scrollbar-color: var(--primary) var(--card-bg);
        }

        .alert-history::-webkit-scrollbar {
            height: 8px;
        }

        .alert-history::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        .alert-history::-webkit-scrollbar-thumb {
            background-color: var(--primary);
            border-radius: 20px;
            border: 2px solid var(--card-bg);
        }

        .alert-history::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .alert-history h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .alert-table {
            width: 100%;
            border-collapse: collapse;
        }

        .alert-table th,
        .alert-table td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .alert-table th {
            font-weight: 600;
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.1);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .alert-table tr:hover {
            background-color: rgba(0, 204, 255, 0.05);
        }

        .alert-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            font-family: 'Rajdhani', sans-serif;
            border: 1px solid;
        }

        .alert-badge.entry {
            background-color: var(--primary);
            border-color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
            box-shadow: 0 0 5px var(--primary-glow);
        }
        .alert-badge.exit {
            background-color: var(--success);
            border-color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
            box-shadow: 0 0 5px var(--success-glow);
        }
        .alert-badge.drawdown {
            background-color: var(--danger);
            border-color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
            box-shadow: 0 0 5px var(--danger-glow);
        }
        .alert-badge.performance {
            background-color: var(--warning);
            border-color: var(--warning);
            text-shadow: 0 0 5px var(--warning-glow);
            box-shadow: 0 0 5px var(--warning-glow);
        }

        .notification-settings {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2.5rem;
            position: relative;
            overflow: hidden;
        }

        .notification-settings::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .notification-settings h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .notification-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .notification-method {
            margin-bottom: 1.5rem;
        }

        .notification-method h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.75rem;
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
        }

        .notification-toggle {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .notification-toggle label {
            margin-left: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-primary);
            font-family: 'Rajdhani', sans-serif;
        }

        .save-button {
            display: inline-block;
            background-color: var(--success);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 255, 136, 0.7);
            cursor: pointer;
            font-family: 'Rajdhani', sans-serif;
            font-size: 1rem;
            margin-top: 1rem;
            box-shadow: 0 0 10px var(--success-glow);
            text-shadow: 0 0 5px var(--success-glow);
        }

        .save-button:hover {
            background-color: rgba(0, 255, 136, 0.8);
            transform: translateY(-3px);
            box-shadow: 0 0 15px var(--success-glow);
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
            font-family: 'Rajdhani', sans-serif;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .back-button {
            display: inline-block;
            background-color: var(--primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
            border: 1px solid var(--primary-dark);
            box-shadow: 0 0 10px var(--primary-glow);
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
        }

        .back-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0 15px var(--primary-glow);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }

            .alert-grid {
                grid-template-columns: 1fr;
            }

            .notification-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>Trading Bot Custom Alerts</h1>
            <p>Set up and manage alerts for your trading bot</p>
        </div>

        <h2 class="section-title">Alert Configuration</h2>

        <div class="alert-grid">
            <div class="alert-card entry">
                <h3>Trade Entry Alerts</h3>
                <p>Get notified when the bot enters a new trade so you can potentially enter the same trade manually.</p>

                <div class="alert-settings">
                    <div class="alert-toggle">
                        <label>Enable Trade Entry Alerts</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="entry-alert-toggle" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>

                    <div class="alert-setting">
                        <label>Minimum Contract Size</label>
                        <input type="number" id="entry-min-contracts" value="1" min="1">
                    </div>

                    <div class="alert-setting">
                        <label>Direction</label>
                        <select id="entry-direction">
                            <option value="both">Both Long & Short</option>
                            <option value="long">Long Only</option>
                            <option value="short">Short Only</option>
                        </select>
                    </div>
                </div>

                <button class="alert-button" onclick="saveAlert('entry')">Save Entry Alert Settings</button>
            </div>

            <div class="alert-card exit">
                <h3>Trade Exit Alerts</h3>
                <p>Get notified when the bot exits a trade so you can potentially exit your manual trade.</p>

                <div class="alert-settings">
                    <div class="alert-toggle">
                        <label>Enable Trade Exit Alerts</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="exit-alert-toggle" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>

                    <div class="alert-setting">
                        <label>Exit Reason</label>
                        <select id="exit-reason">
                            <option value="all">All Exit Types</option>
                            <option value="tp">Take Profit Only</option>
                            <option value="sl">Stop Loss Only</option>
                            <option value="trail">Trail Stop Only</option>
                        </select>
                    </div>

                    <div class="alert-setting">
                        <label>Minimum Profit ($)</label>
                        <input type="number" id="exit-min-profit" value="0" min="0">
                    </div>
                </div>

                <button class="alert-button" onclick="saveAlert('exit')">Save Exit Alert Settings</button>
            </div>

            <div class="alert-card drawdown">
                <h3>Drawdown Alerts</h3>
                <p>Get notified when drawdown exceeds specified thresholds.</p>

                <div class="alert-settings">
                    <div class="alert-toggle">
                        <label>Enable Drawdown Alerts</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="drawdown-alert-toggle">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>

                    <div class="alert-setting">
                        <label>Daily Drawdown Threshold ($)</label>
                        <input type="number" id="daily-drawdown-threshold" value="500" min="0">
                    </div>

                    <div class="alert-setting">
                        <label>Overall Drawdown Threshold ($)</label>
                        <input type="number" id="overall-drawdown-threshold" value="1000" min="0">
                    </div>
                </div>

                <button class="alert-button" onclick="saveAlert('drawdown')">Save Drawdown Alert Settings</button>
            </div>

            <div class="alert-card performance">
                <h3>Performance Alerts</h3>
                <p>Get notified when performance metrics deviate from expected ranges.</p>

                <div class="alert-settings">
                    <div class="alert-toggle">
                        <label>Enable Performance Alerts</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="performance-alert-toggle">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>

                    <div class="alert-setting">
                        <label>Minimum Win Rate (%)</label>
                        <input type="number" id="min-win-rate" value="70" min="0" max="100">
                    </div>

                    <div class="alert-setting">
                        <label>Minimum Daily Profit ($)</label>
                        <input type="number" id="min-daily-profit" value="0" min="0">
                    </div>
                </div>

                <button class="alert-button" onclick="saveAlert('performance')">Save Performance Alert Settings</button>
            </div>
        </div>

        <h2 class="section-title">Notification Methods</h2>

        <div class="notification-settings">
            <h3>How would you like to receive alerts?</h3>

            <div class="notification-grid">
                <div>
                    <div class="notification-method">
                        <h4>Email Notifications</h4>
                        <div class="alert-toggle">
                            <label>Enable Email Alerts</label>
                            <label class="toggle-switch">
                                <input type="checkbox" id="email-toggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="alert-setting">
                            <label>Email Address</label>
                            <input type="email" id="email-address" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="notification-method">
                        <h4>SMS Notifications</h4>
                        <div class="alert-toggle">
                            <label>Enable SMS Alerts</label>
                            <label class="toggle-switch">
                                <input type="checkbox" id="sms-toggle">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="alert-setting">
                            <label>Phone Number</label>
                            <input type="text" id="phone-number" placeholder="+****************">
                        </div>
                    </div>
                </div>

                <div>
                    <div class="notification-method">
                        <h4>Desktop Notifications</h4>
                        <div class="alert-toggle">
                            <label>Enable Desktop Alerts</label>
                            <label class="toggle-switch">
                                <input type="checkbox" id="desktop-toggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="notification-toggle">
                            <input type="checkbox" id="desktop-sound" checked>
                            <label for="desktop-sound">Play sound with notification</label>
                        </div>
                    </div>

                    <div class="notification-method">
                        <h4>Mobile App Notifications</h4>
                        <div class="alert-toggle">
                            <label>Enable Mobile Alerts</label>
                            <label class="toggle-switch">
                                <input type="checkbox" id="mobile-toggle">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="alert-setting">
                            <label>Device ID</label>
                            <input type="text" id="device-id" placeholder="Enter your device ID">
                        </div>
                    </div>
                </div>
            </div>

            <button class="save-button" onclick="saveNotificationSettings()">Save Notification Settings</button>
        </div>

        <h2 class="section-title">Recent Alert History</h2>

        <div class="alert-history">
            <h3>Last 10 Alerts</h3>

            <table class="alert-table">
                <thead>
                    <tr>
                        <th>Date & Time</th>
                        <th>Alert Type</th>
                        <th>Details</th>
                        <th>Notification Sent</th>
                    </tr>
                </thead>
                <tbody id="alert-history-table">
                    <tr>
                        <td>2025-05-06 09:32:15</td>
                        <td><span class="alert-badge entry">Entry</span></td>
                        <td>Long MNQ @ 18245.25, 10 contracts</td>
                        <td>Email, Desktop</td>
                    </tr>
                    <tr>
                        <td>2025-05-06 09:45:22</td>
                        <td><span class="alert-badge exit">Exit</span></td>
                        <td>Exit Long MNQ @ 18256.50, Profit: $225.00</td>
                        <td>Email, Desktop</td>
                    </tr>
                    <tr>
                        <td>2025-05-06 10:15:03</td>
                        <td><span class="alert-badge entry">Entry</span></td>
                        <td>Short MNQ @ 18260.75, 10 contracts</td>
                        <td>Email, Desktop</td>
                    </tr>
                    <tr>
                        <td>2025-05-06 10:22:47</td>
                        <td><span class="alert-badge exit">Exit</span></td>
                        <td>Exit Short MNQ @ 18252.25, Profit: $170.00</td>
                        <td>Email, Desktop</td>
                    </tr>
                    <tr>
                        <td>2025-05-06 11:05:18</td>
                        <td><span class="alert-badge entry">Entry</span></td>
                        <td>Long MNQ @ 18248.50, 10 contracts</td>
                        <td>Email, Desktop</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>Trading Bot Custom Alerts | Generated on May 6, 2025</p>
        </div>
    </div>

    <script>
        // Function to save alert settings
        function saveAlert(alertType) {
            // In a real implementation, this would save to a database or configuration file
            // For this demo, we'll just show a confirmation message

            let message = '';

            switch(alertType) {
                case 'entry':
                    const entryEnabled = document.getElementById('entry-alert-toggle').checked;
                    const minContracts = document.getElementById('entry-min-contracts').value;
                    const direction = document.getElementById('entry-direction').value;

                    message = `Trade Entry Alerts ${entryEnabled ? 'Enabled' : 'Disabled'}\n`;
                    if (entryEnabled) {
                        message += `Minimum Contracts: ${minContracts}\n`;
                        message += `Direction: ${direction}`;
                    }
                    break;

                case 'exit':
                    const exitEnabled = document.getElementById('exit-alert-toggle').checked;
                    const exitReason = document.getElementById('exit-reason').value;
                    const minProfit = document.getElementById('exit-min-profit').value;

                    message = `Trade Exit Alerts ${exitEnabled ? 'Enabled' : 'Disabled'}\n`;
                    if (exitEnabled) {
                        message += `Exit Reason: ${exitReason}\n`;
                        message += `Minimum Profit: $${minProfit}`;
                    }
                    break;

                case 'drawdown':
                    const drawdownEnabled = document.getElementById('drawdown-alert-toggle').checked;
                    const dailyDrawdown = document.getElementById('daily-drawdown-threshold').value;
                    const overallDrawdown = document.getElementById('overall-drawdown-threshold').value;

                    message = `Drawdown Alerts ${drawdownEnabled ? 'Enabled' : 'Disabled'}\n`;
                    if (drawdownEnabled) {
                        message += `Daily Drawdown Threshold: $${dailyDrawdown}\n`;
                        message += `Overall Drawdown Threshold: $${overallDrawdown}`;
                    }
                    break;

                case 'performance':
                    const performanceEnabled = document.getElementById('performance-alert-toggle').checked;
                    const minWinRate = document.getElementById('min-win-rate').value;
                    const minDailyProfit = document.getElementById('min-daily-profit').value;

                    message = `Performance Alerts ${performanceEnabled ? 'Enabled' : 'Disabled'}\n`;
                    if (performanceEnabled) {
                        message += `Minimum Win Rate: ${minWinRate}%\n`;
                        message += `Minimum Daily Profit: $${minDailyProfit}`;
                    }
                    break;
            }

            alert(`Settings Saved:\n${message}`);

            // In a real implementation, we would also update the UI to reflect the saved settings
            // For example, adding a new row to the alert history table

            // Simulate adding a new alert to the history table
            const now = new Date();
            const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

            const alertHistoryTable = document.getElementById('alert-history-table');
            const newRow = alertHistoryTable.insertRow(0);

            const dateCell = newRow.insertCell(0);
            const typeCell = newRow.insertCell(1);
            const detailsCell = newRow.insertCell(2);
            const notificationCell = newRow.insertCell(3);

            dateCell.textContent = formattedDate;
            typeCell.innerHTML = `<span class="alert-badge ${alertType}">${alertType.charAt(0).toUpperCase() + alertType.slice(1)}</span>`;
            detailsCell.textContent = `Alert settings updated`;

            const notifications = [];
            if (document.getElementById('email-toggle').checked) notifications.push('Email');
            if (document.getElementById('sms-toggle').checked) notifications.push('SMS');
            if (document.getElementById('desktop-toggle').checked) notifications.push('Desktop');
            if (document.getElementById('mobile-toggle').checked) notifications.push('Mobile');

            notificationCell.textContent = notifications.join(', ');

            // Remove the last row if there are more than 10 rows
            if (alertHistoryTable.rows.length > 10) {
                alertHistoryTable.deleteRow(10);
            }
        }

        // Function to save notification settings
        function saveNotificationSettings() {
            const emailEnabled = document.getElementById('email-toggle').checked;
            const emailAddress = document.getElementById('email-address').value;
            const smsEnabled = document.getElementById('sms-toggle').checked;
            const phoneNumber = document.getElementById('phone-number').value;
            const desktopEnabled = document.getElementById('desktop-toggle').checked;
            const desktopSound = document.getElementById('desktop-sound').checked;
            const mobileEnabled = document.getElementById('mobile-toggle').checked;
            const deviceId = document.getElementById('device-id').value;

            let message = 'Notification Settings Saved:\n';

            if (emailEnabled) {
                message += `Email Alerts: Enabled (${emailAddress})\n`;
            } else {
                message += 'Email Alerts: Disabled\n';
            }

            if (smsEnabled) {
                message += `SMS Alerts: Enabled (${phoneNumber})\n`;
            } else {
                message += 'SMS Alerts: Disabled\n';
            }

            if (desktopEnabled) {
                message += `Desktop Alerts: Enabled (Sound: ${desktopSound ? 'On' : 'Off'})\n`;
            } else {
                message += 'Desktop Alerts: Disabled\n';
            }

            if (mobileEnabled) {
                message += `Mobile Alerts: Enabled (Device: ${deviceId})\n`;
            } else {
                message += 'Mobile Alerts: Disabled\n';
            }

            alert(message);

            // In a real implementation, we would save these settings to a configuration file or database
        }

        // Function to request notification permissions
        function requestNotificationPermission() {
            if ('Notification' in window) {
                Notification.requestPermission().then(function(permission) {
                    if (permission === 'granted') {
                        // Show a test notification
                        const notification = new Notification('Trading Bot Alerts', {
                            body: 'Notification permissions granted. You will now receive alerts.',
                            icon: 'https://via.placeholder.com/64'
                        });
                    }
                });
            }
        }

        // Request notification permission when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            requestNotificationPermission();
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} Custom Alerts`;
            }

            console.log('Custom alerts dashboard updated with data for:', data.instrumentCode);
        }
    </script>
</body>
</html>
