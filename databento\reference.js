/**
 * Databento Reference API Client
 * For accessing reference data (security information, corporate actions, etc.)
 */

const axios = require('axios');
const { formatDate, handleError } = require('./utils');

class ReferenceClient {
  /**
   * Create a new Reference API client
   * @param {string} apiKey - Your Databento API key
   * @param {string} gateway - Site of historical gateway to connect to
   */
  constructor(apiKey, gateway = 'bo1') {
    this.apiKey = apiKey;
    this.baseUrl = `https://hist.databento.com/v0`;
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Basic ${Buffer.from(`${apiKey}:`).toString('base64')}`,
        'Content-Type': 'application/json'
      }
    });

    // Create sub-clients for different reference data types
    this.securityMaster = {
      getLast: this.getSecurityMasterLast.bind(this),
      getRange: this.getSecurityMasterRange.bind(this)
    };

    this.corporateActions = {
      getRange: this.getCorporateActionsRange.bind(this)
    };

    this.adjustmentFactors = {
      getRange: this.getAdjustmentFactorsRange.bind(this)
    };
  }

  /**
   * Get security master information for symbols (latest)
   * @param {string|Array} symbols - Symbol or array of symbols
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Security master data
   */
  async getSecurityMasterLast(symbols, options = {}) {
    try {
      const params = {
        symbols: Array.isArray(symbols) ? symbols.join(',') : symbols,
        stype_in: options.stype_in || 'raw_symbol',
        countries: options.countries ? (Array.isArray(options.countries) ? options.countries.join(',') : options.countries) : undefined,
        security_types: options.security_types ? (Array.isArray(options.security_types) ? options.security_types.join(',') : options.security_types) : undefined
      };

      const response = await this.client.get('/security_master.get_last', { params });
      return response.data;
    } catch (error) {
      throw handleError(error);
    }
  }

  /**
   * Get security master information for symbols (point-in-time)
   * @param {string|Date} start - Start date/time
   * @param {string|Date} end - End date/time
   * @param {string|Array} symbols - Symbol or array of symbols
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Security master data
   */
  async getSecurityMasterRange(start, end, symbols, options = {}) {
    try {
      const params = {
        start: formatDate(start),
        end: end ? formatDate(end) : undefined,
        index: options.index || 'ts_effective',
        symbols: Array.isArray(symbols) ? symbols.join(',') : symbols,
        stype_in: options.stype_in || 'raw_symbol',
        countries: options.countries ? (Array.isArray(options.countries) ? options.countries.join(',') : options.countries) : undefined,
        security_types: options.security_types ? (Array.isArray(options.security_types) ? options.security_types.join(',') : options.security_types) : undefined
      };

      const response = await this.client.get('/security_master.get_range', { params });
      return response.data;
    } catch (error) {
      throw handleError(error);
    }
  }

  /**
   * Get corporate actions data
   * @param {string|Date} start - Start date/time
   * @param {string|Date} end - End date/time
   * @param {string|Array} symbols - Symbol or array of symbols
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Corporate actions data
   */
  async getCorporateActionsRange(start, end, symbols, options = {}) {
    try {
      const params = {
        start: formatDate(start),
        end: end ? formatDate(end) : undefined,
        index: options.index || 'event_date',
        symbols: Array.isArray(symbols) ? symbols.join(',') : symbols,
        stype_in: options.stype_in || 'raw_symbol',
        events: options.events ? (Array.isArray(options.events) ? options.events.join(',') : options.events) : undefined,
        countries: options.countries ? (Array.isArray(options.countries) ? options.countries.join(',') : options.countries) : undefined,
        security_types: options.security_types ? (Array.isArray(options.security_types) ? options.security_types.join(',') : options.security_types) : undefined,
        flatten: options.flatten !== undefined ? options.flatten : true,
        pit: options.pit !== undefined ? options.pit : false
      };

      const response = await this.client.get('/corporate_actions.get_range', { params });
      return response.data;
    } catch (error) {
      throw handleError(error);
    }
  }

  /**
   * Get adjustment factors data
   * @param {string|Date} start - Start date/time
   * @param {string|Date} end - End date/time
   * @param {string|Array} symbols - Symbol or array of symbols
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Adjustment factors data
   */
  async getAdjustmentFactorsRange(start, end, symbols, options = {}) {
    try {
      const params = {
        start: formatDate(start),
        end: end ? formatDate(end) : undefined,
        symbols: Array.isArray(symbols) ? symbols.join(',') : symbols,
        stype_in: options.stype_in || 'raw_symbol',
        countries: options.countries ? (Array.isArray(options.countries) ? options.countries.join(',') : options.countries) : undefined,
        security_types: options.security_types ? (Array.isArray(options.security_types) ? options.security_types.join(',') : options.security_types) : undefined
      };

      const response = await this.client.get('/adjustment_factors.get_range', { params });
      return response.data;
    } catch (error) {
      throw handleError(error);
    }
  }
}

module.exports = ReferenceClient;
