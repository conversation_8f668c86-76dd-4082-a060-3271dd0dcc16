/**
 * Multi-Market Paper Trading System
 *
 * This module provides an integrated paper trading system for multiple markets:
 * - MNQ (Micro Nasdaq)
 * - MGC (Micro Gold)
 * - MES (Micro S&P 500) - Placeholder for future implementation
 * - M2K (Micro Russell 2000) - Placeholder for future implementation
 *
 * Each market can be individually enabled/disabled and configured with its own
 * optimized parameters.
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api');
const logger = require('./data_logger');
const alerts = require('./alert_system');
const redundancy = require('./redundancy_system');
const fs = require('fs');
const path = require('path');

// Import market configurations
const mnqConfig = require('./paper_trading_config');
const mgcConfig = require('./mgc_paper_trading_config');

// Market definitions with enable/disable flags
const markets = {
    MNQ: {
        enabled: true,
        config: mnqConfig,
        name: "Micro Nasdaq",
        color: "#1E88E5",
        contractMonth: "M4",
        state: {
            isTrading: false,
            openPositions: [],
            lastTradeTime: null,
            equity: mnqConfig.initialBalance || 10000,
            dailyPnL: 0,
            weeklyPnL: 0,
            monthlyPnL: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0
        }
    },
    MGC: {
        enabled: true,
        config: mgcConfig,
        name: "Micro Gold",
        color: "#FFD700",
        contractMonth: "M4",
        state: {
            isTrading: false,
            openPositions: [],
            lastTradeTime: null,
            equity: mgcConfig.initialBalance || 10000,
            dailyPnL: 0,
            weeklyPnL: 0,
            monthlyPnL: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0
        }
    },
    MES: {
        enabled: false, // Disabled by default until configured
        config: null,   // Will be added in the future
        name: "Micro S&P 500",
        color: "#4CAF50",
        contractMonth: "M4",
        state: {
            isTrading: false,
            openPositions: [],
            lastTradeTime: null,
            equity: 10000,
            dailyPnL: 0,
            weeklyPnL: 0,
            monthlyPnL: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0
        }
    },
    M2K: {
        enabled: false, // Disabled by default until configured
        config: null,   // Will be added in the future
        name: "Micro Russell 2000",
        color: "#FF9800",
        contractMonth: "M4",
        state: {
            isTrading: false,
            openPositions: [],
            lastTradeTime: null,
            equity: 10000,
            dailyPnL: 0,
            weeklyPnL: 0,
            monthlyPnL: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0
        }
    }
};

// System state
let systemState = {
    isConnected: false,
    lastConnectionTime: null,
    markets: markets
};

// Load previous state if available
function loadTradingState() {
    try {
        const stateFile = './state/multi_market_state.json';
        if (fs.existsSync(stateFile)) {
            const stateData = fs.readFileSync(stateFile, 'utf8');
            const loadedState = JSON.parse(stateData);

            // Update market states from loaded state
            for (const symbol in loadedState.markets) {
                if (markets[symbol]) {
                    markets[symbol].state = loadedState.markets[symbol].state;
                    markets[symbol].enabled = loadedState.markets[symbol].enabled;
                }
            }

            console.log('Loaded previous multi-market trading state');
        }
    } catch (error) {
        logger.logSystem(`Error loading multi-market trading state: ${error.message}`, 'error');
    }
}

// Save current state
function saveState() {
    try {
        const stateDir = './state';
        if (!fs.existsSync(stateDir)) {
            fs.mkdirSync(stateDir, { recursive: true });
        }

        fs.writeFileSync(
            path.join(stateDir, 'multi_market_state.json'),
            JSON.stringify(systemState, null, 2)
        );

        console.log('Multi-market trading state saved');
    } catch (error) {
        logger.logSystem(`Error saving multi-market trading state: ${error.message}`, 'error');
    }
}

// Initialize Tradovate API connection
async function initializeTradovateConnection() {
    try {
        console.log('Attempting to authenticate...');

        // Prepare authentication payload with hardcoded values for now
        const authPayload = {
            name: "bravesbeatmets",
            password: "Braves12$",
            appId: "Trading Bot",
            appVersion: "0.0.1",
            deviceId: "25e3f568-2890-5442-1e40-b9e8983af7e7",
            cid: "6186",
            sec: "69311dad-75a7-49c6-9958-00ab2c4f1ab6"
        };

        console.log('Authentication payload:', authPayload);

        // Authenticate with Tradovate
        const authResult = await tradovateApi.authenticate(authPayload);

        if (authResult.success) {
            console.log(`Authentication successful! Token expires at: ${new Date(authResult.expirationTime).toLocaleString()}`);
            console.log(`User ID: ${authResult.userId}`);

            // Connect to WebSocket
            console.log(`Connecting to WebSocket: ${tradovateApi.getWebSocketUrl()}`);
            await tradovateApi.connectWebSocket();

            // Configure alert thresholds
            const alertThresholds = {
                drawdown: {
                    warning: 3,
                    critical: 4.5
                },
                winRate: {
                    warning: 65,
                    critical: 60
                }
            };

            console.log('Alert thresholds configured:', alertThresholds);
            alerts.configureAlerts(alertThresholds);

            // Send system startup notification
            alerts.alertSystemStatus('Multi-market paper trading system starting up', 'info');

            systemState.isConnected = true;
            systemState.lastConnectionTime = new Date();

            return true;
        } else {
            console.error('Authentication failed:', authResult.error);
            return false;
        }
    } catch (error) {
        console.error('Error initializing Tradovate connection:', error);
        return false;
    }
}

// Initialize paper trading setup
async function initializeMultiMarketTrading() {
    try {
        console.log('INFO: Initializing multi-market paper trading setup...');

        // Load previous trading state
        loadTradingState();

        // Initialize Tradovate API connection
        const connected = await initializeTradovateConnection();

        if (!connected) {
            console.error('Failed to connect to Tradovate API. Exiting...');
            return false;
        }

        // Initialize trading logic for each market
        const tradingLogic = require('./trading_logic');

        for (const symbol in markets) {
            if (markets[symbol].enabled && markets[symbol].config) {
                console.log(`Initializing trading logic for ${symbol}...`);
                tradingLogic.initializeMarket(symbol, markets[symbol].config);

                // Subscribe to market data
                const contractSymbol = `${symbol}${markets[symbol].contractMonth}`;
                console.log(`Subscribing to ${contractSymbol}...`);
                await tradovateApi.subscribeToChart(contractSymbol, '1m');
            }
        }

        // Initialize alert system
        alerts.alertSystemStatus('Multi-market paper trading setup initialized and ready', 'info');

        console.log('INFO: Multi-market paper trading setup initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing multi-market paper trading:', error);
        return false;
    }
}

// Start trading for a specific market
async function startTrading(symbol) {
    if (!markets[symbol]) {
        console.log(`Market ${symbol} not found.`);
        return;
    }

    if (!markets[symbol].enabled) {
        console.log(`Market ${symbol} is disabled. Enable it first.`);
        return;
    }

    if (markets[symbol].state.isTrading) {
        console.log(`Trading for ${symbol} is already active.`);
        return;
    }

    markets[symbol].state.isTrading = true;
    saveState();

    console.log(`Trading started for ${symbol}.`);
    alerts.alertSystemStatus(`${symbol} paper trading started`, 'info');
}

// Stop trading for a specific market
async function stopTrading(symbol) {
    if (!markets[symbol]) {
        console.log(`Market ${symbol} not found.`);
        return;
    }

    if (!markets[symbol].state.isTrading) {
        console.log(`Trading for ${symbol} is already stopped.`);
        return;
    }

    markets[symbol].state.isTrading = false;
    saveState();

    console.log(`Trading stopped for ${symbol}.`);
    alerts.alertSystemStatus(`${symbol} paper trading stopped`, 'info');
}

// Enable a market
function enableMarket(symbol) {
    if (!markets[symbol]) {
        console.log(`Market ${symbol} not found.`);
        return;
    }

    markets[symbol].enabled = true;
    saveState();

    console.log(`Market ${symbol} enabled.`);
}

// Disable a market
function disableMarket(symbol) {
    if (!markets[symbol]) {
        console.log(`Market ${symbol} not found.`);
        return;
    }

    // Stop trading if active
    if (markets[symbol].state.isTrading) {
        markets[symbol].state.isTrading = false;
        console.log(`Trading for ${symbol} stopped.`);
    }

    markets[symbol].enabled = false;
    saveState();

    console.log(`Market ${symbol} disabled.`);
}

// Show trading status for all markets
function showAllMarketsStatus() {
    console.log('\n=== Multi-Market Paper Trading Status ===');
    console.log(`System Connected: ${systemState.isConnected ? 'Yes' : 'No'}`);
    if (systemState.lastConnectionTime) {
        console.log(`Last Connection: ${systemState.lastConnectionTime.toLocaleString()}`);
    }
    console.log('');

    for (const symbol in markets) {
        const market = markets[symbol];
        console.log(`${market.name} (${symbol}):`);
        console.log(`  Enabled: ${market.enabled ? 'Yes' : 'No'}`);
        console.log(`  Trading Active: ${market.state.isTrading ? 'Yes' : 'No'}`);
        console.log(`  Current Equity: $${market.state.equity.toFixed(2)}`);
        console.log(`  Open Positions: ${market.state.openPositions.length}`);
        console.log(`  Daily P&L: $${market.state.dailyPnL.toFixed(2)}`);
        console.log(`  Total Trades: ${market.state.totalTrades}`);
        console.log(`  Win Rate: ${market.state.totalTrades > 0 ? ((market.state.winningTrades / market.state.totalTrades) * 100).toFixed(2) : 0}%`);
        console.log('');
    }

    console.log('=======================================\n');
}

// Show status for a specific market
function showMarketStatus(symbol) {
    if (!markets[symbol]) {
        console.log(`Market ${symbol} not found.`);
        return;
    }

    const market = markets[symbol];

    console.log(`\n=== ${market.name} (${symbol}) Status ===`);
    console.log(`Enabled: ${market.enabled ? 'Yes' : 'No'}`);
    console.log(`Trading Active: ${market.state.isTrading ? 'Yes' : 'No'}`);
    console.log(`Current Equity: $${market.state.equity.toFixed(2)}`);
    console.log(`Open Positions: ${market.state.openPositions.length}`);
    console.log(`Daily P&L: $${market.state.dailyPnL.toFixed(2)}`);
    console.log(`Total Trades: ${market.state.totalTrades}`);
    console.log(`Win Rate: ${market.state.totalTrades > 0 ? ((market.state.winningTrades / market.state.totalTrades) * 100).toFixed(2) : 0}%`);
    console.log('===============================\n');
}

// Show configuration for a specific market
function showMarketConfiguration(symbol) {
    if (!markets[symbol]) {
        console.log(`Market ${symbol} not found.`);
        return;
    }

    const market = markets[symbol];

    if (!market.config) {
        console.log(`Configuration for ${symbol} not available.`);
        return;
    }

    console.log(`\n=== ${market.name} (${symbol}) Configuration ===`);
    console.log(`Symbol: ${symbol}${market.contractMonth}`);
    console.log(`Strategy Parameters:`);
    console.log(`  - Stop Loss Factor: ${market.config.slFactors}`);
    console.log(`  - Take Profit Factor: ${market.config.tpFactors}`);
    console.log(`  - Trail Factor: ${market.config.trailFactors}`);
    console.log(`Position Size: ${market.config.fixedContracts} contracts`);
    console.log(`Risk Management:`);
    console.log(`  - Daily Stop Loss: $${market.config.dailyStopLoss || 500}`);
    console.log(`  - Max Drawdown: ${market.config.maxDrawdownPercent || 5}%`);
    console.log(`Adaptive Mode: ${market.config.isAdaptiveRun ? 'Enabled' : 'Disabled'}`);
    console.log('======================================\n');
}

// Backup data
function backupData() {
    try {
        saveState();
        console.log('Multi-market trading data backed up successfully.');
    } catch (error) {
        console.error('Error backing up data:', error);
    }
}

// Command menu
function showCommandMenu() {
    console.log('\nMulti-Market Paper Trading Bot - Command Menu');
    console.log('1. Start Trading (All Enabled Markets)');
    console.log('2. Stop Trading (All Enabled Markets)');
    console.log('3. Show All Markets Status');
    console.log('4. Market-Specific Commands');
    console.log('5. Enable/Disable Markets');
    console.log('6. Backup Data');
    console.log('7. Exit');
    console.log('\nEnter command (1-7): ');
}

// Market-specific command menu
function showMarketCommandMenu() {
    console.log('\nMarket-Specific Commands');
    console.log('1. Start Trading for Specific Market');
    console.log('2. Stop Trading for Specific Market');
    console.log('3. Show Status for Specific Market');
    console.log('4. Show Configuration for Specific Market');
    console.log('5. Back to Main Menu');
    console.log('\nEnter command (1-5): ');
}

// Enable/disable markets menu
function showEnableDisableMenu() {
    console.log('\nEnable/Disable Markets');

    let index = 1;
    for (const symbol in markets) {
        console.log(`${index}. ${markets[symbol].enabled ? 'Disable' : 'Enable'} ${markets[symbol].name} (${symbol})`);
        index++;
    }

    console.log(`${index}. Back to Main Menu`);
    console.log(`\nEnter command (1-${index}): `);
}

// Process main command
async function processCommand(command) {
    switch (command) {
        case '1': // Start Trading (All Enabled Markets)
            for (const symbol in markets) {
                if (markets[symbol].enabled) {
                    await startTrading(symbol);
                }
            }
            break;
        case '2': // Stop Trading (All Enabled Markets)
            for (const symbol in markets) {
                if (markets[symbol].enabled && markets[symbol].state.isTrading) {
                    await stopTrading(symbol);
                }
            }
            break;
        case '3': // Show All Markets Status
            showAllMarketsStatus();
            break;
        case '4': // Market-Specific Commands
            showMarketCommandMenu();
            return 'market-specific';
        case '5': // Enable/Disable Markets
            showEnableDisableMenu();
            return 'enable-disable';
        case '6': // Backup Data
            backupData();
            break;
        case '7': // Exit
            console.log('Exiting multi-market paper trading...');
            process.exit(0);
            break;
        default:
            console.log('Invalid command. Please enter a number between 1 and 7.');
    }

    // Show main menu again
    showCommandMenu();
    return 'main';
}

// Process market-specific command
async function processMarketCommand(command) {
    switch (command) {
        case '1': // Start Trading for Specific Market
            console.log('\nAvailable Markets:');
            let index = 1;
            const marketSymbols = [];

            for (const symbol in markets) {
                if (markets[symbol].enabled) {
                    console.log(`${index}. ${markets[symbol].name} (${symbol})`);
                    marketSymbols.push(symbol);
                    index++;
                }
            }

            console.log(`${index}. Back`);
            console.log(`\nSelect market (1-${index}): `);

            return { mode: 'select-market-start', marketSymbols };
        case '2': // Stop Trading for Specific Market
            console.log('\nMarkets Currently Trading:');
            index = 1;
            const tradingSymbols = [];

            for (const symbol in markets) {
                if (markets[symbol].enabled && markets[symbol].state.isTrading) {
                    console.log(`${index}. ${markets[symbol].name} (${symbol})`);
                    tradingSymbols.push(symbol);
                    index++;
                }
            }

            if (tradingSymbols.length === 0) {
                console.log('No markets are currently trading.');
                showMarketCommandMenu();
                return 'market-specific';
            }

            console.log(`${index}. Back`);
            console.log(`\nSelect market (1-${index}): `);

            return { mode: 'select-market-stop', marketSymbols: tradingSymbols };
        case '3': // Show Status for Specific Market
            console.log('\nAvailable Markets:');
            index = 1;
            const statusSymbols = [];

            for (const symbol in markets) {
                console.log(`${index}. ${markets[symbol].name} (${symbol})`);
                statusSymbols.push(symbol);
                index++;
            }

            console.log(`${index}. Back`);
            console.log(`\nSelect market (1-${index}): `);

            return { mode: 'select-market-status', marketSymbols: statusSymbols };
        case '4': // Show Configuration for Specific Market
            console.log('\nAvailable Markets:');
            index = 1;
            const configSymbols = [];

            for (const symbol in markets) {
                if (markets[symbol].config) {
                    console.log(`${index}. ${markets[symbol].name} (${symbol})`);
                    configSymbols.push(symbol);
                    index++;
                }
            }

            console.log(`${index}. Back`);
            console.log(`\nSelect market (1-${index}): `);

            return { mode: 'select-market-config', marketSymbols: configSymbols };
        case '5': // Back to Main Menu
            showCommandMenu();
            return 'main';
        default:
            console.log('Invalid command. Please enter a number between 1 and 5.');
            showMarketCommandMenu();
            return 'market-specific';
    }
}

// Process enable/disable command
function processEnableDisableCommand(command) {
    const marketSymbols = Object.keys(markets);
    const maxIndex = marketSymbols.length + 1;

    const commandNum = parseInt(command);
    if (isNaN(commandNum) || commandNum < 1 || commandNum > maxIndex) {
        console.log(`Invalid command. Please enter a number between 1 and ${maxIndex}.`);
        showEnableDisableMenu();
        return 'enable-disable';
    }

    if (commandNum === maxIndex) {
        // Back to main menu
        showCommandMenu();
        return 'main';
    }

    const symbol = marketSymbols[commandNum - 1];
    if (markets[symbol].enabled) {
        disableMarket(symbol);
    } else {
        enableMarket(symbol);
    }

    showEnableDisableMenu();
    return 'enable-disable';
}

// Process market selection
async function processMarketSelection(command, mode, marketSymbols) {
    const commandNum = parseInt(command);
    if (isNaN(commandNum) || commandNum < 1 || commandNum > marketSymbols.length + 1) {
        console.log(`Invalid selection. Please enter a number between 1 and ${marketSymbols.length + 1}.`);
        return mode;
    }

    if (commandNum === marketSymbols.length + 1) {
        // Back option
        showMarketCommandMenu();
        return 'market-specific';
    }

    const symbol = marketSymbols[commandNum - 1];

    switch (mode) {
        case 'select-market-start':
            await startTrading(symbol);
            break;
        case 'select-market-stop':
            await stopTrading(symbol);
            break;
        case 'select-market-status':
            showMarketStatus(symbol);
            break;
        case 'select-market-config':
            showMarketConfiguration(symbol);
            break;
    }

    showMarketCommandMenu();
    return 'market-specific';
}

// Main function
async function main() {
    console.log('================================================================================');
    console.log('                    MULTI-MARKET PAPER TRADING SYSTEM');
    console.log('================================================================================\n');

    console.log('Initializing trading system...');

    // Initialize paper trading
    const initialized = await initializeMultiMarketTrading();

    if (!initialized) {
        console.error('Failed to initialize multi-market paper trading. Exiting...');
        process.exit(1);
    }

    console.log('Trading system initialized successfully!\n');

    // Show command menu
    showCommandMenu();

    // Current mode
    let currentMode = 'main';
    let selectionContext = null;

    // Process user input
    process.stdin.on('data', async (data) => {
        const command = data.toString().trim();

        if (currentMode === 'main') {
            currentMode = await processCommand(command);
        } else if (currentMode === 'market-specific') {
            const result = await processMarketCommand(command);
            if (typeof result === 'string') {
                currentMode = result;
            } else {
                currentMode = result.mode;
                selectionContext = result.marketSymbols;
            }
        } else if (currentMode === 'enable-disable') {
            currentMode = processEnableDisableCommand(command);
        } else if (currentMode.startsWith('select-market-')) {
            currentMode = await processMarketSelection(command, currentMode, selectionContext);
        }
    });
}

// Start the application
if (require.main === module) {
    main().catch(error => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = {
    markets,
    startTrading,
    stopTrading,
    enableMarket,
    disableMarket,
    showMarketStatus,
    showAllMarketsStatus,
    showMarketConfiguration,
    backupData
};
