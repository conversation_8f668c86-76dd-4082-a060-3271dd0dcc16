#!/usr/bin/env python3
"""
Extract contract data from Databento CSV file.
"""

import os
import sys
import pandas as pd
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Symbol mapping
SYMBOL_MAPPING = {
    'MNQ': ['MNQH5', 'MNQM5', 'MNQU5', 'MNQZ5', 'MNQH6', 'MNQM6'],
    'MES': ['MESH5', 'MESM5', 'MESU5', 'MESZ5', 'MESH6', 'MESM6'],
    'MGC': ['MGCG5', 'MGCJ5', 'MGCM5', 'MGCQ5', 'MGCV5', 'MGCZ5', 'MGCG6', 'MGCJ6', 'MGCM6', 'MGCQ6', 'MGCV6', 'MGCZ6'],
    'M2K': ['M2KH5', 'M2KM5', 'M2KU5', 'M2KZ5', 'M2KH6']
}

def extract_contract_data(input_file, output_dir):
    """
    Extract contract data from CSV file.

    Args:
        input_file (str): Path to CSV file
        output_dir (str): Directory to save extracted data
    """
    try:
        logger.info(f"Reading CSV file: {input_file}")

        # Read CSV file
        df = pd.read_csv(input_file)

        logger.info(f"Read {len(df)} rows from {input_file}")
        logger.info(f"Columns: {df.columns.tolist()}")

        # Check if we have the symbol column
        if 'symbol' not in df.columns:
            logger.error("No 'symbol' column found in CSV file")
            return

        # Get unique symbols
        unique_symbols = df['symbol'].unique()
        logger.info(f"Unique symbols: {unique_symbols}")

        # Create a mapping from contract symbols to base symbols
        contract_to_base = {}
        for base_symbol, contracts in SYMBOL_MAPPING.items():
            for contract in contracts:
                contract_to_base[contract] = base_symbol

        # Process each base symbol
        for base_symbol, contracts in SYMBOL_MAPPING.items():
            logger.info(f"Processing {base_symbol}...")

            # Filter for contracts of this base symbol
            filtered_rows = []
            for _, row in df.iterrows():
                symbol = row['symbol']
                # Check if this is a spread (contains a hyphen)
                if '-' in symbol:
                    # Use the first part of the spread
                    symbol = symbol.split('-')[0]

                # Check if this symbol belongs to our base symbol
                if symbol in contracts:
                    filtered_rows.append(row)

            if not filtered_rows:
                logger.warning(f"No data found for {base_symbol}")
                continue

            # Create DataFrame from filtered rows
            symbol_df = pd.DataFrame(filtered_rows)

            logger.info(f"Found {len(symbol_df)} rows for {base_symbol}")

            # Convert timestamp
            if 'ts_event' in symbol_df.columns:
                symbol_df['timestamp'] = pd.to_datetime(symbol_df['ts_event'])
                symbol_df = symbol_df.drop(columns=['ts_event'])

            # Rename columns
            column_map = {
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume'
            }

            # Select and rename columns
            result_df = pd.DataFrame()
            result_df['timestamp'] = symbol_df['timestamp']

            for old_col, new_col in column_map.items():
                if old_col in symbol_df.columns:
                    result_df[new_col] = symbol_df[old_col]
                else:
                    logger.warning(f"Column {old_col} not found")
                    result_df[new_col] = 0

            # Sort by timestamp
            result_df = result_df.sort_values('timestamp')

            # Remove duplicates
            result_df = result_df.drop_duplicates(subset=['timestamp'])

            # Format timestamp
            result_df['timestamp'] = result_df['timestamp'].dt.strftime('%Y-%m-%dT%H:%M:%SZ')

            # Save to CSV
            output_file = os.path.join(output_dir, f"{base_symbol}_1m.csv")
            result_df.to_csv(output_file, index=False)

            logger.info(f"Saved {len(result_df)} rows to {output_file}")

    except Exception as e:
        logger.error(f"Error extracting contract data: {str(e)}")

def main():
    """Main function."""
    # Configure paths
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    output_dir = input_dir

    # Find CSV files
    csv_files = [f for f in os.listdir(input_dir) if f.endswith('.csv') and not f.endswith('_1m.csv')]

    if not csv_files:
        logger.error("No CSV files found")
        return

    logger.info(f"Found {len(csv_files)} CSV files")

    # Process each CSV file
    for csv_file in csv_files:
        input_file = os.path.join(input_dir, csv_file)
        extract_contract_data(input_file, output_dir)

    # Print summary
    logger.info("Final data files:")
    for file in os.listdir(output_dir):
        if file.endswith('_1m.csv'):
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB
            logger.info(f"- {file}: {file_size:.2f} MB")

if __name__ == "__main__":
    main()
