/**
 * MES (Micro E-mini S&P 500) Trading Configuration
 *
 * This file contains the configuration for trading MES futures.
 * Parameters are based on the backtest results.
 */

module.exports = {
    // Symbol information
    symbol: 'MES',
    pointValue: 5.0,
    tickSize: 0.25,

    // Contract information
    contractMonth: 'M5', // Will be updated dynamically

    // Account information
    accountId: null, // Will be set during initialization

    // Commission and slippage
    commission: 0.40,
    commissionPerContract: 0.40,
    slippage: 0.0,
    slippagePoints: 0.0,

    // Position sizing
    fixedContracts: 3,

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    sma200Period: 0, // Not used
    atrPeriod: 14,

    // Entry/exit parameters
    slFactors: 3.0,
    tpFactors: 3.0,
    trailFactors: 0.01,
    fixedTpPoints: 0,

    // Risk management
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0.5,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    useTwoBarColorExit: false,

    // ATR thresholds for adaptive mode
    isAdaptiveRun: false,
    atrThresholds: {
        low_medium: 3.0,
        medium_high: 5.0
    },

    // Formatting
    pricePrecision: 2,

    // Logging
    logLevel: 'info'
};
