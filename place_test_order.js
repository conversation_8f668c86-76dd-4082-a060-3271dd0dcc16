/**
 * Standalone Test Order Placement Script
 * 
 * This script provides a simple way to test order placement through the Tradovate API
 * without running the full trading bot.
 */

require('dotenv').config();
const explicitTestOrder = require('./test_order_explicit');

// Run the test
console.log('Running standalone test order placement...');
explicitTestOrder.placeExplicitTestOrder()
    .then(result => {
        if (result) {
            console.log('Test order placement successful!');
        } else {
            console.log('Test order placement failed. See logs above for details.');
        }
        process.exit(result ? 0 : 1);
    })
    .catch(error => {
        console.error(`Unhandled error: ${error.message}`);
        process.exit(1);
    });
