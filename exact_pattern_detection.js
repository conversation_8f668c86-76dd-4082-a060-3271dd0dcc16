/**
 * exact_pattern_detection.js
 * Exact implementation of pattern detection from the original backtest code
 */

/**
 * Helper function to determine candle color
 * @param {Object} candle - Candle data
 * @returns {string} - Candle color ('green', 'red', 'doji', or 'invalid')
 */
function candlestickColor(candle) {
    if (!candle ||
        typeof candle.open !== 'number' ||
        typeof candle.close !== 'number' ||
        isNaN(candle.open) ||
        isNaN(candle.close)) {
        return 'invalid';
    }

    return candle.close > candle.open ? 'green' :
           candle.close < candle.open ? 'red' :
           'doji';
}

/**
 * Detect 3-candle pattern (exact implementation from original backtest)
 * @param {Object} c1 - First candle
 * @param {Object} c2 - Second candle
 * @param {Object} c3 - Third candle
 * @returns {string|null} - Pattern direction or null
 */
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) return null;

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;

        if (engulf && wick3 > wick2) return 'bullish';
    }

    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);

        if (engulf && wick3 > wick2) return 'bearish';
    }

    return null;
}

/**
 * Detect 4-candle pattern (exact implementation from original backtest)
 * @param {Object} c0 - First candle
 * @param {Object} c1 - Second candle
 * @param {Object} c2 - Third candle
 * @param {Object} c3 - Fourth candle
 * @returns {string|null} - Pattern direction or null
 */
function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) return null;

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') return null;

    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        if (c3.close > Math.max(c1.open, c2.open)) return 'bullish';
    }

    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        if (c3.close < Math.min(c1.open, c2.open)) return 'bearish';
    }

    return null;
}

/**
 * Validate entry signal (exact implementation from original backtest)
 * @param {string} dir - Pattern direction
 * @param {string} patternType - Pattern type
 * @param {Object} c3 - Current candle
 * @param {number} currentIndex - Current index
 * @param {Array} candlesForPeriod - Array of candles
 * @param {Object} config - Configuration object
 * @returns {boolean} - True if entry is valid
 */
function entryOK(dir, patternType, c3, currentIndex, candlesForPeriod, config) {
    // Basic validation
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr)) {
        return false;
    }

    // WMA filter
    if (config.useWmaFilter && ((dir === 'bullish' && c3.close <= c3.wma50) || (dir === 'bearish' && c3.close >= c3.wma50))) {
        return false;
    }

    // SMA200 filter if enabled
    if (config.useSma200Filter && !isNaN(c3.sma200)) {
        if ((dir === 'bullish' && c3.close <= c3.sma200) || (dir === 'bearish' && c3.close >= c3.sma200)) {
            return false;
        }
    }

    // RSI filter
    if (config.useRsiFilter) {
        if ((dir === 'bullish' && c3.rsi <= c3.rsiMa) || (dir === 'bearish' && c3.rsi >= c3.rsiMa)) {
            return false;
        }
    }

    // Minimum ATR filter
    const minAtrEntry = config.minAtrEntry || 0;
    if (c3.atr < minAtrEntry) {
        return false;
    }

    // RSI-MA separation filter
    const minRsiMaSeparation = config.minRsiMaSeparation || 0;
    if (Math.abs(c3.rsi - c3.rsiMa) < minRsiMaSeparation) {
        return false;
    }

    return true;
}

module.exports = {
    candlestickColor,
    detect3,
    detect4,
    entryOK
};
