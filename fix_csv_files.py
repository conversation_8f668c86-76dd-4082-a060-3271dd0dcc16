#!/usr/bin/env python3
"""
Fix CSV files by removing extra columns and fixing headers.
"""

import os
import sys
import csv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_csv_file(file_path):
    """
    Fix a CSV file by removing extra columns and fixing headers.
    
    Args:
        file_path (str): Path to CSV file
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Fixing {file_path}...")
        
        # Read the CSV file
        with open(file_path, 'r') as f:
            reader = csv.reader(f)
            rows = list(reader)
        
        if not rows:
            logger.error(f"Empty file: {file_path}")
            return False
        
        # Fix header row
        header = rows[0]
        logger.info(f"Original header: {header}")
        
        # Keep only the required columns
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        
        # Check if we have all required columns
        for col in required_columns:
            if col not in header:
                logger.error(f"Missing required column: {col}")
                return False
        
        # Get indices of required columns
        indices = [header.index(col) for col in required_columns]
        
        # Create new rows with only the required columns
        new_rows = [required_columns]
        for row in rows[1:]:
            if len(row) > max(indices):
                new_row = [row[i] for i in indices]
                new_rows.append(new_row)
        
        logger.info(f"Fixed header: {new_rows[0]}")
        
        # Write the fixed file
        with open(file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerows(new_rows)
        
        logger.info(f"Saved fixed file to {file_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error fixing {file_path}: {str(e)}")
        return False

def main():
    """Main function."""
    # Configure paths
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    
    # Find CSV files
    csv_files = [f for f in os.listdir(input_dir) if f.endswith('_1m.csv')]
    
    if not csv_files:
        logger.error("No CSV files found")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files")
    
    # Process each CSV file
    for csv_file in csv_files:
        file_path = os.path.join(input_dir, csv_file)
        success = fix_csv_file(file_path)
        
        if not success:
            logger.error(f"Failed to fix {csv_file}")
    
    logger.info("Data fixing complete!")

if __name__ == "__main__":
    main()
