/**
 * WebSocket Helper
 *
 * A simplified helper for WebSocket connections with improved stability
 */

const WebSocket = require('ws');
const logger = require('./data_logger');

// Constants
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const RECONNECT_DELAY = 5000; // 5 seconds
const MAX_RECONNECT_ATTEMPTS = 30; // Increased from 10 to 30 for better persistence

/**
 * Create a WebSocket connection with improved stability
 * @param {string} url - WebSocket URL
 * @param {Object} options - Options
 * @returns {Object} - WebSocket and control functions
 */
function createStableWebSocket(url, options = {}) {
    const name = options.name || 'WebSocket';
    const debug = options.debug || false;

    let ws = null;
    let heartbeatInterval = null;
    let reconnectTimeout = null;
    let reconnectAttempts = 0;
    let isConnecting = false;
    let lastMessageTime = Date.now();
    let messageHandlers = [];

    // Log function
    const log = (message, level = 'info') => {
        if (debug || level !== 'debug') {
            logger.logSystem(`[${name}] ${message}`, level);
        }
    };

    // Connect function
    const connect = () => {
        return new Promise((resolve) => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected');
                resolve(true);
                return;
            }

            if (isConnecting) {
                log('Already connecting');
                resolve(false);
                return;
            }

            isConnecting = true;
            log(`Connecting to ${url}`);

            try {
                ws = new WebSocket(url);

                ws.on('open', () => {
                    isConnecting = false;
                    reconnectAttempts = 0;
                    lastMessageTime = Date.now();

                    log('Connection established');
                    startHeartbeat();

                    resolve(true);
                });

                ws.on('message', (data) => {
                    lastMessageTime = Date.now();

                    // Call all message handlers
                    for (const handler of messageHandlers) {
                        try {
                            handler(data);
                        } catch (error) {
                            log(`Error in message handler: ${error.message}`, 'error');
                        }
                    }
                });

                ws.on('close', (code, reason) => {
                    isConnecting = false;
                    stopHeartbeat();

                    log(`Connection closed. Code: ${code}, Reason: ${reason || 'No reason'}`);

                    // Attempt to reconnect
                    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                        log(`Attempting to reconnect in ${RECONNECT_DELAY / 1000} seconds...`);

                        reconnectTimeout = setTimeout(() => {
                            reconnect();
                        }, RECONNECT_DELAY);
                    } else {
                        log(`Maximum reconnect attempts (${MAX_RECONNECT_ATTEMPTS}) reached`);
                    }
                });

                ws.on('error', (error) => {
                    log(`WebSocket error: ${error.message}`, 'error');
                });

                ws.on('ping', () => {
                    lastMessageTime = Date.now();
                    log('Received ping', 'debug');
                });

                ws.on('pong', () => {
                    lastMessageTime = Date.now();
                    log('Received pong', 'debug');
                });
            } catch (error) {
                isConnecting = false;
                log(`Connection error: ${error.message}`, 'error');
                resolve(false);
            }
        });
    };

    // Disconnect function
    const disconnect = () => {
        log('Disconnecting');

        // Clear timers
        clearTimeout(reconnectTimeout);
        reconnectTimeout = null;
        stopHeartbeat();

        // Reset state
        isConnecting = false;
        reconnectAttempts = 0;

        // Close WebSocket
        if (ws) {
            try {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close(1000, 'Normal closure');
                } else {
                    ws.terminate();
                }
            } catch (error) {
                log(`Error closing WebSocket: ${error.message}`, 'error');
            }

            ws = null;
        }
    };

    // Reconnect function
    const reconnect = () => {
        reconnectAttempts++;
        log(`Reconnecting... (Attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

        if (reconnectTimeout) {
            clearTimeout(reconnectTimeout);
            reconnectTimeout = null;
        }

        connect()
            .then((connected) => {
                if (connected) {
                    log('Reconnected successfully');
                } else {
                    log('Failed to reconnect');
                }
            })
            .catch((error) => {
                log(`Reconnection error: ${error.message}`, 'error');
            });
    };

    // Start heartbeat function
    const startHeartbeat = () => {
        stopHeartbeat();

        log('Starting heartbeat');

        heartbeatInterval = setInterval(() => {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                stopHeartbeat();
                return;
            }

            // Check if we've received a message recently
            const now = Date.now();
            const elapsed = now - lastMessageTime;

            if (elapsed > HEARTBEAT_INTERVAL * 2) {
                log(`No message received for ${elapsed}ms, reconnecting`, 'warning');

                // Try to send a ping first to see if connection is still alive
                try {
                    const pingPromise = new Promise((resolve) => {
                        const pingTimeout = setTimeout(() => {
                            log('Ping timeout, connection is dead', 'warning');
                            resolve(false);
                        }, 2000);

                        ws.once('pong', () => {
                            clearTimeout(pingTimeout);
                            log('Received pong response to emergency ping', 'info');
                            lastMessageTime = Date.now(); // Update last message time
                            resolve(true);
                        });

                        ws.ping();
                    });

                    // Wait for ping result
                    pingPromise.then((alive) => {
                        if (!alive) {
                            log('Connection is dead, reconnecting', 'warning');
                            disconnect();
                            reconnect();
                        } else {
                            log('Connection is still alive after emergency ping', 'info');
                        }
                    });

                    return;
                } catch (error) {
                    log(`Error sending emergency ping: ${error.message}`, 'error');
                    disconnect();
                    reconnect();
                    return;
                }
            }

            // Send regular ping
            try {
                ws.ping();
                log('Sent ping', 'debug');
            } catch (error) {
                log(`Error sending ping: ${error.message}`, 'error');
                // If we can't send a ping, the connection might be dead
                disconnect();
                reconnect();
            }
        }, HEARTBEAT_INTERVAL);
    };

    // Stop heartbeat function
    const stopHeartbeat = () => {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
            log('Stopped heartbeat');
        }
    };

    // Send function
    const send = (data) => {
        return new Promise((resolve) => {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Not connected, cannot send message', 'warning');
                resolve(false);
                return;
            }

            try {
                ws.send(data, (error) => {
                    if (error) {
                        log(`Send error: ${error.message}`, 'error');
                        resolve(false);
                    } else {
                        log('Message sent successfully', 'debug');
                        resolve(true);
                    }
                });
            } catch (error) {
                log(`Send error: ${error.message}`, 'error');
                resolve(false);
            }
        });
    };

    // Add message handler function
    const addMessageHandler = (handler) => {
        if (typeof handler === 'function') {
            messageHandlers.push(handler);
            return true;
        }
        return false;
    };

    // Remove message handler function
    const removeMessageHandler = (handler) => {
        const index = messageHandlers.indexOf(handler);
        if (index !== -1) {
            messageHandlers.splice(index, 1);
            return true;
        }
        return false;
    };

    // Get state function
    const getState = () => {
        return {
            connected: ws && ws.readyState === WebSocket.OPEN,
            connecting: isConnecting,
            reconnectAttempts,
            lastMessageTime
        };
    };

    // Return WebSocket and control functions
    return {
        connect,
        disconnect,
        reconnect,
        send,
        addMessageHandler,
        removeMessageHandler,
        getState
    };
}

module.exports = {
    createStableWebSocket
};
