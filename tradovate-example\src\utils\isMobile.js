// isMobile.js
// Utility to detect if the current device is a mobile device

/**
 * Check if the current device is a mobile device
 * @returns {boolean} Whether the device is mobile
 */
export const isMobile = () => {
    // Check if window and navigator are available (browser environment)
    if (typeof window === 'undefined' || !window.navigator) {
        return false
    }
    
    // Check for mobile user agent
    const userAgent = navigator.userAgent || navigator.vendor || window.opera
    
    // Regular expression for mobile devices
    const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
    
    return mobileRegex.test(userAgent)
}
