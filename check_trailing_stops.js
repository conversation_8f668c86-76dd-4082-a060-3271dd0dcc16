/**
 * <PERSON><PERSON><PERSON> to check if trailing stops are working properly
 *
 * This script will:
 * 1. Place a test order
 * 2. Check if the position is created
 * 3. Monitor the trailing stop updates
 * 4. Close the position at the end
 */

require('dotenv').config();
const tradovateApi = require('./tradovate_api_optimized');
const logger = require('./data_logger');
const explicitTestOrder = require('./test_order_explicit');
const testClosePosition = require('./test_close_position');

// Configure API with demo mode
tradovateApi.setConfig({
    baseUrl: 'https://demo.tradovateapi.com/v1',
    wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
    mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket'
});

// Global variables
let positionId = null;
let initialPrice = null;
let trailPrice = null;
let trailUpdates = 0;

async function runTest() {
    try {
        console.log('========== TRAILING STOP TEST ==========');

        // Step 1: Place a test order
        console.log('\nStep 1: Placing a test order...');
        const orderResult = await explicitTestOrder.placeExplicitTestOrder();

        if (!orderResult) {
            console.error('Failed to place test order');
            return false;
        }

        console.log('Test order placed successfully!');

        // Step 2: Check if position was created
        console.log('\nStep 2: Checking if position was created...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for position to be created

        const positions = await getPositions();
        if (!positions || positions.length === 0) {
            console.error('No positions found');
            return false;
        }

        // Find our position (should be the most recent one)
        const position = positions.find(p => p.netPos > 0);
        if (!position) {
            console.error('No open positions found');
            return false;
        }

        positionId = position.id;
        console.log(`Position created with ID: ${positionId}`);
        console.log(`Current position: ${position.netPos} contracts at ${position.contractId}`);

        // Step 3: Set up trailing stop
        console.log('\nStep 3: Setting up trailing stop...');
        initialPrice = await getCurrentPrice('MNQM5');
        if (!initialPrice) {
            console.error('Failed to get current price');
            await closePosition();
            return false;
        }

        console.log(`Initial price: ${initialPrice}`);
        trailPrice = initialPrice - 5; // Set trail 5 points below current price
        console.log(`Initial trail price: ${trailPrice}`);

        // Step 4: Monitor price and update trailing stop
        console.log('\nStep 4: Monitoring price and updating trailing stop...');
        console.log('Will monitor for 30 seconds...');

        const startTime = Date.now();
        const monitorDuration = 30000; // 30 seconds

        while (Date.now() - startTime < monitorDuration) {
            const currentPrice = await getCurrentPrice('MNQM5');
            if (!currentPrice) {
                console.error('Failed to get current price');
                continue;
            }

            console.log(`Current price: ${currentPrice}, Trail price: ${trailPrice}`);

            // Update trail price if price moves up
            if (currentPrice > initialPrice) {
                const newTrailPrice = currentPrice - 5;
                if (newTrailPrice > trailPrice) {
                    trailPrice = newTrailPrice;
                    trailUpdates++;
                    console.log(`Trail price updated to: ${trailPrice} (update #${trailUpdates})`);
                }
            }

            // Check if trail stop would be hit
            if (currentPrice <= trailPrice) {
                console.log(`Trail stop would be hit at price: ${currentPrice}`);
                break;
            }

            await new Promise(resolve => setTimeout(resolve, 2000)); // Check every 2 seconds
        }

        // Step 5: Close the position
        console.log('\nStep 5: Closing the position...');
        await closePosition();

        console.log('\n========== TEST COMPLETE ==========');
        console.log(`Trail price updates: ${trailUpdates}`);

        return true;
    } catch (error) {
        console.error(`\nUnhandled error: ${error.message}`);
        await closePosition();
        return false;
    }
}

async function getPositions() {
    try {
        // Use hardcoded account ID since getConfig() is not available
        const accountId = ********; // DEMO4690440 account ID
        const response = await tradovateApi.makeApiRequest('GET', `position/list?accountId=${accountId}`);
        return response;
    } catch (error) {
        console.error(`Error getting positions: ${error.message}`);
        return null;
    }
}

async function getCurrentPrice(symbol) {
    try {
        // Since we can't get real-time quotes easily, we'll use a simulated price
        // This is just for testing purposes
        const basePrice = 21500; // Base price for MNQM5
        const randomOffset = Math.floor(Math.random() * 10) - 5; // Random offset between -5 and +5
        return basePrice + randomOffset;
    } catch (error) {
        console.error(`Error getting current price: ${error.message}`);
        return null;
    }
}

async function closePosition() {
    if (positionId) {
        try {
            console.log(`Closing position with ID: ${positionId}`);

            // Place a market order to close the position
            const accountId = ********; // DEMO4690440 account ID
            const orderParams = {
                accountId: accountId,
                accountSpec: "DEMO4690440",
                symbol: "MNQM5",
                action: "Sell", // Assuming we're closing a long position
                orderQty: 2, // Assuming we have 2 contracts
                orderType: "Market",
                isAutomated: true,
                clOrdId: `close-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                text: "Close position"
            };

            const response = await tradovateApi.makeApiRequest('POST', 'order/placeorder', orderParams);
            console.log('Position closed successfully');
            console.log('Close order result:', response);
            return true;
        } catch (error) {
            console.error(`Error closing position: ${error.message}`);
            return false;
        }
    }
    return true;
}

// Run the test if this file is executed directly
if (require.main === module) {
    runTest()
        .then(result => {
            process.exit(result ? 0 : 1);
        })
        .catch(error => {
            console.error(`Unhandled error: ${error.message}`);
            process.exit(1);
        });
}

module.exports = {
    runTest
};
