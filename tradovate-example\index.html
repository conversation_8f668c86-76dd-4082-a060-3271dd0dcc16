<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tradovate API Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        #output {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 3px;
            min-height: 100px;
        }
        .actions {
            margin: 20px 0;
        }
        .btn {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .account-info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .account-info h1 {
            margin-top: 0;
            color: #333;
            font-size: 24px;
        }
        .account-info h2 {
            color: #666;
            font-size: 18px;
        }
        .account-info section {
            margin-top: 15px;
        }
        .account-info div {
            margin: 5px 0;
            padding: 5px;
            background-color: #fff;
            border-radius: 3px;
        }
        .order-form {
            margin: 20px 0;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 5px;
            border: 1px solid #b0c4de;
        }
        .order-form h2 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .order-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .btn-buy {
            background-color: #4CAF50;
        }
        .btn-sell {
            background-color: #f44336;
        }
        .order-response {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .order-response.success {
            display: block;
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
        }
        .order-response.error {
            display: block;
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            color: #a94442;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tradovate API Example</h1>
        <p>This is a simple example of connecting to the Tradovate API.</p>

        <div class="actions">
            <button id="get-acct-btn" class="btn">Get Account List</button>
        </div>

        <div class="order-form">
            <h2>Place an Order</h2>
            <div class="form-group">
                <label for="symbol">Symbol:</label>
                <input type="text" id="symbol" placeholder="e.g., MNQ" value="MNQ">
            </div>
            <div class="form-group">
                <label for="quantity">Quantity:</label>
                <input type="number" id="quantity" min="1" value="1">
            </div>
            <div class="form-group">
                <label for="order-type">Order Type:</label>
                <select id="order-type">
                    <option value="Market">Market</option>
                    <option value="Limit">Limit</option>
                    <option value="Stop">Stop</option>
                    <option value="StopLimit">Stop Limit</option>
                </select>
            </div>
            <div class="form-group price-field" style="display: none;">
                <label for="price">Price:</label>
                <input type="number" id="price" step="0.01">
            </div>
            <div class="form-group stop-price-field" style="display: none;">
                <label for="stop-price">Stop Price:</label>
                <input type="number" id="stop-price" step="0.01">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="is-automated">
                    Is Automated
                </label>
            </div>
            <div class="order-buttons">
                <button id="buy-btn" class="btn btn-buy">Buy</button>
                <button id="sell-btn" class="btn btn-sell">Sell</button>
            </div>
            <div id="order-response" class="order-response"></div>
        </div>

        <div id="outlet" class="account-info">
            <!-- Account information will be displayed here -->
        </div>

        <div id="output">
            <p>Console output will appear here...</p>
        </div>
    </div>
    <script>
        // Redirect console.log to our output div
        const output = document.getElementById('output');
        const originalConsoleLog = console.log;

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);

            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                } else {
                    return arg;
                }
            }).join(' ');

            const logElement = document.createElement('pre');
            logElement.textContent = message;
            output.appendChild(logElement);
        };
    </script>
</body>
</html>
