// connect.js
// Handles connection to Tradovate API

import { tvPost } from './services'
import { getAccessToken, setAccessToken, tokenIsValid } from './storage'
import { waitForMs } from './utils/waitForMs'

/**
 * Handle time penalty retry logic
 * @param {Object} data - Original request data
 * @param {Object} json - Time penalty response
 * @returns {Promise<Object>} Result of retry attempt
 */
const handleRetry = async (data, json) => {
    const ticket = json['p-ticket']
    const time = json['p-time']
    const captcha = json['p-captcha']

    if (captcha) {
        console.error('Captcha present, cannot retry auth request via third party application. Please try again in an hour.')
        return null
    }

    console.log(`Time Penalty present. Retrying operation in ${time}s`)

    // Wait for the specified time
    await waitForMs(time * 1000)

    // Retry the request with the ticket
    return await connect({ ...data, 'p-ticket': ticket })
}

/**
 * Connect to Tradovate API and get access token
 * @param {Object} data - Authentication data
 * @returns {Promise<Object>} Authentication response
 */
export const connect = async (data) => {
    try {
        // Check if we already have a valid token
        const { token, expiration } = getAccessToken()
        if (token && tokenIsValid(expiration)) {
            console.log('Already have an access token. Using existing token.')
            return { accessToken: token, expirationTime: expiration }
        }

        // No valid token, request a new one
        console.log('Requesting new access token...')

        // Use tvPost to make the access token request
        // The third parameter is false because we don't need auth for this request
        const response = await tvPost('/auth/accesstokenrequest', data, false)

        console.log('Authentication response:', response)

        if (response.accessToken) {
            console.log('Successfully authenticated!')
            console.log('Access Token:', response.accessToken)
            console.log('Expires at:', new Date(response.expirationTime).toLocaleString())

            // Store the access token for future requests
            setAccessToken(response.accessToken, response.expirationTime)

            return response
        } else {
            // Check for time penalty response
            if (response['p-ticket']) {
                console.warn('Received time penalty response.')
                console.warn('Penalty time:', response['p-time'])
                console.warn('Captcha required:', response['p-captcha'] ? 'Yes' : 'No')

                // Try to handle the retry
                return await handleRetry(data, response)
            }

            console.error('Authentication failed:', response.errorText || 'Unknown error')
            return null
        }
    } catch (error) {
        console.error('Error connecting to Tradovate API:', error)
        return null
    }
}
