/**
 * run-bot.js
 * Script to run the trading bot
 */

import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import { execSync } from 'child_process';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

try {
    console.log('Starting Tradovate Trading Bot...');
    console.log('Current directory:', __dirname);

    // Get the absolute path to the webpack.config.mjs file
    const configPath = resolve(__dirname, 'webpack.config.mjs');
    console.log('Config path:', configPath);

    // Run webpack dev server with the specified config
    execSync(`npx webpack serve --open trading-bot.html --config "${configPath}"`, {
        stdio: 'inherit',
        cwd: __dirname
    });

    console.log('Trading bot server started');
} catch (error) {
    console.error('Error starting trading bot:', error);
}
