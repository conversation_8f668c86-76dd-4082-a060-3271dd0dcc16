<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tradovate Trading Bot</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --error-color: #e74c3c;
            --text-color: #ecf0f1;
            --background-color: #1a1a1a;
            --card-background: #2c2c2c;
            --border-color: #444;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 20px;
        }
        
        header {
            grid-column: 1 / span 2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: var(--primary-color);
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        h1, h2, h3 {
            color: var(--secondary-color);
            margin-bottom: 15px;
        }
        
        .card {
            background-color: var(--card-background);
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .status-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-running {
            color: var(--success-color);
            font-weight: bold;
        }
        
        .status-stopped {
            color: var(--error-color);
            font-weight: bold;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: var(--secondary-color);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        th {
            background-color: var(--primary-color);
            color: var(--secondary-color);
        }
        
        tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .empty-row {
            text-align: center;
            color: #95a5a6;
            font-style: italic;
        }
        
        .positive {
            color: var(--success-color);
        }
        
        .negative {
            color: var(--error-color);
        }
        
        .log-container {
            grid-column: 1 / span 2;
            height: 300px;
            overflow-y: auto;
            background-color: #000;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .log-entry.error {
            color: var(--error-color);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .info-label {
            font-weight: bold;
            color: var(--secondary-color);
        }
        
        .info-value {
            text-align: right;
        }
        
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
            
            header {
                grid-column: 1;
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Tradovate Trading Bot</h1>
            <div class="button-group">
                <button id="start-btn">Start Bot</button>
                <button id="stop-btn" disabled>Stop Bot</button>
            </div>
        </header>
        
        <div class="card status-card">
            <div>
                <h2>Bot Status</h2>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span id="status-text" class="status-stopped info-value">Stopped</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Balance:</span>
                    <span id="balance-text" class="info-value">$0.00</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Active Symbols:</span>
                    <span id="symbols-text" class="info-value">None</span>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>Daily Statistics</h2>
            <table id="stats-table">
                <tr>
                    <td>Trades</td>
                    <td>0</td>
                </tr>
                <tr>
                    <td>Win Rate</td>
                    <td>N/A</td>
                </tr>
                <tr>
                    <td>P&L</td>
                    <td>$0.00</td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>Open Positions</h2>
            <table id="positions-table">
                <tr>
                    <th>Symbol</th>
                    <th>Quantity</th>
                    <th>Entry Price</th>
                    <th>Current P&L</th>
                </tr>
                <tr>
                    <td colspan="4" class="empty-row">No open positions</td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>Configuration</h2>
            <div class="info-item">
                <span class="info-label">Instrument:</span>
                <span class="info-value">MNQ, MES, MGC</span>
            </div>
            <div class="info-item">
                <span class="info-label">Strategy:</span>
                <span class="info-value">Pattern Detection</span>
            </div>
            <div class="info-item">
                <span class="info-label">Position Size:</span>
                <span class="info-value">Adaptive</span>
            </div>
            <div class="info-item">
                <span class="info-label">Risk Management:</span>
                <span class="info-value">Trailing Stop</span>
            </div>
        </div>
        
        <div class="card log-container" id="log-container">
            <!-- Log entries will be added here -->
        </div>
    </div>
    
    <script type="module" src="./src/trading-bot-ui.js"></script>
</body>
</html>
