<!DOCTYPE html>
<html>
<head>
    <title>Dashboard Template</title>
    
    <!-- Load Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Load dashboard integration scripts -->
    <script src="dashboard_config.js"></script>
    <script src="dashboard_data_loader.js"></script>
    <script src="dashboard_chart_utils.js"></script>
    <script src="dashboard_integration.js"></script>
    
    <style>
        /* Base styles will be applied by dashboard_integration.js */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-body, 'Rajdhani', sans-serif);
            background-color: var(--bg-main, #0a0e17);
            color: var(--text-primary, #f8fafc);
            line-height: 1.6;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }
        
        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border, #2a3a5a);
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary, #00ccff), transparent);
            opacity: 0.7;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary, #00ccff);
            margin-bottom: 0.5rem;
            text-shadow: 0 0 15px var(--primary-glow, rgba(0, 204, 255, 0.5));
            font-family: var(--font-heading, 'Orbitron', sans-serif);
        }
        
        .header p {
            color: var(--text-secondary, #94a3b8);
            font-size: 1.1rem;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary, #00ccff);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border, #2a3a5a);
            text-shadow: 0 0 10px var(--primary-glow, rgba(0, 204, 255, 0.5));
            font-family: var(--font-heading, 'Orbitron', sans-serif);
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary, #00ccff), transparent);
            opacity: 0.7;
        }
        
        .card {
            background-color: var(--card-bg, #141b2d);
            border: 1px solid var(--border, #2a3a5a);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary, #00ccff), var(--primary-light, #66e0ff));
        }
        
        .card h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary, #00ccff);
            margin-bottom: 1.5rem;
            text-shadow: 0 0 10px var(--primary-glow, rgba(0, 204, 255, 0.5));
            font-family: var(--font-heading, 'Orbitron', sans-serif);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }
        
        .chart-container {
            width: 100%;
            height: 300px;
            position: relative;
        }
        
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border, #2a3a5a);
            color: var(--text-secondary, #94a3b8);
            font-size: 0.9rem;
            position: relative;
        }
        
        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary, #00ccff), transparent);
            opacity: 0.7;
        }
        
        /* Loading indicator */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-family: var(--font-body, 'Rajdhani', sans-serif);
            color: var(--text-secondary, #94a3b8);
        }
        
        .loading::after {
            content: '';
            width: 30px;
            height: 30px;
            border: 3px solid var(--border, #2a3a5a);
            border-top-color: var(--primary, #00ccff);
            border-radius: 50%;
            margin-left: 10px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>Dashboard Title</h1>
            <p>Dashboard description goes here</p>
            <!-- Instrument selector will be added here by dashboard_integration.js -->
        </div>
        
        <h2 class="section-title">Performance Overview</h2>
        
        <div class="grid" id="stats-grid">
            <div class="card">
                <h3>Total Profit</h3>
                <div id="total-profit" class="loading">Loading data...</div>
            </div>
            
            <div class="card">
                <h3>Win Rate</h3>
                <div id="win-rate" class="loading">Loading data...</div>
            </div>
            
            <div class="card">
                <h3>Profit Factor</h3>
                <div id="profit-factor" class="loading">Loading data...</div>
            </div>
            
            <div class="card">
                <h3>Max Drawdown</h3>
                <div id="max-drawdown" class="loading">Loading data...</div>
            </div>
        </div>
        
        <h2 class="section-title">Performance Charts</h2>
        
        <div class="card">
            <h3>Equity Curve</h3>
            <div class="chart-container">
                <canvas id="equity-chart"></canvas>
            </div>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>Monthly Returns</h3>
                <div class="chart-container">
                    <canvas id="monthly-returns-chart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h3>Win/Loss Distribution</h3>
                <div class="chart-container">
                    <canvas id="win-loss-chart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Trading Bot Dashboard &copy; 2023</p>
        </div>
    </div>
    
    <script>
        // Wait for dashboard to initialize
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();
                
                // Update stats
                updateStats(data);
                
                // Create charts
                createEquityChart(data);
                createMonthlyReturnsChart(data);
                createWinLossChart(data);
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });
        
        // Update statistics
        function updateStats(data) {
            // Get stats from backtest results
            const stats = data.backtestResults.statistics || {};
            
            // Update total profit
            const totalProfitElement = document.getElementById('total-profit');
            totalProfitElement.innerHTML = dashboardChartUtils.formatCurrency(stats.totalNetProfit || 0);
            totalProfitElement.classList.remove('loading');
            
            // Update win rate
            const winRateElement = document.getElementById('win-rate');
            winRateElement.innerHTML = dashboardChartUtils.formatPercentage(stats.winRate || 0);
            winRateElement.classList.remove('loading');
            
            // Update profit factor
            const profitFactorElement = document.getElementById('profit-factor');
            profitFactorElement.innerHTML = (stats.profitFactor || 0).toFixed(2);
            profitFactorElement.classList.remove('loading');
            
            // Update max drawdown
            const maxDrawdownElement = document.getElementById('max-drawdown');
            maxDrawdownElement.innerHTML = dashboardChartUtils.formatCurrency(stats.maxDrawdown || 0);
            maxDrawdownElement.classList.remove('loading');
        }
        
        // Create equity chart
        function createEquityChart(data) {
            const dailyPnL = data.dailyPnL || [];
            
            // Prepare data
            const labels = dailyPnL.map(day => day.date);
            const equityValues = [];
            
            let cumulativeEquity = 0;
            dailyPnL.forEach(day => {
                cumulativeEquity += (day.netProfit || 0);
                equityValues.push(cumulativeEquity);
            });
            
            // Create chart config
            const chartConfig = dashboardChartUtils.createLineChartConfig(
                labels,
                [{
                    label: 'Equity',
                    data: equityValues,
                    color: data.instrumentConfig.color
                }],
                {
                    plugins: {
                        title: {
                            display: true,
                            text: `${data.instrumentConfig.name} Equity Curve`,
                            color: DASHBOARD_CONFIG.theme.colors.textPrimary,
                            font: {
                                family: DASHBOARD_CONFIG.theme.fonts.heading,
                                size: 16
                            }
                        }
                    }
                }
            );
            
            // Create chart
            const ctx = document.getElementById('equity-chart').getContext('2d');
            new Chart(ctx, chartConfig);
        }
        
        // Create monthly returns chart
        function createMonthlyReturnsChart(data) {
            const dailyPnL = data.dailyPnL || [];
            
            // Group by month
            const monthlyReturns = {};
            dailyPnL.forEach(day => {
                const date = new Date(day.date);
                const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                
                if (!monthlyReturns[monthKey]) {
                    monthlyReturns[monthKey] = 0;
                }
                
                monthlyReturns[monthKey] += (day.netProfit || 0);
            });
            
            // Prepare data
            const labels = Object.keys(monthlyReturns);
            const returns = Object.values(monthlyReturns);
            
            // Create chart config
            const chartConfig = dashboardChartUtils.createBarChartConfig(
                labels,
                [{
                    label: 'Monthly Returns',
                    data: returns,
                    color: data.instrumentConfig.color
                }]
            );
            
            // Create chart
            const ctx = document.getElementById('monthly-returns-chart').getContext('2d');
            new Chart(ctx, chartConfig);
        }
        
        // Create win/loss distribution chart
        function createWinLossChart(data) {
            const trades = data.trades || [];
            
            // Count wins and losses
            const wins = trades.filter(trade => (trade.netProfit || 0) > 0).length;
            const losses = trades.filter(trade => (trade.netProfit || 0) <= 0).length;
            
            // Create chart config
            const chartConfig = dashboardChartUtils.createPieChartConfig(
                ['Winning Trades', 'Losing Trades'],
                [wins, losses],
                {
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            );
            
            // Create chart
            const ctx = document.getElementById('win-loss-chart').getContext('2d');
            new Chart(ctx, chartConfig);
        }
    </script>
</body>
</html>
