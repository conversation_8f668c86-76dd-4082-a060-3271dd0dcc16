/**
 * WebSocket Manager
 * 
 * This module provides optimized WebSocket connection management with
 * advanced reconnection strategies, connection pooling, and rate limiting.
 */

const WebSocket = require('ws');
const EventEmitter = require('events');
const fs = require('fs');
const path = require('path');
const logger = require('./data_logger');

// Constants
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const INITIAL_RECONNECT_DELAY = 1000; // 1 second
const MAX_RECONNECT_DELAY = 30000; // 30 seconds
const RECONNECT_DECAY = 1.5; // Exponential backoff factor
const MAX_RECONNECT_ATTEMPTS = 20;
const CONNECTION_TIMEOUT = 10000; // 10 seconds
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_MESSAGES_PER_WINDOW = 100;

/**
 * WebSocket Manager Class
 */
class WebSocketManager extends EventEmitter {
    /**
     * Constructor
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
        super();
        
        this.options = {
            name: options.name || 'default',
            url: options.url || '',
            protocols: options.protocols || [],
            autoReconnect: options.autoReconnect !== false,
            maxReconnectAttempts: options.maxReconnectAttempts || MAX_RECONNECT_ATTEMPTS,
            initialReconnectDelay: options.initialReconnectDelay || INITIAL_RECONNECT_DELAY,
            maxReconnectDelay: options.maxReconnectDelay || MAX_RECONNECT_DELAY,
            reconnectDecay: options.reconnectDecay || RECONNECT_DECAY,
            connectionTimeout: options.connectionTimeout || CONNECTION_TIMEOUT,
            heartbeatInterval: options.heartbeatInterval || HEARTBEAT_INTERVAL,
            debug: options.debug || false,
            rateLimitWindow: options.rateLimitWindow || RATE_LIMIT_WINDOW,
            maxMessagesPerWindow: options.maxMessagesPerWindow || MAX_MESSAGES_PER_WINDOW
        };
        
        // State
        this.ws = null;
        this.isConnecting = false;
        this.isReconnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.connectionTimeoutTimer = null;
        this.lastMessageTime = 0;
        this.messageQueue = [];
        this.messageHistory = [];
        this.messagesSentInWindow = 0;
        this.rateLimitTimer = null;
        this.rateLimitResetTime = Date.now() + this.options.rateLimitWindow;
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.reconnect = this.reconnect.bind(this);
        this.send = this.send.bind(this);
        this.startHeartbeat = this.startHeartbeat.bind(this);
        this.stopHeartbeat = this.stopHeartbeat.bind(this);
        this.resetRateLimit = this.resetRateLimit.bind(this);
        this.processQueue = this.processQueue.bind(this);
        
        // Start rate limit timer
        this.startRateLimitTimer();
        
        // Debug logging
        if (this.options.debug) {
            this.on('debug', (message) => {
                logger.logSystem(`[${this.options.name}] ${message}`, 'debug');
            });
        }
    }
    
    /**
     * Connect to WebSocket
     * @returns {Promise<boolean>} - Connection result
     */
    connect() {
        return new Promise((resolve) => {
            if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
                this.emit('debug', 'Already connected or connecting');
                resolve(true);
                return;
            }
            
            if (this.isConnecting) {
                this.emit('debug', 'Connection already in progress');
                resolve(false);
                return;
            }
            
            this.isConnecting = true;
            this.emit('connecting');
            this.emit('debug', `Connecting to ${this.options.url}`);
            
            try {
                // Set connection timeout
                this.connectionTimeoutTimer = setTimeout(() => {
                    if (this.isConnecting) {
                        this.emit('debug', 'Connection timeout');
                        this.isConnecting = false;
                        
                        if (this.ws) {
                            this.ws.terminate();
                            this.ws = null;
                        }
                        
                        this.emit('timeout');
                        
                        if (this.options.autoReconnect) {
                            this.reconnect();
                        }
                        
                        resolve(false);
                    }
                }, this.options.connectionTimeout);
                
                // Create WebSocket
                this.ws = new WebSocket(this.options.url, this.options.protocols);
                
                // Set up event handlers
                this.ws.on('open', () => {
                    clearTimeout(this.connectionTimeoutTimer);
                    this.isConnecting = false;
                    this.reconnectAttempts = 0;
                    this.emit('debug', 'Connection established');
                    this.emit('open');
                    
                    // Start heartbeat
                    this.startHeartbeat();
                    
                    // Process queued messages
                    this.processQueue();
                    
                    resolve(true);
                });
                
                this.ws.on('message', (data) => {
                    this.lastMessageTime = Date.now();
                    this.emit('message', data);
                });
                
                this.ws.on('close', (code, reason) => {
                    clearTimeout(this.connectionTimeoutTimer);
                    this.isConnecting = false;
                    this.stopHeartbeat();
                    
                    this.emit('debug', `Connection closed. Code: ${code}, Reason: ${reason || 'No reason provided'}`);
                    this.emit('close', code, reason);
                    
                    if (this.options.autoReconnect && !this.isReconnecting) {
                        this.reconnect();
                    }
                });
                
                this.ws.on('error', (error) => {
                    this.emit('debug', `WebSocket error: ${error.message}`);
                    this.emit('error', error);
                    
                    // Don't call reconnect here, it will be called by the close event
                });
                
                this.ws.on('ping', (data) => {
                    this.lastMessageTime = Date.now();
                    this.emit('debug', 'Received ping');
                    this.emit('ping', data);
                });
                
                this.ws.on('pong', (data) => {
                    this.lastMessageTime = Date.now();
                    this.emit('debug', 'Received pong');
                    this.emit('pong', data);
                });
            } catch (error) {
                clearTimeout(this.connectionTimeoutTimer);
                this.isConnecting = false;
                this.emit('debug', `Connection error: ${error.message}`);
                this.emit('error', error);
                
                if (this.options.autoReconnect) {
                    this.reconnect();
                }
                
                resolve(false);
            }
        });
    }
    
    /**
     * Disconnect from WebSocket
     * @param {number} code - Close code
     * @param {string} reason - Close reason
     */
    disconnect(code = 1000, reason = 'Normal closure') {
        this.emit('debug', `Disconnecting. Code: ${code}, Reason: ${reason}`);
        
        // Clear timers
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
        this.stopHeartbeat();
        
        // Reset state
        this.isConnecting = false;
        this.isReconnecting = false;
        this.reconnectAttempts = 0;
        
        // Close WebSocket
        if (this.ws) {
            try {
                if (this.ws.readyState === WebSocket.OPEN) {
                    this.ws.close(code, reason);
                } else {
                    this.ws.terminate();
                }
            } catch (error) {
                this.emit('debug', `Error closing WebSocket: ${error.message}`);
            }
            
            this.ws = null;
        }
        
        this.emit('disconnected');
    }
    
    /**
     * Reconnect to WebSocket
     */
    reconnect() {
        if (this.isReconnecting) {
            this.emit('debug', 'Reconnection already in progress');
            return;
        }
        
        this.isReconnecting = true;
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts > this.options.maxReconnectAttempts) {
            this.emit('debug', `Maximum reconnect attempts (${this.options.maxReconnectAttempts}) reached`);
            this.emit('reconnect_failed');
            this.isReconnecting = false;
            return;
        }
        
        // Calculate reconnect delay with exponential backoff
        const delay = Math.min(
            this.options.initialReconnectDelay * Math.pow(this.options.reconnectDecay, this.reconnectAttempts - 1),
            this.options.maxReconnectDelay
        );
        
        this.emit('debug', `Reconnecting in ${delay}ms (Attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
        this.emit('reconnecting', this.reconnectAttempts, delay);
        
        // Set reconnect timer
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = setTimeout(() => {
            this.emit('debug', `Attempting to reconnect (Attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
            this.isReconnecting = false;
            this.connect()
                .then((connected) => {
                    if (connected) {
                        this.emit('reconnected');
                    }
                })
                .catch((error) => {
                    this.emit('debug', `Reconnection error: ${error.message}`);
                });
        }, delay);
    }
    
    /**
     * Send data through WebSocket
     * @param {string|Buffer|ArrayBuffer|Buffer[]} data - Data to send
     * @param {Object} options - Send options
     * @param {boolean} options.queue - Whether to queue the message if not connected
     * @param {boolean} options.priority - Whether to prioritize the message in the queue
     * @returns {Promise<boolean>} - Send result
     */
    send(data, options = { queue: true, priority: false }) {
        return new Promise((resolve) => {
            // Check rate limit
            if (this.messagesSentInWindow >= this.options.maxMessagesPerWindow) {
                this.emit('debug', 'Rate limit exceeded, queueing message');
                
                if (options.queue) {
                    this.queueMessage(data, options.priority);
                    resolve(false);
                } else {
                    this.emit('rate_limit_exceeded');
                    resolve(false);
                }
                
                return;
            }
            
            // Check connection
            if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                this.emit('debug', 'Not connected, queueing message');
                
                if (options.queue) {
                    this.queueMessage(data, options.priority);
                    resolve(false);
                } else {
                    this.emit('not_connected');
                    resolve(false);
                }
                
                return;
            }
            
            try {
                // Send data
                this.ws.send(data, (error) => {
                    if (error) {
                        this.emit('debug', `Send error: ${error.message}`);
                        this.emit('send_error', error);
                        
                        if (options.queue) {
                            this.queueMessage(data, options.priority);
                        }
                        
                        resolve(false);
                    } else {
                        this.messagesSentInWindow++;
                        this.messageHistory.push({
                            timestamp: Date.now(),
                            data: typeof data === 'string' ? data : '[Binary data]'
                        });
                        
                        // Limit message history
                        if (this.messageHistory.length > 100) {
                            this.messageHistory.shift();
                        }
                        
                        this.emit('debug', 'Message sent successfully');
                        this.emit('sent', data);
                        resolve(true);
                    }
                });
            } catch (error) {
                this.emit('debug', `Send error: ${error.message}`);
                this.emit('send_error', error);
                
                if (options.queue) {
                    this.queueMessage(data, options.priority);
                }
                
                resolve(false);
            }
        });
    }
    
    /**
     * Queue a message
     * @param {string|Buffer|ArrayBuffer|Buffer[]} data - Data to queue
     * @param {boolean} priority - Whether to prioritize the message
     */
    queueMessage(data, priority = false) {
        const message = {
            data,
            timestamp: Date.now()
        };
        
        if (priority) {
            this.messageQueue.unshift(message);
        } else {
            this.messageQueue.push(message);
        }
        
        this.emit('debug', `Message queued (${this.messageQueue.length} in queue)`);
        this.emit('queued', data);
    }
    
    /**
     * Process queued messages
     */
    processQueue() {
        if (this.messageQueue.length === 0) {
            return;
        }
        
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            this.emit('debug', 'Cannot process queue, not connected');
            return;
        }
        
        if (this.messagesSentInWindow >= this.options.maxMessagesPerWindow) {
            this.emit('debug', 'Cannot process queue, rate limit exceeded');
            return;
        }
        
        this.emit('debug', `Processing message queue (${this.messageQueue.length} messages)`);
        
        // Process messages until rate limit is reached
        while (this.messageQueue.length > 0 && this.messagesSentInWindow < this.options.maxMessagesPerWindow) {
            const message = this.messageQueue.shift();
            this.send(message.data, { queue: true, priority: false })
                .then((sent) => {
                    if (!sent) {
                        this.emit('debug', 'Failed to send queued message');
                    }
                })
                .catch((error) => {
                    this.emit('debug', `Error sending queued message: ${error.message}`);
                });
        }
    }
    
    /**
     * Start heartbeat
     */
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.emit('debug', 'Starting heartbeat');
        
        this.heartbeatTimer = setInterval(() => {
            if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                this.stopHeartbeat();
                return;
            }
            
            // Check if we've received a message recently
            const now = Date.now();
            const elapsed = now - this.lastMessageTime;
            
            if (elapsed > this.options.heartbeatInterval * 2) {
                this.emit('debug', `No message received for ${elapsed}ms, reconnecting`);
                this.disconnect(1000, 'Heartbeat timeout');
                this.reconnect();
                return;
            }
            
            // Send ping
            try {
                this.ws.ping();
                this.emit('debug', 'Sent ping');
            } catch (error) {
                this.emit('debug', `Error sending ping: ${error.message}`);
            }
        }, this.options.heartbeatInterval);
    }
    
    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
            this.emit('debug', 'Stopped heartbeat');
        }
    }
    
    /**
     * Start rate limit timer
     */
    startRateLimitTimer() {
        this.rateLimitTimer = setInterval(() => {
            this.resetRateLimit();
        }, this.options.rateLimitWindow);
    }
    
    /**
     * Reset rate limit
     */
    resetRateLimit() {
        this.messagesSentInWindow = 0;
        this.rateLimitResetTime = Date.now() + this.options.rateLimitWindow;
        this.emit('debug', 'Rate limit reset');
        
        // Process queued messages
        this.processQueue();
    }
    
    /**
     * Get connection state
     * @returns {Object} - Connection state
     */
    getState() {
        return {
            connected: this.ws && this.ws.readyState === WebSocket.OPEN,
            connecting: this.isConnecting,
            reconnecting: this.isReconnecting,
            reconnectAttempts: this.reconnectAttempts,
            queueLength: this.messageQueue.length,
            messagesSentInWindow: this.messagesSentInWindow,
            rateLimitResetTime: this.rateLimitResetTime,
            lastMessageTime: this.lastMessageTime
        };
    }
}

module.exports = WebSocketManager;
