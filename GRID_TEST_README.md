# Grid Testing System

This system provides an optimized way to test trading strategies across multiple markets and parameter combinations. It's designed to be fast, flexible, and easy to use.

## Files

- `grid_backtest.js` - The core backtesting engine
- `grid_config.js` - Configuration for grid testing
- `run_grid_backtest.js` - <PERSON><PERSON>t to run grid tests on all configured markets
- `quick_grid_test.js` - <PERSON><PERSON><PERSON> to run a quick grid test on a single market

## Getting Started

1. Configure your grid test parameters in `grid_config.js`
2. Run a quick test on a single market to verify everything works
3. Run the full grid test across all markets

## Quick Test

To run a quick test on a single market:

```bash
node quick_grid_test.js MNQ --focused
```

This will run a grid test on MNQ using the focused grid parameters (fewer combinations for faster testing).

## Full Grid Test

To run a full grid test across all configured markets:

```bash
node run_grid_backtest.js
```

To use the focused grid parameters (faster):

```bash
node run_grid_backtest.js --focused
```

## Configuration

Edit `grid_config.js` to configure:

- Date range for testing
- Markets to test
- Grid parameters
- Market-specific configurations

### Grid Parameters

The grid test will test all combinations of these parameters:

- `slFactors` - Stop loss factors (multiplied by ATR)
- `tpFactors` - Take profit factors (multiplied by ATR)
- `trailFactors` - Trail stop factors (multiplied by ATR)
- `fixedTpPoints` - Fixed take profit points (overrides ATR-based TP if greater)

### Market Configurations

Each market has its own configuration:

- `symbol` - Market symbol
- `pointValue` - Point value for calculating P&L
- `commission` - Commission per contract
- `slippage` - Slippage in points
- `fixedContracts` - Number of contracts to trade
- Indicator parameters (RSI, WMA, ATR periods)
- Entry filters (minimum ATR, RSI-MA separation, WMA filter)

## Output

The grid test will generate:

- JSON files with detailed results
- HTML reports with visualizations
- Combined report for all markets

Results are saved in the `output/grid_test` directory by default.

## Adding New Markets

To add a new market:

1. Add the market configuration to `grid_config.js`
2. Add the data path to `grid_config.js`
3. Add the ATR thresholds and adaptive parameters if needed

Example:

```javascript
// Add to configs
M2K: {
    symbol: 'M2K',
    pointValue: 5.0,
    commission: 0.40,
    slippage: 0.0,
    fixedContracts: 10,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14,
    minAtrEntry: 0.5,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    pricePrecision: 2,
    initialBalance: 10000
},

// Add to dataPaths
M2K: 'C:\\backtest-bot\\input\\M2K_2020_2025.csv',

// Add to atrThresholds
M2K: { low_medium: 2.5, medium_high: 4.0 },

// Add to adaptiveParams
M2K: {
    Low: { slFactor: 4.0, tpFactor: 3.0, trailFactor: 0.05 },
    Medium: { slFactor: 4.0, tpFactor: 3.0, trailFactor: 0.05 },
    High: { slFactor: 4.0, tpFactor: 3.0, trailFactor: 0.05 }
}
```

## Customizing Grid Parameters

You can customize the grid parameters in `grid_config.js`:

```javascript
// Full grid (more combinations, slower)
gridParams: {
    slFactors: [3.0, 3.5, 4.0, 4.5, 5.0],
    tpFactors: [2.0, 2.5, 3.0, 3.5, 4.0],
    trailFactors: [0.05, 0.08, 0.11, 0.15, 0.2],
    fixedTpPoints: [0, 20, 40, 60]
},

// Focused grid (fewer combinations, faster)
focusedGridParams: {
    slFactors: [4.0, 4.5, 5.0],
    tpFactors: [2.5, 3.0, 3.5],
    trailFactors: [0.08, 0.11, 0.15],
    fixedTpPoints: [0, 40]
}
```

## Understanding Results

The grid test will rank parameter combinations by total P&L. The best combination will be at the top of the report.

Key metrics to look at:

- **Total PnL** - Total profit and loss
- **Win Rate** - Percentage of winning trades
- **Win Day Rate** - Percentage of profitable days
- **Profit Factor** - Gross profit divided by gross loss
- **Max Drawdown** - Maximum peak-to-trough decline

## Tips for Efficient Grid Testing

1. Start with a focused grid test on a single market
2. Use a shorter date range for initial testing
3. Once you find promising parameters, run a full test with a longer date range
4. Compare results across different markets to find robust parameters
5. Look for parameters that work well across multiple markets
