/**
 * multi_symbol_config.js
 * Configuration for multiple trading symbols (MNQ, MES, MGC)
 */

// Common configuration for all symbols
const commonConfig = {
  // --- General Settings ---
  initialBalance: 10000,

  // --- Costs & Slippage ---
  commissionPerContract: 0.40, // $0.40 per side commission estimate

  // --- Indicator Periods ---
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 200,
  wma50Period: 50,

  // --- Strategy Parameters ---
  useWmaFilter: true,    // Disable WMA filter (matches successful backtest)
  useSma200Filter: false, // Don't use SMA200 filter
  useRsiFilter: true,     // Use RSI filter
  useTwoBarColorExit: false,
  minAtrEntry: 0.5,
  minRsiMaSeparation: 1.0, // Filters Disabled

  // --- RSI Bands ---
  rsiUpperBand: 60,
  rsiLowerBand: 40,
  rsiMiddleBand: 50,

  // --- Run Mode ---
  isAdaptiveRun: true,

  // --- Position Sizing ---
  fixedContracts: 3, // Use 3 contracts for all symbols

  // --- Daily Loss Limit ---
  dailyLossLimitPercent: 0.05 // 5% of account balance
};

// MNQ Configuration - $3.3M in 5-year backtest
const mnqConfig = {
  ...commonConfig,
  symbol: 'MNQ',
  contractMonth: 'M5',
  inputFile: 'C:/backtest-bot/input/MNQ_2020_2025.csv',

  // --- Instrument Specifics ---
  pointValue: 2.00,      // MNQ point value
  tickSize: 0.25,        // MNQ tick size
  pricePrecision: 2,     // MNQ price precision

  // --- Costs & Slippage ---
  slippagePoints: 0.75,  // 3 ticks = $1.50 slippage cost estimate

  // --- Strategy Parameters ---
  fixedTpPoints: 40,     // Fixed TP points (crucial for MNQ profitability)

  // --- Optimized Parameters ---
  slFactors: 4.5,        // Optimized SL for MNQ
  tpFactors: 3.0,        // Optimized TP for MNQ
  trailFactors: 0.11,    // Optimized Trail for MNQ
  fixedContracts: 3,     // Using 3 contracts as requested

  // --- ATR Thresholds ---
  atrThresholds: {
    low_medium: 4.7601,  // Below this is considered low volatility
    medium_high: 7.2605  // Above this is considered high volatility
  },

  // --- Adaptive Parameters ---
  adaptiveParams: {
    Low: {
      slFactor: 4.5,
      tpFactor: 3.0,
      trailFactor: 0.11
    },
    Medium: {
      slFactor: 4.5,
      tpFactor: 3.0,
      trailFactor: 0.11
    },
    High: {
      slFactor: 4.5,
      tpFactor: 3.0,
      trailFactor: 0.11
    }
  }
};

// MES Configuration - $1M in 5-year backtest
const mesConfig = {
  ...commonConfig,
  symbol: 'MES',
  contractMonth: 'M5',
  inputFile: 'C:/backtest-bot/input/MES_2020-2025.csv',

  // --- Instrument Specifics ---
  pointValue: 5.00,      // MES point value
  tickSize: 0.25,        // MES tick size
  pricePrecision: 2,     // MES price precision

  // --- Costs & Slippage ---
  slippagePoints: 0.0,   // No slippage to match original backtest
  commissionPerContract: 0.25, // Lower commission for MES

  // --- Strategy Parameters ---
  fixedTpPoints: 40,     // Fixed TP points (matches successful backtest)

  // --- Optimized Parameters ---
  slFactors: 3.4,        // Original backtest value
  tpFactors: 2.0,        // Original backtest value
  trailFactors: 0.11,    // Original backtest value
  fixedContracts: 3,     // Using 3 contracts as requested

  // --- ATR Thresholds ---
  atrThresholds: {
    low_medium: 3.0,  // Below this is considered low volatility
    medium_high: 5.0  // Above this is considered high volatility
  },

  // --- Adaptive Parameters ---
  adaptiveParams: {
    Low: {
      slFactor: 3.4,
      tpFactor: 2.0,
      trailFactor: 0.11
    },
    Medium: {
      slFactor: 3.4,
      tpFactor: 2.0,
      trailFactor: 0.11
    },
    High: {
      slFactor: 3.4,
      tpFactor: 2.0,
      trailFactor: 0.11
    }
  }
};

// MGC Configuration - $890K in 5-year backtest
const mgcConfig = {
  ...commonConfig,
  symbol: 'MGC',
  contractMonth: 'M5',
  inputFile: 'C:/backtest-bot/input/MGC2020_2025.csv',

  // --- Instrument Specifics ---
  pointValue: 10.00,     // MGC point value
  tickSize: 0.10,        // MGC tick size
  pricePrecision: 1,     // MGC price precision (changed to 1 per optimized config)

  // --- Costs & Slippage ---
  slippagePoints: 0.10,  // 1 tick = $1.00 slippage cost estimate (changed to 0.10 per optimized config)

  // --- Strategy Parameters ---
  fixedTpPoints: 0,      // No fixed TP points for MGC (per optimized config)

  // --- Optimized Parameters ---
  slFactors: 8.0,        // Optimized SL for MGC (changed to 8.0 per optimized config)
  tpFactors: 7.0,        // Optimized TP for MGC
  trailFactors: 0.02,    // Optimized Trail for MGC
  fixedContracts: 3,     // Using 3 contracts as requested

  // --- ATR Thresholds ---
  atrThresholds: {
    low_medium: 1.5,  // Below this is considered low volatility
    medium_high: 3.0  // Above this is considered high volatility
  },

  // --- Adaptive Parameters ---
  adaptiveParams: {
    Low: {
      slFactor: 8.0,     // Updated to match optimized config
      tpFactor: 7.0,
      trailFactor: 0.02
    },
    Medium: {
      slFactor: 8.0,     // Updated to match optimized config
      tpFactor: 7.0,
      trailFactor: 0.02
    },
    High: {
      slFactor: 8.0,     // Updated to match optimized config
      tpFactor: 7.0,
      trailFactor: 0.02
    }
  }
};

// Function to get config by symbol
function getConfigBySymbol(symbol) {
  switch (symbol) {
    case 'MNQ':
      return mnqConfig;
    case 'MES':
      return mesConfig;
    case 'MGC':
      return mgcConfig;
    default:
      console.warn(`No specific config for symbol ${symbol}, using MNQ config`);
      return mnqConfig;
  }
}

// Export all configurations
module.exports = {
  commonConfig,
  mnqConfig,
  mesConfig,
  mgcConfig,
  getConfigBySymbol
};
