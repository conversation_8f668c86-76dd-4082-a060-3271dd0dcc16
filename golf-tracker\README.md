# 🏌️ South Florida Golf Course Tracker

A personal Progressive Web App (PWA) to track every golf course you play in South Florida.

## Features

- 📱 **Install on your phone** - Works like a native app
- 🏌️ **Track rounds** - Course name, location, score, date, notes
- 📊 **Statistics** - Total rounds, unique courses, average score, best score
- 💾 **Offline storage** - All data saved locally on your device
- 🎯 **Simple & fast** - Designed for quick round entry

## Quick Start

### 1. Create App Icons (One-time setup)
```bash
cd golf-tracker
node server.js
```

1. Open http://localhost:3001/create-icons.html in your browser
2. Click each icon to download:
   - Save first icon as `icon-192.png`
   - Save second icon as `icon-512.png`
3. Place both PNG files in the golf-tracker folder

### 2. Run the App
```bash
node server.js
```

### 3. Install on Your Phone

**For Android:**
1. Open http://localhost:3001 in Chrome on your phone
2. Tap the "Install App" banner that appears
3. Or tap the menu (⋮) → "Add to Home screen"

**For iPhone:**
1. Open http://localhost:3001 in Safari on your phone
2. Tap the Share button (□↑)
3. Tap "Add to Home Screen"
4. Tap "Add"

## Usage

### Adding a Round
1. Tap the "Add New Round" button
2. Fill in:
   - **Course Name** (required) - e.g., "Doral Golf Resort"
   - **Location** (required) - e.g., "Miami, FL"
   - **Date** (required) - defaults to today
   - **Score** (required) - your total score
   - **Par** (optional) - course par
   - **Tees** (optional) - which tees you played
   - **Notes** (optional) - weather, memorable shots, etc.
3. Tap "Save Round"

### Viewing Your Stats
The app automatically calculates:
- **Total Rounds** - How many times you've played
- **Unique Courses** - How many different courses
- **Average Score** - Your scoring average
- **Best Score** - Your lowest round

### Managing Rounds
- View all rounds in chronological order (newest first)
- Tap the 🗑️ button to delete a round
- All data is stored locally on your device

## Keyboard Shortcuts
- **Ctrl/Cmd + N** - Add new round
- **Escape** - Close modal

## Technical Details

- **No internet required** - Works completely offline
- **Local storage** - Data saved in your browser's localStorage
- **Progressive Web App** - Installable like a native app
- **Responsive design** - Works on phones, tablets, and desktop

## File Structure
```
golf-tracker/
├── index.html          # Main app interface
├── app.js             # App functionality
├── manifest.json      # PWA configuration
├── sw.js             # Service worker for offline functionality
├── server.js         # Local development server
├── create-icons.html # Icon generator
├── icon-192.png      # App icon (192x192)
├── icon-512.png      # App icon (512x512)
└── README.md         # This file
```

## Customization Ideas

Want to add more features? Here are some ideas:
- **Photos** - Add course photos to rounds
- **GPS tracking** - Auto-detect course location
- **Handicap calculation** - Track your handicap over time
- **Weather data** - Record weather conditions
- **Playing partners** - Track who you played with
- **Course ratings** - Rate courses 1-5 stars
- **Export data** - Export rounds to CSV
- **Course database** - Pre-populate South Florida courses

## Troubleshooting

**App won't install on phone:**
- Make sure you're using Chrome (Android) or Safari (iPhone)
- Try refreshing the page
- Check that both icon files exist

**Data disappeared:**
- Data is stored locally - clearing browser data will delete rounds
- Consider adding export functionality for backups

**App not working offline:**
- Make sure you visited the app while online first
- Service worker needs to cache files on first visit

## Development

To modify the app:
1. Edit the HTML, CSS, or JavaScript files
2. Restart the server: `node server.js`
3. Refresh your browser/app

The app uses vanilla JavaScript and HTML5 - no frameworks required!

---

**Enjoy tracking your golf rounds! 🏌️⛳**
