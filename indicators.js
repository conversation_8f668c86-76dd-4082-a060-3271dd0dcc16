/**
 * Technical indicators for trading strategies
 */

/**
 * Calculate ATR (Average True Range)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - ATR value
 */
function calculateATR(candles, period) {
    if (candles.length <= period + 1) {
        return 0;
    }

    // Simple ATR calculation
    let trSum = 0;

    for (let i = 1; i < period + 1; i++) {
        const high = candles[candles.length - i].high;
        const low = candles[candles.length - i].low;
        const prevClose = candles[candles.length - i - 1].close;

        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);

        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
    }

    return trSum / period;
}

/**
 * Calculate RSI (Relative Strength Index)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - RSI value
 */
function calculateRSI(candles, period) {
    if (candles.length <= period) {
        return 50;
    }

    // Calculate price changes
    const changes = [];
    for (let i = 1; i < candles.length; i++) {
        changes.push(candles[i].close - candles[i - 1].close);
    }

    // Calculate initial average gain and loss
    let avgGain = 0;
    let avgLoss = 0;

    for (let i = 0; i < period; i++) {
        const change = changes[i];
        if (change >= 0) {
            avgGain += change;
        } else {
            avgLoss += Math.abs(change);
        }
    }

    avgGain /= period;
    avgLoss /= period;

    // Calculate smoothed RSI using Wilder's smoothing method
    for (let i = period; i < changes.length; i++) {
        const change = changes[i];
        const gain = change >= 0 ? change : 0;
        const loss = change < 0 ? Math.abs(change) : 0;

        avgGain = ((avgGain * (period - 1)) + gain) / period;
        avgLoss = ((avgLoss * (period - 1)) + loss) / period;
    }

    if (avgLoss === 0) {
        return 100;
    }

    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
}

/**
 * Calculate SMA (Simple Moving Average)
 * @param {Array} values - Values to calculate SMA on
 * @param {number} period - Period
 * @returns {number} - SMA value
 */
function calculateSMA(values, period) {
    if (values.length < period) {
        return 0;
    }

    // If values is an array of numbers, calculate SMA directly
    if (typeof values[0] === 'number') {
        let sum = 0;
        for (let i = 0; i < period; i++) {
            sum += values[values.length - 1 - i];
        }
        return sum / period;
    }

    // If values is an array of candles, calculate SMA of close prices
    let sum = 0;
    for (let i = 0; i < period; i++) {
        sum += values[values.length - 1 - i].close;
    }
    return sum / period;
}

/**
 * Calculate WMA (Weighted Moving Average)
 * @param {Array} values - Values to calculate WMA on
 * @param {number} period - Period
 * @returns {number} - WMA value
 */
function calculateWMA(values, period) {
    if (values.length < period) {
        return 0;
    }

    let sum = 0;
    let weightSum = 0;

    // If values is an array of numbers, calculate WMA directly
    if (typeof values[0] === 'number') {
        for (let i = 0; i < period; i++) {
            const weight = period - i;
            const value = values[values.length - 1 - i];

            sum += value * weight;
            weightSum += weight;
        }
    }
    // If values is an array of candles, calculate WMA of close prices
    else {
        for (let i = 0; i < period; i++) {
            const weight = period - i;
            const price = values[values.length - 1 - i].close;

            sum += price * weight;
            weightSum += weight;
        }
    }

    return sum / weightSum;
}

/**
 * Calculate EMA (Exponential Moving Average)
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @returns {number} - EMA value
 */
function calculateEMA(candles, period) {
    if (candles.length < period) {
        return 0;
    }

    // Calculate SMA for the first period
    let sum = 0;
    for (let i = 0; i < period; i++) {
        sum += candles[i].close;
    }

    let ema = sum / period;

    // Calculate multiplier
    const multiplier = 2 / (period + 1);

    // Calculate EMA for the rest of the candles
    for (let i = period; i < candles.length; i++) {
        ema = (candles[i].close - ema) * multiplier + ema;
    }

    return ema;
}

/**
 * Calculate Bollinger Bands
 * @param {Array} candles - Candles
 * @param {number} period - Period
 * @param {number} stdDev - Standard deviation multiplier
 * @returns {Object} - Bollinger Bands values
 */
function calculateBollingerBands(candles, period, stdDev = 2) {
    if (candles.length < period) {
        return { upper: 0, middle: 0, lower: 0 };
    }

    // Calculate SMA
    const prices = candles.slice(candles.length - period).map(candle => candle.close);
    const sma = calculateSMA(prices, period);

    // Calculate standard deviation
    let sum = 0;
    for (let i = 0; i < prices.length; i++) {
        sum += Math.pow(prices[i] - sma, 2);
    }

    const std = Math.sqrt(sum / period);

    // Calculate bands
    const upper = sma + (std * stdDev);
    const lower = sma - (std * stdDev);

    return {
        upper,
        middle: sma,
        lower
    };
}

module.exports = {
    calculateATR,
    calculateRSI,
    calculateSMA,
    calculateWMA,
    calculateEMA,
    calculateBollingerBands
};
