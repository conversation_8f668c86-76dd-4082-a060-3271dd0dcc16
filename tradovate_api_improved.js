/**
 * Improved Tradovate API Module
 *
 * This module provides enhanced functions to interact with the Tradovate API
 * with improved error handling, retry logic, and WebSocket stability.
 */

const fetch = require('node-fetch');
const WebSocket = require('ws');
const axios = require('axios');
const rax = require('retry-axios');
const fs = require('fs');
const path = require('path');

// Configuration
const configApi = {
    baseUrl: 'https://demo.tradovateapi.com/v1',
    wsUrl: 'wss://demo.tradovateapi.com/v1/websocket',
    mdWsUrl: 'wss://md.tradovateapi.com/v1/websocket',
    name: 'bravesbeatmets',
    password: 'Braves12$',
    appId: 'Trading Bot',
    appVersion: '0.0.1',
    cid: '6186',
    sec: '69311dad-75a7-49c6-9958-00ab2c4f1ab6',
    deviceId: '25e3f568-2890-5442-1e40-b9e8983af7e7',
    accountId: '4690440' // Hardcoded for demo purposes
};

// Create axios instance with retry capability
const axiosInstance = axios.create({
    baseURL: configApi.baseUrl,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

// Add retry-axios interceptor
const interceptorId = rax.attach(axiosInstance);

// Authentication state
let accessToken = null;
let expirationTime = null;
let userId = null;
let isRefreshing = false;
let refreshPromise = null;

// WebSocket state
let ws = null;
let mdWs = null;
let wsHeartbeatInterval = null;
let mdWsHeartbeatInterval = null;
let wsReconnectTimeout = null;
let mdWsReconnectTimeout = null;
let wsConnecting = false;
let mdWsConnecting = false;
let wsAuthenticating = false;
let mdWsAuthenticating = false;
let wsReconnectAttempts = 0;
let mdWsReconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const RECONNECT_DELAY = 5000; // 5 seconds
const HEARTBEAT_INTERVAL = 30000; // 30 seconds

// Callback for WebSocket messages
let onWebSocketMessage = null;

// Token storage path
const TOKEN_STORAGE_PATH = path.join(__dirname, '.tradovate_token.json');

/**
 * Save authentication token to file
 * @param {Object} tokenData - Token data to save
 */
function saveTokenToFile(tokenData) {
    try {
        fs.writeFileSync(TOKEN_STORAGE_PATH, JSON.stringify(tokenData, null, 2));
        console.log('Token saved to file');
    } catch (error) {
        console.error('Error saving token to file:', error.message);
    }
}

/**
 * Load authentication token from file
 * @returns {Object|null} - Token data or null if not found
 */
function loadTokenFromFile() {
    try {
        if (fs.existsSync(TOKEN_STORAGE_PATH)) {
            const data = fs.readFileSync(TOKEN_STORAGE_PATH, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Error loading token from file:', error.message);
    }
    return null;
}

/**
 * Authenticate with Tradovate API
 * @returns {Promise<Object>} - Authentication result
 */
async function authenticate() {
    try {
        // Check if we have a valid token in memory
        if (accessToken && expirationTime && new Date() < expirationTime) {
            console.log('Using existing token');
            return {
                success: true,
                accessToken,
                userId,
                expirationTime
            };
        }

        // Check if we have a valid token in storage
        const storedToken = loadTokenFromFile();
        if (storedToken && storedToken.expirationTime && new Date() < new Date(storedToken.expirationTime)) {
            console.log('Using stored token');
            accessToken = storedToken.accessToken;
            expirationTime = new Date(storedToken.expirationTime);
            userId = storedToken.userId;
            return {
                success: true,
                accessToken,
                userId,
                expirationTime
            };
        }

        // Prepare authentication payload
        const payload = {
            name: configApi.name,
            password: configApi.password,
            appId: configApi.appId,
            appVersion: configApi.appVersion,
            deviceId: configApi.deviceId,
            cid: configApi.cid,
            sec: configApi.sec
        };

        console.log('Attempting to authenticate...');
        console.log('Authentication payload:', payload);

        // Send authentication request
        const response = await axiosInstance.post('/auth/accesstokenrequest', payload);

        if (response.data && response.data.accessToken) {
            accessToken = response.data.accessToken;
            userId = response.data.userId;

            // Calculate expiration time (24 hours from now)
            const now = new Date();
            expirationTime = new Date(now.getTime() + 24 * 60 * 60 * 1000);

            console.log(`Authentication successful! Token expires at: ${expirationTime.toLocaleString()}`);
            console.log(`User ID: ${userId}`);

            // Save token to file
            saveTokenToFile({
                accessToken,
                userId,
                expirationTime
            });

            return {
                success: true,
                accessToken,
                userId,
                expirationTime
            };
        } else {
            throw new Error('Authentication response did not contain an access token');
        }
    } catch (error) {
        console.error('Authentication failed:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Ensure we have a valid token
 * @returns {Promise<boolean>} - True if token is valid
 */
async function ensureValidToken() {
    // If token is valid, return immediately
    if (accessToken && expirationTime && new Date() < expirationTime) {
        return true;
    }

    // If already refreshing, wait for that to complete
    if (isRefreshing) {
        return refreshPromise;
    }

    // Start refreshing
    isRefreshing = true;
    refreshPromise = new Promise(async (resolve) => {
        try {
            const result = await authenticate();
            isRefreshing = false;
            resolve(result.success);
        } catch (error) {
            console.error('Failed to refresh token:', error.message);
            isRefreshing = false;
            resolve(false);
        }
    });

    return refreshPromise;
}

/**
 * Connect to the Tradovate WebSocket
 * @returns {Promise<Object>} - Connection result
 */
async function connectWebSocket() {
    return new Promise((resolve) => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            console.log('WebSocket already connected');
            resolve({ success: true, id: 'main' });
            return;
        }

        if (wsConnecting) {
            console.log('WebSocket already connecting');
            resolve({ success: false, error: 'Already connecting' });
            return;
        }

        wsConnecting = true;
        wsReconnectAttempts = 0;

        console.log(`Connecting to main API WebSocket: ${configApi.wsUrl}`);

        try {
            ws = new WebSocket(configApi.wsUrl);

            ws.on('open', () => {
                console.log('WebSocket connection established.');
                wsConnecting = false;

                // Authenticate the WebSocket connection
                authenticateWebSocket()
                    .then(() => {
                        // Start heartbeat
                        startWebSocketHeartbeat();

                        // Send initial subscriptions
                        sendInitialSubscriptions();

                        resolve({ success: true, id: 'main' });
                    })
                    .catch(error => {
                        console.error('WebSocket authentication failed:', error.message);
                        resolve({ success: false, error: error.message });
                    });
            });

            ws.on('message', (data) => {
                try {
                    console.log('Raw WebSocket message:', data.toString());

                    // Parse the message
                    if (data.toString() === 'o') {
                        console.log('Received WebSocket open frame');
                        return;
                    }

                    if (data.toString().startsWith('a')) {
                        const jsonStr = data.toString().substring(1);
                        const payload = JSON.parse(jsonStr);
                        console.log('Parsed WebSocket payload:', payload);

                        // Check for authentication error
                        if (payload[0] && payload[0].s === 401) {
                            console.log('WebSocket authorization failed:', payload[0]);
                            wsAuthenticating = false;

                            // Re-authenticate with the REST API and reconnect
                            console.log('Attempting to re-authenticate with the REST API...');
                            authenticate()
                                .then(result => {
                                    if (result.success) {
                                        console.log('Re-authentication successful, reconnecting WebSocket...');
                                        reconnectWebSocket();
                                    } else {
                                        console.error('Re-authentication failed');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error re-authenticating:', error.message);
                                });

                            return;
                        }

                        // Process the message
                        if (onWebSocketMessage) {
                            onWebSocketMessage(payload[0]);
                        }
                    }
                } catch (error) {
                    console.error('Error processing WebSocket message:', error.message);
                }
            });

            ws.on('close', (code, reason) => {
                console.log(`WebSocket connection closed. Code: ${code}, Reason: ${reason}`);
                wsConnecting = false;
                wsAuthenticating = false;

                // Stop heartbeat
                stopWebSocketHeartbeat();

                // Attempt to reconnect
                if (wsReconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    console.log(`Attempting to reconnect in ${RECONNECT_DELAY / 1000} seconds...`);
                    wsReconnectTimeout = setTimeout(() => {
                        reconnectWebSocket();
                    }, RECONNECT_DELAY);
                } else {
                    console.error(`Maximum reconnect attempts (${MAX_RECONNECT_ATTEMPTS}) reached`);
                }
            });

            ws.on('error', (error) => {
                console.error('WebSocket Error:', error.message);
                wsConnecting = false;

                // Attempt to reconnect after error
                if (wsReconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    console.log(`Attempting to reconnect after error in ${RECONNECT_DELAY / 1000} seconds...`);
                    wsReconnectTimeout = setTimeout(() => {
                        reconnectWebSocket();
                    }, RECONNECT_DELAY);
                } else {
                    console.error(`Maximum reconnect attempts (${MAX_RECONNECT_ATTEMPTS}) reached`);
                    resolve({ success: false, error: error.message });
                }
            });
        } catch (error) {
            console.error('Error creating WebSocket:', error.message);
            wsConnecting = false;
            resolve({ success: false, error: error.message });
        }
    });
}

/**
 * Authenticate the WebSocket connection
 * @returns {Promise<void>}
 */
async function authenticateWebSocket() {
    return new Promise((resolve, reject) => {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket not open'));
            return;
        }

        if (wsAuthenticating) {
            reject(new Error('Already authenticating'));
            return;
        }

        wsAuthenticating = true;

        // Ensure we have a valid token
        ensureValidToken()
            .then(valid => {
                if (!valid) {
                    wsAuthenticating = false;
                    reject(new Error('Failed to get valid token'));
                    return;
                }

                console.log('Authenticating main API WebSocket connection...');

                // Send authentication message
                const authMessage = {
                    url: 'authorize',
                    body: {
                        accessToken: accessToken
                    }
                };

                console.log('Sending main API WebSocket authentication message...');
                ws.send(JSON.stringify(authMessage));

                // For simplicity, we'll assume authentication is successful
                // In a production environment, you would wait for a response
                wsAuthenticating = false;
                resolve();
            })
            .catch(error => {
                wsAuthenticating = false;
                reject(error);
            });
    });
}

/**
 * Start WebSocket heartbeat
 */
function startWebSocketHeartbeat() {
    if (wsHeartbeatInterval) {
        clearInterval(wsHeartbeatInterval);
    }

    console.log('Starting WebSocket heartbeat...');
    wsHeartbeatInterval = setInterval(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.ping();
        }
    }, HEARTBEAT_INTERVAL);
}

/**
 * Stop WebSocket heartbeat
 */
function stopWebSocketHeartbeat() {
    if (wsHeartbeatInterval) {
        console.log('Stopping WebSocket heartbeat.');
        clearInterval(wsHeartbeatInterval);
        wsHeartbeatInterval = null;
    }
}

/**
 * Reconnect WebSocket
 */
function reconnectWebSocket() {
    wsReconnectAttempts++;
    console.log(`Reconnecting to WebSocket... (Attempt ${wsReconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

    if (wsReconnectTimeout) {
        clearTimeout(wsReconnectTimeout);
        wsReconnectTimeout = null;
    }

    connectWebSocket()
        .then(result => {
            if (!result.success) {
                console.error(`Failed to reconnect to WebSocket: ${result.error}`);
            }
        })
        .catch(error => {
            console.error(`Failed to reconnect to WebSocket: ${error.message}`);
        });
}

/**
 * Send initial subscriptions after connecting
 */
async function sendInitialSubscriptions() {
    console.log('Sending initial subscriptions...');

    // Request user sync (includes account details)
    console.log('Requesting user sync (includes account details)...');
    await sendWebSocketMessage({
        url: 'user/syncrequest',
        params: {
            accounts: [configApi.accountId],
            subscriptionPlans: []
        }
    });

    // Subscribe to account changes
    console.log('Subscribing to account changes...');
    await sendWebSocketMessage({
        url: 'account/subscribe',
        params: {
            id: configApi.accountId
        }
    });

    // Subscribe to position changes
    console.log('Subscribing to position changes...');
    await sendWebSocketMessage({
        url: 'position/subscribe',
        params: {
            accountId: configApi.accountId
        }
    });

    // Subscribe to order changes
    console.log('Subscribing to order changes...');
    await sendWebSocketMessage({
        url: 'order/subscribe',
        params: {
            accountId: configApi.accountId
        }
    });

    // Subscribe to fill changes
    console.log('Subscribing to fill changes...');
    await sendWebSocketMessage({
        url: 'fill/subscribe',
        params: {
            accountId: configApi.accountId
        }
    });
}

/**
 * Send a message through the WebSocket
 * @param {Object} message - Message to send
 * @returns {Promise<boolean>} - True if message was sent
 */
async function sendWebSocketMessage(message) {
    return new Promise((resolve) => {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            console.error('WebSocket not open, cannot send message.');
            resolve(false);
            return;
        }

        try {
            ws.send(JSON.stringify(message));
            resolve(true);
        } catch (error) {
            console.error('Error sending WebSocket message:', error.message);
            resolve(false);
        }
    });
}

/**
 * Connect to the Market Data WebSocket
 * @returns {Promise<Object>} - Connection result
 */
async function connectMarketDataWebSocket() {
    return new Promise((resolve) => {
        if (mdWs && mdWs.readyState === WebSocket.OPEN) {
            console.log('Market Data WebSocket already connected');
            resolve({ success: true, id: 'md' });
            return;
        }

        if (mdWsConnecting) {
            console.log('Market Data WebSocket already connecting');
            resolve({ success: false, error: 'Already connecting' });
            return;
        }

        mdWsConnecting = true;
        mdWsReconnectAttempts = 0;

        console.log(`Connecting to Market Data WebSocket: ${configApi.mdWsUrl}`);

        try {
            mdWs = new WebSocket(configApi.mdWsUrl);

            mdWs.on('open', () => {
                console.log('Market Data WebSocket connection established.');
                mdWsConnecting = false;

                // Authenticate the WebSocket connection
                authenticateMarketDataWebSocket()
                    .then(() => {
                        // Start heartbeat
                        startMarketDataWebSocketHeartbeat();

                        resolve({ success: true, id: 'md' });
                    })
                    .catch(error => {
                        console.error('Market Data WebSocket authentication failed:', error.message);
                        resolve({ success: false, error: error.message });
                    });
            });

            mdWs.on('message', (data) => {
                try {
                    console.log('Raw Market Data WebSocket message:', data.toString().substring(0, 100) + '...');

                    // Parse the message
                    if (data.toString() === 'o') {
                        console.log('Received Market Data WebSocket open frame');
                        return;
                    }

                    if (data.toString().startsWith('a')) {
                        const jsonStr = data.toString().substring(1);
                        const payload = JSON.parse(jsonStr);
                        console.log('Parsed Market Data WebSocket payload:', JSON.stringify(payload).substring(0, 100) + '...');

                        // Process the message
                        handleMarketDataMessage(payload);
                    }
                } catch (error) {
                    console.error('Error processing Market Data WebSocket message:', error.message);
                }
            });

            mdWs.on('close', (code, reason) => {
                console.log(`Market Data WebSocket connection closed. Code: ${code}, Reason: ${reason}`);
                mdWsConnecting = false;
                mdWsAuthenticating = false;

                // Stop heartbeat
                stopMarketDataWebSocketHeartbeat();

                // Attempt to reconnect
                if (mdWsReconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    console.log(`Attempting to reconnect in ${RECONNECT_DELAY / 1000} seconds...`);
                    mdWsReconnectTimeout = setTimeout(() => {
                        reconnectMarketDataWebSocket();
                    }, RECONNECT_DELAY);
                } else {
                    console.error(`Maximum reconnect attempts (${MAX_RECONNECT_ATTEMPTS}) reached`);
                }
            });

            mdWs.on('error', (error) => {
                console.error('Market Data WebSocket Error:', error.message);
                mdWsConnecting = false;

                // Attempt to reconnect after error
                if (mdWsReconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    console.log(`Attempting to reconnect after error in ${RECONNECT_DELAY / 1000} seconds...`);
                    mdWsReconnectTimeout = setTimeout(() => {
                        reconnectMarketDataWebSocket();
                    }, RECONNECT_DELAY);
                } else {
                    console.error(`Maximum reconnect attempts (${MAX_RECONNECT_ATTEMPTS}) reached`);
                    resolve({ success: false, error: error.message });
                }
            });
        } catch (error) {
            console.error('Error creating Market Data WebSocket:', error.message);
            mdWsConnecting = false;
            resolve({ success: false, error: error.message });
        }
    });
}

/**
 * Authenticate the Market Data WebSocket connection
 * @returns {Promise<void>}
 */
async function authenticateMarketDataWebSocket() {
    return new Promise((resolve, reject) => {
        if (!mdWs || mdWs.readyState !== WebSocket.OPEN) {
            reject(new Error('Market Data WebSocket not open'));
            return;
        }

        if (mdWsAuthenticating) {
            reject(new Error('Already authenticating'));
            return;
        }

        mdWsAuthenticating = true;

        // Ensure we have a valid token
        ensureValidToken()
            .then(valid => {
                if (!valid) {
                    mdWsAuthenticating = false;
                    reject(new Error('Failed to get valid token'));
                    return;
                }

                console.log('Authenticating Market Data WebSocket connection...');

                // Send authentication message
                const authMsg = `md/authenticate\n0\n\n{"token":"${accessToken}"}`;

                console.log('Sending Market Data WebSocket authentication message...');
                mdWs.send(authMsg);

                // For simplicity, we'll assume authentication is successful
                // In a production environment, you would wait for a response
                mdWsAuthenticating = false;
                resolve();
            })
            .catch(error => {
                mdWsAuthenticating = false;
                reject(error);
            });
    });
}

/**
 * Start Market Data WebSocket heartbeat
 */
function startMarketDataWebSocketHeartbeat() {
    if (mdWsHeartbeatInterval) {
        clearInterval(mdWsHeartbeatInterval);
    }

    console.log('Starting Market Data WebSocket heartbeat...');
    mdWsHeartbeatInterval = setInterval(() => {
        if (mdWs && mdWs.readyState === WebSocket.OPEN) {
            mdWs.ping();
        }
    }, HEARTBEAT_INTERVAL);
}

/**
 * Stop Market Data WebSocket heartbeat
 */
function stopMarketDataWebSocketHeartbeat() {
    if (mdWsHeartbeatInterval) {
        console.log('Stopping Market Data WebSocket heartbeat.');
        clearInterval(mdWsHeartbeatInterval);
        mdWsHeartbeatInterval = null;
    }
}

/**
 * Reconnect Market Data WebSocket
 */
function reconnectMarketDataWebSocket() {
    mdWsReconnectAttempts++;
    console.log(`Reconnecting to Market Data WebSocket... (Attempt ${mdWsReconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

    if (mdWsReconnectTimeout) {
        clearTimeout(mdWsReconnectTimeout);
        mdWsReconnectTimeout = null;
    }

    connectMarketDataWebSocket()
        .then(result => {
            if (!result.success) {
                console.error(`Failed to reconnect to Market Data WebSocket: ${result.error}`);
            }
        })
        .catch(error => {
            console.error(`Failed to reconnect to Market Data WebSocket: ${error.message}`);
        });
}

/**
 * Handle Market Data WebSocket messages
 * @param {Array} payload - Message payload
 */
function handleMarketDataMessage(payload) {
    // Process each message in the payload
    payload.forEach(item => {
        if (item.e === 'chart') {
            // Handle chart data
            console.log('Received chart data:', JSON.stringify(item.d).substring(0, 100) + '...');

            // Process chart data
            processChartData(item.d);
        } else if (item.e === 'md') {
            // Handle market data
            console.log('Received market data:', JSON.stringify(item.d).substring(0, 100) + '...');
        } else {
            console.log(`Received unknown market data event type: ${item.e}`);
        }
    });
}

/**
 * Process chart data
 * @param {Object} chartData - Chart data
 */
function processChartData(chartData) {
    // This function would process chart data and pass it to the trading logic
    // For now, we'll just log it
    console.log(`Processing chart data for ${chartData.id}`);

    // Extract symbol from chart ID
    const symbolMatch = chartData.id.match(/^([A-Z0-9]+)-/);
    if (symbolMatch && symbolMatch[1]) {
        const symbol = symbolMatch[1];
        console.log(`Extracted symbol: ${symbol}`);

        // Process candles
        if (chartData.candles && chartData.candles.length > 0) {
            console.log(`Received ${chartData.candles.length} candles`);

            // Process the most recent candle
            const latestCandle = chartData.candles[chartData.candles.length - 1];
            console.log(`Latest candle: ${JSON.stringify(latestCandle)}`);

            // Format candle for trading logic
            const formattedCandle = {
                timestamp: new Date(latestCandle.timestamp).getTime(),
                open: latestCandle.open,
                high: latestCandle.high,
                low: latestCandle.low,
                close: latestCandle.close,
                volume: latestCandle.volume || 0
            };

            console.log(`Formatted candle: ${JSON.stringify(formattedCandle)}`);

            // Here you would pass the candle to your trading logic
        }
    }
}

/**
 * Subscribe to chart data for a symbol
 * @param {string} symbol - Symbol to subscribe to
 * @param {string} timeframe - Timeframe (e.g., '1m', '5m', '1h')
 * @returns {Promise<boolean>} - True if subscription was successful
 */
async function subscribeToChart(symbol, timeframe = '1m') {
    if (!mdWs || mdWs.readyState !== WebSocket.OPEN) {
        console.error('Market Data WebSocket not open, cannot subscribe to chart.');
        return false;
    }

    try {
        console.log(`Subscribing to chart data for ${symbol} (${timeframe})...`);

        // Convert timeframe to Tradovate format
        let chartDescription;
        switch (timeframe) {
            case '1m':
                chartDescription = 'Minute';
                break;
            case '5m':
                chartDescription = 'Minute';
                break;
            case '1h':
                chartDescription = 'Hour';
                break;
            default:
                chartDescription = 'Minute';
        }

        // Create subscription request
        const subscribeMsg = `md/subscribechart\n1\n\n{"symbol":"${symbol}","chartDescription":"${chartDescription}","timeframe":${timeframe === '1m' ? 1 : timeframe === '5m' ? 5 : 60}}`;

        console.log(`Sending chart subscription message: ${subscribeMsg}`);
        mdWs.send(subscribeMsg);

        return true;
    } catch (error) {
        console.error(`Error subscribing to chart for ${symbol}:`, error.message);
        return false;
    }
}

/**
 * Place an order
 * @param {Object} orderParams - Order parameters
 * @returns {Promise<Object>} - Order result
 */
async function placeOrder(orderParams) {
    try {
        // Ensure we have a valid token
        const tokenValid = await ensureValidToken();
        if (!tokenValid) {
            throw new Error('Failed to get valid token');
        }

        // Add account ID if not provided
        if (!orderParams.accountId) {
            orderParams.accountId = configApi.accountId;
        }

        // Add isAutomated flag for exchange compliance
        orderParams.isAutomated = true;

        console.log(`Placing order: ${JSON.stringify(orderParams, null, 2)}`);

        // Send the request
        const response = await axiosInstance.post('/order/placeorder', orderParams, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log(`Order placed successfully: ${JSON.stringify(response.data, null, 2)}`);

        return {
            success: true,
            orderId: response.data.orderId || response.data.id,
            orderData: response.data
        };
    } catch (error) {
        console.error('Error placing order:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Find a contract by symbol
 * @param {string} symbol - Symbol to find
 * @returns {Promise<Object|null>} - Contract details or null if not found
 */
async function findContract(symbol) {
    try {
        // Ensure we have a valid token
        const tokenValid = await ensureValidToken();
        if (!tokenValid) {
            throw new Error('Failed to get valid token');
        }

        console.log(`Finding contract for symbol: ${symbol}`);

        // Send the request
        const response = await axiosInstance.get(`/contract/find?name=${encodeURIComponent(symbol)}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log(`Contract found: ${JSON.stringify(response.data, null, 2)}`);

        return response.data;
    } catch (error) {
        console.error(`Error finding contract for ${symbol}:`, error.message);
        return null;
    }
}

/**
 * Disconnect WebSockets
 */
function disconnectWebSockets() {
    console.log('Disconnecting WebSockets...');

    // Stop heartbeats
    stopWebSocketHeartbeat();
    stopMarketDataWebSocketHeartbeat();

    // Clear reconnect timeouts
    if (wsReconnectTimeout) {
        clearTimeout(wsReconnectTimeout);
        wsReconnectTimeout = null;
    }

    if (mdWsReconnectTimeout) {
        clearTimeout(mdWsReconnectTimeout);
        mdWsReconnectTimeout = null;
    }

    // Close WebSockets
    if (ws) {
        console.log('Closing main WebSocket...');
        try {
            ws.close();
        } catch (error) {
            console.error('Error closing main WebSocket:', error.message);
        }
        ws = null;
    }

    if (mdWs) {
        console.log('Closing Market Data WebSocket...');
        try {
            mdWs.close();
        } catch (error) {
            console.error('Error closing Market Data WebSocket:', error.message);
        }
        mdWs = null;
    }

    // Reset connection flags
    wsConnecting = false;
    mdWsConnecting = false;
    wsAuthenticating = false;
    mdWsAuthenticating = false;

    console.log('WebSockets disconnected.');
}

// Export functions
module.exports = {
    authenticate,
    ensureValidToken,
    connectWebSocket,
    connectMarketDataWebSocket,
    sendWebSocketMessage,
    subscribeToChart,
    placeOrder,
    findContract,
    disconnectWebSockets,
    // Set callback for WebSocket messages
    set onWebSocketMessage(callback) {
        onWebSocketMessage = callback;
    }
};
