/**
 * Verify M2K Backtest - Quarterly Analysis
 * Run a series of backtests on M2K for each quarter of 2024-2025
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');
const m2kConfig = require('./m2k_config');

// Set symbol and data path
const symbol = 'M2K';
const dataPath = 'C:\\backtest-bot\\input\\MiniRussell2000_2020_2025.csv';

// Set output directory
const outputDir = './output/verify_m2k_quarterly';
m2kConfig.outputDir = outputDir;

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Extract parameters from m2k_config.js
const params = {
    slFactors: m2kConfig.slFactors,
    tpFactors: m2kConfig.tpFactors,
    trailFactors: m2kConfig.trailFactors,
    fixedTpPoints: m2kConfig.fixedTpPoints
};

// Define quarters to test
const quarters = [
    { name: 'Q1_2024', startDate: '2024-01-01', endDate: '2024-03-31' },
    { name: 'Q2_2024', startDate: '2024-04-01', endDate: '2024-06-30' },
    { name: 'Q3_2024', startDate: '2024-07-01', endDate: '2024-09-30' },
    { name: 'Q4_2024', startDate: '2024-10-01', endDate: '2024-12-31' },
    { name: 'Q1_2025', startDate: '2025-01-01', endDate: '2025-03-31' },
    { name: 'Q2_2025', startDate: '2025-04-01', endDate: '2025-06-30' }
];

// Store results for each quarter
const quarterlyResults = {};

/**
 * Run verification test for M2K for a specific quarter
 * @param {Object} quarter - Quarter information
 * @returns {Promise<Object>} - Backtest results
 */
async function runQuarterlyVerification(quarter) {
    console.log(`\n===== VERIFYING M2K BACKTEST FOR ${quarter.name} =====`);
    console.log(`Date range: ${quarter.startDate} to ${quarter.endDate}`);
    console.log(`Using parameters from m2k_config.js: ${JSON.stringify(params, null, 2)}`);
    
    // Create backtest instance with configuration
    const config = { ...m2kConfig };
    config.outputDir = path.join(outputDir, quarter.name);
    
    // Ensure quarter output directory exists
    if (!fs.existsSync(config.outputDir)) {
        fs.mkdirSync(config.outputDir, { recursive: true });
    }
    
    const backtest = new GridBacktest(config);
    
    // Load historical data with options
    console.log(`Loading data from ${dataPath}...`);
    await backtest.loadData(dataPath, { 
        dateRange: {
            useCustomRange: true,
            startDate: quarter.startDate,
            endDate: quarter.endDate
        }
    });
    
    // Run backtest with parameters
    console.log(`Running backtest with parameters...`);
    const result = backtest.runBacktest(params);
    
    if (!result.success) {
        console.error(`Error running backtest for ${quarter.name}: ${result.error}`);
        return null;
    }
    
    // Save results to file
    const resultsPath = path.join(config.outputDir, 'verification_results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(result, null, 2));
    
    // Generate HTML report
    generateQuarterlyHtmlReport(result, config.outputDir, quarter);
    
    // Print summary
    console.log(`\n===== ${quarter.name} VERIFICATION RESULTS =====`);
    console.log(`Total PnL: $${result.stats.totalPnl.toFixed(2)}`);
    console.log(`Return: ${result.stats.totalReturn.toFixed(2)}%`);
    console.log(`Win Rate: ${result.stats.winRate.toFixed(2)}%`);
    console.log(`Win Day Rate: ${result.stats.winDayRate.toFixed(2)}%`);
    console.log(`Profit Factor: ${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}`);
    console.log(`Max Drawdown: ${result.stats.maxDrawdown.toFixed(2)}%`);
    console.log(`Total Trades: ${result.stats.totalTrades}`);
    console.log(`\nResults saved to ${resultsPath}`);
    
    return result;
}

/**
 * Generate HTML report for quarterly verification results
 * @param {Object} result - Backtest result
 * @param {string} outputDir - Output directory
 * @param {Object} quarter - Quarter information
 */
function generateQuarterlyHtmlReport(result, outputDir, quarter) {
    const reportPath = path.join(outputDir, 'verification_report.html');
    
    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M2K ${quarter.name} Verification Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
        .chart {
            margin-top: 30px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>M2K ${quarter.name} Verification Report</h1>
        <p>Date range: ${quarter.startDate} to ${quarter.endDate}</p>
        <p>Parameters: SL=${params.slFactors}, TP=${params.tpFactors}, Trail=${params.trailFactors}, Fixed TP=${params.fixedTpPoints}</p>
        
        <h2>Performance Summary</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Initial Balance</td>
                <td>$${result.stats.initialBalance.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Final Balance</td>
                <td>$${result.stats.finalBalance.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Total PnL</td>
                <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Return</td>
                <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Trades</td>
                <td>${result.stats.totalTrades}</td>
            </tr>
            <tr>
                <td>Winning Trades</td>
                <td>${result.stats.wins}</td>
            </tr>
            <tr>
                <td>Losing Trades</td>
                <td>${result.stats.losses}</td>
            </tr>
            <tr>
                <td>Win Rate</td>
                <td>${result.stats.winRate.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Profit Factor</td>
                <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
            </tr>
            <tr>
                <td>Average Win</td>
                <td class="positive">$${result.stats.avgWin.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Average Loss</td>
                <td class="negative">$${result.stats.avgLoss.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Max Drawdown</td>
                <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Days</td>
                <td>${result.stats.totalDays}</td>
            </tr>
            <tr>
                <td>Profitable Days</td>
                <td>${result.stats.profitDays}</td>
            </tr>
            <tr>
                <td>Losing Days</td>
                <td>${result.stats.lossDays}</td>
            </tr>
            <tr>
                <td>Win Day Rate</td>
                <td>${result.stats.winDayRate.toFixed(2)}%</td>
            </tr>
        </table>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(reportPath, html);
    console.log(`HTML report generated at ${reportPath}`);
}

/**
 * Generate combined HTML report for all quarters
 * @param {Object} quarterlyResults - Results for each quarter
 */
function generateCombinedReport(quarterlyResults) {
    const reportPath = path.join(outputDir, 'combined_report.html');
    
    // Calculate combined statistics
    const combinedStats = {
        totalPnl: 0,
        totalTrades: 0,
        wins: 0,
        losses: 0,
        profitDays: 0,
        lossDays: 0,
        totalDays: 0
    };
    
    for (const quarter in quarterlyResults) {
        const result = quarterlyResults[quarter];
        if (result && result.stats) {
            combinedStats.totalPnl += result.stats.totalPnl;
            combinedStats.totalTrades += result.stats.totalTrades;
            combinedStats.wins += result.stats.wins;
            combinedStats.losses += result.stats.losses;
            combinedStats.profitDays += result.stats.profitDays;
            combinedStats.lossDays += result.stats.lossDays;
            combinedStats.totalDays += result.stats.totalDays;
        }
    }
    
    // Calculate derived statistics
    combinedStats.winRate = combinedStats.totalTrades > 0 ? 
        (combinedStats.wins / combinedStats.totalTrades * 100) : 0;
    
    combinedStats.winDayRate = combinedStats.totalDays > 0 ? 
        (combinedStats.profitDays / combinedStats.totalDays * 100) : 0;
    
    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>M2K Combined Verification Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>M2K Combined Verification Report</h1>
        <p>Date range: ${quarters[0].startDate} to ${quarters[quarters.length - 1].endDate}</p>
        <p>Parameters: SL=${params.slFactors}, TP=${params.tpFactors}, Trail=${params.trailFactors}, Fixed TP=${params.fixedTpPoints}</p>
        
        <h2>Combined Performance Summary</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Total PnL</td>
                <td class="${combinedStats.totalPnl >= 0 ? 'positive' : 'negative'}">$${combinedStats.totalPnl.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Total Trades</td>
                <td>${combinedStats.totalTrades}</td>
            </tr>
            <tr>
                <td>Winning Trades</td>
                <td>${combinedStats.wins}</td>
            </tr>
            <tr>
                <td>Losing Trades</td>
                <td>${combinedStats.losses}</td>
            </tr>
            <tr>
                <td>Win Rate</td>
                <td>${combinedStats.winRate.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total Days</td>
                <td>${combinedStats.totalDays}</td>
            </tr>
            <tr>
                <td>Profitable Days</td>
                <td>${combinedStats.profitDays}</td>
            </tr>
            <tr>
                <td>Losing Days</td>
                <td>${combinedStats.lossDays}</td>
            </tr>
            <tr>
                <td>Win Day Rate</td>
                <td>${combinedStats.winDayRate.toFixed(2)}%</td>
            </tr>
        </table>
        
        <h2>Quarterly Performance</h2>
        <table>
            <thead>
                <tr>
                    <th>Quarter</th>
                    <th>PnL</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Win Day Rate</th>
                    <th>Profit Factor</th>
                    <th>Max DD</th>
                    <th>Trades</th>
                </tr>
            </thead>
            <tbody>
                ${quarters.map(quarter => {
                    const result = quarterlyResults[quarter.name];
                    if (!result || !result.stats) return `
                    <tr>
                        <td>${quarter.name}</td>
                        <td colspan="7">No data available</td>
                    </tr>`;
                    
                    return `
                    <tr>
                        <td>${quarter.name}</td>
                        <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
                        <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
                        <td>${result.stats.winRate.toFixed(2)}%</td>
                        <td>${result.stats.winDayRate.toFixed(2)}%</td>
                        <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
                        <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
                        <td>${result.stats.totalTrades}</td>
                    </tr>`;
                }).join('')}
            </tbody>
        </table>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(reportPath, html);
    console.log(`Combined HTML report generated at ${reportPath}`);
}

/**
 * Run verification for all quarters
 */
async function runAllQuarters() {
    console.log(`\n===== RUNNING M2K VERIFICATION FOR ALL QUARTERS =====`);
    
    for (const quarter of quarters) {
        try {
            quarterlyResults[quarter.name] = await runQuarterlyVerification(quarter);
        } catch (error) {
            console.error(`Error running verification for ${quarter.name}:`, error);
            quarterlyResults[quarter.name] = null;
        }
    }
    
    // Generate combined report
    generateCombinedReport(quarterlyResults);
    
    console.log(`\n===== ALL QUARTERLY VERIFICATIONS COMPLETED =====`);
}

// Run verification for all quarters
runAllQuarters().catch(error => {
    console.error(`Error running quarterly verifications:`, error);
});
