/**
 * MGC Parameter Optimizer
 * 
 * This script runs multiple backtests with different parameter combinations
 * to find the optimal settings for MGC trading.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Output directory
const outputDir = './output/MGC_Optimization_Results';

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`Created output directory: ${outputDir}`);
}

// Parameter ranges to test
const parameterRanges = {
    slFactors: [7.0, 7.5, 8.0, 8.5, 9.0, 9.5],
    tpFactors: [6.0, 6.5, 7.0, 7.5, 8.0],
    trailFactors: [0.01, 0.02, 0.03, 0.04, 0.05],
    fixedContracts: [5, 10, 15]
};

// Generate parameter combinations
function generateParameterCombinations() {
    const combinations = [];
    
    for (const sl of parameterRanges.slFactors) {
        for (const tp of parameterRanges.tpFactors) {
            for (const trail of parameterRanges.trailFactors) {
                for (const contracts of parameterRanges.fixedContracts) {
                    combinations.push({
                        slFactors: sl,
                        tpFactors: tp,
                        trailFactors: trail,
                        fixedContracts: contracts
                    });
                }
            }
        }
    }
    
    return combinations;
}

// Create a temporary config file with specific parameters
function createTempConfig(params) {
    const configContent = `
// Temporary MGC config for optimization
module.exports = {
  // --- General Settings ---
  inputFile: 'C:/backtest-bot/input/MGC2020_2025.csv',
  initialBalance: 10000,

  // --- Instrument Specifics (MGC Specs) ---
  pointValue: 10.00,
  tickSize: 0.10,
  pricePrecision: 2,

  // --- Costs & Slippage ---
  commissionPerContract: 0.40,
  slippagePoints: 0.20,

  // --- Indicator Periods ---
  atrPeriod: 14, rsiPeriod: 14, rsiMaPeriod: 8, sma200Period: 200, wma50Period: 50,

  // --- Strategy Parameters ---
  fixedTpPoints: 40.0,
  useWmaFilter: false, useTwoBarColorExit: false,
  minAtrEntry: 0, minRsiMaSeparation: 0,

  // --- RSI Bands ---
  rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

  // --- Run Mode ---
  isAdaptiveRun: false,
  costGrid: null, riskPercentGrid: null, fixedContractsGrid: null, fixedTpPointsGrid: null,

  // --- Test Parameters ---
  slFactors: ${params.slFactors},
  tpFactors: ${params.tpFactors},
  trailFactors: ${params.trailFactors},
  riskPercent: 0,
  fixedContracts: ${params.fixedContracts},
  maxContracts: 30,

  // --- Latency Simulation ---
  latencyDelayBars: 0,

  // --- Time Filter Settings ---
  timeFilterEnabled: false,
  timeFilter: { start: 8, end: 15 },
  cstOffset: -5,
};
    `;
    
    fs.writeFileSync('./temp_mgc_config.js', configContent);
    return './temp_mgc_config.js';
}

// Run backtest with specific parameters
function runBacktest(params) {
    console.log(`\nRunning backtest with parameters:`);
    console.log(`SL: ${params.slFactors}, TP: ${params.tpFactors}, Trail: ${params.trailFactors}, Contracts: ${params.fixedContracts}`);
    
    // Create temporary config
    const configPath = createTempConfig(params);
    
    // Modify the backtest script to use the temporary config
    const originalBacktestContent = fs.readFileSync('./mgc_backtest.js', 'utf8');
    const modifiedBacktestContent = originalBacktestContent.replace(
        "const config = require('./config');",
        `const config = require('${configPath.replace(/\\/g, '\\\\')}');`
    );
    
    fs.writeFileSync('./temp_mgc_backtest.js', modifiedBacktestContent);
    
    // Run the backtest
    try {
        execSync('node temp_mgc_backtest.js', { stdio: 'inherit' });
        
        // Get the latest summary file
        const files = fs.readdirSync('./output/MGC_Backtest_Results');
        const summaryFiles = files.filter(file => file.startsWith('mgc_summary_'));
        summaryFiles.sort();
        
        if (summaryFiles.length > 0) {
            const latestSummary = JSON.parse(fs.readFileSync(
                path.join('./output/MGC_Backtest_Results', summaryFiles[summaryFiles.length - 1]),
                'utf8'
            ));
            
            return {
                parameters: params,
                performance: latestSummary.performance
            };
        }
    } catch (error) {
        console.error(`Error running backtest: ${error.message}`);
    }
    
    return {
        parameters: params,
        performance: {
            totalPnL: -Infinity,
            winRate: 0,
            winDayRate: 0,
            maxDrawdown: Infinity
        }
    };
}

// Main optimization function
async function optimizeMGC() {
    console.log("=== MGC Parameter Optimizer ===");
    console.log(`Testing ${Object.values(parameterRanges).reduce((acc, arr) => acc * arr.length, 1)} parameter combinations...`);
    
    const combinations = generateParameterCombinations();
    const results = [];
    
    for (let i = 0; i < combinations.length; i++) {
        console.log(`\nCombination ${i + 1}/${combinations.length}`);
        const result = runBacktest(combinations[i]);
        results.push(result);
    }
    
    // Sort results by different metrics
    const byPnL = [...results].sort((a, b) => b.performance.totalPnL - a.performance.totalPnL);
    const byWinRate = [...results].sort((a, b) => b.performance.winRate - a.performance.winRate);
    const byWinDayRate = [...results].sort((a, b) => b.performance.winDayRate - a.performance.winDayRate);
    const byDrawdown = [...results].sort((a, b) => a.performance.maxDrawdown - b.performance.maxDrawdown);
    
    // Save results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultsFile = path.join(outputDir, `optimization_results_${timestamp}.json`);
    
    fs.writeFileSync(resultsFile, JSON.stringify({
        byPnL: byPnL.slice(0, 10),
        byWinRate: byWinRate.slice(0, 10),
        byWinDayRate: byWinDayRate.slice(0, 10),
        byDrawdown: byDrawdown.slice(0, 10),
        allResults: results
    }, null, 2));
    
    console.log(`\nOptimization complete. Results saved to: ${resultsFile}`);
    
    // Display top results
    console.log("\n=== Top Results by Total P&L ===");
    byPnL.slice(0, 5).forEach((result, i) => {
        console.log(`${i + 1}. SL=${result.parameters.slFactors}, TP=${result.parameters.tpFactors}, Trail=${result.parameters.trailFactors}, Contracts=${result.parameters.fixedContracts}`);
        console.log(`   P&L: $${result.performance.totalPnL.toFixed(2)}, Win Rate: ${result.performance.winRate.toFixed(2)}%, Win Day Rate: ${result.performance.winDayRate.toFixed(2)}%, Max DD: $${result.performance.maxDrawdown.toFixed(2)}`);
    });
    
    console.log("\n=== Top Results by Win Day Rate ===");
    byWinDayRate.slice(0, 5).forEach((result, i) => {
        console.log(`${i + 1}. SL=${result.parameters.slFactors}, TP=${result.parameters.tpFactors}, Trail=${result.parameters.trailFactors}, Contracts=${result.parameters.fixedContracts}`);
        console.log(`   Win Day Rate: ${result.performance.winDayRate.toFixed(2)}%, P&L: $${result.performance.totalPnL.toFixed(2)}, Win Rate: ${result.performance.winRate.toFixed(2)}%, Max DD: $${result.performance.maxDrawdown.toFixed(2)}`);
    });
    
    // Clean up temporary files
    try {
        fs.unlinkSync('./temp_mgc_config.js');
        fs.unlinkSync('./temp_mgc_backtest.js');
    } catch (error) {
        console.error(`Error cleaning up temporary files: ${error.message}`);
    }
}

// Run the optimizer
optimizeMGC().catch(error => {
    console.error(`Optimization error: ${error.message}`);
});
