/**
 * simple-performance-analysis.js
 * Analyze daily and weekly performance of each symbol
 */

const { runBacktest } = require('./verify-backtest');
const { mnqConfig, mesConfig, mgcConfig } = require('../multi_symbol_config');

// Function to run backtest and analyze performance
async function analyzeSymbol(config) {
    console.log(`\n=== ANALYZING PERFORMANCE FOR ${config.symbol} ===\n`);

    // Run backtest
    const result = await runBacktest(config);

    // Calculate daily and weekly performance
    const dailyPerformance = {};
    const weeklyPerformance = {};

    // Check if trades is a number (not detailed trades)
    if (typeof result.trades === 'number') {
        console.log(`No detailed trade data available for ${config.symbol}`);
        return {
            symbol: config.symbol,
            daily: { totalDays: 0, profitableDays: 0, dailyWinRate: "0.00", stats: { mean: 0, median: 0, min: 0, max: 0, stdDev: 0 } },
            weekly: { totalWeeks: 0, profitableWeeks: 0, weeklyWinRate: "0.00", stats: { mean: 0, median: 0, min: 0, max: 0, stdDev: 0 } }
        };
    }

    // Group trades by day
    result.trades.forEach(trade => {
        const date = new Date(trade.exitTimestamp);
        const dayKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
        const weekKey = getWeekNumber(date);

        // Initialize if not exists
        if (!dailyPerformance[dayKey]) {
            dailyPerformance[dayKey] = {
                trades: 0,
                pnl: 0
            };
        }

        if (!weeklyPerformance[weekKey]) {
            weeklyPerformance[weekKey] = {
                trades: 0,
                pnl: 0
            };
        }

        // Update counts
        dailyPerformance[dayKey].trades++;
        weeklyPerformance[weekKey].trades++;

        // Update PnL
        dailyPerformance[dayKey].pnl += trade.pnlNetTotal;
        weeklyPerformance[weekKey].pnl += trade.pnlNetTotal;
    });

    // Convert to arrays for sorting
    const dailyArray = Object.keys(dailyPerformance).map(day => ({
        date: day,
        trades: dailyPerformance[day].trades,
        pnl: dailyPerformance[day].pnl
    }));

    const weeklyArray = Object.keys(weeklyPerformance).map(week => ({
        week,
        trades: weeklyPerformance[week].trades,
        pnl: weeklyPerformance[week].pnl
    }));

    // Sort by date/week
    dailyArray.sort((a, b) => a.date.localeCompare(b.date));
    weeklyArray.sort((a, b) => a.week.localeCompare(b.week));

    // Calculate statistics
    const dailyPnLs = dailyArray.map(d => d.pnl);
    const weeklyPnLs = weeklyArray.map(w => w.pnl);

    const dailyStats = {
        mean: average(dailyPnLs),
        median: median(dailyPnLs),
        min: Math.min(...dailyPnLs),
        max: Math.max(...dailyPnLs),
        stdDev: standardDeviation(dailyPnLs)
    };

    const weeklyStats = {
        mean: average(weeklyPnLs),
        median: median(weeklyPnLs),
        min: Math.min(...weeklyPnLs),
        max: Math.max(...weeklyPnLs),
        stdDev: standardDeviation(weeklyPnLs)
    };

    // Count profitable days and weeks
    const profitableDays = dailyArray.filter(d => d.pnl > 0).length;
    const profitableWeeks = weeklyArray.filter(w => w.pnl > 0).length;

    const dailyWinRate = (profitableDays / dailyArray.length * 100).toFixed(2);
    const weeklyWinRate = (profitableWeeks / weeklyArray.length * 100).toFixed(2);

    // Display summary
    console.log(`\n=== DAILY PERFORMANCE SUMMARY FOR ${config.symbol} ===`);
    console.log(`Total Days: ${dailyArray.length}`);
    console.log(`Profitable Days: ${profitableDays} (${dailyWinRate}%)`);
    console.log(`Average Daily P&L: $${dailyStats.mean.toFixed(2)}`);
    console.log(`Median Daily P&L: $${dailyStats.median.toFixed(2)}`);
    console.log(`Best Day: $${dailyStats.max.toFixed(2)}`);
    console.log(`Worst Day: $${dailyStats.min.toFixed(2)}`);
    console.log(`Standard Deviation: $${dailyStats.stdDev.toFixed(2)}`);

    console.log(`\n=== WEEKLY PERFORMANCE SUMMARY FOR ${config.symbol} ===`);
    console.log(`Total Weeks: ${weeklyArray.length}`);
    console.log(`Profitable Weeks: ${profitableWeeks} (${weeklyWinRate}%)`);
    console.log(`Average Weekly P&L: $${weeklyStats.mean.toFixed(2)}`);
    console.log(`Median Weekly P&L: $${weeklyStats.median.toFixed(2)}`);
    console.log(`Best Week: $${weeklyStats.max.toFixed(2)}`);
    console.log(`Worst Week: $${weeklyStats.min.toFixed(2)}`);
    console.log(`Standard Deviation: $${weeklyStats.stdDev.toFixed(2)}`);

    return {
        symbol: config.symbol,
        daily: {
            totalDays: dailyArray.length,
            profitableDays,
            dailyWinRate,
            stats: dailyStats
        },
        weekly: {
            totalWeeks: weeklyArray.length,
            profitableWeeks,
            weeklyWinRate,
            stats: weeklyStats
        }
    };
}

// Helper function to get week number (YYYY-WW format)
function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
    const week = Math.floor((d.getTime() - new Date(d.getFullYear(), 0, 4).getTime()) / 86400000 / 7) + 1;
    return `${d.getFullYear()}-W${week.toString().padStart(2, '0')}`;
}

// Helper functions for statistics
function average(arr) {
    return arr.reduce((a, b) => a + b, 0) / arr.length;
}

function median(arr) {
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
}

function standardDeviation(arr) {
    const mean = average(arr);
    const squaredDiffs = arr.map(val => Math.pow(val - mean, 2));
    const variance = average(squaredDiffs);
    return Math.sqrt(variance);
}

// Main function to run analysis for all symbols
async function runAllAnalyses() {
    const results = [];

    // Analyze MNQ
    const mnqResult = await analyzeSymbol(mnqConfig);
    results.push(mnqResult);

    // Analyze MES
    const mesResult = await analyzeSymbol(mesConfig);
    results.push(mesResult);

    // Analyze MGC
    const mgcResult = await analyzeSymbol(mgcConfig);
    results.push(mgcResult);

    // Display combined summary
    console.log("\n=== COMBINED PERFORMANCE SUMMARY ===\n");
    console.log("Symbol | Daily Win % | Avg Daily P&L | Weekly Win % | Avg Weekly P&L");
    console.log("-------|-------------|---------------|--------------|---------------");

    results.forEach(result => {
        console.log(`${result.symbol.padEnd(7)} | ${result.daily.dailyWinRate.padEnd(13)}% | $${result.daily.stats.mean.toFixed(2).padEnd(13)} | ${result.weekly.weeklyWinRate.padEnd(12)}% | $${result.weekly.stats.mean.toFixed(2)}`);
    });
}

// Run all analyses
runAllAnalyses().catch(console.error);
