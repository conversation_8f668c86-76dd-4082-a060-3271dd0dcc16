/**
 * Local Data Provider
 * Provides access to local OHLCV data files
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const logger = require('./logger');

class LocalDataProvider {
  /**
   * Create a new Local Data Provider
   * @param {Object} config - Configuration options
   */
  constructor(config = {}) {
    this.config = {
      dataDir: path.join('C:', 'backtest-bot', 'input'),
      filePattern: '{symbol}_{timeframe}.csv',
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      ...config
    };
    
    // Map of symbol to data
    this.dataCache = new Map();
    
    // Map of symbol to last update time
    this.lastUpdateTime = new Map();
    
    // Map of symbol to callbacks
    this.callbacks = new Map();
    
    // Symbol mapping
    this.symbolMap = {
      'MNQ': 'MNQ',
      'MES': 'MES',
      'MGC': 'MGC',
      'M2K': 'MiniRussell2000'
    };
  }
  
  /**
   * Get the file path for a symbol
   * @param {string} symbol - Symbol to get file path for
   * @param {string} timeframe - Timeframe to get file path for
   * @returns {string} - File path
   */
  getFilePath(symbol, timeframe = '1m') {
    const mappedSymbol = this.symbolMap[symbol] || symbol;
    const fileName = this.config.filePattern
      .replace('{symbol}', mappedSymbol)
      .replace('{timeframe}', timeframe);
    
    return path.join(this.config.dataDir, fileName);
  }
  
  /**
   * Load data for a symbol
   * @param {string} symbol - Symbol to load data for
   * @param {string} timeframe - Timeframe to load data for
   * @returns {Promise<Array>} - Data
   */
  async loadData(symbol, timeframe = '1m') {
    const filePath = this.getFilePath(symbol, timeframe);
    
    logger.logSystem(`Loading data from ${filePath}`, 'info');
    
    return new Promise((resolve, reject) => {
      const results = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          // Convert string values to appropriate types
          const parsedData = {
            timestamp: new Date(data.timestamp || data.date || data.time || data.datetime),
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseInt(data.volume || '0', 10)
          };
          
          // Add symbol
          parsedData.symbol = symbol;
          
          results.push(parsedData);
        })
        .on('end', () => {
          // Sort by timestamp
          results.sort((a, b) => a.timestamp - b.timestamp);
          
          // Cache the data
          this.dataCache.set(symbol, results);
          
          logger.logSystem(`Loaded ${results.length} records for ${symbol}`, 'info');
          
          resolve(results);
        })
        .on('error', (error) => {
          logger.logSystem(`Error loading data for ${symbol}: ${error.message}`, 'error');
          reject(error);
        });
    });
  }
  
  /**
   * Get historical data for a symbol
   * @param {string} symbol - Symbol to get data for
   * @param {Date|string} startDate - Start date
   * @param {Date|string} endDate - End date
   * @param {string} timeframe - Timeframe
   * @returns {Promise<Array>} - Historical data
   */
  async getHistoricalData(symbol, startDate, endDate, timeframe = '1m') {
    try {
      // Convert dates to Date objects if they are strings
      const start = startDate instanceof Date ? startDate : new Date(startDate);
      const end = endDate instanceof Date ? endDate : new Date(endDate);
      
      // Load data if not cached
      if (!this.dataCache.has(symbol)) {
        await this.loadData(symbol, timeframe);
      }
      
      // Get data from cache
      const data = this.dataCache.get(symbol);
      
      // Filter by date range
      const filteredData = data.filter(item => 
        item.timestamp >= start && item.timestamp <= end
      );
      
      logger.logSystem(`Returning ${filteredData.length} records for ${symbol} from ${start.toISOString()} to ${end.toISOString()}`, 'info');
      
      return filteredData;
    } catch (error) {
      logger.logSystem(`Error getting historical data for ${symbol}: ${error.message}`, 'error');
      throw error;
    }
  }
  
  /**
   * Subscribe to real-time market data for a symbol
   * @param {string} symbol - Symbol to subscribe to
   * @param {Function} callback - Callback function for data updates
   */
  subscribeToMarketData(symbol, callback) {
    logger.logSystem(`Subscribing to ${symbol} from local data`, 'info');
    
    // Store the callback
    this.callbacks.set(symbol, callback);
    
    // Load data if not cached
    if (!this.dataCache.has(symbol)) {
      this.loadData(symbol)
        .then(() => {
          logger.logSystem(`Data loaded for ${symbol}`, 'info');
        })
        .catch(error => {
          logger.logSystem(`Error loading data for ${symbol}: ${error.message}`, 'error');
        });
    }
  }
  
  /**
   * Unsubscribe from real-time market data for a symbol
   * @param {string} symbol - Symbol to unsubscribe from
   */
  unsubscribeFromMarketData(symbol) {
    logger.logSystem(`Unsubscribing from ${symbol}`, 'info');
    
    // Remove the callback
    this.callbacks.delete(symbol);
  }
  
  /**
   * Simulate real-time data for a symbol
   * @param {string} symbol - Symbol to simulate data for
   * @param {Date} currentTime - Current time
   */
  simulateRealTimeData(symbol, currentTime = new Date()) {
    // Get data from cache
    if (!this.dataCache.has(symbol)) {
      logger.logSystem(`No data for ${symbol}`, 'warning');
      return;
    }
    
    const data = this.dataCache.get(symbol);
    
    // Find the latest data point before the current time
    const latestData = data.filter(item => item.timestamp <= currentTime)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
    
    if (!latestData) {
      logger.logSystem(`No data for ${symbol} before ${currentTime.toISOString()}`, 'warning');
      return;
    }
    
    // Get the last update time
    const lastUpdate = this.lastUpdateTime.get(symbol) || new Date(0);
    
    // If the data is newer than the last update, send it to the callback
    if (latestData.timestamp > lastUpdate) {
      const callback = this.callbacks.get(symbol);
      
      if (callback) {
        callback(latestData);
        
        // Update the last update time
        this.lastUpdateTime.set(symbol, latestData.timestamp);
      }
    }
  }
}

module.exports = new LocalDataProvider();
