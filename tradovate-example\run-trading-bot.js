/**
 * run-trading-bot.js
 *
 * Main file to run the trading bot with Tradovate API
 */

const tradovateConnector = require('./tradovate-connector');
const tradingBot = require('./tradovate-bot');
const fs = require('fs');
const path = require('path');

// Tradovate credentials
const credentials = {
    name: process.env.TRADOVATE_USERNAME || 'your_username', // Replace with your actual username
    password: process.env.TRADOVATE_PASSWORD || 'your_password', // Replace with your actual password
    appId: 'sample_app_id',
    appVersion: '1.0',
    cid: process.env.TRADOVATE_CID || 'your_cid', // Replace with your actual CID
    sec: '69311dad-75a7-49c6-9958-00ab2c4f1ab6' // Your Tradovate API secret key
};

// Initialize dashboard data
let dashboardData = {
    startTime: new Date(),
    symbols: {
        MNQ: { trades: 0, wins: 0, losses: 0, winRate: 0, pnl: 0 },
        MES: { trades: 0, wins: 0, losses: 0, winRate: 0, pnl: 0 },
        MGC: { trades: 0, wins: 0, losses: 0, winRate: 0, pnl: 0 }
    },
    trades: [],
    dailyStats: {}
};

// Update dashboard function
function updateDashboard() {
    // Get current stats
    const stats = tradingBot.getDailyStats();
    const dailyPnL = tradingBot.getDailyPnL();
    const activePosition = tradingBot.getActivePosition();

    // Update dashboard data
    dashboardData.dailyStats = stats;

    // Update symbol-specific data
    Object.keys(dailyPnL).forEach(symbol => {
        const symbolPnL = dailyPnL[symbol];
        let totalPnL = 0;

        symbolPnL.forEach(pnl => {
            totalPnL += pnl;
        });

        dashboardData.symbols[symbol].pnl = totalPnL;
    });

    // Add active position if any
    if (activePosition) {
        dashboardData.activePosition = {
            symbol: activePosition.symbol,
            direction: activePosition.dir,
            entry: activePosition.entry,
            stopLoss: activePosition.stopLossPrice,
            takeProfit: activePosition.dir === 'bullish' ?
                activePosition.entry + activePosition.tpDistance :
                activePosition.entry - activePosition.tpDistance,
            trailStop: activePosition.trailStopPrice,
            contracts: activePosition.contracts,
            entryTime: activePosition.entryTimestamp
        };
    } else {
        dashboardData.activePosition = null;
    }

    // Save dashboard data to file
    const dashboardFile = path.join(__dirname, 'dashboard-data.json');
    fs.writeFileSync(dashboardFile, JSON.stringify(dashboardData, null, 2));

    // Log current stats
    console.log('\n--- DASHBOARD UPDATE ---');
    console.log(`Total Trades: ${stats.trades}, Wins: ${stats.wins}, Losses: ${stats.losses}, Win Rate: ${stats.trades > 0 ? ((stats.wins / stats.trades) * 100).toFixed(2) : 0}%`);
    console.log(`Current Balance: $${stats.currentBalance.toFixed(2)}, P&L: $${stats.pnl.toFixed(2)}, Max Drawdown: $${stats.maxDrawdown.toFixed(2)}`);

    if (activePosition) {
        console.log(`\nActive Position: ${activePosition.symbol} ${activePosition.dir.toUpperCase()}`);
        console.log(`Entry: ${activePosition.entry.toFixed(2)}, SL: ${activePosition.stopLossPrice.toFixed(2)}, TP: ${(activePosition.dir === 'bullish' ? activePosition.entry + activePosition.tpDistance : activePosition.entry - activePosition.tpDistance).toFixed(2)}, Trail: ${activePosition.trailStopPrice.toFixed(2)}`);
    } else {
        console.log('\nNo active position');
    }

    console.log('\nSymbol Stats:');
    Object.keys(dashboardData.symbols).forEach(symbol => {
        console.log(`${symbol}: P&L: $${dashboardData.symbols[symbol].pnl.toFixed(2)}`);
    });
}

// Main function
async function main() {
    console.log('Starting trading bot...');

    // Start trading bot
    const started = await tradovateConnector.startTradingBot(credentials);

    if (!started) {
        console.error('Failed to start trading bot');
        return;
    }

    // Set up dashboard update interval
    setInterval(updateDashboard, 5000);

    console.log('Trading bot started successfully');
}

// Handle errors and exit
process.on('uncaughtException', (error) => {
    console.error('Uncaught exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('SIGINT', () => {
    console.log('Shutting down trading bot...');
    process.exit(0);
});

// Run the main function
main().catch(error => {
    console.error('Error in main function:', error);
    process.exit(1);
});
