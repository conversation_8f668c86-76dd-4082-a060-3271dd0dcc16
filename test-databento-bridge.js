/**
 * Test script for Databento bridge integration
 */

require('dotenv').config();
const marketDataService = require('./market-data-service');
const logger = require('./logger');

// Test functions
async function testHistorical() {
  try {
    console.log('Testing historical data...');
    
    // Get historical data for ES.FUT
    console.log('\nGetting historical data for MES...');
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 1); // 1 day ago
    
    const historicalData = await marketDataService.getHistoricalData(
      'MES',
      startDate,
      endDate,
      '1m'
    );
    
    console.log(`Got ${historicalData.length} bars`);
    if (historicalData.length > 0) {
      console.log('Sample data:');
      console.log(historicalData.slice(0, 3));
    }
    
    return true;
  } catch (error) {
    console.error('Error testing historical data:', error);
    return false;
  }
}

async function testLiveData() {
  try {
    console.log('\nTesting live data...');
    
    // Subscribe to MES
    console.log('Subscribing to MES...');
    
    let messageCount = 0;
    const dataHandler = (data) => {
      messageCount++;
      if (messageCount <= 5) {
        console.log('Received data:', data);
      }
    };
    
    await marketDataService.subscribeToMarketData('MES', dataHandler);
    
    // Wait for some data
    console.log('Waiting for data (30 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    console.log(`Received ${messageCount} messages`);
    
    // Unsubscribe
    console.log('Unsubscribing...');
    await marketDataService.unsubscribeFromMarketData('MES');
    
    return true;
  } catch (error) {
    console.error('Error testing live data:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('Starting Databento bridge integration tests...');
  
  // Initialize market data service
  console.log('Initializing market data service...');
  
  // Configure to use Databento
  marketDataService.config = {
    dataSource: 'databento',
    useDabentoForHistorical: true,
    useDabentoForRealtime: true,
    useDabentoForBackup: true,
    symbols: ['MNQ', 'MES', 'MGC', 'M2K'],
    dabentoDataset: 'GLBX.MDP3',
    dabentoSchema: 'trades',
    dabentoSymbolType: 'parent',
    dabentoUseWebSocket: true,
    dabentoHeartbeatInterval: 30
  };
  
  await marketDataService.initialize();
  
  let success = true;
  
  // Test historical data
  if (await testHistorical()) {
    console.log('\n✅ Historical data test passed');
  } else {
    console.log('\n❌ Historical data test failed');
    success = false;
  }
  
  // Test live data
  if (await testLiveData()) {
    console.log('\n✅ Live data test passed');
  } else {
    console.log('\n❌ Live data test failed');
    success = false;
  }
  
  // Disconnect
  console.log('\nDisconnecting...');
  await marketDataService.disconnect();
  
  console.log('\nTests completed.');
  if (success) {
    console.log('All tests passed! 🎉');
  } else {
    console.log('Some tests failed. 😢');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
