/**
 * Order Manager Module
 *
 * Handles order placement, modification, and cancellation through the Tradovate API.
 * This module bridges the trading logic with the Tradovate API for executing trades.
 */

const tradovateApi = require('./tradovate_api');
const logger = require('./data_logger');
const alerts = require('./alert_system');

// Order tracking
const activeOrders = new Map();
const orderHistory = [];

// Order types
const ORDER_TYPES = {
    MARKET: 'Market',
    LIMIT: 'Limit',
    STOP: 'Stop',
    STOP_LIMIT: 'StopLimit'
};

// Order actions
const ORDER_ACTIONS = {
    BUY: 'Buy',
    SELL: 'Sell'
};

// Order time in force
const TIME_IN_FORCE = {
    DAY: 'Day',
    GTC: 'GTC',
    IOC: 'IOC',
    FOK: 'FOK'
};

/**
 * Place a market order
 * @param {Object} params - Order parameters
 * @returns {Promise<Object>} - Order result
 */
async function placeMarketOrder(params) {
    try {
        // Validate parameters
        if (!params.symbol || !params.action || !params.quantity) {
            throw new Error('Missing required parameters for market order');
        }

        // Ensure we have a valid token
        await tradovateApi.ensureValidToken();

        // Get contract ID for the symbol
        const contractId = await getContractId(params.symbol);
        if (!contractId) {
            throw new Error(`Could not find contract ID for symbol: ${params.symbol}`);
        }

        // Prepare order request
        const orderRequest = {
            accountId: params.accountId,
            contractId: contractId,
            action: params.action === 'bullish' ? ORDER_ACTIONS.BUY : ORDER_ACTIONS.SELL,
            orderQty: params.quantity,
            orderType: ORDER_TYPES.MARKET,
            isAutomated: true // Important for exchange compliance
        };

        logger.logSystem(`Placing market order: ${JSON.stringify(orderRequest)}`, 'info');

        // Send order request to Tradovate API
        const response = await tradovateApi.placeOrder(orderRequest);

        // Process response
        if (response.success) {
            const orderId = response.orderId;

            // Track the order
            activeOrders.set(orderId, {
                ...orderRequest,
                orderId,
                status: 'Pending',
                timestamp: new Date()
            });

            logger.logTradeEvent(params.symbol, {
                action: 'ORDER_PLACED',
                orderType: 'Market',
                direction: params.action,
                quantity: params.quantity,
                orderId: orderId,
                timestamp: new Date()
            });

            alerts.alertTrade(params.symbol, `Market order placed: ${params.action.toUpperCase()} ${params.quantity} contracts`, 'info');

            return {
                success: true,
                orderId: orderId,
                message: 'Market order placed successfully'
            };
        } else {
            throw new Error(`Failed to place market order: ${response.error}`);
        }
    } catch (error) {
        logger.logSystem(`Error placing market order: ${error.message}`, 'error');
        alerts.alertTrade(params.symbol, `Failed to place market order: ${error.message}`, 'error');

        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Place a limit order
 * @param {Object} params - Order parameters
 * @returns {Promise<Object>} - Order result
 */
async function placeLimitOrder(params) {
    try {
        // Validate parameters
        if (!params.symbol || !params.action || !params.quantity || !params.price) {
            throw new Error('Missing required parameters for limit order');
        }

        // Ensure we have a valid token
        await tradovateApi.ensureValidToken();

        // Get contract ID for the symbol
        const contractId = await getContractId(params.symbol);
        if (!contractId) {
            throw new Error(`Could not find contract ID for symbol: ${params.symbol}`);
        }

        // Prepare order request
        const orderRequest = {
            accountId: params.accountId,
            contractId: contractId,
            action: params.action === 'bullish' ? ORDER_ACTIONS.BUY : ORDER_ACTIONS.SELL,
            orderQty: params.quantity,
            orderType: ORDER_TYPES.LIMIT,
            price: params.price,
            timeInForce: params.timeInForce || TIME_IN_FORCE.DAY,
            isAutomated: true // Important for exchange compliance
        };

        logger.logSystem(`Placing limit order: ${JSON.stringify(orderRequest)}`, 'info');

        // Send order request to Tradovate API
        const response = await tradovateApi.placeOrder(orderRequest);

        // Process response
        if (response.success) {
            const orderId = response.orderId;

            // Track the order
            activeOrders.set(orderId, {
                ...orderRequest,
                orderId,
                status: 'Pending',
                timestamp: new Date()
            });

            logger.logTradeEvent(params.symbol, {
                action: 'ORDER_PLACED',
                orderType: 'Limit',
                direction: params.action,
                quantity: params.quantity,
                price: params.price,
                orderId: orderId,
                timestamp: new Date()
            });

            alerts.alertTrade(params.symbol, `Limit order placed: ${params.action.toUpperCase()} ${params.quantity} contracts at ${params.price}`, 'info');

            return {
                success: true,
                orderId: orderId,
                message: 'Limit order placed successfully'
            };
        } else {
            throw new Error(`Failed to place limit order: ${response.error}`);
        }
    } catch (error) {
        logger.logSystem(`Error placing limit order: ${error.message}`, 'error');
        alerts.alertTrade(params.symbol, `Failed to place limit order: ${error.message}`, 'error');

        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Place a stop order
 * @param {Object} params - Order parameters
 * @returns {Promise<Object>} - Order result
 */
async function placeStopOrder(params) {
    try {
        // Validate parameters
        if (!params.symbol || !params.action || !params.quantity || !params.stopPrice) {
            throw new Error('Missing required parameters for stop order');
        }

        // Ensure we have a valid token
        await tradovateApi.ensureValidToken();

        // Get contract ID for the symbol
        const contractId = await getContractId(params.symbol);
        if (!contractId) {
            throw new Error(`Could not find contract ID for symbol: ${params.symbol}`);
        }

        // Prepare order request
        const orderRequest = {
            accountId: params.accountId,
            contractId: contractId,
            action: params.action === 'bullish' ? ORDER_ACTIONS.BUY : ORDER_ACTIONS.SELL,
            orderQty: params.quantity,
            orderType: ORDER_TYPES.STOP,
            stopPrice: params.stopPrice,
            timeInForce: params.timeInForce || TIME_IN_FORCE.DAY,
            isAutomated: true // Important for exchange compliance
        };

        logger.logSystem(`Placing stop order: ${JSON.stringify(orderRequest)}`, 'info');

        // Send order request to Tradovate API
        const response = await tradovateApi.placeOrder(orderRequest);

        // Process response
        if (response.success) {
            const orderId = response.orderId;

            // Track the order
            activeOrders.set(orderId, {
                ...orderRequest,
                orderId,
                status: 'Pending',
                timestamp: new Date()
            });

            logger.logTradeEvent(params.symbol, {
                action: 'ORDER_PLACED',
                orderType: 'Stop',
                direction: params.action,
                quantity: params.quantity,
                stopPrice: params.stopPrice,
                orderId: orderId,
                timestamp: new Date()
            });

            alerts.alertTrade(params.symbol, `Stop order placed: ${params.action.toUpperCase()} ${params.quantity} contracts at ${params.stopPrice}`, 'info');

            return {
                success: true,
                orderId: orderId,
                message: 'Stop order placed successfully'
            };
        } else {
            throw new Error(`Failed to place stop order: ${response.error}`);
        }
    } catch (error) {
        logger.logSystem(`Error placing stop order: ${error.message}`, 'error');
        alerts.alertTrade(params.symbol, `Failed to place stop order: ${error.message}`, 'error');

        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get contract ID for a symbol
 * @param {string} symbol - Trading symbol
 * @returns {Promise<number|null>} - Contract ID or null if not found
 */
async function getContractId(symbol) {
    try {
        // Ensure we have a valid token
        await tradovateApi.ensureValidToken();

        // Call Tradovate API to find contract
        const contract = await tradovateApi.findContract(symbol);

        if (contract && contract.id) {
            return contract.id;
        }

        return null;
    } catch (error) {
        logger.logSystem(`Error getting contract ID for ${symbol}: ${error.message}`, 'error');
        return null;
    }
}

/**
 * Initialize the order manager
 * @param {Object} config - Configuration object
 */
function initialize(config) {
    // Store configuration
    if (config.apiUrl) {
        // Update API URL if provided
        tradovateApi.setApiUrl(config.apiUrl);
    }

    // Set access token if provided
    if (config.accessToken) {
        tradovateApi.setAccessToken(config.accessToken);
    }

    // Set account ID if provided
    if (config.accountId) {
        // Store account ID for future use
        const accountId = config.accountId;
        logger.logSystem(`Order manager initialized with account ID: ${accountId}`, 'info');
    }

    logger.logSystem('Order manager initialized', 'info');
}

module.exports = {
    initialize,
    placeMarketOrder,
    placeLimitOrder,
    placeStopOrder,
    activeOrders,
    orderHistory,
    ORDER_TYPES,
    ORDER_ACTIONS,
    TIME_IN_FORCE
};
