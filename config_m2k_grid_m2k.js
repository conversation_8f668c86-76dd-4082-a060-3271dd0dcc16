// config_m2k_grid_m2k.js - Grid Test Configuration for Micro Russell 2000 Futures (M2K)

module.exports = {
  // --- General Settings ---
  inputFile: 'C:/backtest-bot/input/MiniRussell2000_2020_2025.csv',
  initialBalance: 10000,

  // --- Instrument Specifics (M2K Specs) ---
  pointValue: 5.00,      // M2K point value ($5.00 per 1 point move)
  tickSize: 0.10,        // M2K tick size (0.10 points)
  pricePrecision: 1,     // M2K price precision (1 decimal place)

  // --- Costs & Slippage ---
  commissionPerContract: 0.40, // Realistic commission costs
  slippagePoints: 0.20,       // Realistic slippage (2 ticks)

  // --- Indicator Periods ---
  atrPeriod: 14,
  rsiPeriod: 14,
  rsiMaPeriod: 8,
  sma200Period: 0,
  wma50Period: 0,

  // --- Strategy Parameters ---
  fixedTpPoints: 0,       // Using ATR-based TP instead of fixed points
  useWmaFilter: false,
  useTwoBarColorExit: false,
  minAtrEntry: 0,
  minRsiMaSeparation: 0,

  // --- RSI Bands ---
  rsiUpperBand: 60, rsiLowerBand: 40, rsiMiddleBand: 50,

  // --- Run Mode: GRID TEST CONFIGURATION ---
  isAdaptiveRun: false,   // Disable adaptive mode for grid testing

  // Grid test parameters - wide range to find optimal values for M2K
  slFactors: [4.0, 5.0, 6.0, 7.0, 8.0, 9.0],
  tpFactors: [3.0, 4.0, 5.0, 6.0, 7.0, 8.0],
  trailFactors: [0.01, 0.02, 0.03, 0.05, 0.07, 0.10],

  // 0 bar latency for optimal performance
  latencyDelayBars: 0,

  // Keep these for compatibility
  costGrid: null,
  riskPercentGrid: null,
  fixedContractsGrid: null,
  fixedTpPointsGrid: null,

  // Fixed parameters
  riskPercent: 0,
  fixedContracts: 10,     // Using 10 contracts for grid testing
  maxContracts: 10,

  // For compatibility with backtest.js
  currentRiskPercent: 0,
  currentFixedContracts: 10,

  // --- Time Filter Settings ---
  timeFilterEnabled: true,  // Enable time filtering
  activeHours: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],  // Test all hours initially
};