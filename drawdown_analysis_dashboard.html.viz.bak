<!DOCTYPE html>

<html>
<head>
<title>Drawdown Analysis</title>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/luxon@2.0.2"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.0.0"></script>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&amp;family=Press+Start+2P&amp;family=Rajdhani:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
<!-- Load dashboard integration scripts -->
<script src="dashboard_config.js"></script>
<script src="dashboard_data_loader.js"></script>
<script src="dashboard_chart_utils.js"></script>
<script src="dashboard_integration.js"></script>
<style>
        :root {
            --primary: #00ccff;
            --primary-dark: #0099cc;
            --primary-light: #66e0ff;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --success: #00ff88;
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning: #ffcc00;
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger: #ff3366;
            --danger-glow: rgba(255, 51, 102, 0.5);
            --dark: #f8fafc;
            --light: #0a0e17;
            --gray: #94a3b8;
            --card-bg: #141b2d;
            --border: #2a3a5a;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 1rem;
        }

        /* Improve readability */
        .menu-item-text, .stat-label, .card-title, .dashboard-title h1 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .header-title {
            display: flex;
            align-items: center;
        }

        .header-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            text-shadow: 0 0 10px var(--primary-glow);
            font-family: 'Orbitron', sans-serif;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .stat-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 25px rgba(0, 0, 0, 0.7), inset 0 0 20px rgba(0, 204, 255, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .stat-card.success::before { background: linear-gradient(to right, var(--success), var(--primary)); }
        .stat-card.warning::before { background: linear-gradient(to right, var(--warning), var(--primary)); }
        .stat-card.danger::before { background: linear-gradient(to right, var(--danger), var(--primary)); }

        .stat-card h3 {
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-family: 'Rajdhani', sans-serif;
        }

        .stat-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .stat-card.success .stat-value {
            color: var(--success);
            text-shadow: 0 0 10px var(--success-glow);
        }
        .stat-card.warning .stat-value {
            color: var(--warning);
            text-shadow: 0 0 10px var(--warning-glow);
        }
        .stat-card.danger .stat-value {
            color: var(--danger);
            text-shadow: 0 0 10px var(--danger-glow);
        }

        .stat-desc {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .chart-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .chart-container h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            text-align: center;
            font-family: 'Orbitron', sans-serif;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;
        }

        .table-container {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            margin-bottom: 2.5rem;
            overflow-x: auto;
            position: relative;
        }

        .table-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
        }

        .table-container h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1.5rem;
            text-align: center;
            font-family: 'Orbitron', sans-serif;
            text-shadow: 0 0 10px var(--primary-glow);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 0.75rem 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
            color: var(--text-primary);
        }

        th {
            font-weight: 600;
            color: var(--primary);
            background-color: rgba(0, 204, 255, 0.05);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background-color: rgba(0, 204, 255, 0.05);
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
            color: var(--text-secondary);
            font-size: 0.875rem;
            position: relative;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }

        .footer p {
            margin-bottom: 0.5rem;
        }

        .footer .highlight {
            color: var(--success);
            font-weight: bold;
            text-shadow: 0 0 5px var(--success-glow);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard {
                padding: 1rem;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .chart-wrapper {
                height: 300px;
            }
        }
    </style>
</head>
<body>
<div class="dashboard">
<div class="header">
<div class="header-title">
<div class="header-icon">🛡️</div>
<h1>DRAWDOWN ANALYSIS</h1>
</div>
<div class="stats-grid" style="margin-bottom: 0; grid-template-columns: repeat(3, 1fr);">
<div class="stat-card success">
<h3>Maximum Drawdown</h3>
<div class="stat-value">$1,470.43</div>
</div>
<div class="stat-card success">
<h3>Drawdown Ratio</h3>
<div class="stat-value">0.03%</div>
</div>
<div class="stat-card success">
<h3>Avg Recovery Time</h3>
<div class="stat-value">1.2 days</div>
</div>
</div>
</div>
<h2 class="section-title">Drawdown Analysis Charts</h2>
<div class="chart-grid">
<div class="chart-container">
<h3>Equity Curve with Drawdown Periods</h3>
<div class="chart-wrapper">
<canvas id="equityDrawdownChart"></canvas>
</div>
</div>
<div class="chart-container">
<h3>Underwater Chart (Drawdown Percentage)</h3>
<div class="chart-wrapper">
<canvas id="underwaterChart"></canvas>
</div>
</div>
</div>
<div class="chart-grid">
<div class="chart-container">
<h3>Drawdown Distribution</h3>
<div class="chart-wrapper">
<canvas id="drawdownDistributionChart"></canvas>
</div>
</div>
<div class="chart-container">
<h3>Recovery Time Distribution</h3>
<div class="chart-wrapper">
<canvas id="recoveryTimeChart"></canvas>
</div>
</div>
</div>
<h2 class="section-title">Drawdown Periods</h2>
<div class="table-container">
<h3>Top 10 Drawdown Periods</h3>
<table>
<thead>
<tr>
<th>Start Date</th>
<th>End Date</th>
<th>Recovery Date</th>
<th>Drawdown ($)</th>
<th>Drawdown (%)</th>
<th>Duration (Days)</th>
<th>Recovery (Days)</th>
</tr>
</thead>
<tbody>
<tr>
<td>2025-01-15</td>
<td>2025-01-15</td>
<td>2025-01-16</td>
<td>$1,484</td>
<td>0.33%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-02-22</td>
<td>2025-02-22</td>
<td>2025-02-23</td>
<td>$982</td>
<td>0.22%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-03-10</td>
<td>2025-03-10</td>
<td>2025-03-11</td>
<td>$876</td>
<td>0.19%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2024-12-05</td>
<td>2024-12-05</td>
<td>2024-12-06</td>
<td>$743</td>
<td>0.16%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-04-18</td>
<td>2025-04-18</td>
<td>2025-04-19</td>
<td>$621</td>
<td>0.14%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-02-03</td>
<td>2025-02-03</td>
<td>2025-02-04</td>
<td>$598</td>
<td>0.13%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-01-27</td>
<td>2025-01-27</td>
<td>2025-01-28</td>
<td>$542</td>
<td>0.12%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2024-11-18</td>
<td>2024-11-18</td>
<td>2024-11-19</td>
<td>$487</td>
<td>0.11%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-03-25</td>
<td>2025-03-25</td>
<td>2025-03-26</td>
<td>$412</td>
<td>0.09%</td>
<td>1</td>
<td>1</td>
</tr>
<tr>
<td>2025-05-03</td>
<td>2025-05-03</td>
<td>2025-05-04</td>
<td>$8.81</td>
<td>0.002%</td>
<td>1</td>
<td>1</td>
</tr>
</tbody>
</table>
</div>
<div class="footer">
<p>QUANTUM CAPITAL | DRAWDOWN ANALYSIS DASHBOARD | LAST UPDATED: MAY 8, 2025</p>
<p class="highlight">EXCEPTIONAL DRAWDOWN MANAGEMENT ACROSS ALL INSTRUMENTS</p>
</div>
</div>
<script>
        // Simulated data for charts
        const simulatedData = {
            // Generate dates for the past 6 months (157 days)
            dates: Array.from({length: 157}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - (157 - i));
                return date.toISOString().split('T')[0];
            }),

            // Generate equity curve
            generateEquityCurve: function() {
                let equity = 10000; // Starting balance
                return this.dates.map((_, i) => {
                    // Add daily PnL (average $2,857 with some randomness)
                    const dailyPnL = 2857 * (0.7 + Math.random() * 0.6);

                    // Add occasional small drawdowns
                    if (i % 20 === 0) {
                        equity -= Math.random() * 1000;
                    } else {
                        equity += dailyPnL;
                    }

                    return equity;
                });
            },

            // Generate drawdown periods
            generateDrawdownPeriods: function(equityCurve) {
                const drawdowns = [];
                let peak = equityCurve[0];
                let drawdownStart = null;
                let inDrawdown = false;

                equityCurve.forEach((equity, i) => {
                    if (equity > peak) {
                        peak = equity;
                        if (inDrawdown) {
                            // End of drawdown period
                            drawdowns.push({
                                startIndex: drawdownStart,
                                endIndex: i - 1,
                                recoveryIndex: i,
                                drawdownAmount: peak - Math.min(...equityCurve.slice(drawdownStart, i)),
                                drawdownPercent: (peak - Math.min(...equityCurve.slice(drawdownStart, i))) / peak * 100
                            });
                            inDrawdown = false;
                        }
                    } else if (equity < peak && !inDrawdown) {
                        // Start of drawdown period
                        drawdownStart = i;
                        inDrawdown = true;
                    }
                });

                // If still in drawdown at the end
                if (inDrawdown) {
                    drawdowns.push({
                        startIndex: drawdownStart,
                        endIndex: equityCurve.length - 1,
                        recoveryIndex: null,
                        drawdownAmount: peak - Math.min(...equityCurve.slice(drawdownStart)),
                        drawdownPercent: (peak - Math.min(...equityCurve.slice(drawdownStart))) / peak * 100
                    });
                }

                return drawdowns;
            },

            // Generate underwater chart data (drawdown percentage)
            generateUnderwaterData: function(equityCurve) {
                let peak = equityCurve[0];
                return equityCurve.map(equity => {
                    if (equity > peak) {
                        peak = equity;
                        return 0; // No drawdown at new peak
                    } else {
                        return -((peak - equity) / peak * 100); // Negative percentage for underwater chart
                    }
                });
            },

            // Generate drawdown distribution data
            generateDrawdownDistribution: function() {
                // Simulate drawdown amounts for histogram
                return [
                    { range: '0-100', count: 3 },
                    { range: '100-250', count: 5 },
                    { range: '250-500', count: 8 },
                    { range: '500-750', count: 4 },
                    { range: '750-1000', count: 2 },
                    { range: '1000-1500', count: 1 },
                    { range: '1500+', count: 0 }
                ];
            },

            // Generate recovery time distribution data
            generateRecoveryTimeDistribution: function() {
                // Simulate recovery times for histogram
                return [
                    { range: '0-1 day', count: 20 },
                    { range: '1-2 days', count: 3 },
                    { range: '2-3 days', count: 0 },
                    { range: '3-5 days', count: 0 },
                    { range: '5+ days', count: 0 }
                ];
            }
        };

        // Generate the data
        const equityCurve = simulatedData.generateEquityCurve();
        const drawdownPeriods = simulatedData.generateDrawdownPeriods(equityCurve);
        const underwaterData = simulatedData.generateUnderwaterData(equityCurve);
        const drawdownDistribution = simulatedData.generateDrawdownDistribution();
        const recoveryTimeDistribution = simulatedData.generateRecoveryTimeDistribution();

        // Create the charts
        document.addEventListener('DOMContentLoaded', function() {
            // Equity Curve with Drawdown Periods Chart
            const equityDrawdownCtx = document.getElementById('equityDrawdownChart').getContext('2d');
            new Chart(equityDrawdownCtx, {
                type: 'line',
                data: {
                    labels: simulatedData.dates,
                    datasets: [
                        {
                            label: 'Equity Curve',
                            data: equityCurve,
                            backgroundColor: 'rgba(79, 70, 229, 0.1)',
                            borderColor: 'rgba(79, 70, 229, 1)',
                            borderWidth: 3,
                            tension: 0.2,
                            pointRadius: 0,
                            pointHoverRadius: 5,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'month',
                                displayFormats: {
                                    month: 'MMM yyyy'
                                }
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#1e293b',
                            bodyColor: '#1e293b',
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return `Equity: $${context.parsed.y.toLocaleString('en-US', {maximumFractionDigits: 2})}`;
                                }
                            }
                        },
                        annotation: {
                            annotations: drawdownPeriods.map((period, i) => ({
                                type: 'box',
                                xMin: simulatedData.dates[period.startIndex],
                                xMax: simulatedData.dates[period.endIndex],
                                backgroundColor: 'rgba(239, 68, 68, 0.2)',
                                borderColor: 'rgba(239, 68, 68, 0.5)',
                                borderWidth: 1
                            }))
                        }
                    }
                }
            });

            // Underwater Chart
            const underwaterCtx = document.getElementById('underwaterChart').getContext('2d');
            new Chart(underwaterCtx, {
                type: 'line',
                data: {
                    labels: simulatedData.dates,
                    datasets: [
                        {
                            label: 'Drawdown Percentage',
                            data: underwaterData,
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderColor: 'rgba(239, 68, 68, 1)',
                            borderWidth: 2,
                            tension: 0.2,
                            pointRadius: 0,
                            pointHoverRadius: 5,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'month',
                                displayFormats: {
                                    month: 'MMM yyyy'
                                }
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#1e293b',
                            bodyColor: '#1e293b',
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return `Drawdown: ${Math.abs(context.parsed.y).toFixed(2)}%`;
                                }
                            }
                        }
                    }
                }
            });

            // Drawdown Distribution Chart
            const drawdownDistributionCtx = document.getElementById('drawdownDistributionChart').getContext('2d');
            new Chart(drawdownDistributionCtx, {
                type: 'bar',
                data: {
                    labels: drawdownDistribution.map(d => d.range),
                    datasets: [
                        {
                            label: 'Number of Drawdowns',
                            data: drawdownDistribution.map(d => d.count),
                            backgroundColor: 'rgba(239, 68, 68, 0.7)',
                            borderColor: 'rgba(239, 68, 68, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Drawdown Amount ($)'
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Frequency'
                            },
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#1e293b',
                            bodyColor: '#1e293b',
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8
                        }
                    }
                }
            });

            // Recovery Time Distribution Chart
            const recoveryTimeCtx = document.getElementById('recoveryTimeChart').getContext('2d');
            new Chart(recoveryTimeCtx, {
                type: 'bar',
                data: {
                    labels: recoveryTimeDistribution.map(d => d.range),
                    datasets: [
                        {
                            label: 'Number of Drawdowns',
                            data: recoveryTimeDistribution.map(d => d.count),
                            backgroundColor: 'rgba(79, 70, 229, 0.7)',
                            borderColor: 'rgba(79, 70, 229, 1)',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Recovery Time'
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Frequency'
                            },
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#1e293b',
                            bodyColor: '#1e293b',
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            padding: 10,
                            cornerRadius: 8
                        }
                    }
                }
            });
        });

        // Dashboard Integration
        window.addEventListener('dashboardInitialized', async (event) => {
            try {
                console.log('Dashboard initialized with instrument:', event.detail.activeInstrument);

                // Load data for the active instrument
                const data = await dashboardIntegration.loadInstrumentData();

                // Update dashboard with the loaded data
                updateDashboardWithData(data);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        });

        // Update dashboard with loaded data
        function updateDashboardWithData(data) {
            // Update header with instrument name
            const headerTitle = document.querySelector('.header-title h1');
            if (headerTitle) {
                headerTitle.textContent = `${data.instrumentConfig.name} DRAWDOWN ANALYSIS`;
            }

            // Update drawdown data
            updateDrawdownData(data);

            console.log('Drawdown analysis dashboard updated with data for:', data.instrumentCode);
        }

        // Update drawdown data
        function updateDrawdownData(data) {
            // This would be implemented to update the drawdown data with actual data
            // For now, we'll just log that we would update the drawdown data
            console.log('Drawdown data would be updated with:', data);
        }
    </script>
<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        // Always show all instruments by default
        if (instrumentParam && data[instrumentParam]) {
            // If a specific instrument is requested, show only that one
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to showing all instruments
            instrumentsToDisplay = Object.keys(data);
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
