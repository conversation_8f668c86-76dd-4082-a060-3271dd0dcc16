/**
 * Live Trading Bot for Tradovate
 * Implements pattern recognition strategy with ATR-based trailing stops
 */

// Import required modules
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Import trading indicators and logic
const { calculateRSI, calculateSMA, calculateWMA, calculateATR } = require('./indicators');

// Configuration
const CONFIG = {
    // API settings
    apiUrl: 'https://demo.tradovateapi.com/v1',

    // Account settings
    accountId: null, // Will be set after login

    // Trading parameters
    symbols: ['MNQ', 'MES', 'MGC'],

    // Position sizing
    fixedContracts: {
        MNQ: 3,
        MES: 3,
        MGC: 3
    },

    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14,

    // Entry/exit parameters
    slFactors: {
        MNQ: 4.5,
        MES: 3.0,
        MGC: 8.0
    },
    tpFactors: {
        MNQ: 3.0,
        MES: 3.0,
        MGC: 7.0
    },
    trailFactors: {
        MNQ: 0.11,
        MES: 0.01,
        MGC: 0.02
    },
    fixedTpPoints: {
        MNQ: 40,
        MES: 0,
        MGC: 0
    },

    // Risk management
    maxDailyLoss: 0.10, // 10% of account

    // Filters
    minAtrEntry: 0.5,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,

    // Logging
    logLevel: 'info', // 'debug', 'info', 'warn', 'error'

    // Credentials (will be loaded from env or config file)
    credentials: {
        name: '',
        password: '',
        appId: '',
        appVersion: '',
        cid: '',
        sec: ''
    }
};

// Global variables
let accessToken = null;
let mdAccessToken = null;
let expirationTime = null;
let mdExpirationTime = null;
let marketData = {};
let activePositions = {};
let dailyStats = {};
let circuitBreakers = {
    dailyLoss: 0,
    isTripped: false,
    lastResetDate: null
};

/**
 * Initialize the trading bot
 */
async function initialize() {
    try {
        // Load credentials
        loadCredentials();

        // Connect to Tradovate API
        await login();

        // Get account information
        await getAccountInfo();

        // Subscribe to market data
        await subscribeToMarketData();

        // Initialize market data structures
        initializeMarketData();

        // Start the main trading loop
        startTradingLoop();

        console.log('Trading bot initialized successfully');
    } catch (error) {
        console.error('Error initializing trading bot:', error);
        process.exit(1);
    }
}

/**
 * Load credentials from environment variables or config file
 */
function loadCredentials() {
    // Try to load from environment variables first
    if (process.env.TRADOVATE_NAME && process.env.TRADOVATE_PASSWORD) {
        CONFIG.credentials.name = process.env.TRADOVATE_NAME;
        CONFIG.credentials.password = process.env.TRADOVATE_PASSWORD;
        CONFIG.credentials.appId = process.env.TRADOVATE_APP_ID || 'Sample App';
        CONFIG.credentials.appVersion = process.env.TRADOVATE_APP_VERSION || '1.0';
        CONFIG.credentials.cid = process.env.TRADOVATE_CID;
        CONFIG.credentials.sec = process.env.TRADOVATE_SEC;
    } else {
        // Try to load from config file
        try {
            const credentialsPath = path.join(__dirname, 'credentials.json');
            if (fs.existsSync(credentialsPath)) {
                const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
                CONFIG.credentials = { ...CONFIG.credentials, ...credentials };
            } else {
                throw new Error('Credentials file not found');
            }
        } catch (error) {
            console.error('Error loading credentials:', error);
            throw new Error('Failed to load credentials. Please set environment variables or create a credentials.json file.');
        }
    }

    // Validate credentials
    if (!CONFIG.credentials.name || !CONFIG.credentials.password) {
        throw new Error('Missing required credentials');
    }
}

/**
 * Login to Tradovate API
 */
async function login() {
    try {
        // Get access token
        const response = await axios.post(`${CONFIG.apiUrl}/auth/accesstokenrequest`, {
            name: CONFIG.credentials.name,
            password: CONFIG.credentials.password,
            appId: CONFIG.credentials.appId,
            appVersion: CONFIG.credentials.appVersion,
            cid: CONFIG.credentials.cid,
            sec: CONFIG.credentials.sec
        });

        if (response.data && response.data.accessToken) {
            accessToken = response.data.accessToken;
            expirationTime = new Date().getTime() + (response.data.expirationTime * 1000);
            console.log('Successfully logged in to Tradovate API');

            // Also get market data access token
            await getMarketDataAccessToken();
        } else {
            throw new Error('Failed to get access token');
        }
    } catch (error) {
        console.error('Login error:', error.response ? error.response.data : error.message);
        throw new Error('Failed to login to Tradovate API');
    }
}

/**
 * Get market data access token
 */
async function getMarketDataAccessToken() {
    try {
        const response = await axios.post(`${CONFIG.apiUrl}/auth/mdaccesstokenrequest`, {}, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.data && response.data.accessToken) {
            mdAccessToken = response.data.accessToken;
            mdExpirationTime = new Date().getTime() + (response.data.expirationTime * 1000);
            console.log('Successfully obtained market data access token');
        } else {
            throw new Error('Failed to get market data access token');
        }
    } catch (error) {
        console.error('Market data access token error:', error.response ? error.response.data : error.message);
        throw new Error('Failed to get market data access token');
    }
}

/**
 * Get account information
 */
async function getAccountInfo() {
    try {
        const response = await axios.get(`${CONFIG.apiUrl}/account/list`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (response.data && response.data.length > 0) {
            // Use the first active account
            const account = response.data.find(acc => acc.active);
            if (account) {
                CONFIG.accountId = account.id;
                console.log(`Using account: ${account.name} (ID: ${account.id})`);
            } else {
                throw new Error('No active account found');
            }
        } else {
            throw new Error('No accounts found');
        }
    } catch (error) {
        console.error('Account info error:', error.response ? error.response.data : error.message);
        throw new Error('Failed to get account information');
    }
}

// Start the bot when this script is run directly
if (require.main === module) {
    initialize().catch(error => {
        console.error('Initialization error:', error);
        process.exit(1);
    });
}

// Export functions for testing
module.exports = {
    initialize,
    CONFIG
};
