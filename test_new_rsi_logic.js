/**
 * Test New RSI Logic
 *
 * This script runs a backtest with the new RSI logic:
 * - RSI length = 14
 * - RSI smoothing = SMA with length 8 (RSI-based MA)
 * - Long: RSI must be trading ABOVE RSI-based MA AND price must be trading above 50 WMA
 * - Short: RSI must be trading BELOW RSI-based MA AND price must be trading below 50 WMA
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Configuration
const config = {
    // Input file
    inputFile: process.argv[2] || './data/MNQ_1min_5years.csv',

    // Indicator periods
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14,
    sma200Period: 0, // Not using SMA200

    // Trade parameters
    slFactors: 4.5, // For MNQ
    tpFactors: 3.0, // For MNQ
    trailFactors: 0.11, // For MNQ
    fixedTpPoints: 40, // For MNQ

    // Position sizing
    fixedContracts: 5,

    // Costs
    commissionPerContract: 0.40,
    slippagePoints: 0.75,
    pointValue: 0.50, // For MNQ

    // Other settings
    initialBalance: 10000,
    pricePrecision: 2,
    useWmaFilter: false,
    useTwoBarColorExit: false,
    timeFilterEnabled: false,

    // ATR thresholds for adaptive mode
    atrThresholds: {
        low_medium: 4.7601,
        medium_high: 7.2605
    },

    // Adaptive parameters (not used in this test)
    isAdaptiveRun: false
};

// Check if we're testing MGC
if (process.argv[3] === 'MGC') {
    console.log("Testing MGC with new RSI logic");
    config.slFactors = 8.0;
    config.tpFactors = 7.0;
    config.trailFactors = 0.02;
    config.pointValue = 10.0;
    config.atrThresholds = {
        low_medium: 1.5,
        medium_high: 3.0
    };
}
// Check if we're testing MES
else if (process.argv[3] === 'MES') {
    console.log("Testing MES with new RSI logic");
    config.slFactors = 3.0;
    config.tpFactors = 3.0;
    config.trailFactors = 0.01;
    config.pointValue = 5.0;
    config.atrThresholds = {
        low_medium: 3.0,
        medium_high: 5.0
    };
} else {
    console.log("Testing MNQ with new RSI logic");
}

// Global variables
const allCandles = [];
let tradeLog = [];

// --- Main Execution ---
console.log(`Loading data from ${config.inputFile}...`);

fs.createReadStream(config.inputFile)
    .pipe(csv({ separator: ';', mapHeaders: ({ header }) => header.trim() }))
    .on('data', d => {
        const open = +d['Open'];
        const high = +d['High'];
        const low = +d['Low'];
        const close = +d['Close'];
        const timeString = d['Time'] || d['Date'] || d['Time left'];

        let timestampSeconds = NaN;
        if (timeString) {
            let parsedDate;
            try {
                parsedDate = new Date(timeString);
            } catch (e) {}

            if (parsedDate && !isNaN(parsedDate.getTime())) {
                timestampSeconds = Math.floor(parsedDate.getTime() / 1000);
            } else if (timeString && !isNaN(Number(timeString))) {
                const tsNum = Number(timeString);
                timestampSeconds = (tsNum > 1e11) ? Math.floor(tsNum / 1000) : tsNum;
            }
        }

        if (isNaN(timestampSeconds) || isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close)) {
            return;
        }

        allCandles.push({ timestamp: timestampSeconds, open, high, low, close });
    })
    .on('end', () => {
        console.log(`Parsed ${allCandles.length} candles.`);
        allCandles.sort((a, b) => a.timestamp - b.timestamp);

        console.log(`Data time range: ${new Date(allCandles[0].timestamp * 1000).toISOString()} to ${new Date(allCandles[allCandles.length - 1].timestamp * 1000).toISOString()}`);

        // Calculate indicators and run backtest
        computeIndicators(allCandles);
        const results = runBacktest();

        // Save results
        const symbol = process.argv[3] || 'MNQ';
        const resultsFilename = `${symbol}_new_rsi_logic_results.json`;
        fs.writeFileSync(resultsFilename, JSON.stringify(results, null, 2));
        console.log(`Results saved to ${resultsFilename}`);

        // Save trade log
        const tradeLogFilename = `${symbol}_new_rsi_logic_trades.csv`;
        saveTradeLog(tradeLog, tradeLogFilename);
        console.log(`Trade log saved to ${tradeLogFilename}`);
    })
    .on('error', (err) => {
        console.error(`Error reading CSV file ${config.inputFile}:`, err);
        process.exit(1);
    });

// --- Indicator Calculation ---
function computeIndicators(candles) {
    console.log("Computing indicators...");

    // Calculate WMA50
    if (config.wma50Period > 0) {
        const closes = candles.map(c => c.close);
        for (let i = 0; i < candles.length; i++) {
            if (i >= config.wma50Period - 1) {
                let weightedSum = 0;
                let weightSum = 0;
                for (let j = 0; j < config.wma50Period; j++) {
                    const weight = config.wma50Period - j;
                    weightedSum += closes[i - j] * weight;
                    weightSum += weight;
                }
                candles[i].wma50 = weightedSum / weightSum;
            } else {
                candles[i].wma50 = NaN;
            }
        }
    }

    // Calculate RSI
    if (config.rsiPeriod > 0) {
        const closes = candles.map(c => c.close);
        for (let i = 0; i < candles.length; i++) {
            if (i >= config.rsiPeriod) {
                let gains = 0, losses = 0;
                for (let j = i - config.rsiPeriod + 1; j <= i; j++) {
                    if (j > 0) {
                        const delta = closes[j] - closes[j - 1];
                        if (delta > 0) {
                            gains += delta;
                        } else {
                            losses -= delta;
                        }
                    }
                }

                const avgGain = gains / config.rsiPeriod;
                const avgLoss = losses / config.rsiPeriod;

                if (avgLoss === 0) {
                    candles[i].rsi = 100;
                } else if (avgGain === 0) {
                    candles[i].rsi = 0;
                } else {
                    const rs = avgGain / avgLoss;
                    candles[i].rsi = 100 - (100 / (1 + rs));
                }
            } else {
                candles[i].rsi = NaN;
            }
        }

        // Calculate RSI MA (SMA of RSI)
        if (config.rsiMaPeriod > 0) {
            const rsiValues = candles.map(c => c.rsi);
            for (let i = 0; i < candles.length; i++) {
                if (i >= config.rsiPeriod + config.rsiMaPeriod - 1) {
                    let sum = 0;
                    for (let j = i - config.rsiMaPeriod + 1; j <= i; j++) {
                        sum += rsiValues[j];
                    }
                    candles[i].rsiMa = sum / config.rsiMaPeriod;
                } else {
                    candles[i].rsiMa = NaN;
                }
            }
        }
    }

    // Calculate ATR
    if (config.atrPeriod > 0) {
        const trs = [];
        for (let i = 0; i < candles.length; i++) {
            if (i === 0) {
                trs.push(candles[i].high - candles[i].low);
            } else {
                const tr = Math.max(
                    candles[i].high - candles[i].low,
                    Math.abs(candles[i].high - candles[i-1].close),
                    Math.abs(candles[i].low - candles[i-1].close)
                );
                trs.push(tr);
            }
        }

        for (let i = 0; i < candles.length; i++) {
            if (i >= config.atrPeriod - 1) {
                let sum = 0;
                for (let j = i - config.atrPeriod + 1; j <= i; j++) {
                    sum += trs[j];
                }
                candles[i].atr = sum / config.atrPeriod;
            } else {
                candles[i].atr = NaN;
            }
        }
    }

    console.log("Indicators computed.");
}

// --- Helper Functions ---
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number' || isNaN(candle.open) || isNaN(candle.close)) {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji';
}

function getDayIdentifier(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toISOString().slice(0, 10);
}

// --- Entry Filter with New RSI Logic ---
function entryOK(dir, c3) {
    // Basic validation
    if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
        return false;
    }

    // Calculate RSI-MA separation (minimum distance between RSI and its MA)
    const rsiMaSeparation = Math.abs(c3.rsi - c3.rsiMa);
    const minSeparation = 0.5; // Minimum required separation (can be adjusted)

    // NEW RSI LOGIC with separation filter:
    // Long: RSI > RSI-MA AND price > WMA50 AND RSI-MA separation > minimum
    // Short: RSI < RSI-MA AND price < WMA50 AND RSI-MA separation > minimum

    if (dir === 'bullish') {
        return c3.rsi > c3.rsiMa && c3.close > c3.wma50 && rsiMaSeparation > minSeparation;
    } else if (dir === 'bearish') {
        return c3.rsi < c3.rsiMa && c3.close < c3.wma50 && rsiMaSeparation > minSeparation;
    }

    return false;
}

// --- Main Backtest Function ---
function runBacktest() {
    console.log("Running backtest with new RSI logic...");

    // Initialize variables
    let balance = config.initialBalance;
    let peakBalance = balance;
    let maxDrawdown = 0;
    let trades = 0;
    let wins = 0;
    let losses = 0;
    let position = null;
    let dailyPnL = new Map();
    let exitCounts = { sl: 0, tp: 0, trail: 0, color_flow_2bar: 0, end_of_period: 0 };

    // Start from the 4th candle to have enough lookback
    const startIdx = 3;

    // Loop through candles
    for (let i = startIdx; i < allCandles.length; i++) {
        const c0 = allCandles[i-3];
        const c1 = allCandles[i-2];
        const c2 = allCandles[i-1];
        const c3 = allCandles[i];

        // Skip if missing data
        if (!c3) continue;

        // Skip if indicators are not valid
        if (isNaN(c3.wma50) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
            continue;
        }

        // Check for entry if no position
        if (!position) {
            // Check for bullish signal
            if (c3.rsi > c3.rsiMa && c3.close > c3.wma50) {
                trades++;

                // Calculate entry parameters
                const entryPrice = c3.close;
                const atr = c3.atr;
                const slDistance = atr * config.slFactors;
                const tpDistance = Math.max(config.fixedTpPoints || 0, atr * config.tpFactors);
                const contracts = config.fixedContracts;

                // Create position object
                position = {
                    entryTime: new Date(c3.timestamp * 1000),
                    direction: 'bullish',
                    entry: entryPrice,
                    atr: atr,
                    stopLoss: entryPrice - slDistance,
                    takeProfit: entryPrice + tpDistance,
                    trailStop: entryPrice - (atr * config.trailFactors),
                    trailFactor: config.trailFactors,
                    trailHigh: c3.high,
                    trailLow: c3.low,
                    contracts: contracts,
                    entryBar: i
                };
            }
            // Check for bearish signal
            else if (c3.rsi < c3.rsiMa && c3.close < c3.wma50) {
                trades++;

                // Calculate entry parameters
                const entryPrice = c3.close;
                const atr = c3.atr;
                const slDistance = atr * config.slFactors;
                const tpDistance = Math.max(config.fixedTpPoints || 0, atr * config.tpFactors);
                const contracts = config.fixedContracts;

                // Create position object
                position = {
                    entryTime: new Date(c3.timestamp * 1000),
                    direction: 'bearish',
                    entry: entryPrice,
                    atr: atr,
                    stopLoss: entryPrice + slDistance,
                    takeProfit: entryPrice - tpDistance,
                    trailStop: entryPrice + (atr * config.trailFactors),
                    trailFactor: config.trailFactors,
                    trailHigh: c3.high,
                    trailLow: c3.low,
                    contracts: contracts,
                    entryBar: i
                };
            }
        }

        // Manage existing position
        if (position) {
            // Update trail values
            position.trailHigh = Math.max(position.trailHigh, c3.high);
            position.trailLow = Math.min(position.trailLow, c3.low);

            // Update trailing stop
            if (position.direction === 'bullish') {
                position.trailStop = Math.max(position.trailStop, position.trailHigh - (c3.atr * position.trailFactor));
            } else {
                position.trailStop = Math.min(position.trailStop, position.trailLow + (c3.atr * position.trailFactor));
            }

            // Check for exit conditions
            let exitReason = null;
            let exitPrice = null;

            if (position.direction === 'bullish') {
                // Stop loss hit
                if (c3.low <= position.stopLoss) {
                    exitReason = 'sl';
                    exitPrice = position.stopLoss;
                }
                // Take profit hit
                else if (c3.high >= position.takeProfit) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfit;
                }
                // Trailing stop hit
                else if (c3.low <= position.trailStop) {
                    exitReason = 'trail';
                    exitPrice = position.trailStop;
                }
                // Two-bar color exit
                else if (config.useTwoBarColorExit && i > position.entryBar + 1) {
                    const prevColor = candlestickColor(c2);
                    const curColor = candlestickColor(c3);
                    if (prevColor === 'red' && curColor === 'red') {
                        exitReason = 'color_flow_2bar';
                        exitPrice = c3.close;
                    }
                }
            } else { // Bearish position
                // Stop loss hit
                if (c3.high >= position.stopLoss) {
                    exitReason = 'sl';
                    exitPrice = position.stopLoss;
                }
                // Take profit hit
                else if (c3.low <= position.takeProfit) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfit;
                }
                // Trailing stop hit
                else if (c3.high >= position.trailStop) {
                    exitReason = 'trail';
                    exitPrice = position.trailStop;
                }
                // Two-bar color exit
                else if (config.useTwoBarColorExit && i > position.entryBar + 1) {
                    const prevColor = candlestickColor(c2);
                    const curColor = candlestickColor(c3);
                    if (prevColor === 'green' && curColor === 'green') {
                        exitReason = 'color_flow_2bar';
                        exitPrice = c3.close;
                    }
                }
            }

            // Process exit if triggered
            if (exitReason && exitPrice !== null) {
                // Apply slippage to exit price
                let adjustedExitPrice = exitPrice;
                if (['sl', 'trail', 'color_flow_2bar'].includes(exitReason) && config.slippagePoints > 0) {
                    adjustedExitPrice = position.direction === 'bullish' ?
                        exitPrice - config.slippagePoints :
                        exitPrice + config.slippagePoints;
                }

                // Calculate P&L
                const pnlPoints = position.direction === 'bullish' ?
                    adjustedExitPrice - position.entry :
                    position.entry - adjustedExitPrice;

                const pnlGross = pnlPoints * config.pointValue * position.contracts;
                const commissionCost = config.commissionPerContract * position.contracts;
                const pnlNet = pnlGross - commissionCost;

                // Update statistics
                balance += pnlNet;
                peakBalance = Math.max(peakBalance, balance);
                maxDrawdown = Math.max(maxDrawdown, peakBalance - balance);

                if (pnlNet > 0) {
                    wins++;
                } else {
                    losses++;
                }

                // Update exit counts
                exitCounts[exitReason] = (exitCounts[exitReason] || 0) + 1;

                // Update daily P&L
                const day = getDayIdentifier(c3.timestamp);
                dailyPnL.set(day, (dailyPnL.get(day) || 0) + pnlNet);

                // Log trade
                tradeLog.push({
                    EntryTime: position.entryTime.toISOString(),
                    ExitTime: new Date(c3.timestamp * 1000).toISOString(),
                    Direction: position.direction,
                    Entry: position.entry.toFixed(config.pricePrecision),
                    Exit: adjustedExitPrice.toFixed(config.pricePrecision),
                    Reason: exitReason,
                    PnL_Points: pnlPoints.toFixed(config.pricePrecision),
                    PnL_Net: pnlNet.toFixed(2),
                    Contracts: position.contracts,
                    Duration: i - position.entryBar + 1
                });

                // Reset position
                position = null;
            }
        }
    }

    // Close any open position at the end
    if (position) {
        const lastCandle = allCandles[allCandles.length - 1];
        const exitPrice = lastCandle.close;

        // Calculate P&L
        const pnlPoints = position.direction === 'bullish' ?
            exitPrice - position.entry :
            position.entry - exitPrice;

        const pnlGross = pnlPoints * config.pointValue * position.contracts;
        const commissionCost = config.commissionPerContract * position.contracts;
        const pnlNet = pnlGross - commissionCost;

        // Update statistics
        balance += pnlNet;
        peakBalance = Math.max(peakBalance, balance);
        maxDrawdown = Math.max(maxDrawdown, peakBalance - balance);

        if (pnlNet > 0) {
            wins++;
        } else {
            losses++;
        }

        // Update exit counts
        exitCounts['end_of_period'] = (exitCounts['end_of_period'] || 0) + 1;

        // Update daily P&L
        const day = getDayIdentifier(lastCandle.timestamp);
        dailyPnL.set(day, (dailyPnL.get(day) || 0) + pnlNet);

        // Log trade
        tradeLog.push({
            EntryTime: position.entryTime.toISOString(),
            ExitTime: new Date(lastCandle.timestamp * 1000).toISOString(),
            Direction: position.direction,
            Entry: position.entry.toFixed(config.pricePrecision),
            Exit: exitPrice.toFixed(config.pricePrecision),
            Reason: 'end_of_period',
            PnL_Points: pnlPoints.toFixed(config.pricePrecision),
            PnL_Net: pnlNet.toFixed(2),
            Contracts: position.contracts,
            Duration: allCandles.length - 1 - position.entryBar + 1
        });
    }

    // Calculate statistics
    const winRate = trades > 0 ? (wins / trades * 100) : 0;
    const profitFactor = losses > 0 ? (wins / losses) : (wins > 0 ? Infinity : 0);

    // Calculate daily statistics
    const days = dailyPnL.size;
    const winDays = Array.from(dailyPnL.values()).filter(pnl => pnl > 0).length;
    const winDayRate = days > 0 ? (winDays / days * 100) : 0;

    // Print results
    console.log("\n=== Backtest Results with New RSI Logic ===");
    console.log(`Initial Balance: $${config.initialBalance.toFixed(2)}`);
    console.log(`Final Balance: $${balance.toFixed(2)}`);
    console.log(`Net Profit: $${(balance - config.initialBalance).toFixed(2)}`);
    console.log(`Max Drawdown: $${maxDrawdown.toFixed(2)}`);
    console.log(`Total Trades: ${trades}`);
    console.log(`Winning Trades: ${wins}`);
    console.log(`Losing Trades: ${losses}`);
    console.log(`Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`Profit Factor: ${profitFactor === Infinity ? 'Infinity' : profitFactor.toFixed(2)}`);
    console.log(`Total Days: ${days}`);
    console.log(`Winning Days: ${winDays}`);
    console.log(`Win Day Rate: ${winDayRate.toFixed(2)}%`);
    console.log("\nExit Counts:");
    for (const [reason, count] of Object.entries(exitCounts)) {
        console.log(`  ${reason}: ${count} (${(count / trades * 100).toFixed(2)}%)`);
    }

    return {
        initialBalance: config.initialBalance,
        finalBalance: balance,
        netProfit: balance - config.initialBalance,
        maxDrawdown: maxDrawdown,
        totalTrades: trades,
        winningTrades: wins,
        losingTrades: losses,
        winRate: winRate,
        profitFactor: profitFactor === Infinity ? 'Infinity' : profitFactor,
        totalDays: days,
        winningDays: winDays,
        winDayRate: winDayRate,
        exitCounts: exitCounts
    };
}

// --- Save Trade Log ---
function saveTradeLog(trades, filename) {
    if (!trades || trades.length === 0) {
        console.log("No trades to save.");
        return;
    }

    // Create CSV header
    const headers = Object.keys(trades[0]);
    const csvContent = [
        headers.join(','),
        ...trades.map(trade => headers.map(header => trade[header]).join(','))
    ].join('\n');

    // Save to file
    fs.writeFileSync(filename, csvContent);
}
