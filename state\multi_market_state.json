{"isConnected": true, "lastConnectionTime": "2025-05-09T06:57:27.855Z", "markets": {"MNQ": {"enabled": true, "config": {"symbol": "MNQ", "contractMonth": "M5", "accountType": "DEMO", "initialBalance": 10000, "pointValue": 2, "tickSize": 0.25, "pricePrecision": 2, "commissionPerContract": 0.4, "slippagePoints": 0.75, "atrPeriod": 14, "rsiPeriod": 14, "rsiMaPeriod": 8, "sma200Period": 0, "wma50Period": 0, "fixedTpPoints": 40, "useWmaFilter": false, "useTwoBarColorExit": false, "minAtrEntry": 0, "minRsiMaSeparation": 0, "rsiUpperBand": 60, "rsiLowerBand": 40, "rsiMiddleBand": 50, "isAdaptiveRun": true, "slFactors": 4.5, "tpFactors": 3, "trailFactors": 0.11, "latencyDelayBars": 0, "riskPercent": 0, "fixedContracts": 3, "maxContracts": 10, "dailyStopLoss": 500, "dailyProfitTarget": 0, "maxDrawdownPercent": 5, "timeFilterEnabled": false, "useAdaptiveSlippage": false, "minSpreadPoints": 0.25, "defaultSpreadPoints": 0.5, "useVolatilityPositionSizing": false, "useDynamicPositionSizing": false, "basePositionSize": 3, "maxPositionSize": 3, "minPositionSize": 3, "useMLEntryFilter": false, "useAdvancedMLFilter": false, "mlEntryThreshold": 0.5, "useTimeAnalysis": false, "timeScoreThreshold": 0.5, "useCircuitBreakers": false, "maxSlippageMultiplier": 5, "pauseTradingOnExcessiveSlippage": false, "useSteppedTrail": false, "trailStepSizeMultiplier": 0.1, "spreadBufferMultiplier": 2}, "name": "Micro Nasdaq", "color": "#1E88E5", "contractMonth": "M5", "state": {"isTrading": false, "openPositions": [], "lastTradeTime": null, "equity": 10000, "dailyPnL": 0, "weeklyPnL": 0, "monthlyPnL": 0, "totalTrades": 0, "winningTrades": 0, "losingTrades": 0}}, "MGC": {"enabled": true, "config": {"symbol": "MGC", "contractMonth": "M5", "accountType": "DEMO", "initialBalance": 10000, "pointValue": 10, "tickSize": 0.1, "pricePrecision": 1, "commissionPerContract": 0.4, "slippagePoints": 0.1, "atrPeriod": 14, "rsiPeriod": 14, "rsiMaPeriod": 8, "sma200Period": 0, "wma50Period": 0, "fixedTpPoints": 0, "useWmaFilter": false, "useTwoBarColorExit": false, "minAtrEntry": 0, "minRsiMaSeparation": 0, "rsiUpperBand": 60, "rsiLowerBand": 40, "rsiMiddleBand": 50, "isAdaptiveRun": true, "slFactors": 8, "tpFactors": 7, "trailFactors": 0.02, "latencyDelayBars": 0, "riskPercent": 0, "fixedContracts": 3, "maxContracts": 3, "dailyStopLoss": 500, "dailyProfitTarget": 0, "maxDrawdownPercent": 5, "timeFilterEnabled": false, "useAdaptiveSlippage": false, "minSpreadPoints": 0.1, "defaultSpreadPoints": 0.2, "useVolatilityPositionSizing": false, "useDynamicPositionSizing": false, "basePositionSize": 3, "maxPositionSize": 3, "minPositionSize": 3, "useMLEntryFilter": false, "useAdvancedMLFilter": false, "mlEntryThreshold": 0.5, "useTimeAnalysis": false, "timeScoreThreshold": 0.5, "useCircuitBreakers": false, "maxSlippageMultiplier": 5, "pauseTradingOnExcessiveSlippage": false, "useSteppedTrail": false, "trailStepSizeMultiplier": 0.1, "spreadBufferMultiplier": 2}, "name": "Micro Gold", "color": "#FFD700", "contractMonth": "M5", "state": {"isTrading": false, "openPositions": [], "lastTradeTime": null, "equity": 10000, "dailyPnL": 0, "weeklyPnL": 0, "monthlyPnL": 0, "totalTrades": 0, "winningTrades": 0, "losingTrades": 0}}, "MES": {"enabled": false, "config": null, "name": "Micro S&P 500", "color": "#4CAF50", "contractMonth": "M5", "state": {"isTrading": false, "openPositions": [], "lastTradeTime": null, "equity": 10000, "dailyPnL": 0, "weeklyPnL": 0, "monthlyPnL": 0, "totalTrades": 0, "winningTrades": 0, "losingTrades": 0}}, "M2K": {"enabled": false, "config": null, "name": "Micro Russell 2000", "color": "#FF9800", "contractMonth": "M5", "state": {"isTrading": false, "openPositions": [], "lastTradeTime": null, "equity": 10000, "dailyPnL": 0, "weeklyPnL": 0, "monthlyPnL": 0, "totalTrades": 0, "winningTrades": 0, "losingTrades": 0}}}}