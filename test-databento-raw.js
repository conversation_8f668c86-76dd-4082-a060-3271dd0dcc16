/**
 * Test script for Databento integration using Raw API (TCP socket)
 */

require('dotenv').config();
const net = require('net');

// Use the exact API key from the Python example
const API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq";

console.log(`Using Databento API key: ${API_KEY.substring(0, 8)}...`);

// Create a TCP socket connection to Databento
console.log('Connecting to Databento Raw API...');

// Try different host/port combinations
const connections = [
  { host: 'api.databento.com', port: 8080 },
  { host: 'live.databento.com', port: 8080 },
  { host: 'raw.databento.com', port: 8080 }
];

let currentConnectionIndex = 0;
let socket = null;

function connectSocket() {
  if (currentConnectionIndex >= connections.length) {
    console.error('All connection attempts failed. Please check your API key and network connection.');
    process.exit(1);
  }

  const connection = connections[currentConnectionIndex];
  console.log(`Trying connection to ${connection.host}:${connection.port}`);
  
  socket = new net.Socket();
  
  socket.connect(connection.port, connection.host, () => {
    console.log('TCP socket connection established!');
    
    // Authenticate with API key
    console.log('Authenticating...');
    socket.write(JSON.stringify({
      action: 'auth',
      token: API_KEY
    }) + '\n');
    
    // Set a timeout for authentication response
    setTimeout(() => {
      if (!socket.destroyed) {
        console.log('Authentication successful or no explicit auth response. Subscribing...');
        
        // Subscribe to ES.FUT trades
        socket.write(JSON.stringify({
          action: 'subscribe',
          dataset: 'GLBX.MDP3',
          schema: 'trades',
          stype_in: 'parent',
          symbols: 'ES.FUT'
        }) + '\n');
        
        console.log('Subscription request sent. Waiting for data...');
      }
    }, 2000);
  });
  
  let buffer = '';
  socket.on('data', (data) => {
    buffer += data.toString();
    
    // Process complete messages
    let endIndex;
    while ((endIndex = buffer.indexOf('\n')) !== -1) {
      const message = buffer.substring(0, endIndex);
      buffer = buffer.substring(endIndex + 1);
      
      try {
        const parsedMessage = JSON.parse(message);
        console.log('Received message:', JSON.stringify(parsedMessage, null, 2));
        
        // If this is a subscription response, check if it was successful
        if (parsedMessage.type === 'subscription_response') {
          if (parsedMessage.status === 'success') {
            console.log('Subscription successful!');
          } else {
            console.error('Subscription failed:', parsedMessage.message);
          }
        }
        
        // If this is market data, display it
        if (parsedMessage.type === 'data') {
          console.log('Received market data!');
        }
      } catch (error) {
        console.log('Received raw message:', message);
      }
    }
  });
  
  socket.on('error', (error) => {
    console.error(`Socket error with ${connection.host}:${connection.port}:`, error.message);
    socket.destroy();
    
    // Try the next connection
    currentConnectionIndex++;
    connectSocket();
  });
  
  socket.on('close', () => {
    console.log('Socket connection closed.');
  });
}

// Start the connection process
connectSocket();

// Keep the process running
const interval = setInterval(() => {
  if (socket && socket.destroyed) {
    console.log('Socket connection destroyed. Exiting...');
    clearInterval(interval);
    process.exit(0);
  }
}, 1000);

// Handle process termination
process.on('SIGINT', () => {
  console.log('Received SIGINT. Closing socket connection...');
  if (socket) {
    socket.destroy();
  }
  clearInterval(interval);
  process.exit(0);
});

// Set a timeout for the entire script
setTimeout(() => {
  console.log('Test timeout reached. Exiting...');
  if (socket) {
    socket.destroy();
  }
  clearInterval(interval);
  process.exit(0);
}, 30000); // 30 seconds
