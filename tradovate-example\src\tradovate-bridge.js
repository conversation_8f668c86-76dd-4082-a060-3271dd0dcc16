/**
 * tradovate-bridge.js
 * Bridge module to connect backtesting logic to Tradovate API for live trading
 */

import { TradovateSocket } from '../TradovateSocket.js';

// Import utility modules
import * as spreadUtils from '../../spread_volatility_utils.js';
import * as enhancedPM from '../../enhanced_position_management.js';
import { getConfigBySymbol } from '../../multi_symbol_config.js';

class TradovateBridge {
    constructor(config, credentials, urls) {
        // Store configuration
        this.config = config;
        this.credentials = credentials;
        this.urls = urls;

        // Initialize state
        this.socket = null;
        this.marketDataSocket = null;
        this.accessToken = null;
        this.userId = null;
        this.accountId = null;
        this.accountName = null;
        this.positions = new Map();
        this.orders = new Map();
        this.candles = [];
        this.recentTrades = [];
        this.marketDataSubscriptions = new Map();
        this.isConnected = false;
        this.balance = 0;

        // Initialize indicators
        this.indicators = {
            sma200: [],
            wma50: [],
            rsi: [],
            rsiMa: [],
            atr: []
        };

        // Callbacks
        this.onConnected = null;
        this.onDisconnected = null;
        this.onError = null;
        this.onTrade = null;
        this.onPositionUpdate = null;
        this.onMarketData = null;

        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.subscribeToMarketData = this.subscribeToMarketData.bind(this);
        this.unsubscribeFromMarketData = this.unsubscribeFromMarketData.bind(this);
        this.placeOrder = this.placeOrder.bind(this);
        this.cancelOrder = this.cancelOrder.bind(this);
        this.getAccounts = this.getAccounts.bind(this);
        this.syncUserData = this.syncUserData.bind(this);
        this.processCandle = this.processCandle.bind(this);
        this.calculateIndicators = this.calculateIndicators.bind(this);
        this.detectPattern = this.detectPattern.bind(this);
        this.checkEntrySignal = this.checkEntrySignal.bind(this);
        this.managePositions = this.managePositions.bind(this);
    }

    /**
     * Connect to Tradovate API
     * @returns {Promise<boolean>} - True if connection successful
     */
    async connect() {
        try {
            console.log('Connecting to Tradovate API...');

            // Create main socket
            this.socket = new TradovateSocket({ debugLabel: 'Main API' });

            // Authenticate and get access token
            const authResponse = await this.socket.send({
                url: 'auth/accesstokenrequest',
                method: 'POST',
                body: {
                    name: this.credentials.name,
                    password: this.credentials.password,
                    appId: this.credentials.appId,
                    appVersion: this.credentials.appVersion,
                    cid: this.credentials.cid,
                    sec: this.credentials.sec
                }
            });

            if (!authResponse || !authResponse.accessToken) {
                throw new Error('Authentication failed: No access token received');
            }

            this.accessToken = authResponse.accessToken;
            this.userId = authResponse.userId;

            // Connect to WebSocket
            await this.socket.connect(this.urls.WS_DEMO_URL, this.accessToken, () => {
                console.log('Main WebSocket connected and authorized');

                // Get user accounts
                this.getAccounts().then(() => {
                    // Sync user data
                    this.syncUserData();

                    // Connect to market data WebSocket
                    this.connectToMarketData();

                    this.isConnected = true;

                    // Call onConnected callback if provided
                    if (typeof this.onConnected === 'function') {
                        this.onConnected();
                    }
                });
            });

            return true;
        } catch (error) {
            console.error('Error connecting to Tradovate API:', error);

            // Call onError callback if provided
            if (typeof this.onError === 'function') {
                this.onError(error);
            }

            return false;
        }
    }

    /**
     * Connect to market data WebSocket
     * @returns {Promise<boolean>} - True if connection successful
     */
    async connectToMarketData() {
        try {
            console.log('Connecting to Market Data API...');

            // Create market data socket
            this.marketDataSocket = new TradovateSocket({ debugLabel: 'Market Data API' });

            // Connect to market data WebSocket
            await this.marketDataSocket.connect(this.urls.MD_DEMO_WS_URL, this.accessToken, () => {
                console.log('Market Data WebSocket connected and authorized');
            });

            return true;
        } catch (error) {
            console.error('Error connecting to Market Data API:', error);

            // Call onError callback if provided
            if (typeof this.onError === 'function') {
                this.onError(error);
            }

            return false;
        }
    }

    /**
     * Disconnect from Tradovate API
     */
    disconnect() {
        // Unsubscribe from all market data
        for (const [symbol, unsubscribe] of this.marketDataSubscriptions.entries()) {
            if (typeof unsubscribe === 'function') {
                unsubscribe();
                console.log(`Unsubscribed from market data for ${symbol}`);
            }
        }

        // Clear market data subscriptions
        this.marketDataSubscriptions.clear();

        // Disconnect from market data WebSocket
        if (this.marketDataSocket) {
            this.marketDataSocket.disconnect();
            this.marketDataSocket = null;
        }

        // Disconnect from main WebSocket
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }

        this.isConnected = false;

        // Call onDisconnected callback if provided
        if (typeof this.onDisconnected === 'function') {
            this.onDisconnected();
        }

        console.log('Disconnected from Tradovate API');
    }

    /**
     * Get user accounts
     * @returns {Promise<Array>} - Array of accounts
     */
    async getAccounts() {
        try {
            const accounts = await this.socket.send({ url: 'user/accounts' });

            if (!accounts || accounts.length === 0) {
                throw new Error('No accounts found');
            }

            // Use the first account
            const account = accounts[0];
            this.accountId = account.id;
            this.accountName = account.name;

            console.log(`Using account: ${this.accountName} (ID: ${this.accountId})`);

            return accounts;
        } catch (error) {
            console.error('Error getting accounts:', error);
            throw error;
        }
    }

    /**
     * Sync user data (positions, orders, etc.)
     */
    async syncUserData() {
        try {
            // Subscribe to user sync
            const unsubscribe = await this.socket.subscribe({
                url: 'user/syncrequest',
                body: { users: [this.userId] },
                subscription: (data) => {
                    // Process initial response
                    if (data.users) {
                        // Process positions
                        if (data.positions) {
                            this.processPositions(data.positions, data.contracts, data.products);
                        }

                        // Process orders
                        if (data.orders) {
                            this.processOrders(data.orders);
                        }

                        // Process cash balances
                        if (data.cashBalances) {
                            this.processCashBalances(data.cashBalances);
                        }
                    }

                    // Process incremental updates
                    if (data.changes) {
                        this.processChanges(data.changes);
                    }
                }
            });

            console.log('Subscribed to user sync');
        } catch (error) {
            console.error('Error syncing user data:', error);
            throw error;
        }
    }

    /**
     * Process positions from user sync
     * @param {Array} positions - Array of positions
     * @param {Array} contracts - Array of contracts
     * @param {Array} products - Array of products
     */
    processPositions(positions, contracts, products) {
        // Clear existing positions
        this.positions.clear();

        // Process each position
        for (const position of positions) {
            // Skip positions with no net position
            if (position.netPos === 0 && position.prevPos === 0) {
                continue;
            }

            // Find contract
            const contract = contracts.find(c => c.id === position.contractId);
            if (!contract) {
                continue;
            }

            // Find product
            const product = products.find(p => p.name.startsWith(contract.name.substring(0, 3)));
            if (!product) {
                continue;
            }

            // Store position with additional data
            this.positions.set(contract.name, {
                ...position,
                contractName: contract.name,
                valuePerPoint: product.valuePerPoint,
                product: product.name
            });

            // Subscribe to market data for this contract if not already subscribed
            if (!this.marketDataSubscriptions.has(contract.name)) {
                this.subscribeToMarketData(contract.name);
            }
        }

        console.log(`Processed ${this.positions.size} positions`);
    }

    /**
     * Process orders from user sync
     * @param {Array} orders - Array of orders
     */
    processOrders(orders) {
        // Clear existing orders
        this.orders.clear();

        // Process each order
        for (const order of orders) {
            // Skip filled or canceled orders
            if (order.status === 'Filled' || order.status === 'Canceled') {
                continue;
            }

            // Store order
            this.orders.set(order.id, order);
        }

        console.log(`Processed ${this.orders.size} active orders`);
    }

    /**
     * Process cash balances from user sync
     * @param {Array} cashBalances - Array of cash balances
     */
    processCashBalances(cashBalances) {
        // Find cash balance for current account
        const accountBalance = cashBalances.find(cb => cb.accountId === this.accountId);

        if (accountBalance) {
            this.balance = accountBalance.amount;
            console.log(`Account balance: $${this.balance.toFixed(2)}`);
        }
    }

    /**
     * Process changes from user sync
     * @param {Object} changes - Changes object
     */
    processChanges(changes) {
        // Process position changes
        if (changes.positions) {
            for (const position of changes.positions) {
                // Find contract
                const contract = changes.contracts?.find(c => c.id === position.contractId);

                if (contract) {
                    // Update or add position
                    this.positions.set(contract.name, {
                        ...position,
                        contractName: contract.name,
                        valuePerPoint: this.positions.get(contract.name)?.valuePerPoint || 1
                    });

                    // Call onPositionUpdate callback if provided
                    if (typeof this.onPositionUpdate === 'function') {
                        this.onPositionUpdate(contract.name, this.positions.get(contract.name));
                    }
                }
            }
        }

        // Process order changes
        if (changes.orders) {
            for (const order of changes.orders) {
                if (order.status === 'Filled') {
                    // Remove filled order
                    this.orders.delete(order.id);

                    // Call onTrade callback if provided
                    if (typeof this.onTrade === 'function') {
                        this.onTrade(order);
                    }

                    // Add to recent trades
                    this.recentTrades.push(order);

                    // Keep only the last 20 trades
                    if (this.recentTrades.length > 20) {
                        this.recentTrades.shift();
                    }
                } else if (order.status === 'Canceled') {
                    // Remove canceled order
                    this.orders.delete(order.id);
                } else {
                    // Update or add order
                    this.orders.set(order.id, order);
                }
            }
        }

        // Process cash balance changes
        if (changes.cashBalances) {
            const accountBalance = changes.cashBalances.find(cb => cb.accountId === this.accountId);

            if (accountBalance) {
                this.balance = accountBalance.amount;
                console.log(`Account balance updated: $${this.balance.toFixed(2)}`);
            }
        }
    }

    /**
     * Subscribe to market data for a symbol
     * @param {string} symbol - Symbol to subscribe to
     * @returns {Promise<Function>} - Unsubscribe function
     */
    async subscribeToMarketData(symbol) {
        try {
            // Check if already subscribed
            if (this.marketDataSubscriptions.has(symbol)) {
                console.log(`Already subscribed to market data for ${symbol}`);
                return this.marketDataSubscriptions.get(symbol);
            }

            console.log(`Subscribing to market data for ${symbol}...`);

            // Subscribe to quotes
            const unsubscribeQuote = await this.marketDataSocket.subscribe({
                url: 'md/subscribequote',
                body: { symbol },
                subscription: (data) => {
                    if (data && data.quotes && data.quotes.length > 0) {
                        const quote = data.quotes[0];

                        // Process quote data
                        this.processQuote(symbol, quote);
                    }
                }
            });

            // Subscribe to chart data
            const unsubscribeChart = await this.marketDataSocket.subscribe({
                url: 'md/getchart',
                body: {
                    symbol,
                    chartDescription: {
                        underlyingType: 'MinuteBar',
                        elementSize: 1,
                        elementSizeUnit: 'UnderlyingUnits',
                        withHistogram: false
                    },
                    timeRange: {
                        asMuchAsElements: 200 // Get 200 bars for initial history
                    }
                },
                subscription: (chart) => {
                    // Process chart data
                    this.processChart(symbol, chart);
                }
            });

            // Create combined unsubscribe function
            const unsubscribe = () => {
                unsubscribeQuote();
                unsubscribeChart();
                this.marketDataSubscriptions.delete(symbol);
            };

            // Store unsubscribe function
            this.marketDataSubscriptions.set(symbol, unsubscribe);

            console.log(`Successfully subscribed to market data for ${symbol}`);

            return unsubscribe;
        } catch (error) {
            console.error(`Error subscribing to market data for ${symbol}:`, error);
            throw error;
        }
    }

    /**
     * Unsubscribe from market data for a symbol
     * @param {string} symbol - Symbol to unsubscribe from
     */
    unsubscribeFromMarketData(symbol) {
        const unsubscribe = this.marketDataSubscriptions.get(symbol);

        if (typeof unsubscribe === 'function') {
            unsubscribe();
            this.marketDataSubscriptions.delete(symbol);
            console.log(`Unsubscribed from market data for ${symbol}`);
        }
    }

    /**
     * Process quote data
     * @param {string} symbol - Symbol
     * @param {Object} quote - Quote data
     */
    processQuote(symbol, quote) {
        // Update position P&L if we have a position for this symbol
        if (this.positions.has(symbol) && quote.entries && quote.entries.Trade) {
            const position = this.positions.get(symbol);
            const currentPrice = quote.entries.Trade.price;

            // Calculate P&L
            const buyPrice = position.netPrice || position.prevPrice;
            const pl = (currentPrice - buyPrice) * position.valuePerPoint * position.netPos;

            // Update position with current P&L
            this.positions.set(symbol, {
                ...position,
                currentPrice,
                currentPL: pl
            });

            // Call onPositionUpdate callback if provided
            if (typeof this.onPositionUpdate === 'function') {
                this.onPositionUpdate(symbol, this.positions.get(symbol));
            }
        }

        // Call onMarketData callback if provided
        if (typeof this.onMarketData === 'function') {
            this.onMarketData('quote', symbol, quote);
        }
    }

    /**
     * Process chart data
     * @param {string} symbol - Symbol
     * @param {Object} chart - Chart data
     */
    processChart(symbol, chart) {
        // Check for end of history
        if (chart.eoh) {
            console.log(`End of chart history reached for ${symbol}`);
            return;
        }

        // Process chart bars
        if (chart.bars && chart.bars.length > 0) {
            // Add bars to candles array
            for (const bar of chart.bars) {
                // Convert to candle format used by backtest code
                const candle = {
                    timestamp: Math.floor(new Date(bar.timestamp).getTime() / 1000),
                    open: bar.open,
                    high: bar.high,
                    low: bar.low,
                    close: bar.close,
                    volume: (bar.upVolume || 0) + (bar.downVolume || 0),
                    symbol
                };

                // Process candle (calculate indicators, check for signals)
                this.processCandle(candle);
            }

            // Call onMarketData callback if provided
            if (typeof this.onMarketData === 'function') {
                this.onMarketData('chart', symbol, chart);
            }
        }
    }

    /**
     * Process a single candle
     * @param {Object} candle - Candle data
     */
    processCandle(candle) {
        // Add candle to candles array
        this.candles.push(candle);

        // Keep only the last 500 candles
        // Need at least 200 for SMA200 calculation
        if (this.candles.length > 500) {
            this.candles.shift();
        }

        // Calculate indicators
        this.calculateIndicators();

        // Check for entry signals
        const entrySignal = this.checkEntrySignal();

        if (entrySignal) {
            console.log(`Entry signal detected: ${entrySignal.direction} ${entrySignal.symbol}`);

            // Place order based on entry signal
            this.placeOrder(entrySignal);
        }

        // Manage existing positions
        this.managePositions();
    }

    /**
     * Calculate indicators for the current candles
     */
    calculateIndicators() {
        if (this.candles.length < 200) {
            // Not enough data for indicators
            return;
        }

        const closes = this.candles.map(c => c.close);

        // Calculate SMA200
        if (this.config.sma200Period > 0) {
            const sma200 = this.SMA(closes, this.config.sma200Period, closes.length - 1);
            this.candles[this.candles.length - 1].sma200 = sma200;
        }

        // Calculate WMA50
        if (this.config.wma50Period > 0) {
            const wma50 = this.WMA(closes, this.config.wma50Period, closes.length - 1);
            this.candles[this.candles.length - 1].wma50 = wma50;
        }

        // Calculate RSI
        if (this.config.rsiPeriod > 0) {
            const rsi = this.RSI(closes, this.config.rsiPeriod, closes.length - 1);
            this.candles[this.candles.length - 1].rsi = rsi;

            // Calculate RSI MA
            if (this.config.rsiMaPeriod > 0) {
                const rsiValues = this.candles.slice(-this.config.rsiPeriod - this.config.rsiMaPeriod).map(c => c.rsi).filter(r => !isNaN(r));
                if (rsiValues.length >= this.config.rsiMaPeriod) {
                    const rsiMa = this.SMA(rsiValues, this.config.rsiMaPeriod, rsiValues.length - 1);
                    this.candles[this.candles.length - 1].rsiMa = rsiMa;
                }
            }
        }

        // Calculate ATR
        if (this.config.atrPeriod > 0) {
            const atr = this.ATR(this.candles, this.config.atrPeriod, this.candles.length - 1);
            this.candles[this.candles.length - 1].atr = atr;
        }
    }

    /**
     * Place an order based on entry signal
     * @param {Object} signal - Entry signal
     * @returns {Promise<Object>} - Order response
     */
    async placeOrder(signal) {
        try {
            // Get current candle and ATR
            const currentCandle = this.candles[this.candles.length - 1];
            const atr = currentCandle.atr;

            // Get symbol-specific configuration
            const symbolConfig = getConfigBySymbol(signal.symbol);

            // Determine ATR regime using symbol-specific thresholds
            const atrRegime = atr < symbolConfig.atrThresholds.low_medium ? 'Low' :
                             (atr > symbolConfig.atrThresholds.medium_high ? 'High' : 'Medium');

            console.log(`Symbol: ${signal.symbol}, ATR: ${atr}, ATR Regime: ${atrRegime}`);

            // Get adaptive parameters based on ATR regime
            let slFactor, tpFactor, trailFactor;

            if (symbolConfig.isAdaptiveRun) {
                // Use adaptive parameters based on ATR regime
                slFactor = symbolConfig.adaptiveParams[atrRegime].slFactor;
                tpFactor = symbolConfig.adaptiveParams[atrRegime].tpFactor;
                trailFactor = symbolConfig.adaptiveParams[atrRegime].trailFactor;

                console.log(`Using adaptive parameters for ${signal.symbol} (${atrRegime}): SL=${slFactor}, TP=${tpFactor}, Trail=${trailFactor}`);
            } else {
                // Use fixed parameters
                slFactor = symbolConfig.slFactors;
                tpFactor = symbolConfig.tpFactors;
                trailFactor = symbolConfig.trailFactors;

                console.log(`Using fixed parameters for ${signal.symbol}: SL=${slFactor}, TP=${tpFactor}, Trail=${trailFactor}`);
            }

            // Calculate stop loss and take profit distances
            const slDistance = atr * slFactor;
            const atrTpDistance = atr * tpFactor;
            const fixTpDistance = symbolConfig.fixedTpPoints || 0;
            const tpDistance = Math.max(fixTpDistance, atrTpDistance);

            // Calculate stop loss and take profit prices
            const stopLossPrice = signal.direction === 'bullish' ?
                currentCandle.close - slDistance :
                currentCandle.close + slDistance;

            const takeProfitPrice = signal.direction === 'bullish' ?
                currentCandle.close + tpDistance :
                currentCandle.close - tpDistance;

            // Calculate trail stop price
            const trailStopPrice = signal.direction === 'bullish' ?
                currentCandle.close - (atr * trailFactor) :
                currentCandle.close + (atr * trailFactor);

            // Calculate position size
            let contracts = symbolConfig.fixedContracts;

            // Use fixed contracts as specified in the symbol config
            console.log(`Using fixed contracts: ${contracts} contracts for ${signal.symbol}`);

            // Note: We're not using volatility-based or dynamic position sizing as per your configuration

            // Ensure minimum and maximum contracts
            contracts = Math.max(1, Math.min(contracts, symbolConfig.maxContracts || 10));

            // Create order
            const order = {
                accountId: this.accountId,
                symbol: signal.symbol,
                orderQty: contracts,
                orderType: 'Market',
                action: signal.direction === 'bullish' ? 'Buy' : 'Sell',
                isAutomated: true // Required for algorithmic trading
            };

            console.log(`Placing ${order.action} order for ${order.orderQty} ${order.symbol}...`);

            // Place order
            const response = await this.socket.send({
                url: 'order/placeorder',
                method: 'POST',
                body: order
            });

            console.log(`Order placed: ${response.orderId}`);

            // Store position information for management
            const position = {
                symbol: signal.symbol,
                dir: signal.direction,
                entry: currentCandle.close,
                atr: atr,
                tpDistance: tpDistance,
                slDistance: slDistance,
                stopLossPrice: stopLossPrice,
                takeProfitPrice: takeProfitPrice,
                trailStopPrice: trailStopPrice,
                trailFactor: trailFactor,
                entryAtrRegime: atrRegime,
                trailHigh: currentCandle.high,
                trailLow: currentCandle.low,
                entryBarIndex: this.candles.length - 1,
                tpType: tpDistance === fixTpDistance ? 'Fixed' : 'ATR',
                contracts: contracts,
                entryTimestamp: new Date(currentCandle.timestamp * 1000),
                pointValue: symbolConfig.pointValue,
                tickSize: symbolConfig.tickSize,
                symbolConfig: symbolConfig // Store the symbol-specific config for future reference
            };

            // Store position in memory (will be updated by position updates from API)
            this.positions.set(signal.symbol, {
                ...this.positions.get(signal.symbol),
                ...position
            });

            return response;
        } catch (error) {
            console.error('Error placing order:', error);
            throw error;
        }
    }

    /**
     * Cancel an order
     * @param {number} orderId - Order ID
     * @returns {Promise<Object>} - Cancel response
     */
    async cancelOrder(orderId) {
        try {
            console.log(`Canceling order ${orderId}...`);

            // Cancel order
            const response = await this.socket.send({
                url: 'order/cancelorder',
                method: 'POST',
                body: { orderId }
            });

            console.log(`Order canceled: ${orderId}`);

            return response;
        } catch (error) {
            console.error(`Error canceling order ${orderId}:`, error);
            throw error;
        }
    }

    /**
     * Check for entry signals
     * @returns {Object|null} - Entry signal or null
     */
    checkEntrySignal() {
        if (this.candles.length < 4) {
            return null;
        }

        const currentIndex = this.candles.length - 1;
        const c0 = this.candles[currentIndex - 3];
        const c1 = this.candles[currentIndex - 2];
        const c2 = this.candles[currentIndex - 1];
        const c3 = this.candles[currentIndex];

        // Check if indicators are available
        if (isNaN(c3.wma50) || isNaN(c3.sma200) || isNaN(c3.rsi) || isNaN(c3.rsiMa) || isNaN(c3.atr) || c3.atr <= 0) {
            return null;
        }

        // Detect patterns
        const p3 = this.detect3(c1, c2, c3);
        const p4 = this.detect4(c0, c1, c2, c3);

        let pattern = null;
        let patternType = null;

        if (p4) {
            pattern = p4;
            patternType = 'four';
        } else if (p3) {
            pattern = p3;
            patternType = 'three';
        }

        // Check if entry is OK
        if (pattern && this.entryOK(pattern, patternType, c3, currentIndex, this.candles)) {
            return {
                symbol: c3.symbol,
                direction: pattern,
                patternType,
                timestamp: c3.timestamp,
                price: c3.close,
                atr: c3.atr
            };
        }

        return null;
    }

    /**
     * Manage existing positions
     */
    managePositions() {
        if (this.positions.size === 0 || this.candles.length < 1) {
            return;
        }

        const currentCandle = this.candles[this.candles.length - 1];
        const currentIndex = this.candles.length - 1;

        // Check each position
        for (const [symbol, position] of this.positions.entries()) {
            // Skip positions with no net position
            if (position.netPos === 0) {
                continue;
            }

            // Check if we have market data for this symbol
            if (currentCandle.symbol !== symbol) {
                continue;
            }

            // Get symbol-specific configuration
            const symbolConfig = position.symbolConfig || getConfigBySymbol(symbol);

            // Get recent candles for spread estimation (look back 10 candles)
            const lookbackStart = Math.max(0, currentIndex - 10);
            const recentCandles = this.candles.slice(lookbackStart, currentIndex + 1);

            // Estimate current spread
            const estimatedSpread = spreadUtils.estimateSpread(recentCandles, symbolConfig);

            // Calculate adaptive slippage
            const adaptiveSlippage = symbolConfig.useAdaptiveSlippage ?
                spreadUtils.calculateAdaptiveSlippage(recentCandles, symbolConfig) :
                symbolConfig.slippagePoints || 0.5;

            // Get ATR for trail calculation
            const atrTrail = (typeof currentCandle.atr === 'number' && !isNaN(currentCandle.atr) && currentCandle.atr > 0) ?
                currentCandle.atr : position.atr || 1.0;

            // Store the trail stop value before potential update this bar
            const trailStopBeforeUpdate = position.trailStopPrice ||
                (position.dir === 'bullish' ? position.entry - (atrTrail * position.trailFactor) :
                                             position.entry + (atrTrail * position.trailFactor));

            // --- ENHANCED: Stepped Trail Stop Logic ---
            // 1. Calculate raw trail distance with spread buffer
            const spreadBuffer = estimatedSpread * 1.2; // Reduced buffer to 20% to avoid premature exits

            // Increase trail factor by 50% to make it less sensitive
            const adjustedTrailF = position.trailFactor * 1.5;
            const rawTrailDistance = (atrTrail * adjustedTrailF);

            // 2. Apply stepped trail logic - trail moves in steps rather than continuously
            const trailStepSize = Math.max(atrTrail * (symbolConfig.trailStepSizeMultiplier || 0.05), symbolConfig.tickSize || 0.25);
            const trailSteps = Math.floor(rawTrailDistance / trailStepSize);
            const steppedTrailDistance = trailSteps * trailStepSize;

            // 3. Update trail high/low and potential new stop price based on current candle
            let potentialNewTrailStopPrice;

            if (position.dir === 'bullish') {
                // For long positions, track new highs
                position.trailHigh = Math.max(position.trailHigh || currentCandle.high, currentCandle.high);

                // Calculate potential new trail stop with stepped approach
                potentialNewTrailStopPrice = position.trailHigh - steppedTrailDistance;

                // Only move trail stop up (never down for long positions)
                position.trailStopPrice = Math.max(trailStopBeforeUpdate, potentialNewTrailStopPrice);
            } else {
                // For short positions, track new lows
                position.trailLow = Math.min(position.trailLow || currentCandle.low, currentCandle.low);

                // Calculate potential new trail stop with stepped approach
                potentialNewTrailStopPrice = position.trailLow + steppedTrailDistance;

                // Only move trail stop down (never up for short positions)
                position.trailStopPrice = Math.min(trailStopBeforeUpdate, potentialNewTrailStopPrice);
            }

            // --- Check Exit Conditions ---
            let exitReason = null, exitPrice = null;

            // --- ENHANCED: Intra-bar Trail Stop Check with Spread Buffer ---
            if (position.dir === 'bullish') {
                // For long positions, check if price went below trail stop
                const trailTriggerLevel = trailStopBeforeUpdate - spreadBuffer;

                // Only trigger if the low went significantly below the trail stop
                // This helps avoid premature exits due to minor price fluctuations
                if (currentCandle.low <= trailTriggerLevel - (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'trail';
                    exitPrice = trailStopBeforeUpdate;
                }
                // Stop loss with spread buffer - only trigger if significantly breached
                else if (position.stopLossPrice && currentCandle.low <= position.stopLossPrice - spreadBuffer - (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'sl';
                    exitPrice = position.stopLossPrice;
                }
                // Take profit - make sure it's a significant move beyond TP level
                else if (position.takeProfitPrice && currentCandle.high >= position.takeProfitPrice + (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfitPrice;
                }
                // Trail stop with spread buffer - only trigger if significantly breached
                else if (currentCandle.low <= position.trailStopPrice - spreadBuffer - (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'trail';
                    exitPrice = position.trailStopPrice;
                }
            } else {
                // For short positions, check if price went above trail stop
                const trailTriggerLevel = trailStopBeforeUpdate + spreadBuffer;

                // Only trigger if the high went significantly above the trail stop
                // This helps avoid premature exits due to minor price fluctuations
                if (currentCandle.high >= trailTriggerLevel + (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'trail';
                    exitPrice = trailStopBeforeUpdate;
                }
                // Stop loss with spread buffer - only trigger if significantly breached
                else if (position.stopLossPrice && currentCandle.high >= position.stopLossPrice + spreadBuffer + (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'sl';
                    exitPrice = position.stopLossPrice;
                }
                // Take profit - make sure it's a significant move beyond TP level
                else if (position.takeProfitPrice && currentCandle.low <= position.takeProfitPrice - (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'tp';
                    exitPrice = position.takeProfitPrice;
                }
                // Trail stop with spread buffer - only trigger if significantly breached
                else if (currentCandle.high >= position.trailStopPrice + spreadBuffer + (symbolConfig.tickSize || 0.25)) {
                    exitReason = 'trail';
                    exitPrice = position.trailStopPrice;
                }
            }

            // Check for two-bar color exit if enabled
            if (!exitReason && this.config.useTwoBarColorExit && this.candles.length > 1) {
                const prevCandle = this.candles[this.candles.length - 2];
                const prevColor = this.candlestickColor(prevCandle);
                const curColor = this.candlestickColor(currentCandle);

                if (position.dir === 'bullish' && prevColor === 'red' && curColor === 'red') {
                    exitReason = 'color_flow_2bar';
                    exitPrice = currentCandle.close;
                } else if (position.dir === 'bearish' && prevColor === 'green' && curColor === 'green') {
                    exitReason = 'color_flow_2bar';
                    exitPrice = currentCandle.close;
                }
            }

            // --- Handle Exit Execution with Adaptive Slippage ---
            if (exitReason && exitPrice !== null) {
                console.log(`Exit signal detected: ${exitReason} for ${symbol} at ${exitPrice}`);

                // Apply adaptive slippage
                let finalExitPrice = exitPrice;
                if (exitReason === 'sl' || exitReason === 'trail' || exitReason === 'color_flow_2bar') {
                    finalExitPrice = position.dir === 'bullish' ?
                        exitPrice - adaptiveSlippage :
                        exitPrice + adaptiveSlippage;
                }

                // Get symbol-specific configuration if not already in position
                const symbolConfig = position.symbolConfig || getConfigBySymbol(symbol);

                // Calculate P&L
                const pnlPoints = position.dir === 'bullish' ?
                    finalExitPrice - position.entry :
                    position.entry - finalExitPrice;

                const pointValue = position.pointValue || symbolConfig.pointValue;
                const pnlGross = pnlPoints * pointValue * position.netPos;
                const commCost = symbolConfig.commissionPerContract * position.netPos;
                const pnlNet = pnlGross - commCost;

                console.log(`Closing position: ${position.dir} ${position.netPos} ${symbol} @ ${finalExitPrice} (P&L: ${pnlNet.toFixed(2)})`);

                // Place market order to close position
                this.placeCloseOrder(symbol, position.netPos, position.dir, finalExitPrice, exitReason);
            }
        }
    }

    /**
     * Place an order to close a position
     * @param {string} symbol - Symbol
     * @param {number} quantity - Position quantity
     * @param {string} direction - Position direction ('bullish' or 'bearish')
     * @param {number} price - Exit price
     * @param {string} reason - Exit reason
     * @returns {Promise<Object>} - Order response
     */
    async placeCloseOrder(symbol, quantity, direction, price, reason) {
        try {
            // Create order
            const order = {
                accountId: this.accountId,
                symbol: symbol,
                orderQty: quantity,
                orderType: 'Market',
                action: direction === 'bullish' ? 'Sell' : 'Buy', // Opposite of position direction
                isAutomated: true // Required for algorithmic trading
            };

            console.log(`Placing ${order.action} order for ${order.orderQty} ${order.symbol} to close position...`);

            // Place order
            const response = await this.socket.send({
                url: 'order/placeorder',
                method: 'POST',
                body: order
            });

            console.log(`Close order placed: ${response.orderId}`);

            // Get symbol-specific configuration
            const position = this.positions.get(symbol);
            const symbolConfig = position?.symbolConfig || getConfigBySymbol(symbol);
            const pointValue = position?.pointValue || symbolConfig.pointValue;

            // Log the trade
            this.logTrade({
                symbol: symbol,
                direction: direction,
                quantity: quantity,
                entryPrice: position?.entry || 0,
                exitPrice: price,
                exitReason: reason,
                pnl: (direction === 'bullish' ? price - (position?.entry || 0) : (position?.entry || 0) - price) *
                     quantity * pointValue - (symbolConfig.commissionPerContract * quantity)
            });

            return response;
        } catch (error) {
            console.error('Error placing close order:', error);
            throw error;
        }
    }

    /**
     * Log a completed trade
     * @param {Object} trade - Trade object
     */
    logTrade(trade) {
        // Add trade to recent trades
        this.recentTrades.push({
            ...trade,
            timestamp: new Date().toISOString()
        });

        // Keep only the last 20 trades
        if (this.recentTrades.length > 20) {
            this.recentTrades.shift();
        }

        // Call onTrade callback if provided
        if (typeof this.onTrade === 'function') {
            this.onTrade(trade);
        }
    }

    /**
     * Get candle color
     * @param {Object} candle - Candle data
     * @returns {string} - Candle color ('green', 'red', 'doji', or 'invalid')
     */
    candlestickColor(candle) {
        if (!candle ||
            typeof candle.open !== 'number' ||
            typeof candle.close !== 'number' ||
            isNaN(candle.open) ||
            isNaN(candle.close)) {
            return 'invalid';
        }

        if (candle.close > candle.open) {
            return 'green';
        } else if (candle.close < candle.open) {
            return 'red';
        } else {
            return 'doji';
        }
    }

    // --- Indicator Helper Functions ---

    /**
     * Calculate Simple Moving Average
     * @param {Array} arr - Array of values
     * @param {number} period - Period
     * @param {number} index - Index
     * @returns {number} - SMA value
     */
    SMA(arr, period, index) {
        if (!arr || index < period - 1 || arr.length <= index) return NaN;

        let sum = 0;
        let validCount = 0;

        for (let j = index - period + 1; j <= index; j++) {
            if (typeof arr[j] === 'number' && !isNaN(arr[j])) {
                sum += arr[j];
                validCount++;
            } else {
                return NaN;
            }
        }

        return validCount === period ? sum / period : NaN;
    }

    /**
     * Calculate Weighted Moving Average
     * @param {Array} arr - Array of values
     * @param {number} period - Period
     * @param {number} index - Index
     * @returns {number} - WMA value
     */
    WMA(arr, period, index) {
        if (!arr || index < period - 1 || arr.length <= index) return NaN;

        let weightedSum = 0;
        let weightSum = 0;
        let validCount = 0;

        for (let j = 0; j < period; j++) {
            const idx = index - j;
            const weight = period - j;

            if (idx < 0) return NaN;

            if (typeof arr[idx] === 'number' && !isNaN(arr[idx])) {
                weightedSum += arr[idx] * weight;
                weightSum += weight;
                validCount++;
            } else {
                return NaN;
            }
        }

        return validCount === period ? weightedSum / weightSum : NaN;
    }

    /**
     * Calculate Relative Strength Index
     * @param {Array} arr - Array of values
     * @param {number} period - Period
     * @param {number} index - Index
     * @returns {number} - RSI value
     */
    RSI(arr, period, index) {
        if (!arr || index < period || arr.length <= index) return NaN;

        let gains = 0;
        let losses = 0;
        let validDeltas = 0;

        for (let j = index - period + 1; j <= index; j++) {
            if (j > 0 && typeof arr[j - 1] === 'number' && !isNaN(arr[j - 1]) && typeof arr[j] === 'number' && !isNaN(arr[j])) {
                const delta = arr[j] - arr[j - 1];

                if (delta > 0) {
                    gains += delta;
                } else {
                    losses -= delta;
                }

                validDeltas++;
            } else {
                return NaN;
            }
        }

        if (validDeltas < period) return NaN;
        if (losses === 0) return 100;
        if (gains === 0) return 0;

        const avgGain = gains / period;
        const avgLoss = losses / period;
        const rs = avgGain / avgLoss;

        return 100 - (100 / (1 + rs));
    }

    /**
     * Calculate Average True Range
     * @param {Array} candles - Array of candles
     * @param {number} period - Period
     * @param {number} index - Index
     * @returns {number} - ATR value
     */
    ATR(candles, period, index) {
        if (!candles || index < period || candles.length <= index) return NaN;

        const trs = [];

        for (let i = index - period + 1; i <= index; i++) {
            const cur = candles[i];

            if (!cur) {
                trs.push(NaN);
                continue;
            }

            if (i === index - period + 1) {
                if (!isNaN(cur.high) && !isNaN(cur.low)) {
                    trs.push(cur.high - cur.low);
                } else {
                    trs.push(NaN);
                }
                continue;
            }

            const prev = candles[i - 1];

            if (!prev || isNaN(prev.close) || isNaN(cur.high) || isNaN(cur.low)) {
                trs.push(NaN);
                continue;
            }

            trs.push(Math.max(
                cur.high - cur.low,
                Math.abs(cur.high - prev.close),
                Math.abs(cur.low - prev.close)
            ));
        }

        // Calculate average
        let sum = 0;
        let validCount = 0;

        for (const tr of trs) {
            if (!isNaN(tr)) {
                sum += tr;
                validCount++;
            }
        }

        return validCount > 0 ? sum / validCount : NaN;
    }

    /**
     * Detect 3-candle pattern
     * @param {Object} c1 - First candle
     * @param {Object} c2 - Second candle
     * @param {Object} c3 - Third candle
     * @returns {string|null} - Pattern direction or null
     */
    detect3(c1, c2, c3) {
        // This method should be set from outside with the pattern detection function
        // Default implementation returns null
        return null;
    }

    /**
     * Detect 4-candle pattern
     * @param {Object} c0 - First candle
     * @param {Object} c1 - Second candle
     * @param {Object} c2 - Third candle
     * @param {Object} c3 - Fourth candle
     * @returns {string|null} - Pattern direction or null
     */
    detect4(c0, c1, c2, c3) {
        // This method should be set from outside with the pattern detection function
        // Default implementation returns null
        return null;
    }

    /**
     * Check if entry is OK
     * @param {string} pattern - Pattern direction
     * @param {string} patternType - Pattern type
     * @param {Object} candle - Current candle
     * @param {number} index - Current index
     * @param {Array} candles - Array of candles
     * @returns {boolean} - True if entry is OK
     */
    entryOK(pattern, patternType, candle, index, candles) {
        // This method should be set from outside with the entry validation function
        // Default implementation returns false
        return false;
    }
}

export default TradovateBridge;
