/**
 * trading-bot.js
 * Main application file for the trading bot
 */

import TradovateBridge from './tradovate-bridge.js';
import { credentials } from '../tutorialsCredentials.js';
import { URLs } from '../tutorialsURLs.js';

// Import configuration
import { commonConfig, mnqConfig, mesConfig, mgcConfig } from '../../multi_symbol_config.js';

// Import pattern detection functions
import * as patternDetection from '../../pattern_detection.js';

class TradingBot {
    constructor() {
        // Initialize state
        this.bridge = null;
        this.isRunning = false;
        this.config = commonConfig;
        this.symbolConfigs = {
            MNQ: mnqConfig,
            MES: mesConfig,
            MGC: mgcConfig
        };
        this.symbols = ['MNQ', 'MES', 'MGC']; // Default symbols to trade
        this.activeSymbols = new Set();
        this.lastSignalTime = {};
        this.dailyStats = {
            trades: 0,
            wins: 0,
            losses: 0,
            pnl: 0,
            startBalance: 0,
            currentBalance: 0,
            maxDrawdown: 0,
            peakBalance: 0
        };

        // Bind methods
        this.start = this.start.bind(this);
        this.stop = this.stop.bind(this);
        this.onConnected = this.onConnected.bind(this);
        this.onDisconnected = this.onDisconnected.bind(this);
        this.onError = this.onError.bind(this);
        this.onTrade = this.onTrade.bind(this);
        this.onPositionUpdate = this.onPositionUpdate.bind(this);
        this.onMarketData = this.onMarketData.bind(this);
        this.resetDailyStats = this.resetDailyStats.bind(this);

        // Initialize bridge with pattern detection functions
        this.initializeBridge();
    }

    /**
     * Initialize the Tradovate bridge
     */
    initializeBridge() {
        // Create bridge instance
        this.bridge = new TradovateBridge(this.config, credentials, URLs);

        // Set callbacks
        this.bridge.onConnected = this.onConnected;
        this.bridge.onDisconnected = this.onDisconnected;
        this.bridge.onError = this.onError;
        this.bridge.onTrade = this.onTrade;
        this.bridge.onPositionUpdate = this.onPositionUpdate;
        this.bridge.onMarketData = this.onMarketData;

        // Set pattern detection functions
        this.bridge.detect3 = patternDetection.detect3Pattern;
        this.bridge.detect4 = patternDetection.detect4Pattern;
        this.bridge.entryOK = patternDetection.validateEntry;
    }

    /**
     * Start the trading bot
     * @returns {Promise<boolean>} - True if started successfully
     */
    async start() {
        if (this.isRunning) {
            console.log('Trading bot is already running');
            return true;
        }

        try {
            console.log('Starting trading bot...');

            // Connect to Tradovate API
            const connected = await this.bridge.connect();

            if (!connected) {
                throw new Error('Failed to connect to Tradovate API');
            }

            this.isRunning = true;

            // Reset daily stats
            this.resetDailyStats();

            // Set up daily stats reset at midnight
            const now = new Date();
            const midnight = new Date(now);
            midnight.setHours(24, 0, 0, 0);
            const timeUntilMidnight = midnight - now;

            setTimeout(() => {
                this.resetDailyStats();

                // Set up daily reset interval
                setInterval(this.resetDailyStats, 24 * 60 * 60 * 1000);
            }, timeUntilMidnight);

            console.log('Trading bot started successfully');

            return true;
        } catch (error) {
            console.error('Error starting trading bot:', error);
            return false;
        }
    }

    /**
     * Stop the trading bot
     */
    stop() {
        if (!this.isRunning) {
            console.log('Trading bot is not running');
            return;
        }

        console.log('Stopping trading bot...');

        // Disconnect from Tradovate API
        this.bridge.disconnect();

        this.isRunning = false;

        console.log('Trading bot stopped');
    }

    /**
     * Reset daily statistics
     */
    resetDailyStats() {
        const previousStats = { ...this.dailyStats };

        this.dailyStats = {
            trades: 0,
            wins: 0,
            losses: 0,
            pnl: 0,
            startBalance: this.bridge.balance,
            currentBalance: this.bridge.balance,
            maxDrawdown: 0,
            peakBalance: this.bridge.balance
        };

        console.log('Daily stats reset');
        console.log('Previous day stats:', previousStats);
    }

    /**
     * Handle connected event
     */
    onConnected() {
        console.log('Connected to Tradovate API');

        // Subscribe to market data for all symbols
        for (const symbol of this.symbols) {
            this.bridge.subscribeToMarketData(symbol)
                .then(() => {
                    this.activeSymbols.add(symbol);
                    console.log(`Subscribed to market data for ${symbol}`);
                })
                .catch(error => {
                    console.error(`Error subscribing to market data for ${symbol}:`, error);
                });
        }
    }

    /**
     * Handle disconnected event
     */
    onDisconnected() {
        console.log('Disconnected from Tradovate API');
        this.isRunning = false;
        this.activeSymbols.clear();
    }

    /**
     * Handle error event
     * @param {Error} error - Error object
     */
    onError(error) {
        console.error('Tradovate API error:', error);
    }

    /**
     * Handle trade event
     * @param {Object} trade - Trade object
     */
    onTrade(trade) {
        console.log(`Trade executed: ${trade.action} ${trade.orderQty} ${trade.symbol} @ ${trade.price}`);

        // Update daily stats
        this.dailyStats.trades++;

        // Calculate P&L
        const pnl = trade.pnl || 0;
        this.dailyStats.pnl += pnl;

        if (pnl > 0) {
            this.dailyStats.wins++;
        } else if (pnl < 0) {
            this.dailyStats.losses++;
        }

        // Update balance
        this.dailyStats.currentBalance = this.bridge.balance;

        // Update peak balance
        if (this.dailyStats.currentBalance > this.dailyStats.peakBalance) {
            this.dailyStats.peakBalance = this.dailyStats.currentBalance;
        }

        // Update max drawdown
        const currentDrawdown = this.dailyStats.peakBalance - this.dailyStats.currentBalance;
        if (currentDrawdown > this.dailyStats.maxDrawdown) {
            this.dailyStats.maxDrawdown = currentDrawdown;
        }

        // Check if daily loss limit is reached
        if (this.config.dailyLossLimitPercent > 0) {
            const dailyLossLimit = this.dailyStats.startBalance * this.config.dailyLossLimitPercent;

            if (this.dailyStats.pnl < -dailyLossLimit) {
                console.log(`Daily loss limit reached: ${this.dailyStats.pnl.toFixed(2)} < -${dailyLossLimit.toFixed(2)}`);
                console.log('Stopping trading for the day');

                // Unsubscribe from all symbols
                for (const symbol of this.activeSymbols) {
                    this.bridge.unsubscribeFromMarketData(symbol);
                }

                this.activeSymbols.clear();
            }
        }
    }

    /**
     * Handle position update event
     * @param {string} symbol - Symbol
     * @param {Object} position - Position object
     */
    onPositionUpdate(symbol, position) {
        console.log(`Position updated: ${symbol} ${position.netPos} @ ${position.netPrice} (P&L: ${position.currentPL?.toFixed(2) || 'N/A'})`);
    }

    /**
     * Handle market data event
     * @param {string} type - Data type ('quote' or 'chart')
     * @param {string} symbol - Symbol
     * @param {Object} data - Market data
     */
    onMarketData(type, symbol, data) {
        // This method can be used for logging or additional processing
        // For now, we'll let the bridge handle the data
    }

    /**
     * Get current statistics
     * @returns {Object} - Statistics object
     */
    getStats() {
        const winRate = this.dailyStats.trades > 0 ?
            (this.dailyStats.wins / this.dailyStats.trades * 100).toFixed(2) + '%' :
            'N/A';

        return {
            isRunning: this.isRunning,
            activeSymbols: Array.from(this.activeSymbols),
            balance: this.bridge.balance,
            positions: Array.from(this.bridge.positions.entries()).map(([symbol, position]) => ({
                symbol,
                quantity: position.netPos,
                entryPrice: position.netPrice,
                currentPnL: position.currentPL
            })),
            dailyStats: {
                ...this.dailyStats,
                winRate
            }
        };
    }
}

// Export the TradingBot class
export default TradingBot;
