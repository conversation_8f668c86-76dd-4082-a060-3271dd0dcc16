# Multi-Market Paper Trading System

This system provides an integrated paper trading platform for multiple futures markets using the Tradovate API. It allows you to trade different markets simultaneously with individual on/off switches for each market.

## Supported Markets

The system currently supports the following markets:

1. **MNQ (Micro Nasdaq)** - Enabled by default
   - SL Factor: 4.5
   - TP Factor: 3.0
   - Trail Factor: 0.11
   - Fixed Contracts: 5
   - Win Rate: ~70%
   - Win Day Rate: ~98%

2. **MGC (Micro Gold)** - Enabled by default
   - SL Factor: 8.0
   - TP Factor: 7.0
   - Trail Factor: 0.02
   - Fixed Contracts: 10
   - Win Rate: ~70%
   - Win Day Rate: ~93%

3. **MES (Micro S&P 500)** - Disabled by default (placeholder for future implementation)

4. **M2K (Micro Russell 2000)** - Disabled by default (placeholder for future implementation)

## Getting Started

To start the multi-market trading system:

```bash
npm run multi-market
```

Or directly:

```bash
node multi_market_trading.js
```

## Command Menu

The system provides an interactive command menu with the following options:

### Main Menu
1. **Start Trading (All Enabled Markets)** - Begins trading for all enabled markets
2. **Stop Trading (All Enabled Markets)** - Stops trading for all enabled markets
3. **Show All Markets Status** - Displays status for all markets
4. **Market-Specific Commands** - Access market-specific commands
5. **Enable/Disable Markets** - Toggle markets on/off
6. **Backup Data** - Manually save the current trading state
7. **Exit** - Close the trading system

### Market-Specific Commands
1. **Start Trading for Specific Market** - Start trading for a selected market
2. **Stop Trading for Specific Market** - Stop trading for a selected market
3. **Show Status for Specific Market** - Display detailed status for a selected market
4. **Show Configuration for Specific Market** - Display configuration for a selected market
5. **Back to Main Menu** - Return to the main menu

## Adding New Markets

To add a new market:

1. Create a configuration file for the market (e.g., `mes_paper_trading_config.js`)
2. Update the `markets` object in `multi_market_trading.js` to include the new market
3. Set the `enabled` flag to `true` when ready to trade

## State Persistence

The system automatically saves the trading state to `./state/multi_market_state.json`. This includes:
- Enabled/disabled status for each market
- Trading status (active/inactive)
- Performance metrics
- Open positions

The state is loaded when the system starts, allowing it to resume from where it left off.

## Monitoring

Use the monitoring dashboard to track performance across all markets:

```bash
npm run monitoring
```

This will start the monitoring server and open the dashboard in your browser.

## Alerts

The system includes alerts for:
- Drawdown warnings (60% and 90% of maximum allowed drawdown)
- Win rate drops below thresholds (65% and 60%)
- System status changes (startup, shutdown, etc.)
- Trading status changes (start/stop trading)

## Requirements

- Node.js 14+
- Tradovate API credentials (set in .env file)
- Internet connection

## Troubleshooting

If you encounter connection issues:
1. Check your Tradovate API credentials in the .env file
2. Ensure your internet connection is stable
3. Verify that the Tradovate API is available
4. Check the logs for specific error messages
