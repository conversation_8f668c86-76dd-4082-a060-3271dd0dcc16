<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tradovate WebSocket Example</title>
    <script type="text/javascript" src="https://canvasjs.com/assets/script/canvasjs.stock.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .actions {
            margin: 20px 0;
        }
        .btn {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn-secondary {
            background-color: #2196F3;
        }
        .btn-secondary:hover {
            background-color: #0b7dda;
        }
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        #output {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 3px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent {
            background-color: #e8f5e9;
            border-left: 3px solid #4CAF50;
        }
        .message.received {
            background-color: #e3f2fd;
            border-left: 3px solid #2196F3;
        }
        .message.error {
            background-color: #ffebee;
            border-left: 3px solid #f44336;
        }
        .message.info {
            background-color: #f5f5f5;
            border-left: 3px solid #9e9e9e;
        }
        .connection-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            margin-left: 10px;
        }
        .connection-status.connected {
            background-color: #4CAF50;
            color: white;
        }
        .connection-status.disconnected {
            background-color: #f44336;
            color: white;
        }
        .connection-status.connecting {
            background-color: #ff9800;
            color: white;
        }

        /* Product details styles */
        #outlet {
            margin-top: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .product-details h1 {
            color: #333;
            margin-top: 0;
        }
        .product-details h3 {
            color: #555;
            margin-top: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .info-item {
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .info-item .label {
            font-weight: bold;
            color: #555;
        }
        .info-item .value {
            margin-left: 5px;
        }
        .currency {
            font-size: 16px;
            color: #666;
        }
        .description {
            line-height: 1.5;
            color: #555;
        }

        /* Quote data styles */
        .quote-container {
            margin-top: 20px;
        }
        .quote-symbol {
            color: #333;
            margin-top: 0;
            text-align: center;
            font-size: 24px;
        }
        .quote-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .quote-card {
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .quote-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .quote-card h3 {
            color: #444;
            margin-top: 0;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .quote-card ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .quote-card li {
            padding: 3px 0;
            font-family: monospace;
            font-size: 14px;
        }

        /* DOM data styles */
        .dom-container {
            margin-top: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .dom-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .dom-symbol {
            color: #333;
            margin: 0;
            font-size: 20px;
        }
        .dom-info {
            display: flex;
            flex-direction: column;
            font-size: 12px;
            color: #666;
        }
        .dom-columns {
            display: flex;
            gap: 20px;
        }
        .dom-column {
            flex: 1;
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 10px;
        }
        .dom-column-header {
            margin: 0 0 10px 0;
            text-align: center;
            color: #444;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .dom-column-labels {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
            color: #555;
        }
        .dom-entries {
            max-height: 300px;
            overflow-y: auto;
        }
        .dom-entry {
            margin-bottom: 5px;
        }
        .dom-item {
            display: flex;
            justify-content: space-between;
            list-style-type: none;
            padding: 3px 0;
            border-bottom: 1px solid #eee;
        }
        .dom-price {
            font-weight: bold;
            font-family: monospace;
        }
        .dom-size {
            font-family: monospace;
        }
        .dom-empty {
            text-align: center;
            color: #999;
            padding: 20px 0;
        }

        /* Histogram styles */
        .histogram-container {
            margin-top: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .histogram-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .histogram-symbol {
            color: #333;
            margin: 0;
            font-size: 20px;
        }
        .histogram-info {
            display: flex;
            flex-direction: column;
            font-size: 12px;
            color: #666;
        }
        .histogram-items {
            max-height: 400px;
            overflow-y: auto;
        }
        .histogram-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .histogram-offset {
            width: 40px;
            text-align: right;
            margin-right: 10px;
            font-family: monospace;
        }
        .histogram-bar-container {
            flex: 1;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }
        .histogram-bar {
            height: 100%;
            background-color: #4CAF50;
        }
        .histogram-value {
            width: 60px;
            text-align: right;
            margin-left: 10px;
            font-family: monospace;
        }
        .histogram-empty {
            text-align: center;
            color: #999;
            padding: 20px 0;
        }
        #status {
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background-color: silver;
            transition: background-color 0.33s ease-in;
            display: inline-block;
            margin-left: 10px;
        }
        .market-data-container {
            margin-top: 20px;
        }
        .market-data-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .market-data-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .market-data-controls {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 10px;
        }
        .market-data-controls input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 100px;
        }
        .market-data-outlet {
            min-height: 50px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .chart-container {
            height: 400px;
            width: 100%;
            margin-top: 15px;
        }
        .form-select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }

        /* P&L styles */
        .pl-container {
            padding: 15px;
        }
        .pl-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .pl-header h4 {
            margin: 0;
            color: #333;
        }
        .pl-total {
            font-size: 18px;
            font-weight: bold;
        }
        #open-pl {
            color: #4CAF50;
        }
        #open-pl.negative {
            color: #f44336;
        }
        .pl-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .pl-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }
        .pl-item:hover {
            background-color: #f5f5f5;
        }
        .pl-symbol {
            font-weight: bold;
            font-size: 16px;
        }
        .pl-details {
            display: flex;
            align-items: center;
        }
        .pl-position {
            margin-right: 15px;
            font-family: monospace;
        }
        .pl-value {
            font-weight: bold;
            font-family: monospace;
            font-size: 16px;
        }
        .pl-value.positive {
            color: #4CAF50;
        }
        .pl-value.negative {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tradovate WebSocket Example <span id="connection-status" class="connection-status disconnected">Disconnected</span></h1>

        <div class="actions">
            <button id="connect-btn" class="btn">Connect WebSocket</button>
            <button id="disconnect-btn" class="btn btn-danger" disabled>Disconnect</button>
            <button id="clear-btn" class="btn btn-secondary">Clear Output</button>
        </div>

        <div class="actions">
            <button id="user-accounts-btn" class="btn" disabled>Get User Accounts</button>
            <button id="user-sync-btn" class="btn" disabled>Sync User</button>
            <button id="eth-details-btn" class="btn" disabled>ETH Details</button>
        </div>

        <div class="actions">
            <div id="status"></div>
        </div>

        <div class="market-data-container">
            <div class="market-data-section">
                <h3>Quote Data</h3>
                <div class="market-data-controls">
                    <input id="symbol-input" type="text" placeholder="MNQM3" value="MNQM3" />
                    <button id="watch-btn" class="btn" disabled>Watch</button>
                    <button id="unwatch-btn" class="btn btn-danger" disabled>Unwatch</button>
                </div>
                <div id="outlet" class="market-data-outlet">
                    <!-- Quote data will be displayed here -->
                </div>
            </div>

            <div class="market-data-section">
                <h3>Depth of Market (DOM)</h3>
                <div class="market-data-controls">
                    <input id="dom-symbol-input" type="text" placeholder="MNQM3" value="MNQM3" />
                    <button id="watch-dom-btn" class="btn" disabled>Watch DOM</button>
                    <button id="unwatch-dom-btn" class="btn btn-danger" disabled>Unwatch DOM</button>
                </div>
                <div id="dom-outlet" class="market-data-outlet">
                    <!-- DOM data will be displayed here -->
                </div>
            </div>

            <div class="market-data-section">
                <h3>Histogram Data</h3>
                <div class="market-data-controls">
                    <input id="histogram-symbol-input" type="text" placeholder="MNQM3" value="MNQM3" />
                    <button id="watch-histogram-btn" class="btn" disabled>Watch Histogram</button>
                    <button id="unwatch-histogram-btn" class="btn btn-danger" disabled>Unwatch Histogram</button>
                </div>
                <div id="histogram-outlet" class="market-data-outlet">
                    <!-- Histogram data will be displayed here -->
                </div>
            </div>

            <div class="market-data-section">
                <h3>Chart Data</h3>
                <div class="market-data-controls">
                    <input id="chart-symbol-input" type="text" placeholder="MNQM3" value="MNQM3" />
                    <select id="chart-type" class="form-select">
                        <option value="MinuteBar">MinuteBar</option>
                        <option value="Tick">Tick</option>
                        <option value="DailyBar">DailyBar</option>
                    </select>
                    <input id="chart-elements" type="number" min="1" max="500" value="100" placeholder="# Elements" title="Number of elements" />
                    <input id="chart-element-size" type="number" min="1" max="720" value="30" placeholder="Element size" title="Element size" />
                    <button id="get-chart-btn" class="btn" disabled>Get Chart</button>
                </div>
                <div id="chart-outlet" class="market-data-outlet chart-container">
                    <!-- Chart data will be displayed here -->
                </div>
            </div>

            <div class="market-data-section">
                <h3>Real-Time P&L Calculator</h3>
                <div class="market-data-controls">
                    <input id="pl-symbol-input" type="text" placeholder="MNQM3" value="MNQM3" />
                    <input id="pl-qty-input" type="number" min="1" max="100" value="1" placeholder="Quantity" title="Contract quantity" />
                    <button id="buy-btn" class="btn" disabled>Buy</button>
                    <button id="sell-btn" class="btn btn-danger" disabled>Sell</button>
                </div>
                <div id="pl-outlet" class="market-data-outlet">
                    <div class="pl-container">
                        <div class="pl-header">
                            <h4>Positions</h4>
                            <div class="pl-total">Total P&L: <span id="open-pl">$0.00</span></div>
                        </div>
                        <ul id="position-list" class="pl-list">
                            <!-- Position data will be displayed here -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div id="output">
            <div class="message info">WebSocket messages will appear here...</div>
        </div>
    </div>

    <script type="module" src="app.js"></script>
    <script>
        // Redirect console.log to our output div
        const output = document.getElementById('output');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);

            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                } else {
                    return arg;
                }
            }).join(' ');

            const logElement = document.createElement('div');
            logElement.className = 'message received';
            logElement.textContent = message;
            output.appendChild(logElement);
            output.scrollTop = output.scrollHeight;
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);

            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                } else {
                    return arg;
                }
            }).join(' ');

            const logElement = document.createElement('div');
            logElement.className = 'message error';
            logElement.textContent = message;
            output.appendChild(logElement);
            output.scrollTop = output.scrollHeight;
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);

            const message = args.map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                } else {
                    return arg;
                }
            }).join(' ');

            const logElement = document.createElement('div');
            logElement.className = 'message info';
            logElement.textContent = message;
            output.appendChild(logElement);
            output.scrollTop = output.scrollHeight;
        };

        // Add event listeners for buttons
        document.addEventListener('DOMContentLoaded', () => {
            const connectBtn = document.getElementById('connect-btn');
            const disconnectBtn = document.getElementById('disconnect-btn');
            const clearBtn = document.getElementById('clear-btn');
            const userAccountsBtn = document.getElementById('user-accounts-btn');
            const userSyncBtn = document.getElementById('user-sync-btn');
            const connectionStatus = document.getElementById('connection-status');

            // Clear output button
            clearBtn.addEventListener('click', () => {
                output.innerHTML = '<div class="message info">WebSocket messages will appear here...</div>';
            });

            // Connect button
            connectBtn.addEventListener('click', () => {
                connectionStatus.textContent = 'Connecting...';
                connectionStatus.className = 'connection-status connecting';

                // The main function in app.js will be called automatically
                // when the page loads, but we can trigger it again here
                if (window.tradovateWS && window.tradovateWS.readyState === WebSocket.OPEN) {
                    console.log('WebSocket already connected');
                    connectionStatus.textContent = 'Connected';
                    connectionStatus.className = 'connection-status connected';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    userAccountsBtn.disabled = false;
                    userSyncBtn.disabled = false;
                } else {
                    console.log('Initializing WebSocket connection...');
                    // The main function will handle the connection
                }
            });

            // Disconnect button
            disconnectBtn.addEventListener('click', () => {
                if (window.tradovateWS) {
                    window.tradovateWS.close();
                    console.log('WebSocket disconnected by user');
                    connectionStatus.textContent = 'Disconnected';
                    connectionStatus.className = 'connection-status disconnected';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    userAccountsBtn.disabled = true;
                    userSyncBtn.disabled = true;
                }
            });

            // User accounts button
            userAccountsBtn.addEventListener('click', () => {
                if (window.tradovateWS && window.tradovateWS.readyState === WebSocket.OPEN) {
                    const request = `user/accounts\n${Date.now()}\n\n`;
                    window.tradovateWS.send(request);

                    const logElement = document.createElement('div');
                    logElement.className = 'message sent';
                    logElement.textContent = `Sent request: ${request}`;
                    output.appendChild(logElement);
                    output.scrollTop = output.scrollHeight;
                } else {
                    console.error('WebSocket not connected');
                }
            });

            // User sync button
            userSyncBtn.addEventListener('click', () => {
                if (window.tradovateWS && window.tradovateWS.readyState === WebSocket.OPEN) {
                    const request = `user/syncrequest\n${Date.now()}\n\n`;
                    window.tradovateWS.send(request);

                    const logElement = document.createElement('div');
                    logElement.className = 'message sent';
                    logElement.textContent = `Sent request: ${request}`;
                    output.appendChild(logElement);
                    output.scrollTop = output.scrollHeight;
                } else {
                    console.error('WebSocket not connected');
                }
            });

            // Update connection status when WebSocket state changes
            const checkConnectionStatus = setInterval(() => {
                if (window.tradovateWS) {
                    switch (window.tradovateWS.readyState) {
                        case WebSocket.CONNECTING:
                            connectionStatus.textContent = 'Connecting...';
                            connectionStatus.className = 'connection-status connecting';
                            connectBtn.disabled = true;
                            disconnectBtn.disabled = true;
                            userAccountsBtn.disabled = true;
                            userSyncBtn.disabled = true;
                            break;
                        case WebSocket.OPEN:
                            connectionStatus.textContent = 'Connected';
                            connectionStatus.className = 'connection-status connected';
                            connectBtn.disabled = true;
                            disconnectBtn.disabled = false;
                            userAccountsBtn.disabled = false;
                            userSyncBtn.disabled = false;
                            break;
                        case WebSocket.CLOSING:
                        case WebSocket.CLOSED:
                            connectionStatus.textContent = 'Disconnected';
                            connectionStatus.className = 'connection-status disconnected';
                            connectBtn.disabled = false;
                            disconnectBtn.disabled = true;
                            userAccountsBtn.disabled = true;
                            userSyncBtn.disabled = true;
                            break;
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
