/**
 * Simple script to test Databento authentication
 */

require('dotenv').config();
const axios = require('axios');

// Check if API key is available
if (!process.env.DATABENTO_API_KEY || process.env.DATABENTO_API_KEY === 'your_databento_api_key') {
  console.error('DATABENTO_API_KEY environment variable is required. Please update your .env file with your actual Databento API key.');
  process.exit(1);
}

console.log(`Using Databento API key: ${process.env.DATABENTO_API_KEY.substring(0, 5)}...`);

// Test authentication with different methods
async function testAuth() {
  console.log('Testing Databento authentication...');
  
  // Method 1: Bearer token
  try {
    console.log('\nTesting with Bearer token:');
    const response1 = await axios.get('https://api.databento.com/v0/account.get', {
      headers: {
        'Authorization': `Bearer ${process.env.DATABENTO_API_KEY}`
      }
    });
    console.log('Success! Response:', response1.status, response1.statusText);
    console.log('Account info:', response1.data);
  } catch (error) {
    console.error('Error with Bearer token:', error.response ? `${error.response.status} ${error.response.statusText}` : error.message);
    if (error.response && error.response.data) {
      console.error('Response data:', error.response.data);
    }
  }
  
  // Method 2: Basic auth
  try {
    console.log('\nTesting with Basic auth:');
    const response2 = await axios.get('https://api.databento.com/v0/account.get', {
      auth: {
        username: process.env.DATABENTO_API_KEY,
        password: ''
      }
    });
    console.log('Success! Response:', response2.status, response2.statusText);
    console.log('Account info:', response2.data);
  } catch (error) {
    console.error('Error with Basic auth:', error.response ? `${error.response.status} ${error.response.statusText}` : error.message);
    if (error.response && error.response.data) {
      console.error('Response data:', error.response.data);
    }
  }
  
  // Method 3: API key as query parameter
  try {
    console.log('\nTesting with API key as query parameter:');
    const response3 = await axios.get(`https://api.databento.com/v0/account.get?key=${process.env.DATABENTO_API_KEY}`);
    console.log('Success! Response:', response3.status, response3.statusText);
    console.log('Account info:', response3.data);
  } catch (error) {
    console.error('Error with query parameter:', error.response ? `${error.response.status} ${error.response.statusText}` : error.message);
    if (error.response && error.response.data) {
      console.error('Response data:', error.response.data);
    }
  }
  
  console.log('\nAuthentication tests completed.');
}

// Run the tests
testAuth().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
