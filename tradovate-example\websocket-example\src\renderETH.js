// renderETH.js
// Renders Ethereum product data to HTML

/**
 * Render Ethereum product data to HTML
 * @param {Object} product - Product data
 * @returns {string} HTML representation of the product
 */
export const renderETH = ({
    allowProviderContractInfo,
    contractGroupId,
    currencyId,
    description,
    exchangeChannelId,
    exchangeId,
    id,
    isMicro,
    marketDataSource,
    name,
    priceFormat,
    priceFormatType,
    productType,
    status,
    tickSize,
    valuePerPoint,
}) => {
    return `
        <section class="product-details">
            <h1>${name}</h1>
            <p class="currency">Currency ID: ${currencyId == 1 ? '$' : currencyId}</p>
            <h3>Product Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">Allow Provider Contract Info:</span>
                    <span class="value">${allowProviderContractInfo}</span>
                </div>
                <div class="info-item">
                    <span class="label">Contract Group ID:</span>
                    <span class="value">${contractGroupId}</span>
                </div>
                <div class="info-item">
                    <span class="label">Exchange Channel ID:</span>
                    <span class="value">${exchangeChannelId}</span>
                </div>
                <div class="info-item">
                    <span class="label">Exchange ID:</span>
                    <span class="value">${exchangeId}</span>
                </div>
                <div class="info-item">
                    <span class="label">ID:</span>
                    <span class="value">${id}</span>
                </div>
                <div class="info-item">
                    <span class="label">Is Micro:</span>
                    <span class="value">${isMicro}</span>
                </div>
                <div class="info-item">
                    <span class="label">Market Data Source:</span>
                    <span class="value">${marketDataSource}</span>
                </div>
                <div class="info-item">
                    <span class="label">Price Format:</span>
                    <span class="value">${priceFormat}</span>
                </div>
                <div class="info-item">
                    <span class="label">Price Format Type:</span>
                    <span class="value">${priceFormatType}</span>
                </div>
                <div class="info-item">
                    <span class="label">Product Type:</span>
                    <span class="value">${productType}</span>
                </div>
                <div class="info-item">
                    <span class="label">Status:</span>
                    <span class="value">${status}</span>
                </div>
                <div class="info-item">
                    <span class="label">Tick Size:</span>
                    <span class="value">${tickSize}</span>
                </div>
                <div class="info-item">
                    <span class="label">Value Per Point:</span>
                    <span class="value">${valuePerPoint}</span>
                </div>
            </div>
            <h3>Description</h3>
            <p class="description">${description || 'No description available'}</p>
        </section>
    `
}
