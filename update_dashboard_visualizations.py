import os
import re
import glob
import json
from bs4 import BeautifulSoup
import shutil

# Directories
DASHBOARD_DIR = "C:/backtest-bot"
DATA_DIR = "C:/backtest-bot/market_data"

def find_dashboard_files():
    """Find all dashboard HTML files"""
    dashboard_files = []
    
    # Find all HTML files in the dashboard directory
    html_files = glob.glob(os.path.join(DASHBOARD_DIR, "*.html"))
    
    # Filter for dashboard files
    for file_path in html_files:
        file_name = os.path.basename(file_path)
        if "dashboard" in file_name.lower() or file_name in [
            "hedge_dashboard.html", "mnq_dashboard.html", "mgc_dashboard.html", "mes_dashboard.html",
            "performance_comparison_dashboard.html", "market_intelligence.html", "correlation_analysis_dashboard.html",
            "monte_carlo_dashboard.html", "drawdown_analysis_dashboard.html", "portfolio_manager.html",
            "portfolio_overview.html", "performance_forecast.html", "market_volatility.html",
            "daily_pnl_dashboard.html", "premium_dashboard.html", "custom_alerts_dashboard.html",
            "investor_reporting.html"
        ]:
            dashboard_files.append(file_path)
    
    return dashboard_files

def load_instrument_data():
    """Load instrument data from the market_data directory"""
    all_data = {}
    
    # Load the all_instruments_data.json file
    all_data_file = os.path.join(DATA_DIR, "all_instruments_data.json")
    if os.path.exists(all_data_file):
        with open(all_data_file, 'r') as f:
            all_data = json.load(f)
    
    return all_data

def update_dashboard_file(file_path, instrument_data):
    """Update a dashboard file to display data from all instruments"""
    file_name = os.path.basename(file_path)
    print(f"Updating {file_name}...")
    
    # Create a backup of the original file
    backup_file = f"{file_path}.viz.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(file_path, backup_file)
        print(f"Created backup of {file_path} to {backup_file}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the HTML content
        soup = BeautifulSoup(content, 'html.parser')
        
        # Add multi-instrument visualization script
        add_multi_instrument_visualization_script(soup, instrument_data)
        
        # Update chart initialization code
        update_chart_initialization(soup, instrument_data)
        
        # Write the updated content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        print(f"Updated {file_name} successfully")
        return True
    
    except Exception as e:
        print(f"Error updating {file_name}: {e}")
        
        # Restore the backup
        shutil.copy2(backup_file, file_path)
        print(f"Restored {file_path} from backup due to error")
        
        return False

def add_multi_instrument_visualization_script(soup, instrument_data):
    """Add a script to visualize data from all instruments"""
    # Create a new script element
    new_script = soup.new_tag('script')
    new_script.string = """
// Multi-Instrument Visualization Functions
function createMultiInstrumentCharts(data, instruments) {
    console.log('Creating multi-instrument charts for', instruments);
    
    // Get all chart containers
    const chartContainers = document.querySelectorAll('.chart-container');
    
    // For each chart container, create a multi-instrument chart
    chartContainers.forEach(container => {
        const chartId = container.id;
        const chartType = container.getAttribute('data-chart-type') || 'line';
        const chartTitle = container.getAttribute('data-chart-title') || 'Chart';
        
        // Create a multi-instrument chart
        createMultiInstrumentChart(container, chartId, chartType, chartTitle, data, instruments);
    });
    
    // Create comparison tables
    createComparisonTables(data, instruments);
}

function createMultiInstrumentChart(container, chartId, chartType, chartTitle, data, instruments) {
    // Clear the container
    container.innerHTML = '';
    
    // Create a canvas element for the chart
    const canvas = document.createElement('canvas');
    canvas.id = chartId + '-canvas';
    container.appendChild(canvas);
    
    // Prepare data for the chart
    const chartData = {
        labels: [],
        datasets: []
    };
    
    // Add a dataset for each instrument
    instruments.forEach(instrumentCode => {
        const instrumentData = data[instrumentCode];
        const instrumentConfig = instrumentData.instrumentConfig;
        const color = instrumentConfig.color;
        
        // Create a dataset for the instrument
        const dataset = {
            label: instrumentConfig.name,
            data: [],
            borderColor: color,
            backgroundColor: hexToRgba(color, 0.2),
            borderWidth: 2,
            pointRadius: 0,
            pointHoverRadius: 5,
            pointHitRadius: 10,
            pointHoverBackgroundColor: color,
            pointHoverBorderColor: '#fff',
            tension: 0.4
        };
        
        // Add the dataset to the chart data
        chartData.datasets.push(dataset);
    });
    
    // Create the chart
    const ctx = canvas.getContext('2d');
    const chart = new Chart(ctx, {
        type: chartType,
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: chartTitle,
                    font: {
                        family: "'Orbitron', sans-serif",
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#00ccff',
                    padding: 20
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 12
                        },
                        color: '#f8fafc'
                    }
                },
                tooltip: {
                    enabled: true,
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(20, 27, 45, 0.9)',
                    titleFont: {
                        family: "'Orbitron', sans-serif",
                        size: 12
                    },
                    bodyFont: {
                        family: "'Rajdhani', sans-serif",
                        size: 12
                    },
                    borderColor: '#2a3a5a',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(42, 58, 90, 0.5)'
                    },
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 10
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(42, 58, 90, 0.5)'
                    },
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            family: "'Rajdhani', sans-serif",
                            size: 10
                        }
                    }
                }
            }
        }
    });
    
    // Store the chart in the global charts object
    if (!window.multiInstrumentCharts) {
        window.multiInstrumentCharts = {};
    }
    window.multiInstrumentCharts[chartId] = chart;
    
    return chart;
}

function createComparisonTables(data, instruments) {
    // Get all table containers
    const tableContainers = document.querySelectorAll('.comparison-table-container');
    
    // For each table container, create a comparison table
    tableContainers.forEach(container => {
        const tableId = container.id;
        const tableTitle = container.getAttribute('data-table-title') || 'Comparison';
        
        // Create a comparison table
        createComparisonTable(container, tableId, tableTitle, data, instruments);
    });
}

function createComparisonTable(container, tableId, tableTitle, data, instruments) {
    // Clear the container
    container.innerHTML = '';
    
    // Create a title for the table
    const title = document.createElement('h3');
    title.textContent = tableTitle;
    title.className = 'comparison-table-title';
    container.appendChild(title);
    
    // Create a table element
    const table = document.createElement('table');
    table.id = tableId + '-table';
    table.className = 'comparison-table';
    container.appendChild(table);
    
    // Create the table header
    const thead = document.createElement('thead');
    table.appendChild(thead);
    
    const headerRow = document.createElement('tr');
    thead.appendChild(headerRow);
    
    // Add the metric column header
    const metricHeader = document.createElement('th');
    metricHeader.textContent = 'Metric';
    headerRow.appendChild(metricHeader);
    
    // Add a column header for each instrument
    instruments.forEach(instrumentCode => {
        const instrumentData = data[instrumentCode];
        const instrumentConfig = instrumentData.instrumentConfig;
        
        const instrumentHeader = document.createElement('th');
        instrumentHeader.textContent = instrumentConfig.name;
        instrumentHeader.style.color = instrumentConfig.color;
        headerRow.appendChild(instrumentHeader);
    });
    
    // Create the table body
    const tbody = document.createElement('tbody');
    table.appendChild(tbody);
    
    // Add rows for each metric
    const metrics = [
        { name: 'Total P&L', key: 'totalPnL', format: 'currency' },
        { name: 'Win Rate', key: 'winRate', format: 'percent' },
        { name: 'Win Day Rate', key: 'winDayRate', format: 'percent' },
        { name: 'Max Drawdown', key: 'maxDrawdown', format: 'currency' },
        { name: 'Profit Factor', key: 'profitFactor', format: 'number' },
        { name: 'Sharpe Ratio', key: 'sharpeRatio', format: 'number' },
        { name: 'Total Trades', key: 'totalTrades', format: 'number' }
    ];
    
    metrics.forEach(metric => {
        const row = document.createElement('tr');
        tbody.appendChild(row);
        
        // Add the metric name
        const metricCell = document.createElement('td');
        metricCell.textContent = metric.name;
        row.appendChild(metricCell);
        
        // Add a cell for each instrument
        instruments.forEach(instrumentCode => {
            const instrumentData = data[instrumentCode];
            const backtestResults = instrumentData.backtestResults || {};
            
            const cell = document.createElement('td');
            
            // Get the metric value
            let value = backtestResults[metric.key];
            
            // Format the value
            if (value !== undefined) {
                if (metric.format === 'currency') {
                    cell.textContent = '$' + value.toLocaleString('en-US', { maximumFractionDigits: 2 });
                    if (value >= 0) {
                        cell.className = 'positive';
                    } else {
                        cell.className = 'negative';
                    }
                } else if (metric.format === 'percent') {
                    cell.textContent = (value * 100).toFixed(2) + '%';
                } else {
                    cell.textContent = value.toLocaleString('en-US', { maximumFractionDigits: 2 });
                }
            } else {
                cell.textContent = 'N/A';
            }
            
            row.appendChild(cell);
        });
    });
}

// Helper function to convert hex color to rgba
function hexToRgba(hex, alpha) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

// Call the multi-instrument visualization functions when the dashboard is initialized
if (typeof updateDashboardContent === 'function') {
    const originalUpdateDashboardContent = updateDashboardContent;
    updateDashboardContent = function(data, instruments) {
        // Call the original function
        originalUpdateDashboardContent(data, instruments);
        
        // Create multi-instrument charts
        createMultiInstrumentCharts(data, instruments);
    };
}
"""
    
    # Add the script to the head
    head = soup.find('head')
    if head:
        head.append(new_script)
    else:
        soup.append(new_script)

def update_chart_initialization(soup, instrument_data):
    """Update chart initialization code to support multi-instrument data"""
    # Find all script tags
    script_tags = soup.find_all('script')
    
    for script in script_tags:
        if script.string and 'createChart' in script.string:
            # Update the script to support multi-instrument data
            script_content = script.string
            
            # Replace single-instrument chart initialization with multi-instrument support
            old_chart_init = re.search(r'function createChart\([^)]*\)\s*{[^}]*}', script_content, re.DOTALL)
            if old_chart_init:
                new_chart_init = """
function createChart(container, chartId, chartType, chartTitle, data) {
    // Check if we have multi-instrument data
    const isMultiInstrument = Array.isArray(data) || (typeof data === 'object' && Object.keys(data).length > 1);
    
    if (isMultiInstrument) {
        // Use the multi-instrument chart creation function
        return createMultiInstrumentChart(container, chartId, chartType, chartTitle, data, Object.keys(data));
    } else {
        // Use the original single-instrument chart creation function
        // ... (original chart creation code) ...
    }
}
"""
                script_content = script_content.replace(old_chart_init.group(0), new_chart_init)
                script.string = script_content
    
    return soup

def update_all_dashboards():
    """Update all dashboard files to display data from all instruments"""
    # Load instrument data
    instrument_data = load_instrument_data()
    
    if not instrument_data:
        print("No instrument data found. Please run import_all_market_data.py first.")
        return
    
    # Find all dashboard files
    dashboard_files = find_dashboard_files()
    
    if not dashboard_files:
        print("No dashboard files found.")
        return
    
    print(f"Found {len(dashboard_files)} dashboard files:")
    for file_path in dashboard_files:
        print(f"  - {os.path.basename(file_path)}")
    
    # Update each dashboard file
    success_count = 0
    for file_path in dashboard_files:
        if update_dashboard_file(file_path, instrument_data):
            success_count += 1
    
    print(f"Updated {success_count} out of {len(dashboard_files)} dashboard files successfully")

def main():
    """Main function"""
    print("Updating all dashboards to display data from all instruments...")
    update_all_dashboards()
    print("Dashboard visualization update complete!")

if __name__ == "__main__":
    main()
