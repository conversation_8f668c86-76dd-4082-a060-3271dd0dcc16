/**
 * Overnight Trading Bot Runner
 * This script runs the demo trading bot continuously overnight
 * without requiring CLI interaction.
 */

const demoTrading = require('./demo_trading_optimized');
const logger = require('./data_logger');

// Configuration
const config = {
    // How often to check for market data (in milliseconds)
    checkInterval: 60000, // 1 minute

    // How often to log status (in milliseconds)
    statusInterval: 300000, // 5 minutes

    // How long to run before exiting (in milliseconds)
    // Set to 0 to run indefinitely
    runDuration: 0
};

// State variables
let isRunning = false;
let startTime = null;
let lastStatusTime = null;
let checkIntervalId = null;
let statusIntervalId = null;

/**
 * Initialize the trading bot
 */
async function initialize() {
    try {
        console.log('Starting initialization of overnight trading bot...');
        logger.logSystem('Initializing overnight trading bot...', 'info');

        // Initialize the demo trading bot
        console.log('Calling demoTrading.initialize()...');
        const result = await demoTrading.initialize();
        console.log(`Initialize result: ${result}`);

        if (result) {
            isRunning = true;
            startTime = Date.now();
            lastStatusTime = startTime;

            logger.logSystem('Overnight trading bot initialized successfully', 'info');
            console.log('Overnight trading bot initialized successfully');

            // Start the check interval
            console.log(`Setting up check interval (${config.checkInterval}ms)...`);
            checkIntervalId = setInterval(checkMarketData, config.checkInterval);

            // Start the status interval
            console.log(`Setting up status interval (${config.statusInterval}ms)...`);
            statusIntervalId = setInterval(logStatus, config.statusInterval);

            // If run duration is set, schedule shutdown
            if (config.runDuration > 0) {
                console.log(`Setting up shutdown timer (${config.runDuration}ms)...`);
                setTimeout(shutdown, config.runDuration);
            }

            console.log('Initialization complete. Bot is now running.');
        } else {
            throw new Error('Failed to initialize demo trading bot');
        }
    } catch (error) {
        logger.logSystem(`Initialization failed: ${error.message}`, 'error');
        console.error(`Initialization failed: ${error.message}`);
        process.exit(1);
    }
}

/**
 * Check for market data
 */
async function checkMarketData() {
    try {
        // Get the status of the trading bot
        const status = await demoTrading.getStatus();

        // Check if we're receiving market data
        const cacheStats = status.cacheStats;
        const isReceivingData = cacheStats.chartDataCache.sets > 0 || cacheStats.contractCache.sets > 0;

        if (isReceivingData) {
            logger.logSystem('Receiving market data!', 'info');
            console.log('Receiving market data!');

            // Log the cache stats
            logger.logSystem(`Chart data cache: ${JSON.stringify(cacheStats.chartDataCache)}`, 'info');
            logger.logSystem(`Contract cache: ${JSON.stringify(cacheStats.contractCache)}`, 'info');
        } else {
            logger.logSystem('Not receiving market data yet...', 'info');
        }
    } catch (error) {
        logger.logSystem(`Error checking market data: ${error.message}`, 'error');
    }
}

/**
 * Log the status of the trading bot
 */
async function logStatus() {
    try {
        // Get the status of the trading bot
        const status = await demoTrading.getStatus();

        // Calculate runtime
        const runtime = Math.floor((Date.now() - startTime) / 1000 / 60); // in minutes

        logger.logSystem(`Bot has been running for ${runtime} minutes`, 'info');
        console.log(`Bot has been running for ${runtime} minutes`);

        // Log the status
        logger.logSystem(`Status: ${JSON.stringify(status)}`, 'info');

        // Log positions
        const positions = await demoTrading.getPositions();
        logger.logSystem(`Positions: ${JSON.stringify(positions)}`, 'info');

        // Log orders
        const orders = await demoTrading.getOrders();
        logger.logSystem(`Orders: ${JSON.stringify(orders)}`, 'info');
    } catch (error) {
        logger.logSystem(`Error logging status: ${error.message}`, 'error');
    }
}

/**
 * Shutdown the trading bot
 */
async function shutdown() {
    try {
        logger.logSystem('Shutting down overnight trading bot...', 'info');
        console.log('Shutting down overnight trading bot...');

        // Clear intervals
        if (checkIntervalId) {
            clearInterval(checkIntervalId);
        }

        if (statusIntervalId) {
            clearInterval(statusIntervalId);
        }

        // Shutdown the demo trading bot
        await demoTrading.shutdown();

        logger.logSystem('Overnight trading bot shut down successfully', 'info');
        console.log('Overnight trading bot shut down successfully');

        process.exit(0);
    } catch (error) {
        logger.logSystem(`Shutdown failed: ${error.message}`, 'error');
        console.error(`Shutdown failed: ${error.message}`);
        process.exit(1);
    }
}

// Handle process signals
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT. Shutting down...');
    await shutdown();
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM. Shutting down...');
    await shutdown();
});

// Print startup message
console.log('=== Overnight Trading Bot ===');
console.log('Starting bot...');
console.log('This bot will run continuously and check for market data.');
console.log('Press Ctrl+C to stop.');
console.log('');

// Start the bot
initialize();

console.log('Overnight trading bot started. Press Ctrl+C to stop.');
