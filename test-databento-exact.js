/**
 * Test script for Databento integration
 * Following the exact Python example provided by the user
 */

require('dotenv').config();
const WebSocket = require('ws');

// Use the exact API key from the Python example
const API_KEY = "db-ERYWTvYcLAFRA5R6LMXxfjihfncGq";

console.log(`Using Databento API key: ${API_KEY.substring(0, 8)}...`);

// Create a WebSocket connection to Databento
console.log('Connecting to Databento live API...');

// Try different WebSocket URLs
const wsUrls = [
  'wss://api.databento.com/v0/live',
  'wss://live.databento.com/v0',
  'wss://live.databento.com/v0/websocket'
];

let currentUrlIndex = 0;
let ws = null;

function connectWebSocket() {
  if (currentUrlIndex >= wsUrls.length) {
    console.error('All WebSocket URLs failed. Please check your API key and network connection.');
    process.exit(1);
  }

  const wsUrl = wsUrls[currentUrlIndex];
  console.log(`Trying WebSocket URL: ${wsUrl}`);
  
  ws = new WebSocket(wsUrl);
  
  ws.on('open', () => {
    console.log('WebSocket connection established!');
    
    // Authenticate with API key
    console.log('Authenticating...');
    ws.send(JSON.stringify({
      action: 'auth',
      token: API_KEY
    }));
    
    // Set a timeout for authentication response
    setTimeout(() => {
      if (ws.readyState === WebSocket.OPEN) {
        console.log('Authentication successful or no explicit auth response. Subscribing...');
        
        // Subscribe to ES.FUT trades
        ws.send(JSON.stringify({
          action: 'subscribe',
          dataset: 'GLBX.MDP3',
          schema: 'trades',
          stype_in: 'parent',
          symbols: 'ES.FUT'
        }));
        
        console.log('Subscription request sent. Waiting for data...');
      }
    }, 2000);
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log('Received message:', JSON.stringify(message, null, 2));
      
      // If this is a subscription response, check if it was successful
      if (message.type === 'subscription_response') {
        if (message.status === 'success') {
          console.log('Subscription successful!');
        } else {
          console.error('Subscription failed:', message.message);
        }
      }
      
      // If this is market data, display it
      if (message.type === 'data') {
        console.log('Received market data!');
      }
    } catch (error) {
      console.log('Received raw message:', data.toString());
    }
  });
  
  ws.on('error', (error) => {
    console.error(`WebSocket error with URL ${wsUrl}:`, error.message);
    ws.close();
    
    // Try the next URL
    currentUrlIndex++;
    connectWebSocket();
  });
  
  ws.on('close', (code, reason) => {
    if (code !== 1000) { // Normal closure
      console.log(`WebSocket closed with code ${code} and reason: ${reason}`);
    }
  });
}

// Start the connection process
connectWebSocket();

// Keep the process running
const interval = setInterval(() => {
  if (ws && ws.readyState === WebSocket.CLOSED) {
    console.log('WebSocket connection closed. Exiting...');
    clearInterval(interval);
    process.exit(0);
  }
}, 1000);

// Handle process termination
process.on('SIGINT', () => {
  console.log('Received SIGINT. Closing WebSocket connection...');
  if (ws) {
    ws.close();
  }
  clearInterval(interval);
  process.exit(0);
});

// Set a timeout for the entire script
setTimeout(() => {
  console.log('Test timeout reached. Exiting...');
  if (ws) {
    ws.close();
  }
  clearInterval(interval);
  process.exit(0);
}, 30000); // 30 seconds
