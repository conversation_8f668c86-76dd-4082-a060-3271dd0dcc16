# Trading Bot Backtesting Framework

This framework allows you to backtest your trading strategy against 5 years of historical data for MNQ, MES, and MGC instruments.

## Features

- Backtests trading strategy on historical data
- Supports multiple instruments (MNQ, MES, MGC)
- Calculates key performance metrics (win rate, profit factor, drawdown, etc.)
- Generates detailed HTML reports with interactive charts
- Simulates realistic trading conditions (slippage, commission, etc.)
- Implements circuit breakers with daily loss limits

## Files

- `backtest_framework.js` - Core backtesting engine
- `indicators.js` - Technical indicators implementation
- `run_backtest.js` - Main script to run backtests
- `BACKTEST_README.md` - This documentation file

## Configuration

Each instrument has its own configuration in the `run_backtest.js` file:

```javascript
const CONFIGS = {
    MNQ: {
        symbol: 'MNQ',
        pointValue: 2.0,
        commission: 0.40,
        slippage: 0.0,
        fixedContracts: 10,
        rsiPeriod: 14,
        rsiMaPeriod: 8,
        wma50Period: 50,
        atrPeriod: 14,
        slFactors: 4.5,
        tpFactors: 3.0,
        trailFactors: 0.11,
        fixedTpPoints: 40,
        maxDailyLoss: 0.10,
        minAtrEntry: 0,
        minRsiMaSeparation: 0,
        initialBalance: 10000
    },
    // MES and MGC configurations...
};
```

### Configuration Parameters

- `symbol` - Instrument symbol
- `pointValue` - Dollar value per point (MNQ: $2, MES: $5, MGC: $10)
- `commission` - Commission per contract per side
- `slippage` - Slippage in points per trade
- `fixedContracts` - Number of contracts per trade
- `rsiPeriod` - Period for RSI calculation
- `rsiMaPeriod` - Period for RSI-based MA calculation
- `wma50Period` - Period for WMA calculation
- `atrPeriod` - Period for ATR calculation
- `slFactors` - Stop loss factor (multiplied by ATR)
- `tpFactors` - Take profit factor (multiplied by ATR)
- `trailFactors` - Trailing stop factor
- `fixedTpPoints` - Fixed take profit points (if > 0)
- `maxDailyLoss` - Maximum daily loss as percentage of account
- `minAtrEntry` - Minimum ATR for entry
- `minRsiMaSeparation` - Minimum separation between RSI and RSI-MA for entry
- `initialBalance` - Initial account balance

## Data Files

The framework expects historical data files in CSV format:

```javascript
const DATA_PATHS = {
    MNQ: 'C:\\backtest-bot\\input\\MNQ_2020_2025.csv',
    MES: 'C:\\backtest-bot\\input\\MES_2020-2025.csv',
    MGC: 'C:\\backtest-bot\\input\\MGC2020_2025.csv'
};
```

The CSV files should have the following columns:
- timestamp (or date/time)
- open
- high
- low
- close
- volume (optional)

## Running Backtests

To run backtests for all instruments:

```bash
npm run run-backtest
```

This will:
1. Load historical data for each instrument
2. Run the backtest with the configured parameters
3. Generate individual HTML reports for each instrument
4. Generate a combined HTML report with overall statistics

## Reports

Reports are generated in the `C:\backtest-bot\reports` directory:

- `MNQ_backtest_report.html` - MNQ backtest report
- `MES_backtest_report.html` - MES backtest report
- `MGC_backtest_report.html` - MGC backtest report
- `combined_backtest_report.html` - Combined report for all instruments

Each report includes:
- Performance summary (total return, win rate, profit factor, etc.)
- Equity curve chart
- Monthly performance chart
- Trade distribution chart

## Trading Strategy

The framework implements the following trading strategy:

### Entry Conditions
- Long: RSI > RSI-MA and Price > WMA50
- Short: RSI < RSI-MA and Price < WMA50

### Exit Conditions
- Stop Loss: ATR-based stop loss
- Take Profit: ATR-based take profit
- Trailing Stop: Percentage-based trailing stop

### Risk Management
- Fixed position size
- Daily loss limit (10% of account)

## Customization

To customize the trading strategy, you can modify:

1. Entry conditions in the `checkForSignals` method
2. Exit conditions in the `checkExitConditions` method
3. Position sizing in the `openLongPosition` and `openShortPosition` methods
4. Risk management in the `closePosition` method

## Dependencies

- Node.js
- csv-parser

## License

ISC
