/**
 * trading-bot-ui.js
 * UI for the trading bot
 */

import TradingBot from './trading-bot.js';

// Make config globally available for pattern detection
import { commonConfig } from '../../multi_symbol_config.js';
global.config = commonConfig;

// DOM elements
let $startBtn;
let $stopBtn;
let $statusText;
let $balanceText;
let $symbolsText;
let $positionsTable;
let $statsTable;
let $logContainer;

// Trading bot instance
let tradingBot;

/**
 * Initialize the application
 */
function init() {
    console.log('Initializing application...');

    // Get DOM elements
    $startBtn = document.getElementById('start-btn');
    $stopBtn = document.getElementById('stop-btn');
    $statusText = document.getElementById('status-text');
    $balanceText = document.getElementById('balance-text');
    $symbolsText = document.getElementById('symbols-text');
    $positionsTable = document.getElementById('positions-table');
    $statsTable = document.getElementById('stats-table');
    $logContainer = document.getElementById('log-container');

    // Create trading bot instance
    tradingBot = new TradingBot();

    // Set up event listeners
    $startBtn.addEventListener('click', startBot);
    $stopBtn.addEventListener('click', stopBot);

    // Override console.log to display in the UI
    const originalConsoleLog = console.log;
    console.log = function(...args) {
        // Call original console.log
        originalConsoleLog.apply(console, args);

        // Add to log container
        const message = args.map(arg => {
            if (typeof arg === 'object') {
                try {
                    return JSON.stringify(arg);
                } catch (e) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');

        addLogMessage(message);
    };

    // Override console.error to display in the UI
    const originalConsoleError = console.error;
    console.error = function(...args) {
        // Call original console.error
        originalConsoleError.apply(console, args);

        // Add to log container with error class
        const message = args.map(arg => {
            if (typeof arg === 'object') {
                try {
                    return JSON.stringify(arg);
                } catch (e) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');

        addLogMessage(message, 'error');
    };

    // Update UI
    updateUI();

    // Set up interval to update UI
    setInterval(updateUI, 5000);

    console.log('Application initialized');
}

/**
 * Start the trading bot
 */
async function startBot() {
    console.log('Starting trading bot...');

    // Disable start button
    $startBtn.disabled = true;

    // Start the bot
    const started = await tradingBot.start();

    if (started) {
        console.log('Trading bot started successfully');

        // Enable stop button
        $stopBtn.disabled = false;
    } else {
        console.error('Failed to start trading bot');

        // Enable start button
        $startBtn.disabled = false;
    }

    // Update UI
    updateUI();
}

/**
 * Stop the trading bot
 */
function stopBot() {
    console.log('Stopping trading bot...');

    // Disable stop button
    $stopBtn.disabled = true;

    // Stop the bot
    tradingBot.stop();

    // Enable start button
    $startBtn.disabled = false;

    // Update UI
    updateUI();
}

/**
 * Update the UI with current state
 */
function updateUI() {
    if (!tradingBot) {
        return;
    }

    // Get current stats
    const stats = tradingBot.getStats();

    // Update status
    $statusText.textContent = stats.isRunning ? 'Running' : 'Stopped';
    $statusText.className = stats.isRunning ? 'status-running' : 'status-stopped';

    // Update balance
    $balanceText.textContent = `$${stats.balance.toFixed(2)}`;

    // Update symbols
    $symbolsText.textContent = stats.activeSymbols.join(', ') || 'None';

    // Update positions table
    updatePositionsTable(stats.positions);

    // Update stats table
    updateStatsTable(stats.dailyStats);

    // Update buttons
    $startBtn.disabled = stats.isRunning;
    $stopBtn.disabled = !stats.isRunning;
}

/**
 * Update the positions table
 * @param {Array} positions - Array of positions
 */
function updatePositionsTable(positions) {
    // Clear table
    $positionsTable.innerHTML = '';

    // Create header row
    const headerRow = document.createElement('tr');
    headerRow.innerHTML = `
        <th>Symbol</th>
        <th>Quantity</th>
        <th>Entry Price</th>
        <th>Current P&L</th>
    `;
    $positionsTable.appendChild(headerRow);

    // Add positions
    if (positions.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="4" class="empty-row">No open positions</td>
        `;
        $positionsTable.appendChild(emptyRow);
    } else {
        for (const position of positions) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${position.symbol}</td>
                <td>${position.quantity}</td>
                <td>${position.entryPrice?.toFixed(2) || 'N/A'}</td>
                <td class="${position.currentPnL > 0 ? 'positive' : position.currentPnL < 0 ? 'negative' : ''}">${position.currentPnL?.toFixed(2) || 'N/A'}</td>
            `;
            $positionsTable.appendChild(row);
        }
    }
}

/**
 * Update the stats table
 * @param {Object} stats - Daily stats object
 */
function updateStatsTable(stats) {
    // Clear table
    $statsTable.innerHTML = '';

    // Create rows
    const rows = [
        { label: 'Trades', value: stats.trades },
        { label: 'Wins', value: stats.wins },
        { label: 'Losses', value: stats.losses },
        { label: 'Win Rate', value: stats.winRate },
        { label: 'P&L', value: `$${stats.pnl.toFixed(2)}`, class: stats.pnl > 0 ? 'positive' : stats.pnl < 0 ? 'negative' : '' },
        { label: 'Start Balance', value: `$${stats.startBalance.toFixed(2)}` },
        { label: 'Current Balance', value: `$${stats.currentBalance.toFixed(2)}` },
        { label: 'Max Drawdown', value: `$${stats.maxDrawdown.toFixed(2)}` }
    ];

    // Add rows to table
    for (const row of rows) {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.label}</td>
            <td class="${row.class || ''}">${row.value}</td>
        `;
        $statsTable.appendChild(tr);
    }
}

/**
 * Add a message to the log container
 * @param {string} message - Message to add
 * @param {string} type - Message type ('log' or 'error')
 */
function addLogMessage(message, type = 'log') {
    // Create log entry
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;

    // Add timestamp
    const timestamp = new Date().toLocaleTimeString();
    logEntry.textContent = `[${timestamp}] ${message}`;

    // Add to container
    $logContainer.appendChild(logEntry);

    // Scroll to bottom
    $logContainer.scrollTop = $logContainer.scrollHeight;

    // Limit log entries
    while ($logContainer.children.length > 100) {
        $logContainer.removeChild($logContainer.firstChild);
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', init);

// Export functions for testing
export {
    init,
    startBot,
    stopBot,
    updateUI,
    updatePositionsTable,
    updateStatsTable,
    addLogMessage
};
