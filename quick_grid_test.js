/**
 * Quick Grid Test
 * Run a quick grid test on a single market
 * 
 * Usage:
 * node quick_grid_test.js <symbol> [--focused]
 * 
 * Example:
 * node quick_grid_test.js MNQ --focused
 */

const fs = require('fs');
const path = require('path');
const GridBacktest = require('./grid_backtest');
const gridConfig = require('./grid_config');

// Parse command line arguments
const args = process.argv.slice(2);
const symbol = args[0] || 'MNQ';
const useFocused = args.includes('--focused');

// Get configuration from grid_config.js
const OPTIONS = gridConfig.options;
const GRID_PARAMS = useFocused ? 
    gridConfig.focusedGridParams : 
    gridConfig.gridParams;
const CONFIGS = gridConfig.configs;
const DATA_PATHS = gridConfig.dataPaths;

// Validate symbol
if (!CONFIGS[symbol]) {
    console.error(`Error: Symbol ${symbol} not found in configuration.`);
    console.error(`Available symbols: ${Object.keys(CONFIGS).join(', ')}`);
    process.exit(1);
}

// Set output directory
const outputDir = path.join(OPTIONS.outputDir, symbol);
CONFIGS[symbol].outputDir = outputDir;

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

/**
 * Run grid test for a single instrument
 * @param {string} symbol - Instrument symbol
 * @returns {Promise<Object>} - Grid test results
 */
async function runGridTest(symbol) {
    console.log(`\n===== RUNNING GRID TEST FOR ${symbol} =====`);
    console.log(`Date range: ${OPTIONS.dateRange.startDate} to ${OPTIONS.dateRange.endDate}`);
    console.log(`Using ${useFocused ? 'focused' : 'full'} grid test mode`);
    console.log(`Grid parameters: ${JSON.stringify(GRID_PARAMS, null, 2)}`);
    
    // Create backtest instance with configuration
    const config = CONFIGS[symbol];
    const backtest = new GridBacktest(config);
    
    // Load historical data with options
    await backtest.loadData(DATA_PATHS[symbol], {
        dateRange: OPTIONS.dateRange
    });
    
    // Run grid test
    const results = backtest.runGridTest(GRID_PARAMS);
    
    // Save results to file
    const resultsFilename = `grid_test_results_${useFocused ? 'focused' : 'full'}.json`;
    const resultsPath = path.join(outputDir, resultsFilename);
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    
    // Generate HTML report
    generateHtmlReport(symbol, results, outputDir);
    
    console.log(`\nGrid test for ${symbol} completed.`);
    console.log(`Results saved to ${resultsPath}`);
    
    return results;
}

/**
 * Generate HTML report for grid test results
 * @param {string} symbol - Instrument symbol
 * @param {Array} results - Grid test results
 * @param {string} outputDir - Output directory
 */
function generateHtmlReport(symbol, results, outputDir) {
    const reportFilename = `grid_test_report_${useFocused ? 'focused' : 'full'}.html`;
    const reportPath = path.join(outputDir, reportFilename);
    
    // Create HTML content
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${symbol} Grid Test Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${symbol} Grid Test Report</h1>
        <p>Date range: ${OPTIONS.dateRange.startDate} to ${OPTIONS.dateRange.endDate}</p>
        <p>Mode: ${useFocused ? 'Focused' : 'Full'} grid test</p>
        
        <h2>Top 20 Parameter Combinations</h2>
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>SL</th>
                    <th>TP</th>
                    <th>Trail</th>
                    <th>Fixed TP</th>
                    <th>Total PnL</th>
                    <th>Return</th>
                    <th>Win Rate</th>
                    <th>Win Day Rate</th>
                    <th>Profit Factor</th>
                    <th>Max DD</th>
                    <th>Trades</th>
                </tr>
            </thead>
            <tbody>
                ${results.slice(0, 20).map((result, index) => `
                <tr>
                    <td>${index + 1}</td>
                    <td>${result.params.slFactors}</td>
                    <td>${result.params.tpFactors}</td>
                    <td>${result.params.trailFactors}</td>
                    <td>${result.params.fixedTpPoints}</td>
                    <td class="${result.stats.totalPnl >= 0 ? 'positive' : 'negative'}">$${result.stats.totalPnl.toFixed(2)}</td>
                    <td class="${result.stats.totalReturn >= 0 ? 'positive' : 'negative'}">${result.stats.totalReturn.toFixed(2)}%</td>
                    <td>${result.stats.winRate.toFixed(2)}%</td>
                    <td>${result.stats.winDayRate.toFixed(2)}%</td>
                    <td>${typeof result.stats.profitFactor === 'number' ? result.stats.profitFactor.toFixed(2) : result.stats.profitFactor}</td>
                    <td>${result.stats.maxDrawdown.toFixed(2)}%</td>
                    <td>${result.stats.totalTrades}</td>
                </tr>
                `).join('')}
            </tbody>
        </table>
        
        <h2>Best Parameters Summary</h2>
        <p>Based on the grid test results, the optimal parameters for ${symbol} are:</p>
        <ul>
            <li><strong>Stop Loss Factor:</strong> ${results[0].params.slFactors}</li>
            <li><strong>Take Profit Factor:</strong> ${results[0].params.tpFactors}</li>
            <li><strong>Trail Factor:</strong> ${results[0].params.trailFactors}</li>
            <li><strong>Fixed TP Points:</strong> ${results[0].params.fixedTpPoints}</li>
        </ul>
        
        <h3>Performance with Optimal Parameters</h3>
        <ul>
            <li><strong>Total PnL:</strong> $${results[0].stats.totalPnl.toFixed(2)}</li>
            <li><strong>Return:</strong> ${results[0].stats.totalReturn.toFixed(2)}%</li>
            <li><strong>Win Rate:</strong> ${results[0].stats.winRate.toFixed(2)}%</li>
            <li><strong>Win Day Rate:</strong> ${results[0].stats.winDayRate.toFixed(2)}%</li>
            <li><strong>Profit Factor:</strong> ${typeof results[0].stats.profitFactor === 'number' ? results[0].stats.profitFactor.toFixed(2) : results[0].stats.profitFactor}</li>
            <li><strong>Max Drawdown:</strong> ${results[0].stats.maxDrawdown.toFixed(2)}%</li>
            <li><strong>Total Trades:</strong> ${results[0].stats.totalTrades}</li>
        </ul>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(reportPath, html);
    console.log(`HTML report generated at ${reportPath}`);
}

// Run grid test
runGridTest(symbol).catch(error => {
    console.error(`Error running grid test for ${symbol}:`, error);
});
