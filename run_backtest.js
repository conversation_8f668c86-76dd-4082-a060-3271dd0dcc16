/**
 * Run backtest on historical data
 */

const fs = require('fs');
const path = require('path');
const Backtest = require('./backtest_framework');

// Runtime options
const OPTIONS = {
    // Set to true to run a quick test with limited data
    quickTest: true,

    // Date range options
    dateRange: {
        // Use custom date range instead of days from now
        useCustomRange: true,
        // Start date (YYYY-MM-DD)
        startDate: '2025-01-01',
        // End date (YYYY-MM-DD) - leave empty for current date
        endDate: '2025-01-31'  // Just January 2025 for a quick test
    },

    // Number of days to use for quick test (only used if useCustomRange is false)
    quickTestDays: 30,

    // Symbols to test (comment out to disable)
    symbols: [
        'MNQ',
        'MES',
        'MGC'
    ],

    // Verbose mode (set to false to reduce console output)
    verbose: false
};

// Configuration for each instrument
const CONFIGS = {
    MNQ: {
        symbol: 'MNQ',
        pointValue: 2.0,
        commission: 0.40,
        slippage: 0.0,
        fixedContracts: 10,
        rsiPeriod: 14,
        rsiMaPeriod: 8,
        wma50Period: 50,
        atrPeriod: 14,
        slFactors: 4.5,
        tpFactors: 3.0,
        trailFactors: 0.11,
        fixedTpPoints: 40,
        maxDailyLoss: 0.10,
        minAtrEntry: 0.5,  // Minimum ATR for entry
        minRsiMaSeparation: 1.0,  // Minimum RSI-MA separation
        useWmaFilter: true,  // Use WMA filter for entries
        pricePrecision: 2,  // Price precision for logging
        initialBalance: 10000
    },
    MES: {
        symbol: 'MES',
        pointValue: 5.0,
        commission: 0.40,
        slippage: 0.0,
        fixedContracts: 10,
        rsiPeriod: 14,
        rsiMaPeriod: 8,
        wma50Period: 50,
        atrPeriod: 14,
        slFactors: 3.0,
        tpFactors: 3.0,
        trailFactors: 0.01,
        fixedTpPoints: 0,
        maxDailyLoss: 0.10,
        minAtrEntry: 0.5,  // Minimum ATR for entry
        minRsiMaSeparation: 1.0,  // Minimum RSI-MA separation
        useWmaFilter: true,  // Use WMA filter for entries
        pricePrecision: 2,  // Price precision for logging
        initialBalance: 10000
    },
    MGC: {
        symbol: 'MGC',
        pointValue: 10.0,
        commission: 0.40,
        slippage: 0.0,
        fixedContracts: 10,
        rsiPeriod: 14,
        rsiMaPeriod: 8,
        wma50Period: 50,
        atrPeriod: 14,
        slFactors: 8.0,
        tpFactors: 7.0,
        trailFactors: 0.02,
        fixedTpPoints: 0,
        maxDailyLoss: 0.10,
        minAtrEntry: 0.5,  // Minimum ATR for entry
        minRsiMaSeparation: 1.0,  // Minimum RSI-MA separation
        useWmaFilter: true,  // Use WMA filter for entries
        pricePrecision: 2,  // Price precision for logging
        initialBalance: 10000
    }
};

// File paths for historical data
const DATA_PATHS = {
    MNQ: 'C:\\backtest-bot\\input\\MNQ_2020_2025.csv',
    MES: 'C:\\backtest-bot\\input\\MES_2020-2025.csv',
    MGC: 'C:\\backtest-bot\\input\\MGC2020_2025.csv'
};

// Output directory for reports
const REPORT_DIR = 'C:\\backtest-bot\\reports';

// Ensure report directory exists
if (!fs.existsSync(REPORT_DIR)) {
    fs.mkdirSync(REPORT_DIR, { recursive: true });
}

/**
 * Run backtest for a single instrument
 * @param {string} symbol - Instrument symbol
 * @returns {Promise<Object>} - Backtest results
 */
async function runBacktest(symbol) {
    console.log(`\n===== RUNNING BACKTEST FOR ${symbol} =====`);

    // Create backtest instance with configuration and options
    const backtest = new Backtest(CONFIGS[symbol], OPTIONS);

    // Load historical data with options
    await backtest.loadData(DATA_PATHS[symbol], {
        quickTest: OPTIONS.quickTest,
        quickTestDays: OPTIONS.quickTestDays,
        dateRange: OPTIONS.dateRange
    });

    // Run backtest
    backtest.run();

    // Generate HTML report
    const reportPath = path.join(REPORT_DIR, `${symbol}_backtest_report.html`);
    backtest.generateReport(reportPath);

    return backtest;
}

/**
 * Run backtest for all instruments
 */
async function runAllBacktests() {
    const results = {};

    // Get symbols to test
    const symbolsToTest = OPTIONS.symbols || Object.keys(CONFIGS);

    console.log(`\n===== BACKTEST CONFIGURATION =====`);
    console.log(`Quick test mode: ${OPTIONS.quickTest ? 'Enabled' : 'Disabled'}`);
    if (OPTIONS.quickTest) {
        console.log(`Quick test days: ${OPTIONS.quickTestDays}`);
    }
    console.log(`Symbols to test: ${symbolsToTest.join(', ')}`);
    console.log(`\n`);

    // Run backtest for each instrument
    for (const symbol of symbolsToTest) {
        if (!CONFIGS[symbol]) {
            console.error(`Configuration not found for symbol: ${symbol}`);
            continue;
        }

        try {
            results[symbol] = await runBacktest(symbol);
        } catch (error) {
            console.error(`Error running backtest for ${symbol}:`, error);
        }
    }

    // Generate combined report
    generateCombinedReport(results);
}

/**
 * Generate combined report for all instruments
 * @param {Object} results - Backtest results for all instruments
 */
function generateCombinedReport(results) {
    console.log('\n===== COMBINED RESULTS =====');

    // Calculate combined statistics
    let totalInitialBalance = 0;
    let totalFinalBalance = 0;
    let totalTrades = 0;
    let totalWins = 0;
    let totalProfitDays = 0;
    let totalDays = 0;

    const summaryTable = [];
    const validResults = {};

    // Check which results have data
    for (const symbol of Object.keys(results)) {
        if (results[symbol].candles.length === 0) {
            console.log(`No data for ${symbol}, skipping from combined results`);
            continue;
        }

        validResults[symbol] = results[symbol];
    }

    // If no valid results, generate error report
    if (Object.keys(validResults).length === 0) {
        console.log('No valid data for any instrument');

        // Print empty statistics
        console.log('\nOVERALL STATISTICS:');
        console.log(`Initial Balance: $${totalInitialBalance.toFixed(2)}`);
        console.log(`Final Balance: $${totalFinalBalance.toFixed(2)}`);
        console.log(`Total Return: NaN%`);
        console.log(`Overall Win Rate: NaN%`);
        console.log(`Overall Win Day Rate: NaN%`);
        console.log(`Total Trades: ${totalTrades}`);

        // Generate error report
        generateErrorReport();
        return;
    }

    // Process valid results
    for (const symbol of Object.keys(validResults)) {
        const stats = validResults[symbol].calculateStatistics();

        if (!stats) continue;

        totalInitialBalance += stats.initialBalance || 0;
        totalFinalBalance += stats.finalBalance || 0;
        totalTrades += stats.totalTrades || 0;
        totalWins += Math.round((stats.totalTrades || 0) * (stats.winRate || 0) / 100);
        totalProfitDays += stats.profitDays || 0;
        totalDays += stats.totalDays || 0;

        summaryTable.push({
            Symbol: symbol,
            'Total Return': `${(stats.totalReturn || 0).toFixed(2)}%`,
            'Win Rate': `${(stats.winRate || 0).toFixed(2)}%`,
            'Win Day Rate': `${(stats.winDayRate || 0).toFixed(2)}%`,
            'Profit Factor': (stats.profitFactor || 0).toFixed(2),
            'Max Drawdown': `${(stats.maxDrawdown || 0).toFixed(2)}%`,
            'Total Trades': stats.totalTrades || 0
        });
    }

    // Calculate overall statistics
    const overallReturn = totalInitialBalance === 0 ? 0 : ((totalFinalBalance - totalInitialBalance) / totalInitialBalance) * 100;
    const overallWinRate = totalTrades === 0 ? 0 : (totalWins / totalTrades) * 100;
    const overallWinDayRate = totalDays === 0 ? 0 : (totalProfitDays / totalDays) * 100;

    // Print summary table
    console.table(summaryTable);

    // Print overall statistics
    console.log('\nOVERALL STATISTICS:');
    console.log(`Initial Balance: $${totalInitialBalance.toFixed(2)}`);
    console.log(`Final Balance: $${totalFinalBalance.toFixed(2)}`);
    console.log(`Total Return: ${overallReturn.toFixed(2)}%`);
    console.log(`Overall Win Rate: ${overallWinRate.toFixed(2)}%`);
    console.log(`Overall Win Day Rate: ${overallWinDayRate.toFixed(2)}%`);
    console.log(`Total Trades: ${totalTrades}`);

    // Generate combined HTML report
    const reportPath = path.join(REPORT_DIR, 'combined_backtest_report.html');

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Combined Backtest Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #4caf50;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .positive {
            color: #4caf50;
        }
        .negative {
            color: #f44336;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        th {
            background-color: #333;
            color: #4caf50;
        }
        tr:hover {
            background-color: #333;
        }
        .chart-container {
            margin-bottom: 30px;
            height: 400px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>Combined Backtest Report</h1>

        <h2>Overall Performance</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Return</h3>
                <div class="stat-value ${overallReturn >= 0 ? 'positive' : 'negative'}">${overallReturn.toFixed(2)}%</div>
            </div>
            <div class="stat-card">
                <h3>Final Balance</h3>
                <div class="stat-value ${totalFinalBalance >= totalInitialBalance ? 'positive' : 'negative'}">$${totalFinalBalance.toFixed(2)}</div>
                <div>Initial: $${totalInitialBalance.toFixed(2)}</div>
            </div>
            <div class="stat-card">
                <h3>Win Rate</h3>
                <div class="stat-value">${overallWinRate.toFixed(2)}%</div>
                <div>${totalTrades} total trades</div>
            </div>
            <div class="stat-card">
                <h3>Win Day Rate</h3>
                <div class="stat-value">${overallWinDayRate.toFixed(2)}%</div>
                <div>${totalDays} total days</div>
            </div>
        </div>

        <h2>Instrument Comparison</h2>
        <table>
            <thead>
                <tr>
                    <th>Symbol</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Win Day Rate</th>
                    <th>Profit Factor</th>
                    <th>Max Drawdown</th>
                    <th>Total Trades</th>
                </tr>
            </thead>
            <tbody>
                ${summaryTable.map(row => `
                <tr>
                    <td>${row.Symbol}</td>
                    <td>${row['Total Return']}</td>
                    <td>${row['Win Rate']}</td>
                    <td>${row['Win Day Rate']}</td>
                    <td>${row['Profit Factor']}</td>
                    <td>${row['Max Drawdown']}</td>
                    <td>${row['Total Trades']}</td>
                </tr>
                `).join('')}
            </tbody>
        </table>

        <h2>Links to Individual Reports</h2>
        <ul>
            ${Object.keys(results).map(symbol => `
            <li><a href="./${symbol}_backtest_report.html" style="color: #4caf50;">${symbol} Backtest Report</a></li>
            `).join('')}
        </ul>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`Combined report generated at ${reportPath}`);
}

/**
 * Generate error report when no data is available
 */
function generateErrorReport() {
    const reportPath = path.join(REPORT_DIR, 'combined_backtest_report.html');

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtest Error Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #e0e0e0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #f44336;
        }
        .error-box {
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #f44336;
        }
        code {
            background-color: #333;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        ul {
            margin-top: 20px;
        }
        li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Backtest Error Report</h1>

        <div class="error-box">
            <h2>No Data Available</h2>
            <p>The backtest could not be run because no data was loaded for any instrument.</p>

            <h3>Possible Causes:</h3>
            <ul>
                <li>The data files do not exist at the specified paths</li>
                <li>The data files exist but are empty or have invalid format</li>
                <li>The CSV column headers do not match the expected format</li>
            </ul>

            <h3>Data Paths:</h3>
            <ul>
                ${Object.entries(DATA_PATHS).map(([symbol, path]) => `
                <li><strong>${symbol}:</strong> <code>${path}</code> - ${fs.existsSync(path) ? 'File exists' : 'File not found'}</li>
                `).join('')}
            </ul>

            <h3>Next Steps:</h3>
            <ul>
                <li>Check that the data files exist at the specified paths</li>
                <li>Verify that the CSV files have the correct format (timestamp/date, open, high, low, close columns)</li>
                <li>Try running the backtest with a single instrument to isolate the issue</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync(reportPath, html);
    console.log(`Error report generated at ${reportPath}`);
}

// Run all backtests
runAllBacktests().catch(error => {
    console.error('Error running backtests:', error);
});
