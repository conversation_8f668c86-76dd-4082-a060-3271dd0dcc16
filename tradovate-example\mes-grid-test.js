/**
 * mes-grid-test.js
 * Comprehensive grid test for MES parameters
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');
const { mesConfig } = require('../multi_symbol_config');

// Define parameter ranges for grid testing
const parameterRanges = {
    slFactors: [2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5],
    tpFactors: [2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0],
    trailFactors: [0.005, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1],
    atrLowMedium: [2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0],
    atrMediumHigh: [4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0]
};

// Function to run a single test with specific parameters
async function runSingleTest(params) {
    // Create a config with the specified parameters
    const testConfig = {
        ...mesConfig,
        slFactors: params.slFactors,
        tpFactors: params.tpFactors,
        trailFactors: params.trailFactors,
        atrThresholds: {
            low_medium: params.atrLowMedium,
            medium_high: params.atrMediumHigh
        },
        adaptiveParams: {
            Low: {
                slFactor: params.slFactors,
                tpFactor: params.tpFactors,
                trailFactor: params.trailFactors
            },
            Medium: {
                slFactor: params.slFactors,
                tpFactor: params.tpFactors,
                trailFactor: params.trailFactors
            },
            High: {
                slFactor: params.slFactors,
                tpFactor: params.tpFactors,
                trailFactor: params.trailFactors
            }
        }
    };
    
    // Run backtest
    const result = await runBacktest(testConfig);
    
    // Calculate win rate
    const winRate = (result.wins / result.totalTrades * 100).toFixed(2);
    
    // Calculate P&L
    const pnl = result.finalBalance - testConfig.initialBalance;
    
    // Calculate profit factor
    const profitFactor = result.wins > 0 ? 
        (pnl + result.losses * testConfig.commissionPerContract * testConfig.fixedContracts) / 
        (result.losses * testConfig.commissionPerContract * testConfig.fixedContracts) : 0;
    
    return {
        slFactors: params.slFactors,
        tpFactors: params.tpFactors,
        trailFactors: params.trailFactors,
        atrLowMedium: params.atrLowMedium,
        atrMediumHigh: params.atrMediumHigh,
        totalTrades: result.totalTrades,
        wins: result.wins,
        losses: result.losses,
        winRate: parseFloat(winRate),
        pnl: pnl,
        maxDrawdown: result.maxDrawdown,
        profitFactor: profitFactor.toFixed(2)
    };
}

// Function to run a focused grid test
async function runFocusedGridTest() {
    console.log("Starting focused grid test for MES...");
    
    // Use the current best parameters as a starting point
    const baseParams = {
        slFactors: 3.0,
        tpFactors: 3.0,
        trailFactors: 0.01,
        atrLowMedium: 3.0,
        atrMediumHigh: 5.0
    };
    
    // Define focused parameter ranges around the current best
    const focusedRanges = {
        slFactors: [2.5, 3.0, 3.5],
        tpFactors: [2.5, 3.0, 3.5],
        trailFactors: [0.005, 0.01, 0.015, 0.02],
        atrLowMedium: [2.5, 3.0, 3.5],
        atrMediumHigh: [4.5, 5.0, 5.5]
    };
    
    // Generate test combinations
    const testCombinations = [];
    
    // Test SL and TP factors
    for (const sl of focusedRanges.slFactors) {
        for (const tp of focusedRanges.tpFactors) {
            testCombinations.push({
                ...baseParams,
                slFactors: sl,
                tpFactors: tp
            });
        }
    }
    
    // Test trail factors
    for (const trail of focusedRanges.trailFactors) {
        testCombinations.push({
            ...baseParams,
            trailFactors: trail
        });
    }
    
    // Test ATR thresholds
    for (const lowMedium of focusedRanges.atrLowMedium) {
        for (const mediumHigh of focusedRanges.atrMediumHigh) {
            if (lowMedium < mediumHigh) {
                testCombinations.push({
                    ...baseParams,
                    atrLowMedium: lowMedium,
                    atrMediumHigh: mediumHigh
                });
            }
        }
    }
    
    // Run tests
    const results = [];
    let testCount = 0;
    const totalTests = testCombinations.length;
    
    for (const params of testCombinations) {
        testCount++;
        console.log(`Running test ${testCount}/${totalTests}: SL=${params.slFactors}, TP=${params.tpFactors}, Trail=${params.trailFactors}, ATR=${params.atrLowMedium}/${params.atrMediumHigh}`);
        
        const result = await runSingleTest(params);
        results.push(result);
        
        // Log progress
        console.log(`Test ${testCount}/${totalTests} completed: Win Rate=${result.winRate}%, P&L=$${result.pnl.toFixed(2)}, Max DD=$${result.maxDrawdown.toFixed(2)}`);
    }
    
    // Sort results by P&L
    results.sort((a, b) => b.pnl - a.pnl);
    
    // Display top results
    console.log("\n=== TOP 10 RESULTS BY P&L ===");
    console.table(results.slice(0, 10));
    
    // Sort results by win rate
    results.sort((a, b) => b.winRate - a.winRate);
    
    // Display top results by win rate
    console.log("\n=== TOP 10 RESULTS BY WIN RATE ===");
    console.table(results.slice(0, 10));
    
    // Sort results by profit factor
    results.sort((a, b) => b.profitFactor - a.profitFactor);
    
    // Display top results by profit factor
    console.log("\n=== TOP 10 RESULTS BY PROFIT FACTOR ===");
    console.table(results.slice(0, 10));
    
    // Save all results to file
    const resultsFile = path.join(__dirname, 'mes_grid_test_results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    console.log(`\nAll results saved to ${resultsFile}`);
    
    // Return the best result
    return results[0];
}

// Run the focused grid test
runFocusedGridTest()
    .then(bestResult => {
        console.log("\n=== BEST CONFIGURATION ===");
        console.log(`SL Factor: ${bestResult.slFactors}`);
        console.log(`TP Factor: ${bestResult.tpFactors}`);
        console.log(`Trail Factor: ${bestResult.trailFactors}`);
        console.log(`ATR Low-Medium: ${bestResult.atrLowMedium}`);
        console.log(`ATR Medium-High: ${bestResult.atrMediumHigh}`);
        console.log(`Win Rate: ${bestResult.winRate}%`);
        console.log(`P&L: $${bestResult.pnl.toFixed(2)}`);
        console.log(`Max Drawdown: $${bestResult.maxDrawdown.toFixed(2)}`);
        console.log(`Profit Factor: ${bestResult.profitFactor}`);
    })
    .catch(error => {
        console.error('Error running grid test:', error);
    });
