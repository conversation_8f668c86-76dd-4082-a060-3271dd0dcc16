/**
 * Demo Trading Script
 *
 * This script connects to the Tradovate API and runs the trading bot in demo mode.
 * It uses the same trading logic as the backtest but executes trades through the Tradovate API.
 */

const tradovateApi = require('./tradovate_api');
const tradingLogic = require('./trading_logic');
const logger = require('./data_logger');
const alerts = require('./alert_system');
const orderManager = require('./order_manager');
const { mnqConfig, mesConfig, mgcConfig } = require('./multi_symbol_config');

// Global variables
let isConnected = false;
let webSocketId = null;
let mdWebSocketId = null;
let chartSubscriptions = {};
let accountId = null;

// Initialize the trading bot
async function initialize() {
    try {
        logger.logSystem('Initializing demo trading bot...', 'info');

        // Authenticate with Tradovate API
        const authResult = await tradovateApi.authenticate();
        if (!authResult.success) {
            throw new Error(`Authentication failed: ${authResult.error}`);
        }
        logger.logSystem('Authentication successful', 'info');

        // Use the account ID from the config
        accountId = '4690440'; // Hardcoded for demo purposes
        logger.logSystem(`Using account ID: ${accountId}`, 'info');

        // Update configs with account ID
        mnqConfig.accountId = accountId;
        mesConfig.accountId = accountId;
        mgcConfig.accountId = accountId;

        // Initialize market data for each symbol
        tradingLogic.initializeMarket('MNQ', mnqConfig);
        tradingLogic.initializeMarket('MES', mesConfig);
        tradingLogic.initializeMarket('MGC', mgcConfig);
        logger.logSystem('Market data initialized for all symbols', 'info');

        // Connect to WebSocket for user data
        const wsResult = await tradovateApi.connectWebSocket();
        if (!wsResult.success) {
            throw new Error(`WebSocket connection failed: ${wsResult.error}`);
        }
        webSocketId = wsResult.id;
        logger.logSystem(`Connected to user WebSocket with ID: ${webSocketId}`, 'info');

        // Connect to WebSocket for market data
        const mdWsResult = await tradovateApi.connectMarketDataWebSocket();
        if (!mdWsResult.success) {
            throw new Error(`Market data WebSocket connection failed: ${mdWsResult.error}`);
        }
        mdWebSocketId = mdWsResult.id;
        logger.logSystem(`Connected to market data WebSocket with ID: ${mdWebSocketId}`, 'info');

        // Subscribe to user sync request to get real-time account updates
        await tradovateApi.sendWebSocketMessage({
            url: 'user/syncrequest',
            params: {
                accounts: [accountId],
                subscriptionPlans: []
            }
        });
        logger.logSystem('Subscribed to user sync request', 'info');

        // Subscribe to chart data for each symbol
        await subscribeToCharts();

        isConnected = true;
        logger.logSystem('Demo trading bot initialized successfully', 'info');
        console.log('Demo trading bot initialized successfully');

        return true;
    } catch (error) {
        logger.logSystem(`Initialization failed: ${error.message}`, 'error');
        console.error(`Initialization failed: ${error.message}`);
        return false;
    }
}

// Subscribe to chart data for all symbols
async function subscribeToCharts() {
    try {
        // Subscribe to MNQ chart data
        const mnqSubscription = await tradovateApi.subscribeToChart({
            symbol: `MNQ${mnqConfig.contractMonth}`,
            chartDescription: {
                underlyingType: 'Futures',
                chartType: 'Minutes',
                elementSize: mnqConfig.timeframe || 1
            }
        });
        chartSubscriptions.MNQ = mnqSubscription;
        logger.logSystem(`Subscribed to MNQ${mnqConfig.contractMonth} chart data`, 'info');

        // Subscribe to MES chart data
        const mesSubscription = await tradovateApi.subscribeToChart({
            symbol: `MES${mesConfig.contractMonth}`,
            chartDescription: {
                underlyingType: 'Futures',
                chartType: 'Minutes',
                elementSize: mesConfig.timeframe || 1
            }
        });
        chartSubscriptions.MES = mesSubscription;
        logger.logSystem(`Subscribed to MES${mesConfig.contractMonth} chart data`, 'info');

        // Subscribe to MGC chart data
        const mgcSubscription = await tradovateApi.subscribeToChart({
            symbol: `MGC${mgcConfig.contractMonth}`,
            chartDescription: {
                underlyingType: 'Futures',
                chartType: 'Minutes',
                elementSize: mgcConfig.timeframe || 1
            }
        });
        chartSubscriptions.MGC = mgcSubscription;
        logger.logSystem(`Subscribed to MGC${mgcConfig.contractMonth} chart data`, 'info');

        return true;
    } catch (error) {
        logger.logSystem(`Failed to subscribe to charts: ${error.message}`, 'error');
        return false;
    }
}

// Process incoming WebSocket messages
function processWebSocketMessage(message) {
    try {
        // Handle different message types
        if (message.e === 'md') {
            // Market data message
            processMarketDataMessage(message);
        } else if (message.e === 'user') {
            // User data message (orders, positions, etc.)
            processUserDataMessage(message);
        } else if (message.e === 'chart') {
            // Chart data message
            processChartDataMessage(message);
        } else {
            logger.logSystem(`Received unknown message type: ${message.e}`, 'warning');
        }
    } catch (error) {
        logger.logSystem(`Error processing WebSocket message: ${error.message}`, 'error');
    }
}

// Process market data messages
function processMarketDataMessage(message) {
    // Handle market data updates (quotes, DOM, etc.)
    logger.logSystem(`Received market data message: ${JSON.stringify(message)}`, 'debug');
}

// Process user data messages
function processUserDataMessage(message) {
    // Handle user data updates (orders, positions, account balance, etc.)
    logger.logSystem(`Received user data message: ${JSON.stringify(message)}`, 'debug');
}

// Process chart data messages
async function processChartDataMessage(message) {
    try {
        if (!message.d || !message.d.charts) {
            return;
        }

        // Process each chart update
        for (const chartId in message.d.charts) {
            const chartData = message.d.charts[chartId];
            const symbol = getSymbolFromChartId(chartId);

            if (!symbol) {
                logger.logSystem(`Unknown chart ID: ${chartId}`, 'warning');
                continue;
            }

            // Process each candle in the update
            if (chartData.candles && chartData.candles.length > 0) {
                logger.logSystem(`Received ${chartData.candles.length} candles for ${symbol}`, 'info');

                // Process the most recent complete candle
                const candles = chartData.candles.filter(c => c.completed);
                if (candles.length > 0) {
                    const latestCandle = candles[candles.length - 1];

                    // Convert to our candle format
                    const formattedCandle = {
                        timestamp: new Date(latestCandle.timestamp).getTime(),
                        open: latestCandle.open,
                        high: latestCandle.high,
                        low: latestCandle.low,
                        close: latestCandle.close,
                        volume: latestCandle.volume
                    };

                    // Process the candle through our trading logic
                    await tradingLogic.processCandle(symbol, formattedCandle);
                }
            }
        }
    } catch (error) {
        logger.logSystem(`Error processing chart data: ${error.message}`, 'error');
    }
}

// Get symbol from chart ID
function getSymbolFromChartId(chartId) {
    for (const symbol in chartSubscriptions) {
        if (chartSubscriptions[symbol] && chartSubscriptions[symbol].chartId === chartId) {
            return symbol;
        }
    }
    return null;
}

// Shutdown the trading bot
async function shutdown() {
    try {
        logger.logSystem('Shutting down demo trading bot...', 'info');

        // Disconnect from WebSockets
        if (webSocketId) {
            await tradovateApi.disconnectWebSocket(webSocketId);
            logger.logSystem('Disconnected from user WebSocket', 'info');
        }

        if (mdWebSocketId) {
            await tradovateApi.disconnectWebSocket(mdWebSocketId);
            logger.logSystem('Disconnected from market data WebSocket', 'info');
        }

        isConnected = false;
        logger.logSystem('Demo trading bot shutdown complete', 'info');
        console.log('Demo trading bot shutdown complete');

        return true;
    } catch (error) {
        logger.logSystem(`Shutdown failed: ${error.message}`, 'error');
        console.error(`Shutdown failed: ${error.message}`);
        return false;
    }
}

// Start the demo trading bot
async function start() {
    const initResult = await initialize();
    if (!initResult) {
        logger.logSystem('Failed to initialize demo trading bot', 'error');
        return false;
    }

    // Set up WebSocket message handlers
    tradovateApi.onWebSocketMessage = processWebSocketMessage;

    logger.logSystem('Demo trading bot started', 'info');
    console.log('Demo trading bot started');
    return true;
}

// Main function
async function main() {
    try {
        await start();
    } catch (error) {
        logger.logSystem(`Error in main function: ${error.message}`, 'error');
        await shutdown();
    }
}

// Run the main function
if (require.main === module) {
    main().catch(error => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}

// Export functions for external use
module.exports = {
    start,
    shutdown,
    isConnected: () => isConnected
};
