/**
 * Trading Logic Module
 *
 * This module implements the core trading strategy from the backtest code
 * for use in the paper trading system.
 */

const logger = require('./data_logger');
const enhancedLogger = require('./enhanced_logger');
const alerts = require('./alert_system');
const orderManager = require('./order_manager');

// Market data storage
const marketData = {
    // MNQ: { candles: [], indicators: {}, positions: [] },
    // MGC: { candles: [], indicators: {}, positions: [] }
};

// Initialize market data
function initializeMarket(symbol, config) {
    if (!marketData[symbol]) {
        marketData[symbol] = {
            candles: [],
            indicators: {
                atr: [],
                rsi: [],
                rsiMa: [],
                wma50: [],
                sma200: []
            },
            positions: [],
            config: config
        };
        logger.logSystem(`Initialized market data for ${symbol}`, 'info');
    }
}

// Process incoming candle data
async function processCandle(symbol, candle) {
    console.log(`Processing candle for ${symbol}:`, JSON.stringify(candle));

    if (!marketData[symbol]) {
        console.error(`Cannot process candle for ${symbol}: Market not initialized`);
        logger.logSystem(`Cannot process candle for ${symbol}: Market not initialized`, 'error');
        return;
    }

    // Add candle to the array
    marketData[symbol].candles.push(candle);
    console.log(`Added candle to ${symbol} market data. Total candles: ${marketData[symbol].candles.length}`);

    // Keep only the last 300 candles to avoid memory issues
    if (marketData[symbol].candles.length > 300) {
        marketData[symbol].candles.shift();
    }

    // Calculate indicators
    console.log(`Calculating indicators for ${symbol}...`);
    calculateIndicators(symbol);

    // Manage existing positions first
    if (marketData[symbol].positions.length > 0) {
        console.log(`Managing positions for ${symbol}...`);
        await managePositions(symbol);
    }

    // Check for entry signals if we have enough candles and no open position
    if (marketData[symbol].candles.length >= 4 && marketData[symbol].positions.length === 0) {
        console.log(`Checking for entry signals for ${symbol}...`);
        await checkForEntrySignals(symbol);
    } else {
        console.log(`Not checking for entry signals for ${symbol}. Candles: ${marketData[symbol].candles.length}, Positions: ${marketData[symbol].positions.length}`);
    }
}

// Calculate technical indicators
function calculateIndicators(symbol) {
    const market = marketData[symbol];
    const candles = market.candles;
    const config = market.config;

    if (candles.length < 14) {
        enhancedLogger.logDecision(symbol, 'NO_INDICATORS', 'Not enough candles for indicator calculation', {
            requiredCandles: 14,
            availableCandles: candles.length
        });
        return; // Not enough data for indicators
    }

    // Calculate ATR
    const atr = calculateATR(candles, config.atrPeriod || 14);
    market.indicators.atr = atr;

    // Calculate RSI
    const rsi = calculateRSI(candles, config.rsiPeriod || 14);
    market.indicators.rsi = rsi;

    // Log the latest RSI value
    if (rsi.length > 0) {
        const latestRSI = rsi[rsi.length - 1];
        logger.logSystem(`${symbol} Latest RSI(${config.rsiPeriod || 14}): ${latestRSI.toFixed(2)}`, 'info');
    }

    // Calculate RSI MA
    if (rsi.length >= config.rsiMaPeriod) {
        const rsiMa = calculateSMA(rsi, config.rsiMaPeriod || 8);
        market.indicators.rsiMa = rsiMa;

        // Log the latest RSI-MA value
        if (rsiMa.length > 0) {
            const latestRSIMA = rsiMa[rsiMa.length - 1];
            logger.logSystem(`${symbol} Latest RSI-MA(${config.rsiMaPeriod || 8}): ${latestRSIMA.toFixed(2)}`, 'info');

            // Log the comparison between RSI and RSI-MA
            const latestRSI = rsi[rsi.length - 1];
            logger.logSystem(`${symbol} RSI vs RSI-MA: ${latestRSI.toFixed(2)} vs ${latestRSIMA.toFixed(2)} (diff: ${(latestRSI - latestRSIMA).toFixed(2)})`, 'info');
        }
    }

    // Calculate WMA50 if enabled
    if (config.wma50Period > 0 && candles.length >= config.wma50Period) {
        const closePrices = candles.map(c => c.close);
        const wma50 = calculateWMA(closePrices, config.wma50Period);
        market.indicators.wma50 = wma50;
    }

    // Calculate SMA200 if enabled
    if (config.sma200Period > 0 && candles.length >= config.sma200Period) {
        const closePrices = candles.map(c => c.close);
        const sma200 = calculateSMA(closePrices, config.sma200Period);
        market.indicators.sma200 = sma200;
    }

    // Log all indicator values using enhanced logger
    const currentCandle = candles[candles.length - 1];
    enhancedLogger.logIndicators(symbol, market.indicators, currentCandle);
}

// Calculate Average True Range (ATR)
function calculateATR(candles, period) {
    if (candles.length < period + 1) {
        return [];
    }

    const trValues = [];
    for (let i = 1; i < candles.length; i++) {
        const high = candles[i].high;
        const low = candles[i].low;
        const prevClose = candles[i - 1].close;

        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);

        const tr = Math.max(tr1, tr2, tr3);
        trValues.push(tr);
    }

    // Calculate simple moving average of TR values
    const atrValues = [];
    for (let i = period - 1; i < trValues.length; i++) {
        let sum = 0;
        for (let j = i - period + 1; j <= i; j++) {
            sum += trValues[j];
        }
        atrValues.push(sum / period);
    }

    return atrValues;
}

// Calculate Relative Strength Index (RSI)
function calculateRSI(candles, period) {
    if (candles.length < period + 1) {
        console.log(`Not enough candles for RSI calculation. Need ${period + 1}, have ${candles.length}`);
        return [];
    }

    // Log the first and last few candles for debugging
    console.log(`RSI calculation with ${candles.length} candles, period=${period}`);
    console.log(`First 3 candles: ${JSON.stringify(candles.slice(0, 3).map(c => ({ close: c.close })))}`);
    console.log(`Last 3 candles: ${JSON.stringify(candles.slice(-3).map(c => ({ close: c.close })))}`);

    const changes = [];
    for (let i = 1; i < candles.length; i++) {
        changes.push(candles[i].close - candles[i - 1].close);
    }

    // Log the price changes
    console.log(`First 3 price changes: ${JSON.stringify(changes.slice(0, 3))}`);
    console.log(`Last 3 price changes: ${JSON.stringify(changes.slice(-3))}`);

    const rsiValues = [];

    // Calculate initial average gain and loss
    let avgGain = 0;
    let avgLoss = 0;

    for (let i = 0; i < period; i++) {
        const change = changes[i];
        if (change >= 0) {
            avgGain += change;
        } else {
            avgLoss += Math.abs(change);
        }
    }

    avgGain /= period;
    avgLoss /= period;

    console.log(`Initial avgGain: ${avgGain}, avgLoss: ${avgLoss}`);

    // First RSI value
    let firstRSI;
    if (avgLoss === 0) {
        firstRSI = 100;
        rsiValues.push(100);
    } else {
        const rs = avgGain / avgLoss;
        firstRSI = 100 - (100 / (1 + rs));
        rsiValues.push(firstRSI);
    }

    console.log(`First RSI value: ${firstRSI}`);

    // Calculate smoothed RSI using Wilder's smoothing method
    for (let i = period; i < changes.length; i++) {
        const change = changes[i];
        const gain = change >= 0 ? change : 0;
        const loss = change < 0 ? Math.abs(change) : 0;

        const prevAvgGain = avgGain;
        const prevAvgLoss = avgLoss;

        avgGain = ((avgGain * (period - 1)) + gain) / period;
        avgLoss = ((avgLoss * (period - 1)) + loss) / period;

        // Log every 10th calculation or the last one
        if (i % 10 === 0 || i === changes.length - 1) {
            console.log(`RSI calculation step ${i}: change=${change}, gain=${gain}, loss=${loss}`);
            console.log(`  prevAvgGain=${prevAvgGain}, prevAvgLoss=${prevAvgLoss}`);
            console.log(`  newAvgGain=${avgGain}, newAvgLoss=${avgLoss}`);
        }

        let rsiValue;
        if (avgLoss === 0) {
            rsiValue = 100;
            rsiValues.push(100);
        } else {
            const rs = avgGain / avgLoss;
            rsiValue = 100 - (100 / (1 + rs));
            rsiValues.push(rsiValue);
        }

        // Log every 10th RSI value or the last one
        if (i % 10 === 0 || i === changes.length - 1) {
            console.log(`  RSI[${i}] = ${rsiValue}`);
        }
    }

    // Log the final RSI values
    if (rsiValues.length > 0) {
        console.log(`Final RSI value: ${rsiValues[rsiValues.length - 1]}`);
    }

    return rsiValues;
}

// Calculate Simple Moving Average (SMA)
function calculateSMA(values, period) {
    if (values.length < period) {
        return [];
    }

    const smaValues = [];
    for (let i = period - 1; i < values.length; i++) {
        let sum = 0;
        for (let j = i - period + 1; j <= i; j++) {
            sum += values[j];
        }
        smaValues.push(sum / period);
    }

    return smaValues;
}

// Calculate Weighted Moving Average (WMA)
function calculateWMA(values, period) {
    if (values.length < period) {
        return [];
    }

    const wmaValues = [];

    // If values is an array of numbers, calculate WMA directly
    if (typeof values[0] === 'number') {
        for (let i = period - 1; i < values.length; i++) {
            let weightedSum = 0;
            let weightSum = 0;

            for (let j = 0; j < period; j++) {
                const weight = period - j;
                weightedSum += values[i - j] * weight;
                weightSum += weight;
            }

            wmaValues.push(weightedSum / weightSum);
        }
    }
    // If values is an array of candles, calculate WMA of close prices
    else {
        for (let i = period - 1; i < values.length; i++) {
            let weightedSum = 0;
            let weightSum = 0;

            for (let j = 0; j < period; j++) {
                const weight = period - j;
                weightedSum += values[i - j].close * weight;
                weightSum += weight;
            }

            wmaValues.push(weightedSum / weightSum);
        }
    }

    return wmaValues;
}

// Determine candlestick color
function candlestickColor(candle) {
    if (!candle || typeof candle.open !== 'number' || typeof candle.close !== 'number') {
        return 'invalid';
    }
    return candle.close > candle.open ? 'green' : candle.close < candle.open ? 'red' : 'doji';
}

// Detect three-candle pattern
function detect3(c1, c2, c3) {
    if (!c1 || !c2 || !c3) {
        console.log(`detect3: Missing candle data`);
        return null;
    }

    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    console.log(`detect3: Checking candle colors: ${col1}-${col2}-${col3}`);
    console.log(`detect3: Candle 1: O=${c1.open} H=${c1.high} L=${c1.low} C=${c1.close}`);
    console.log(`detect3: Candle 2: O=${c2.open} H=${c2.high} L=${c2.low} C=${c2.close}`);
    console.log(`detect3: Candle 3: O=${c3.open} H=${c3.high} L=${c3.low} C=${c3.close}`);

    if (col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') {
        console.log(`detect3: Invalid candle color detected`);
        return null;
    }

    if (col1 === 'green' && col2 === 'red' && col3 === 'green') {
        console.log(`detect3: Found green-red-green color pattern, checking conditions...`);

        const engulf = c3.close > c2.open && c3.open < c2.close;
        const wick3 = Math.min(c3.open, c3.close) - c3.low;
        const wick2 = Math.min(c2.open, c2.close) - c2.low;

        console.log(`detect3: Engulfing check: ${engulf} (c3.close=${c3.close} > c2.open=${c2.open} && c3.open=${c3.open} < c2.close=${c2.close})`);
        console.log(`detect3: Wick comparison: wick3=${wick3} > wick2=${wick2} = ${wick3 > wick2}`);

        if (engulf && wick3 > wick2) {
            console.log(`Detected bullish 3-candle pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} > c2.open=${c2.open} && c3.open=${c3.open} < c2.close=${c2.close})`);
            return 'bullish';
        } else {
            console.log(`detect3: Bullish pattern conditions not met`);
        }
    }

    if (col1 === 'red' && col2 === 'green' && col3 === 'red') {
        console.log(`detect3: Found red-green-red color pattern, checking conditions...`);

        const engulf = c3.close < c2.open && c3.open > c2.close;
        const wick3 = c3.high - Math.max(c3.open, c3.close);
        const wick2 = c2.high - Math.max(c2.open, c2.close);

        console.log(`detect3: Engulfing check: ${engulf} (c3.close=${c3.close} < c2.open=${c2.open} && c3.open=${c3.open} > c2.close=${c2.close})`);
        console.log(`detect3: Wick comparison: wick3=${wick3} > wick2=${wick2} = ${wick3 > wick2}`);

        if (engulf && wick3 > wick2) {
            console.log(`Detected bearish 3-candle pattern: ${col1}-${col2}-${col3} (c3.close=${c3.close} < c2.open=${c2.open} && c3.open=${c3.open} > c2.close=${c2.close})`);
            return 'bearish';
        } else {
            console.log(`detect3: Bearish pattern conditions not met`);
        }
    }

    console.log(`detect3: No 3-candle pattern detected`);
    return null;
}

// Detect four-candle pattern
function detect4(c0, c1, c2, c3) {
    if (!c0 || !c1 || !c2 || !c3) {
        console.log(`detect4: Missing candle data`);
        return null;
    }

    const col0 = candlestickColor(c0);
    const col1 = candlestickColor(c1);
    const col2 = candlestickColor(c2);
    const col3 = candlestickColor(c3);

    console.log(`detect4: Checking candle colors: ${col0}-${col1}-${col2}-${col3}`);
    console.log(`detect4: Candle 0: O=${c0.open} H=${c0.high} L=${c0.low} C=${c0.close}`);
    console.log(`detect4: Candle 1: O=${c1.open} H=${c1.high} L=${c1.low} C=${c1.close}`);
    console.log(`detect4: Candle 2: O=${c2.open} H=${c2.high} L=${c2.low} C=${c2.close}`);
    console.log(`detect4: Candle 3: O=${c3.open} H=${c3.high} L=${c3.low} C=${c3.close}`);

    if (col0 === 'invalid' || col1 === 'invalid' || col2 === 'invalid' || col3 === 'invalid') {
        console.log(`detect4: Invalid candle color detected`);
        return null;
    }

    if (col0 === 'green' && col1 === 'red' && col2 === 'red' && col3 === 'green') {
        console.log(`detect4: Found green-red-red-green color pattern, checking conditions...`);

        const maxOpen = Math.max(c1.open, c2.open);
        console.log(`detect4: Max open of middle candles: ${maxOpen} (c1.open=${c1.open}, c2.open=${c2.open})`);
        console.log(`detect4: Checking if c3.close=${c3.close} > ${maxOpen} = ${c3.close > maxOpen}`);

        if (c3.close > maxOpen) {
            console.log(`Detected bullish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close} > max(c1.open=${c1.open}, c2.open=${c2.open}))`);
            return 'bullish';
        } else {
            console.log(`detect4: Bullish pattern conditions not met`);
        }
    }

    if (col0 === 'red' && col1 === 'green' && col2 === 'green' && col3 === 'red') {
        console.log(`detect4: Found red-green-green-red color pattern, checking conditions...`);

        const minOpen = Math.min(c1.open, c2.open);
        console.log(`detect4: Min open of middle candles: ${minOpen} (c1.open=${c1.open}, c2.open=${c2.open})`);
        console.log(`detect4: Checking if c3.close=${c3.close} < ${minOpen} = ${c3.close < minOpen}`);

        if (c3.close < minOpen) {
            console.log(`Detected bearish 4-candle pattern: ${col0}-${col1}-${col2}-${col3} (c3.close=${c3.close} < min(c1.open=${c1.open}, c2.open=${c2.open}))`);
            return 'bearish';
        } else {
            console.log(`detect4: Bearish pattern conditions not met`);
        }
    }

    console.log(`detect4: No 4-candle pattern detected`);
    return null;
}

// Check if entry conditions are met
function entryOK(symbol, dir, patternType, c3) {
    const market = marketData[symbol];
    const config = market.config;
    const indicators = market.indicators;

    console.log(`${symbol} Checking entry conditions for ${dir} ${patternType} pattern...`);

    // Log detailed entry check
    enhancedLogger.logDecision(symbol, 'CHECKING_ENTRY', `Evaluating ${dir} ${patternType} pattern`, {
        direction: dir,
        patternType: patternType,
        candleClose: c3.close
    });

    // Debug log indicator arrays
    console.log(`${symbol} Indicator array lengths: ATR=${indicators.atr ? indicators.atr.length : 0}, RSI=${indicators.rsi ? indicators.rsi.length : 0}, RSIMA=${indicators.rsiMa ? indicators.rsiMa.length : 0}, WMA50=${indicators.wma50 ? indicators.wma50.length : 0}, SMA200=${indicators.sma200 ? indicators.sma200.length : 0}`);

    // Check if we have enough data for indicators
    if (!indicators.atr || indicators.atr.length === 0) {
        console.log(`${symbol} Entry filter failed: No ATR data available`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Missing ATR data', {
            direction: dir,
            patternType: patternType
        });
        return false;
    }

    if (!indicators.rsi || indicators.rsi.length === 0) {
        console.log(`${symbol} Entry filter failed: No RSI data available`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Missing RSI data', {
            direction: dir,
            patternType: patternType
        });
        return false;
    }

    if (!indicators.rsiMa || indicators.rsiMa.length === 0) {
        console.log(`${symbol} Entry filter failed: No RSI-MA data available`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Missing RSI-MA data', {
            direction: dir,
            patternType: patternType
        });
        return false;
    }

    // Get the latest indicator values
    const latestATR = indicators.atr[indicators.atr.length - 1];
    const latestRSI = indicators.rsi[indicators.rsi.length - 1];
    const latestRSIMA = indicators.rsiMa[indicators.rsiMa.length - 1];
    const latestWMA50 = config.wma50Period > 0 && indicators.wma50 && indicators.wma50.length > 0 ?
                        indicators.wma50[indicators.wma50.length - 1] : null;
    const latestSMA200 = config.sma200Period > 0 && indicators.sma200 && indicators.sma200.length > 0 ?
                         indicators.sma200[indicators.sma200.length - 1] : null;

    console.log(`${symbol} Indicator values: ATR=${latestATR}, RSI=${latestRSI}, RSIMA=${latestRSIMA}, WMA50=${latestWMA50}, SMA200=${latestSMA200}`);

    // Basic validation
    if (isNaN(latestATR) || isNaN(latestRSI) || isNaN(latestRSIMA) || latestATR <= 0) {
        console.log(`${symbol} Entry filter failed: Invalid indicator values (ATR=${latestATR}, RSI=${latestRSI}, RSIMA=${latestRSIMA})`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Invalid indicator values', {
            direction: dir,
            patternType: patternType,
            ATR: latestATR,
            RSI: latestRSI,
            RSIMA: latestRSIMA
        });
        return false;
    }

    // WMA filter
    if (config.useWmaFilter && latestWMA50 !== null) {
        if ((dir === 'bullish' && c3.close <= latestWMA50) || (dir === 'bearish' && c3.close >= latestWMA50)) {
            console.log(`${symbol} Entry filter failed: WMA filter (${dir}, close=${c3.close}, WMA50=${latestWMA50})`);
            enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Failed WMA filter', {
                direction: dir,
                patternType: patternType,
                close: c3.close,
                WMA50: latestWMA50,
                requirement: dir === 'bullish' ? 'close > WMA50' : 'close < WMA50'
            });
            return false;
        }
        console.log(`${symbol} WMA filter passed: ${dir}, close=${c3.close}, WMA50=${latestWMA50}`);
        enhancedLogger.logDecision(symbol, 'FILTER_PASSED', 'WMA filter passed', {
            direction: dir,
            patternType: patternType,
            close: c3.close,
            WMA50: latestWMA50
        });
    }

    // SMA filter
    if (config.sma200Period > 0 && latestSMA200 !== null) {
        if ((dir === 'bullish' && c3.close <= latestSMA200) || (dir === 'bearish' && c3.close >= latestSMA200)) {
            console.log(`${symbol} Entry filter failed: SMA filter (${dir}, close=${c3.close}, SMA200=${latestSMA200})`);
            enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Failed SMA filter', {
                direction: dir,
                patternType: patternType,
                close: c3.close,
                SMA200: latestSMA200,
                requirement: dir === 'bullish' ? 'close > SMA200' : 'close < SMA200'
            });
            return false;
        }
        console.log(`${symbol} SMA filter passed: ${dir}, close=${c3.close}, SMA200=${latestSMA200}`);
        enhancedLogger.logDecision(symbol, 'FILTER_PASSED', 'SMA filter passed', {
            direction: dir,
            patternType: patternType,
            close: c3.close,
            SMA200: latestSMA200
        });
    }

    // RSI filter
    if ((dir === 'bullish' && latestRSI <= latestRSIMA) || (dir === 'bearish' && latestRSI >= latestRSIMA)) {
        console.log(`${symbol} Entry filter failed: RSI filter (${dir}, RSI=${latestRSI}, RSIMA=${latestRSIMA})`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Failed RSI filter', {
            direction: dir,
            patternType: patternType,
            RSI: latestRSI,
            RSIMA: latestRSIMA,
            requirement: dir === 'bullish' ? 'RSI > RSI-MA' : 'RSI < RSI-MA'
        });
        return false;
    }
    console.log(`${symbol} RSI filter passed: ${dir}, RSI=${latestRSI}, RSIMA=${latestRSIMA}`);
    enhancedLogger.logDecision(symbol, 'FILTER_PASSED', 'RSI filter passed', {
        direction: dir,
        patternType: patternType,
        RSI: latestRSI,
        RSIMA: latestRSIMA
    });

    // Minimum ATR filter
    if (latestATR < (config.minAtrEntry || 0)) {
        console.log(`${symbol} Entry filter failed: Minimum ATR filter (ATR=${latestATR}, min=${config.minAtrEntry || 0})`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Failed minimum ATR filter', {
            direction: dir,
            patternType: patternType,
            ATR: latestATR,
            minATR: config.minAtrEntry || 0
        });
        return false;
    }
    console.log(`${symbol} Minimum ATR filter passed: ATR=${latestATR}, min=${config.minAtrEntry || 0}`);
    enhancedLogger.logDecision(symbol, 'FILTER_PASSED', 'Minimum ATR filter passed', {
        direction: dir,
        patternType: patternType,
        ATR: latestATR,
        minATR: config.minAtrEntry || 0
    });

    // RSI-MA separation filter
    if (Math.abs(latestRSI - latestRSIMA) < (config.minRsiMaSeparation || 0)) {
        console.log(`${symbol} Entry filter failed: RSI-MA separation filter (separation=${Math.abs(latestRSI - latestRSIMA)}, min=${config.minRsiMaSeparation || 0})`);
        enhancedLogger.logDecision(symbol, 'NO_TRADE', 'Failed RSI-MA separation filter', {
            direction: dir,
            patternType: patternType,
            RSI: latestRSI,
            RSIMA: latestRSIMA,
            separation: Math.abs(latestRSI - latestRSIMA),
            minSeparation: config.minRsiMaSeparation || 0
        });
        return false;
    }
    console.log(`${symbol} RSI-MA separation filter passed: separation=${Math.abs(latestRSI - latestRSIMA)}, min=${config.minRsiMaSeparation || 0}`);
    enhancedLogger.logDecision(symbol, 'FILTER_PASSED', 'RSI-MA separation filter passed', {
        direction: dir,
        patternType: patternType,
        RSI: latestRSI,
        RSIMA: latestRSIMA,
        separation: Math.abs(latestRSI - latestRSIMA),
        minSeparation: config.minRsiMaSeparation || 0
    });

    console.log(`${symbol} All entry filters passed for ${dir} ${patternType} pattern!`);
    enhancedLogger.logDecision(symbol, 'ALL_FILTERS_PASSED', 'All entry filters passed', {
        direction: dir,
        patternType: patternType,
        RSI: latestRSI,
        RSIMA: latestRSIMA,
        WMA50: latestWMA50,
        ATR: latestATR,
        close: c3.close
    });
    return true;
}

// Check for entry signals
async function checkForEntrySignals(symbol) {
    const market = marketData[symbol];
    const candles = market.candles;
    const config = market.config;
    const indicators = market.indicators;

    // Need at least 4 candles for pattern detection
    if (candles.length < 4) {
        console.log(`Not enough candles for ${symbol} pattern detection. Need 4, have ${candles.length}`);
        enhancedLogger.logDecision(symbol, 'NO_PATTERN_CHECK', 'Not enough candles for pattern detection', {
            requiredCandles: 4,
            availableCandles: candles.length
        });
        return;
    }

    // Get the last 4 candles
    const c0 = candles[candles.length - 4];
    const c1 = candles[candles.length - 3];
    const c2 = candles[candles.length - 2];
    const c3 = candles[candles.length - 1];

    console.log(`Checking patterns for ${symbol} with last 4 candles:`,
        JSON.stringify([c0, c1, c2, c3].map(c => ({
            timestamp: new Date(c.timestamp).toISOString(),
            open: c.open,
            high: c.high,
            low: c.low,
            close: c.close
        })))
    );

    // Log candle data for pattern detection
    enhancedLogger.logDecision(symbol, 'PATTERN_CHECK', 'Checking for patterns', {
        candle0: {
            timestamp: new Date(c0.timestamp).toISOString(),
            open: c0.open,
            high: c0.high,
            low: c0.low,
            close: c0.close,
            color: candlestickColor(c0)
        },
        candle1: {
            timestamp: new Date(c1.timestamp).toISOString(),
            open: c1.open,
            high: c1.high,
            low: c1.low,
            close: c1.close,
            color: candlestickColor(c1)
        },
        candle2: {
            timestamp: new Date(c2.timestamp).toISOString(),
            open: c2.open,
            high: c2.high,
            low: c2.low,
            close: c2.close,
            color: candlestickColor(c2)
        },
        candle3: {
            timestamp: new Date(c3.timestamp).toISOString(),
            open: c3.open,
            high: c3.high,
            low: c3.low,
            close: c3.close,
            color: candlestickColor(c3)
        }
    });

    // Get the latest WMA50 value
    const latestWMA50 = config.wma50Period > 0 && indicators.wma50 && indicators.wma50.length > 0 ?
                        indicators.wma50[indicators.wma50.length - 1] : null;

    // Determine which patterns to look for based on price relative to WMA50
    let lookForBullish = true;
    let lookForBearish = true;

    if (config.useWmaFilter && latestWMA50 !== null) {
        // If price is below WMA50, only look for bearish patterns
        // If price is above WMA50, only look for bullish patterns
        lookForBullish = c3.close > latestWMA50;
        lookForBearish = c3.close < latestWMA50;

        console.log(`${symbol} WMA50 directional bias: Price=${c3.close}, WMA50=${latestWMA50}, Looking for Bullish=${lookForBullish}, Looking for Bearish=${lookForBearish}`);

        enhancedLogger.logDecision(symbol, 'DIRECTIONAL_BIAS', 'WMA50 directional bias', {
            price: c3.close,
            WMA50: latestWMA50,
            lookForBullish: lookForBullish,
            lookForBearish: lookForBearish
        });
    }

    // Check for patterns based on WMA50 directional bias
    let p3 = null;
    let p4 = null;

    // Only run pattern detection if we're looking for that direction
    const p3Result = detect3(c1, c2, c3);
    const p4Result = detect4(c0, c1, c2, c3);

    // Filter patterns based on directional bias
    if (p3Result === 'bullish' && lookForBullish) {
        p3 = p3Result;
    } else if (p3Result === 'bearish' && lookForBearish) {
        p3 = p3Result;
    } else if (p3Result) {
        // Pattern detected but filtered out by directional bias
        enhancedLogger.logDecision(symbol, 'PATTERN_FILTERED', '3-candle pattern filtered by directional bias', {
            pattern: p3Result,
            lookForBullish: lookForBullish,
            lookForBearish: lookForBearish,
            price: c3.close,
            WMA50: latestWMA50
        });
    }

    if (p4Result === 'bullish' && lookForBullish) {
        p4 = p4Result;
    } else if (p4Result === 'bearish' && lookForBearish) {
        p4 = p4Result;
    } else if (p4Result) {
        // Pattern detected but filtered out by directional bias
        enhancedLogger.logDecision(symbol, 'PATTERN_FILTERED', '4-candle pattern filtered by directional bias', {
            pattern: p4Result,
            lookForBullish: lookForBullish,
            lookForBearish: lookForBearish,
            price: c3.close,
            WMA50: latestWMA50
        });
    }

    console.log(`${symbol} pattern detection results: 3-candle=${p3}, 4-candle=${p4}`);

    enhancedLogger.logDecision(symbol, 'PATTERN_RESULTS', 'Pattern detection results', {
        threeCandle: p3,
        fourCandle: p4
    });

    let pattern = null;
    let patternType = null;

    if (p4) {
        pattern = p4;
        patternType = 'four';
        console.log(`${symbol} 4-candle ${p4} pattern detected`);
        enhancedLogger.logDecision(symbol, 'PATTERN_DETECTED', '4-candle pattern detected', {
            pattern: p4,
            candle0Color: candlestickColor(c0),
            candle1Color: candlestickColor(c1),
            candle2Color: candlestickColor(c2),
            candle3Color: candlestickColor(c3)
        });
    } else if (p3) {
        pattern = p3;
        patternType = 'three';
        console.log(`${symbol} 3-candle ${p3} pattern detected`);
        enhancedLogger.logDecision(symbol, 'PATTERN_DETECTED', '3-candle pattern detected', {
            pattern: p3,
            candle1Color: candlestickColor(c1),
            candle2Color: candlestickColor(c2),
            candle3Color: candlestickColor(c3)
        });
    } else {
        console.log(`${symbol} No pattern detected`);
        enhancedLogger.logDecision(symbol, 'NO_PATTERN', 'No pattern detected', {
            candle0Color: candlestickColor(c0),
            candle1Color: candlestickColor(c1),
            candle2Color: candlestickColor(c2),
            candle3Color: candlestickColor(c3)
        });
    }

    // If pattern found and entry filter passes, enter trade
    if (pattern) {
        console.log(`${symbol} Checking entry filter for ${pattern} ${patternType} pattern...`);
        const entryFilterResult = entryOK(symbol, pattern, patternType, c3);

        if (entryFilterResult) {
            console.log(`${symbol} Entry filter passed for ${pattern} ${patternType} pattern. Entering trade...`);
            enhancedLogger.logDecision(symbol, 'TRADE_ENTRY', 'Entry filters passed, entering trade', {
                pattern: pattern,
                patternType: patternType,
                price: c3.close
            });
            await enterTrade(symbol, pattern, c3);
        } else {
            console.log(`${symbol} Entry filter failed for ${pattern} ${patternType} pattern. No trade.`);
            // Note: The specific filter that failed is already logged in the entryOK function
        }
    }
}

// Enter a new trade
async function enterTrade(symbol, direction, candle) {
    const market = marketData[symbol];
    const config = market.config;

    // Get the latest ATR value
    const atr = market.indicators.atr[market.indicators.atr.length - 1];

    // Determine parameters based on mode (adaptive or fixed)
    let slFactor, tpFactor, trailFactor;

    if (config.isAdaptiveRun) {
        // Determine ATR regime
        const atrRegime = atr < config.atrThresholds.low_medium ? 'Low' :
                         (atr > config.atrThresholds.medium_high ? 'High' : 'Medium');

        // Get parameters for current regime
        slFactor = config.adaptiveParams[atrRegime].slFactor;
        tpFactor = config.adaptiveParams[atrRegime].tpFactor;
        trailFactor = config.adaptiveParams[atrRegime].trailFactor;
    } else {
        slFactor = config.slFactors;
        tpFactor = config.tpFactors;
        trailFactor = config.trailFactors;
    }

    // Calculate entry parameters
    const entryPrice = candle.close;
    const slDistance = atr * slFactor;
    const tpDistance = Math.max(config.fixedTpPoints || 0, atr * tpFactor);
    const contracts = config.fixedContracts;

    // Create position object
    const position = {
        entryTime: new Date(candle.timestamp),
        direction: direction,
        entry: entryPrice,
        atr: atr,
        stopLoss: direction === 'bullish' ? entryPrice - slDistance : entryPrice + slDistance,
        takeProfit: direction === 'bullish' ? entryPrice + tpDistance : entryPrice - tpDistance,
        trailStop: direction === 'bullish' ? entryPrice - (atr * trailFactor) : entryPrice + (atr * trailFactor),
        trailFactor: trailFactor,
        trailHigh: candle.high,
        trailLow: candle.low,
        contracts: contracts,
        entryBar: market.candles.length - 1,
        atrRegime: config.isAdaptiveRun ?
            (atr < config.atrThresholds.low_medium ? 'Low' :
            (atr > config.atrThresholds.medium_high ? 'High' : 'Medium')) :
            'Fixed'
    };

    // Add position to the market
    market.positions.push(position);

    // Log entry
    logger.logTradeEvent(symbol, {
        action: 'ENTRY',
        direction: direction,
        price: entryPrice,
        contracts: contracts,
        stopLoss: position.stopLoss,
        takeProfit: position.takeProfit,
        trailStop: position.trailStop,
        atr: atr,
        timestamp: new Date()
    });

    // Send alert
    alerts.alertTrade(symbol, `New ${direction.toUpperCase()} trade entered at ${entryPrice}`, 'info');

    // Place the actual order through the order manager
    try {
        // Determine the full symbol name with contract month
        const fullSymbol = `${symbol}${config.contractMonth}`;

        // Place a market order
        const orderResult = await orderManager.placeMarketOrder({
            symbol: fullSymbol,
            action: direction,
            quantity: contracts,
            accountId: config.accountId
        });

        if (orderResult.success) {
            // Store the order ID in the position
            position.orderId = orderResult.orderId;
            logger.logSystem(`Market order placed for ${symbol} ${direction} trade. Order ID: ${orderResult.orderId}`, 'info');

            // Place stop loss order
            const stopOrderResult = await orderManager.placeStopOrder({
                symbol: fullSymbol,
                action: direction === 'bullish' ? 'bearish' : 'bullish', // Opposite direction for exit
                quantity: contracts,
                stopPrice: position.stopLoss,
                accountId: config.accountId
            });

            if (stopOrderResult.success) {
                position.stopOrderId = stopOrderResult.orderId;
                logger.logSystem(`Stop loss order placed for ${symbol} ${direction} trade. Order ID: ${stopOrderResult.orderId}`, 'info');
            } else {
                logger.logSystem(`Failed to place stop loss order for ${symbol} ${direction} trade: ${stopOrderResult.error}`, 'error');
            }

            // Place take profit order if not using trailing stop only
            if (config.tpFactors > 0) {
                const limitOrderResult = await orderManager.placeLimitOrder({
                    symbol: fullSymbol,
                    action: direction === 'bullish' ? 'bearish' : 'bullish', // Opposite direction for exit
                    quantity: contracts,
                    price: position.takeProfit,
                    accountId: config.accountId
                });

                if (limitOrderResult.success) {
                    position.tpOrderId = limitOrderResult.orderId;
                    logger.logSystem(`Take profit order placed for ${symbol} ${direction} trade. Order ID: ${limitOrderResult.orderId}`, 'info');
                } else {
                    logger.logSystem(`Failed to place take profit order for ${symbol} ${direction} trade: ${limitOrderResult.error}`, 'error');
                }
            }
        } else {
            logger.logSystem(`Failed to place market order for ${symbol} ${direction} trade: ${orderResult.error}`, 'error');
            alerts.alertTrade(symbol, `Failed to place market order: ${orderResult.error}`, 'error');
        }
    } catch (error) {
        logger.logSystem(`Error placing orders for ${symbol} ${direction} trade: ${error.message}`, 'error');
        alerts.alertTrade(symbol, `Error placing orders: ${error.message}`, 'error');
    }
}

// Manage existing positions
async function managePositions(symbol) {
    const market = marketData[symbol];
    const positions = market.positions;
    const config = market.config;

    if (positions.length === 0) {
        return;
    }

    const currentCandle = market.candles[market.candles.length - 1];

    // Process each position
    for (let i = positions.length - 1; i >= 0; i--) {
        const position = positions[i];

        // Update trail values
        position.trailHigh = Math.max(position.trailHigh, currentCandle.high);
        position.trailLow = Math.min(position.trailLow, currentCandle.low);

        // Update trailing stop
        if (position.direction === 'bullish') {
            const newTrailStop = Math.max(position.trailStop, position.trailHigh - (market.indicators.atr[market.indicators.atr.length - 1] * position.trailFactor));

            // If trail stop has moved significantly, update the stop order
            if (newTrailStop > position.trailStop + (config.tickSize * 2) && position.stopOrderId) {
                try {
                    // Determine the full symbol name with contract month
                    const fullSymbol = `${symbol}${config.contractMonth}`;

                    // Modify the stop loss order
                    const modifyResult = await orderManager.modifyOrder({
                        orderId: position.stopOrderId,
                        stopPrice: newTrailStop
                    });

                    if (modifyResult.success) {
                        logger.logSystem(`Updated trailing stop for ${symbol} ${position.direction} trade from ${position.trailStop} to ${newTrailStop}`, 'info');
                    } else {
                        logger.logSystem(`Failed to update trailing stop order: ${modifyResult.error}`, 'error');
                    }
                } catch (error) {
                    logger.logSystem(`Error updating trailing stop order: ${error.message}`, 'error');
                }
            }

            position.trailStop = newTrailStop;
        } else {
            const newTrailStop = Math.min(position.trailStop, position.trailLow + (market.indicators.atr[market.indicators.atr.length - 1] * position.trailFactor));

            // If trail stop has moved significantly, update the stop order
            if (newTrailStop < position.trailStop - (config.tickSize * 2) && position.stopOrderId) {
                try {
                    // Determine the full symbol name with contract month
                    const fullSymbol = `${symbol}${config.contractMonth}`;

                    // Modify the stop loss order
                    const modifyResult = await orderManager.modifyOrder({
                        orderId: position.stopOrderId,
                        stopPrice: newTrailStop
                    });

                    if (modifyResult.success) {
                        logger.logSystem(`Updated trailing stop for ${symbol} ${position.direction} trade from ${position.trailStop} to ${newTrailStop}`, 'info');
                    } else {
                        logger.logSystem(`Failed to update trailing stop order: ${modifyResult.error}`, 'error');
                    }
                } catch (error) {
                    logger.logSystem(`Error updating trailing stop order: ${error.message}`, 'error');
                }
            }

            position.trailStop = newTrailStop;
        }

        // Check for exit conditions
        let exitReason = null;
        let exitPrice = null;

        if (position.direction === 'bullish') {
            // Stop loss hit
            if (currentCandle.low <= position.stopLoss) {
                exitReason = 'sl';
                exitPrice = position.stopLoss;
            }
            // Take profit hit
            else if (currentCandle.high >= position.takeProfit) {
                exitReason = 'tp';
                exitPrice = position.takeProfit;
            }
            // Trailing stop hit
            else if (currentCandle.low <= position.trailStop) {
                exitReason = 'trail';
                exitPrice = position.trailStop;
            }
            // Two-bar color exit
            else if (config.useTwoBarColorExit && market.candles.length > position.entryBar + 1) {
                const prevCandle = market.candles[market.candles.length - 2];
                const prevColor = candlestickColor(prevCandle);
                const curColor = candlestickColor(currentCandle);
                if (prevColor === 'red' && curColor === 'red') {
                    exitReason = 'color_flow_2bar';
                    exitPrice = currentCandle.close;
                }
            }
        } else { // Bearish position
            // Stop loss hit
            if (currentCandle.high >= position.stopLoss) {
                exitReason = 'sl';
                exitPrice = position.stopLoss;
            }
            // Take profit hit
            else if (currentCandle.low <= position.takeProfit) {
                exitReason = 'tp';
                exitPrice = position.takeProfit;
            }
            // Trailing stop hit
            else if (currentCandle.high >= position.trailStop) {
                exitReason = 'trail';
                exitPrice = position.trailStop;
            }
            // Two-bar color exit
            else if (config.useTwoBarColorExit && market.candles.length > position.entryBar + 1) {
                const prevCandle = market.candles[market.candles.length - 2];
                const prevColor = candlestickColor(prevCandle);
                const curColor = candlestickColor(currentCandle);
                if (prevColor === 'green' && curColor === 'green') {
                    exitReason = 'color_flow_2bar';
                    exitPrice = currentCandle.close;
                }
            }
        }

        // Process exit if triggered
        if (exitReason && exitPrice !== null) {
            await exitTrade(symbol, i, exitReason, exitPrice);
        }
    }
}

// Exit a trade
async function exitTrade(symbol, positionIndex, reason, exitPrice) {
    const market = marketData[symbol];
    const config = market.config;
    const position = market.positions[positionIndex];

    // Apply slippage to exit price
    let adjustedExitPrice = exitPrice;
    if (['sl', 'trail', 'color_flow_2bar'].includes(reason) && config.slippagePoints > 0) {
        adjustedExitPrice = position.direction === 'bullish' ?
            exitPrice - config.slippagePoints :
            exitPrice + config.slippagePoints;
    }

    // Calculate P&L
    const pnlPoints = position.direction === 'bullish' ?
        adjustedExitPrice - position.entry :
        position.entry - adjustedExitPrice;

    const pnlGross = pnlPoints * config.pointValue * position.contracts;
    const commissionCost = config.commissionPerContract * position.contracts;
    const pnlNet = pnlGross - commissionCost;

    // Log exit
    logger.logTradeEvent(symbol, {
        action: 'EXIT',
        direction: position.direction,
        entryPrice: position.entry,
        exitPrice: adjustedExitPrice,
        reason: reason,
        pnlPoints: pnlPoints,
        pnlNet: pnlNet,
        contracts: position.contracts,
        timestamp: new Date()
    });

    // Send alert
    alerts.alertTrade(symbol, `${position.direction.toUpperCase()} trade exited at ${adjustedExitPrice} (${reason}). P&L: $${pnlNet.toFixed(2)}`, pnlNet > 0 ? 'success' : 'warning');

    // Place the actual exit order through the order manager
    try {
        // Determine the full symbol name with contract month
        const fullSymbol = `${symbol}${config.contractMonth}`;

        // Cancel any existing stop loss or take profit orders
        if (position.stopOrderId) {
            await orderManager.cancelOrder({
                orderId: position.stopOrderId
            });
            logger.logSystem(`Cancelled stop loss order for ${symbol} ${position.direction} trade. Order ID: ${position.stopOrderId}`, 'info');
        }

        if (position.tpOrderId) {
            await orderManager.cancelOrder({
                orderId: position.tpOrderId
            });
            logger.logSystem(`Cancelled take profit order for ${symbol} ${position.direction} trade. Order ID: ${position.tpOrderId}`, 'info');
        }

        // Place a market order to close the position
        const orderResult = await orderManager.placeMarketOrder({
            symbol: fullSymbol,
            action: position.direction === 'bullish' ? 'bearish' : 'bullish', // Opposite direction for exit
            quantity: position.contracts,
            accountId: config.accountId
        });

        if (orderResult.success) {
            logger.logSystem(`Market order placed to close ${symbol} ${position.direction} trade. Order ID: ${orderResult.orderId}`, 'info');
        } else {
            logger.logSystem(`Failed to place market order to close ${symbol} ${position.direction} trade: ${orderResult.error}`, 'error');
            alerts.alertTrade(symbol, `Failed to place exit order: ${orderResult.error}`, 'error');
        }
    } catch (error) {
        logger.logSystem(`Error placing exit order for ${symbol} ${position.direction} trade: ${error.message}`, 'error');
        alerts.alertTrade(symbol, `Error placing exit order: ${error.message}`, 'error');
    }

    // Remove position from the array
    market.positions.splice(positionIndex, 1);

    // Return trade result for further processing
    return {
        pnlNet: pnlNet,
        exitReason: reason
    };
}

module.exports = {
    initializeMarket,
    processCandle,
    marketData,
    detect3,
    detect4,
    candlestickColor,
    calculateRSI,
    calculateSMA,
    calculateWMA,
    calculateATR
};
