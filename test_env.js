// test_env.js
require('dotenv').config();

console.log('Environment Variables:');
console.log('TRADOVATE_USERNAME:', process.env.TRADOVATE_USERNAME);
console.log('TRADOVATE_PASSWORD:', process.env.TRADOVATE_PASSWORD);
console.log('TRADOVATE_APP_ID:', process.env.TRADOVATE_APP_ID);
console.log('TRADOVATE_APP_VERSION:', process.env.TRADOVATE_APP_VERSION);
console.log('TRADOVATE_DEVICE_ID:', process.env.TRADOVATE_DEVICE_ID);
console.log('TRADOVATE_CID:', process.env.TRADOVATE_CID);
console.log('TRADOVATE_SEC:', process.env.TRADOVATE_SEC);
console.log('TRADOVATE_ENV:', process.env.TRADOVATE_ENV);
console.log('TRADOVATE_ACCOUNT_ID:', process.env.TRADOVATE_ACCOUNT_ID);
