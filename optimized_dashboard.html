<!DOCTYPE html>
<html class="game-mode">
<head>
    <title>Optimized Trading Bot Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Press+Start+2P&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <script src="dashboard_integration_optimized.js"></script>
    <style>
        :root {
            /* Colors */
            --bg-main: #0a0e17;
            --bg-sidebar: #141b2d;
            --card-bg: #1a2035;
            --border: #2a3a5a;
            --text-primary: #ffffff;
            --text-secondary: #a0aec0;
            --primary: #00ccff;
            --secondary: #ff00aa;
            --success: #00ff88;
            --warning: #ffcc00;
            --danger: #ff3366;
            --primary-glow: rgba(0, 204, 255, 0.5);
            --secondary-glow: rgba(255, 0, 170, 0.5);
            --success-glow: rgba(0, 255, 136, 0.5);
            --warning-glow: rgba(255, 204, 0, 0.5);
            --danger-glow: rgba(255, 51, 102, 0.5);
            
            /* Layout */
            --sidebar-width: 280px;
            --header-height: 60px;
            
            /* Shadows */
            --shadow-sm: 0 0 5px rgba(0, 204, 255, 0.2);
            --shadow: 0 0 15px rgba(0, 204, 255, 0.3);
            --shadow-lg: 0 0 25px rgba(0, 204, 255, 0.4);
            
            /* Grid */
            --grid-lines: rgba(42, 58, 90, 0.5);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Rajdhani', sans-serif;
            background-color: var(--bg-main);
            color: var(--text-primary);
            line-height: 1.6;
            overflow: hidden;
            height: 100vh;
            transition: background-color 0.3s ease, color 0.3s ease;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 204, 255, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 0, 170, 0.03) 0%, transparent 20%),
                linear-gradient(rgba(10, 14, 23, 0.99), rgba(10, 14, 23, 0.99)),
                url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%232a3a5a" stroke-width="0.5" stroke-dasharray="2,8" /></svg>');
            background-attachment: fixed;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        .dashboard-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--bg-sidebar);
            color: white;
            height: 100%;
            overflow-y: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), inset 0 0 15px rgba(0, 204, 255, 0.1);
            z-index: 10;
            position: relative;
            border-right: 1px solid var(--border);
            background-image: linear-gradient(to bottom,
                rgba(20, 27, 45, 0.95),
                rgba(20, 27, 45, 0.95)
            );
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }
        
        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.7;
        }
        
        .sidebar-header h1 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            text-shadow: 0 0 10px var(--primary-glow);
            transition: opacity 0.3s ease;
            font-family: 'Orbitron', sans-serif;
            letter-spacing: 1px;
        }
        
        .main-content {
            flex: 1;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .header {
            height: var(--header-height);
            background-color: var(--card-bg);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            box-shadow: var(--shadow-sm);
            z-index: 5;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--primary), transparent);
            opacity: 0.5;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            transition: all 0.3s ease;
            text-shadow: 0 0 10px var(--primary-glow);
            letter-spacing: 1px;
            font-family: 'Orbitron', sans-serif;
            position: relative;
            padding-left: 15px;
        }
        
        .header-title::before {
            content: '>';
            position: absolute;
            left: 0;
            color: var(--primary);
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-right: 1rem;
            background-color: rgba(0, 0, 0, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            border: 1px solid var(--success);
            box-shadow: 0 0 10px var(--success-glow);
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-dot.active {
            background-color: var(--success);
            box-shadow: 0 0 5px var(--success);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }
        
        .status-text {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }
        
        .status-text.disconnected {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }
        
        .header-button {
            background-color: var(--card-bg);
            color: var(--primary);
            border: 1px solid var(--primary);
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-left: 0.75rem;
            position: relative;
            overflow: hidden;
            text-shadow: 0 0 5px var(--primary-glow);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
            box-shadow: 0 0 10px rgba(0, 204, 255, 0.2);
        }
        
        .header-button:hover {
            background-color: rgba(0, 204, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 204, 255, 0.3);
        }
        
        .header-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 204, 255, 0.2);
        }
        
        .header-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .dashboard-view {
            flex: 1;
            overflow: auto;
            padding: 1.5rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 1.5rem;
        }
        
        .dashboard-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .dashboard-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .dashboard-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, var(--primary), var(--secondary));
            opacity: 0.7;
        }
        
        .dashboard-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .dashboard-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }
        
        .dashboard-card-content {
            position: relative;
        }
        
        .bot-status-card {
            grid-column: span 4;
        }
        
        .performance-card {
            grid-column: span 8;
        }
        
        .symbol-tabs-card {
            grid-column: span 12;
        }
        
        .bot-status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 1rem;
        }
        
        .bot-status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.5rem;
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }
        
        .bot-status-item:hover {
            background-color: rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .bot-status-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .bot-status-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }
        
        .circuit-breaker-status {
            font-size: 1.25rem;
            font-weight: 700;
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            text-align: center;
        }
        
        .circuit-breaker-status.normal {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
            background-color: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--success);
        }
        
        .circuit-breaker-status.tripped {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
            background-color: rgba(255, 51, 102, 0.1);
            border: 1px solid var(--danger);
        }
        
        .cache-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }
        
        .cache-stat {
            flex: 1;
            padding: 1rem;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.5rem;
            border: 1px solid var(--border);
            margin: 0 0.5rem;
            text-align: center;
        }
        
        .cache-stat:first-child {
            margin-left: 0;
        }
        
        .cache-stat:last-child {
            margin-right: 0;
        }
        
        .cache-stat-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .cache-stat-value {
            font-size: 1rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }
        
        .performance-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .performance-table th,
        .performance-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }
        
        .performance-table th {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .performance-table td {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .performance-table tr:hover td {
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        .symbol-tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 1.5rem;
        }
        
        .symbol-tab {
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            border-bottom: 2px solid transparent;
        }
        
        .symbol-tab:hover {
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        .symbol-tab.active {
            border-bottom: 2px solid var(--primary);
        }
        
        .symbol-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .symbol-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .symbol-content {
            display: none;
        }
        
        .symbol-content.active {
            display: block;
        }
        
        .symbol-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }
        
        .symbol-stats {
            display: flex;
        }
        
        .stat {
            margin-left: 1.5rem;
            text-align: center;
        }
        
        .stat-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }
        
        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 0 5px var(--primary-glow);
        }
        
        .stat-value.positive {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        
        .stat-value.negative {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }
        
        .symbol-chart {
            height: 300px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.5rem;
            border: 1px solid var(--border);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chart-placeholder {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
        }
        
        .symbol-positions,
        .symbol-orders {
            margin-bottom: 1.5rem;
        }
        
        .positions-table,
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 0.75rem;
        }
        
        .table-header {
            display: flex;
            background-color: rgba(0, 0, 0, 0.2);
            border-top: 1px solid var(--border);
            border-bottom: 1px solid var(--border);
        }
        
        .table-cell {
            flex: 1;
            padding: 0.75rem;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .table-row {
            display: flex;
            border-bottom: 1px solid var(--border);
            transition: all 0.3s ease;
        }
        
        .table-row:hover {
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        .table-row.empty {
            justify-content: center;
            padding: 1.5rem;
            color: var(--text-secondary);
            font-style: italic;
        }
        
        .table-body .table-cell {
            font-weight: 500;
            color: var(--text-primary);
            text-transform: none;
            letter-spacing: normal;
        }
        
        .table-cell.buy {
            color: var(--success);
            text-shadow: 0 0 5px var(--success-glow);
        }
        
        .table-cell.sell {
            color: var(--danger);
            text-shadow: 0 0 5px var(--danger-glow);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>Optimized Trading Bot</h1>
            </div>
        </div>
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <h1 class="header-title">Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <div class="status-dot" id="status-indicator"></div>
                        <div class="status-text" id="status-text">Disconnected</div>
                    </div>
                    <button class="header-button" id="start-bot">Start Bot</button>
                    <button class="header-button" id="stop-bot" disabled>Stop Bot</button>
                </div>
            </div>
            <div class="dashboard-view">
                <div class="dashboard-grid">
                    <div class="dashboard-card bot-status-card">
                        <div class="dashboard-card-header">
                            <h2 class="dashboard-card-title">Bot Status</h2>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="bot-status-grid">
                                <div class="bot-status-item">
                                    <div class="bot-status-label">Active Positions</div>
                                    <div class="bot-status-value" id="active-positions">0</div>
                                </div>
                                <div class="bot-status-item">
                                    <div class="bot-status-label">Active Orders</div>
                                    <div class="bot-status-value" id="active-orders">0</div>
                                </div>
                                <div class="bot-status-item">
                                    <div class="bot-status-label">Circuit Breakers</div>
                                    <div class="circuit-breaker-status normal" id="circuit-breaker-status">Normal</div>
                                </div>
                                <div class="bot-status-item">
                                    <div class="bot-status-label">Cache Stats</div>
                                    <div class="cache-stats" id="cache-stats">
                                        <div class="cache-stat">
                                            <div class="cache-stat-label">Contract Cache</div>
                                            <div class="cache-stat-value">0 items</div>
                                            <div class="cache-stat-value">0% hit rate</div>
                                        </div>
                                        <div class="cache-stat">
                                            <div class="cache-stat-label">Chart Data Cache</div>
                                            <div class="cache-stat-value">0 items</div>
                                            <div class="cache-stat-value">0% hit rate</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="dashboard-card performance-card">
                        <div class="dashboard-card-header">
                            <h2 class="dashboard-card-title">Performance Overview</h2>
                        </div>
                        <div class="dashboard-card-content">
                            <table class="performance-table" id="performance-table">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Total Trades</th>
                                        <th>Win Rate</th>
                                        <th>Net P&L</th>
                                        <th>Profit Factor</th>
                                        <th>Avg Win</th>
                                        <th>Avg Loss</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>MNQ</td>
                                        <td>0</td>
                                        <td>0%</td>
                                        <td>$0.00</td>
                                        <td>0.00</td>
                                        <td>$0.00</td>
                                        <td>$0.00</td>
                                    </tr>
                                    <tr>
                                        <td>MES</td>
                                        <td>0</td>
                                        <td>0%</td>
                                        <td>$0.00</td>
                                        <td>0.00</td>
                                        <td>$0.00</td>
                                        <td>$0.00</td>
                                    </tr>
                                    <tr>
                                        <td>MGC</td>
                                        <td>0</td>
                                        <td>0%</td>
                                        <td>$0.00</td>
                                        <td>0.00</td>
                                        <td>$0.00</td>
                                        <td>$0.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="dashboard-card symbol-tabs-card">
                        <div class="dashboard-card-header">
                            <h2 class="dashboard-card-title">Symbol Details</h2>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="symbol-tabs" id="symbol-tabs">
                                <!-- Symbol tabs will be added here by JavaScript -->
                            </div>
                            <div class="symbol-content-container" id="symbol-content">
                                <!-- Symbol content will be added here by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
