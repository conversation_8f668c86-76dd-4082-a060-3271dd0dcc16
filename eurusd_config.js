/**
 * EURUSD Trading Configuration
 *
 * This file contains the configuration for trading EURUSD forex pair.
 * Parameters will be optimized based on grid test results.
 */

module.exports = {
    // Symbol information
    symbol: 'EURUSD',
    pointValue: 1.0,     // Each pip is worth $1 per standard lot (100,000 units)
    tickSize: 0.0001,    // Forex typically has 4 or 5 decimal places for major pairs
    
    // Contract information
    contractMonth: null, // Not applicable for forex
    
    // Account information
    accountId: null, // Will be set during initialization
    
    // Commission and slippage
    commission: 0.0,     // Forex typically has no commission but uses spread
    commissionPerContract: 0.0,
    slippage: 0.0002,    // 2 pips of slippage (typical for EURUSD)
    slippagePoints: 0.0002,
    
    // Position sizing
    fixedContracts: 10,  // Will represent mini lots (10,000 units each)
    
    // Indicator parameters
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    sma200Period: 0, // Not used
    atrPeriod: 14,
    
    // Entry/exit parameters - Will be optimized through grid testing
    slFactors: 5.0,
    tpFactors: 4.0,
    trailFactors: 0.03,
    fixedTpPoints: 0,
    
    // Risk management
    maxDailyLoss: 0.10, // 10% of account
    
    // Filters
    minAtrEntry: 0.0005,
    minRsiMaSeparation: 1.0,
    useWmaFilter: true,
    useTwoBarColorExit: false,
    
    // ATR thresholds for adaptive mode
    isAdaptiveRun: false,
    atrThresholds: {
        low_medium: 0.0010,
        medium_high: 0.0020
    },
    
    // Formatting
    pricePrecision: 5,
    
    // Logging
    logLevel: 'info'
};
