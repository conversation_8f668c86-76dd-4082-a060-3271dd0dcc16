<!DOCTYPE html>

<html lang="en">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>MNQ Trading Bot - Monitoring Dashboard</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<style>
        body {
            background-color: #0f172a;
            color: #e2e8f0;
            font-family: 'Inter', sans-serif;
        }
        .card {
            background-color: #1e293b;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-green {
            background-color: #10b981;
            box-shadow: 0 0 8px #10b981;
        }
        .status-yellow {
            background-color: #f59e0b;
            box-shadow: 0 0 8px #f59e0b;
        }
        .status-red {
            background-color: #ef4444;
            box-shadow: 0 0 8px #ef4444;
        }
        .value-positive {
            color: #10b981;
        }
        .value-negative {
            color: #ef4444;
        }
        .value-neutral {
            color: #e2e8f0;
        }
        .grid-item {
            min-height: 200px;
        }
        .trade-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .trade-list::-webkit-scrollbar {
            width: 6px;
        }
        .trade-list::-webkit-scrollbar-track {
            background: #1e293b;
        }
        .trade-list::-webkit-scrollbar-thumb {
            background-color: #4b5563;
            border-radius: 3px;
        }
        .alert-item {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        .alert-item:hover {
            background-color: #2d3748;
        }
        .alert-info {
            border-color: #3b82f6;
        }
        .alert-warning {
            border-color: #f59e0b;
        }
        .alert-critical {
            border-color: #ef4444;
        }
        .tab-button {
            padding: 0.5rem 1rem;
            border-radius: 0.25rem 0.25rem 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .tab-button.active {
            background-color: #1e293b;
            color: #e2e8f0;
            border-bottom: 2px solid #3b82f6;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
<script src="dashboard_config.js"></script><script src="dashboard_data_loader.js"></script><script src="dashboard_chart_utils.js"></script></head>
<body class="min-h-screen p-4">
<div class="container mx-auto">
<!-- Header -->
<div class="flex justify-between items-center mb-6">
<div>
<h1 class="text-2xl font-bold">MNQ Trading Bot - Monitoring Dashboard</h1>
<p class="text-gray-400">Real-time performance monitoring and alerts</p>
</div>
<div class="flex items-center">
<div class="mr-6">
<span class="status-indicator status-green"></span>
<span class="font-medium">System Active</span>
</div>
<div class="mr-6">
<span class="text-gray-400" id="last-update">Last update: Just now</span>
</div>
<button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition" id="refresh-btn">
                    Refresh
                </button>
</div>
</div>
<!-- Status Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
<div class="card p-4">
<h3 class="text-gray-400 text-sm mb-1">Trading Status</h3>
<div class="flex items-center">
<span class="status-indicator status-green"></span>
<span class="text-xl font-bold">Active</span>
</div>
<p class="text-gray-400 text-sm mt-2">Running for 2d 4h 35m</p>
</div>
<div class="card p-4">
<h3 class="text-gray-400 text-sm mb-1">Today's P&amp;L</h3>
<div class="text-xl font-bold value-positive">+$1,245.67</div>
<p class="text-gray-400 text-sm mt-2">12 trades (75% win rate)</p>
</div>
<div class="card p-4">
<h3 class="text-gray-400 text-sm mb-1">Current Drawdown</h3>
<div class="text-xl font-bold value-neutral">$87.50 (0.87%)</div>
<p class="text-gray-400 text-sm mt-2">Max today: $120.30 (1.2%)</p>
</div>
<div class="card p-4">
<h3 class="text-gray-400 text-sm mb-1">Account Balance</h3>
<div class="text-xl font-bold">$10,245.67</div>
<p class="text-gray-400 text-sm mt-2">+12.45% since start</p>
</div>
</div>
<!-- Main Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
<!-- Left Column -->
<div class="lg:col-span-2">
<!-- Performance Chart -->
<div class="card p-4 mb-6">
<div class="flex justify-between items-center mb-4">
<h2 class="text-lg font-bold">Performance</h2>
<div class="flex space-x-2">
<button class="tab-button active" data-tab="equity-curve">Equity Curve</button>
<button class="tab-button" data-tab="daily-pnl">Daily P&amp;L</button>
<button class="tab-button" data-tab="drawdown">Drawdown</button>
</div>
</div>
<div class="tab-content active" id="equity-curve">
<canvas height="250" id="equity-chart"></canvas>
</div>
<div class="tab-content" id="daily-pnl">
<canvas height="250" id="pnl-chart"></canvas>
</div>
<div class="tab-content" id="drawdown">
<canvas height="250" id="drawdown-chart"></canvas>
</div>
</div>
<!-- Recent Trades -->
<div class="card p-4 mb-6">
<div class="flex justify-between items-center mb-4">
<h2 class="text-lg font-bold">Recent Trades</h2>
<button class="text-blue-500 hover:text-blue-400 text-sm">View All</button>
</div>
<div class="overflow-x-auto">
<table class="min-w-full">
<thead>
<tr class="text-left text-gray-400 text-sm">
<th class="pb-2">Time</th>
<th class="pb-2">Direction</th>
<th class="pb-2">Entry</th>
<th class="pb-2">Exit</th>
<th class="pb-2">P&amp;L</th>
<th class="pb-2">Duration</th>
<th class="pb-2">Exit Reason</th>
</tr>
</thead>
<tbody class="text-sm">
<tr class="border-t border-gray-700">
<td class="py-3">10:45:23</td>
<td class="py-3"><span class="px-2 py-1 bg-green-900 text-green-300 rounded-md">LONG</span></td>
<td class="py-3">18245.50</td>
<td class="py-3">18256.75</td>
<td class="py-3 value-positive">+$112.50</td>
<td class="py-3">2m 15s</td>
<td class="py-3">Trail Stop</td>
</tr>
<tr class="border-t border-gray-700">
<td class="py-3">10:32:17</td>
<td class="py-3"><span class="px-2 py-1 bg-red-900 text-red-300 rounded-md">SHORT</span></td>
<td class="py-3">18267.25</td>
<td class="py-3">18258.50</td>
<td class="py-3 value-positive">+$87.50</td>
<td class="py-3">4m 32s</td>
<td class="py-3">Take Profit</td>
</tr>
<tr class="border-t border-gray-700">
<td class="py-3">10:15:42</td>
<td class="py-3"><span class="px-2 py-1 bg-green-900 text-green-300 rounded-md">LONG</span></td>
<td class="py-3">18234.75</td>
<td class="py-3">18230.25</td>
<td class="py-3 value-negative">-$45.00</td>
<td class="py-3">1m 47s</td>
<td class="py-3">Stop Loss</td>
</tr>
<tr class="border-t border-gray-700">
<td class="py-3">09:58:03</td>
<td class="py-3"><span class="px-2 py-1 bg-red-900 text-red-300 rounded-md">SHORT</span></td>
<td class="py-3">18245.00</td>
<td class="py-3">18232.25</td>
<td class="py-3 value-positive">+$127.50</td>
<td class="py-3">5m 12s</td>
<td class="py-3">Trail Stop</td>
</tr>
<tr class="border-t border-gray-700">
<td class="py-3">09:42:51</td>
<td class="py-3"><span class="px-2 py-1 bg-green-900 text-green-300 rounded-md">LONG</span></td>
<td class="py-3">18220.50</td>
<td class="py-3">18235.75</td>
<td class="py-3 value-positive">+$152.50</td>
<td class="py-3">3m 28s</td>
<td class="py-3">Take Profit</td>
</tr>
</tbody>
</table>
</div>
</div>
<!-- System Metrics -->
<div class="card p-4">
<h2 class="text-lg font-bold mb-4">System Metrics</h2>
<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
<div>
<h3 class="text-gray-400 text-sm mb-2">CPU Usage</h3>
<div class="h-4 bg-gray-700 rounded-full overflow-hidden">
<div class="h-full bg-blue-500" style="width: 35%"></div>
</div>
<p class="text-sm mt-1">35%</p>
</div>
<div>
<h3 class="text-gray-400 text-sm mb-2">Memory Usage</h3>
<div class="h-4 bg-gray-700 rounded-full overflow-hidden">
<div class="h-full bg-purple-500" style="width: 42%"></div>
</div>
<p class="text-sm mt-1">42%</p>
</div>
<div>
<h3 class="text-gray-400 text-sm mb-2">Network Latency</h3>
<div class="h-4 bg-gray-700 rounded-full overflow-hidden">
<div class="h-full bg-green-500" style="width: 15%"></div>
</div>
<p class="text-sm mt-1">15ms</p>
</div>
</div>
<div class="mt-4">
<h3 class="text-gray-400 text-sm mb-2">API Connections</h3>
<div class="grid grid-cols-2 gap-2 text-sm">
<div class="flex items-center">
<span class="status-indicator status-green"></span>
<span>Tradovate API</span>
</div>
<div class="flex items-center">
<span class="status-indicator status-green"></span>
<span>Market Data</span>
</div>
<div class="flex items-center">
<span class="status-indicator status-green"></span>
<span>WebSocket</span>
</div>
<div class="flex items-center">
<span class="status-indicator status-green"></span>
<span>Database</span>
</div>
</div>
</div>
</div>
</div>
<!-- Right Column -->
<div>
<!-- Current Position -->
<div class="card p-4 mb-6">
<h2 class="text-lg font-bold mb-4">Current Position</h2>
<div class="text-center py-4">
<div class="text-2xl font-bold mb-2">
<span class="px-3 py-1 bg-green-900 text-green-300 rounded-md">LONG</span>
</div>
<div class="grid grid-cols-2 gap-4 mt-4">
<div>
<p class="text-gray-400 text-sm">Entry Price</p>
<p class="text-xl font-medium">18245.50</p>
</div>
<div>
<p class="text-gray-400 text-sm">Current Price</p>
<p class="text-xl font-medium">18256.75</p>
</div>
<div>
<p class="text-gray-400 text-sm">Unrealized P&amp;L</p>
<p class="text-xl font-medium value-positive">+$112.50</p>
</div>
<div>
<p class="text-gray-400 text-sm">Position Size</p>
<p class="text-xl font-medium">5 contracts</p>
</div>
</div>
<div class="grid grid-cols-3 gap-2 mt-4">
<div>
<p class="text-gray-400 text-sm">Stop Loss</p>
<p class="text-sm font-medium">18232.25</p>
</div>
<div>
<p class="text-gray-400 text-sm">Take Profit</p>
<p class="text-sm font-medium">18267.75</p>
</div>
<div>
<p class="text-gray-400 text-sm">Trail Stop</p>
<p class="text-sm font-medium">18252.50</p>
</div>
</div>
<div class="mt-4">
<p class="text-gray-400 text-sm">Duration</p>
<p class="text-sm font-medium">1m 45s</p>
</div>
</div>
</div>
<!-- Daily Stats -->
<div class="card p-4 mb-6">
<h2 class="text-lg font-bold mb-4">Today's Stats</h2>
<div class="grid grid-cols-2 gap-4">
<div>
<p class="text-gray-400 text-sm">Total Trades</p>
<p class="text-xl font-medium">12</p>
</div>
<div>
<p class="text-gray-400 text-sm">Win Rate</p>
<p class="text-xl font-medium">75%</p>
</div>
<div>
<p class="text-gray-400 text-sm">Avg Win</p>
<p class="text-xl font-medium value-positive">$118.75</p>
</div>
<div>
<p class="text-gray-400 text-sm">Avg Loss</p>
<p class="text-xl font-medium value-negative">$42.50</p>
</div>
<div>
<p class="text-gray-400 text-sm">Profit Factor</p>
<p class="text-xl font-medium">2.8</p>
</div>
<div>
<p class="text-gray-400 text-sm">Avg Duration</p>
<p class="text-xl font-medium">3m 24s</p>
</div>
</div>
</div>
<!-- Alerts -->
<div class="card p-4">
<div class="flex justify-between items-center mb-4">
<h2 class="text-lg font-bold">Recent Alerts</h2>
<button class="text-blue-500 hover:text-blue-400 text-sm">View All</button>
</div>
<div class="space-y-3 trade-list">
<div class="alert-item alert-info p-3">
<div class="flex justify-between">
<p class="font-medium">Trade Entered</p>
<p class="text-gray-400 text-sm">10:45:23</p>
</div>
<p class="text-sm text-gray-300 mt-1">LONG 5 contracts at 18245.50</p>
</div>
<div class="alert-item alert-info p-3">
<div class="flex justify-between">
<p class="font-medium">Trade Exited</p>
<p class="text-gray-400 text-sm">10:32:17</p>
</div>
<p class="text-sm text-gray-300 mt-1">SHORT 5 contracts at 18258.50 (+$87.50)</p>
</div>
<div class="alert-item alert-warning p-3">
<div class="flex justify-between">
<p class="font-medium">Drawdown Warning</p>
<p class="text-gray-400 text-sm">10:15:42</p>
</div>
<p class="text-sm text-gray-300 mt-1">Current drawdown approaching warning threshold (2.8%)</p>
</div>
<div class="alert-item alert-info p-3">
<div class="flex justify-between">
<p class="font-medium">Trade Exited</p>
<p class="text-gray-400 text-sm">09:58:03</p>
</div>
<p class="text-sm text-gray-300 mt-1">SHORT 5 contracts at 18232.25 (+$127.50)</p>
</div>
<div class="alert-item alert-critical p-3">
<div class="flex justify-between">
<p class="font-medium">Connection Issue</p>
<p class="text-gray-400 text-sm">09:45:12</p>
</div>
<p class="text-sm text-gray-300 mt-1">WebSocket connection lost. Attempting to reconnect...</p>
</div>
<div class="alert-item alert-info p-3">
<div class="flex justify-between">
<p class="font-medium">Connection Restored</p>
<p class="text-gray-400 text-sm">09:45:18</p>
</div>
<p class="text-sm text-gray-300 mt-1">WebSocket connection restored successfully.</p>
</div>
<div class="alert-item alert-info p-3">
<div class="flex justify-between">
<p class="font-medium">Trade Exited</p>
<p class="text-gray-400 text-sm">09:42:51</p>
</div>
<p class="text-sm text-gray-300 mt-1">LONG 5 contracts at 18235.75 (+$152.50)</p>
</div>
</div>
</div>
</div>
</div>
</div>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    const tabContainer = this.closest('div').parentElement;
                    
                    // Remove active class from all buttons and content
                    tabContainer.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                    tabContainer.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding content
                    this.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // Equity Chart
            const equityCtx = document.getElementById('equity-chart').getContext('2d');
            const equityChart = new Chart(equityCtx, {
                type: 'line',
                data: {
                    labels: ['May 1', 'May 2', 'May 3', 'May 4', 'May 5', 'May 6', 'May 7'],
                    datasets: [{
                        label: 'Equity Curve',
                        data: [10000, 10245, 10187, 10356, 10512, 10678, 10845],
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(75, 85, 99, 0.2)'
                            },
                            ticks: {
                                color: '#9ca3af'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(75, 85, 99, 0.2)'
                            },
                            ticks: {
                                color: '#9ca3af'
                            }
                        }
                    }
                }
            });
            
            // P&L Chart
            const pnlCtx = document.getElementById('pnl-chart').getContext('2d');
            const pnlChart = new Chart(pnlCtx, {
                type: 'bar',
                data: {
                    labels: ['May 1', 'May 2', 'May 3', 'May 4', 'May 5', 'May 6', 'May 7'],
                    datasets: [{
                        label: 'Daily P&L',
                        data: [245, -58, 169, 156, 166, 167, 245],
                        backgroundColor: function(context) {
                            const value = context.dataset.data[context.dataIndex];
                            return value >= 0 ? 'rgba(16, 185, 129, 0.7)' : 'rgba(239, 68, 68, 0.7)';
                        },
                        borderColor: function(context) {
                            const value = context.dataset.data[context.dataIndex];
                            return value >= 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)';
                        },
                        borderWidth: 1,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(75, 85, 99, 0.2)'
                            },
                            ticks: {
                                color: '#9ca3af'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(75, 85, 99, 0.2)'
                            },
                            ticks: {
                                color: '#9ca3af'
                            }
                        }
                    }
                }
            });
            
            // Drawdown Chart
            const drawdownCtx = document.getElementById('drawdown-chart').getContext('2d');
            const drawdownChart = new Chart(drawdownCtx, {
                type: 'line',
                data: {
                    labels: ['May 1', 'May 2', 'May 3', 'May 4', 'May 5', 'May 6', 'May 7'],
                    datasets: [{
                        label: 'Drawdown',
                        data: [0, -0.8, -1.2, -0.5, -0.3, -0.7, -0.4],
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(75, 85, 99, 0.2)'
                            },
                            ticks: {
                                color: '#9ca3af'
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(75, 85, 99, 0.2)'
                            },
                            ticks: {
                                color: '#9ca3af'
                            },
                            reverse: true
                        }
                    }
                }
            });
            
            // Refresh button
            document.getElementById('refresh-btn').addEventListener('click', function() {
                // In a real implementation, this would fetch new data
                document.getElementById('last-update').textContent = 'Last update: Just now';
            });
            
            // Update last update time every minute
            setInterval(function() {
                const lastUpdate = document.getElementById('last-update');
                lastUpdate.textContent = 'Last update: 1m ago';
            }, 60000);
        });
    </script>
<script>
// Load data for all instruments
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the data loader
    const dataLoader = dashboardDataLoader;
    
    // Load data for all instruments
    dataLoader.loadAllData()
        .then(data => {
            console.log('All data loaded successfully');
            // Initialize the dashboard with the loaded data
            initializeDashboard(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
        });
    
    // Function to initialize the dashboard with the loaded data
    function initializeDashboard(data) {
        // Get the instrument from the URL query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const instrumentParam = urlParams.get('instrument');
        const showAllInstruments = urlParams.get('showAllInstruments') === 'true';
        
        // Determine which instrument(s) to display
        let instrumentsToDisplay = [];
        // Always show all instruments by default
        if (instrumentParam && data[instrumentParam]) {
            // If a specific instrument is requested, show only that one
            instrumentsToDisplay = [instrumentParam];
        } else {
            // Default to showing all instruments
            instrumentsToDisplay = Object.keys(data);
        }
        
        // Update the dashboard title to reflect the selected instrument(s)
        updateDashboardTitle(instrumentsToDisplay);
        
        // Update the dashboard content with the selected instrument(s)
        updateDashboardContent(data, instrumentsToDisplay);
    }
    
    // Function to update the dashboard title
    function updateDashboardTitle(instruments) {
        const titleElement = document.querySelector('h1');
        if (titleElement) {
            if (instruments.length === 1) {
                const instrumentName = DASHBOARD_CONFIG.dataSources.instruments[instruments[0]].name;
                titleElement.textContent = titleElement.textContent.replace('Dashboard', `${instrumentName} Dashboard`);
            } else if (instruments.length > 1) {
                titleElement.textContent = titleElement.textContent.replace('Dashboard', 'Multi-Instrument Dashboard');
            }
        }
    }
    
    // Function to update the dashboard content
    function updateDashboardContent(data, instruments) {
        // This function should be customized for each dashboard
        // For now, we'll just log the data for the selected instruments
        instruments.forEach(instrumentCode => {
            console.log(`Data for ${instrumentCode}:`, data[instrumentCode]);
        });
        
        // Call any dashboard-specific initialization functions
        if (typeof initializeDashboardCharts === 'function') {
            initializeDashboardCharts(data, instruments);
        }
        
        if (typeof updateDashboardStats === 'function') {
            updateDashboardStats(data, instruments);
        }
        
        if (typeof updateDashboardTables === 'function') {
            updateDashboardTables(data, instruments);
        }
    }
});
</script>
</body>
</html>
