<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>South Florida Golf Course Tracker</title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2e7d32">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Golf Tracker">
    <link rel="apple-touch-icon" href="icon-192.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .add-round-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(255,107,53,0.3);
            transition: transform 0.2s;
        }

        .add-round-btn:hover {
            transform: translateY(-2px);
        }

        .rounds-list {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .round-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .round-item:last-child {
            border-bottom: none;
        }

        .round-info h3 {
            color: #2e7d32;
            margin-bottom: 5px;
        }

        .round-details {
            color: #666;
            font-size: 0.9rem;
        }

        .round-score {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b35;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 50px auto;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2e7d32;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #2e7d32;
            color: white;
        }

        .btn-primary:hover {
            background: #1b5e20;
        }

        .btn-secondary {
            background: #ddd;
            color: #333;
        }

        .btn-secondary:hover {
            background: #bbb;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
        }

        @media (max-width: 600px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .modal-content {
                margin: 20px auto;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏌️ Golf Tracker</h1>
            <p>Track every course you play in South Florida</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalRounds">0</div>
                <div class="stat-label">Total Rounds</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueCourses">0</div>
                <div class="stat-label">Unique Courses</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgScore">--</div>
                <div class="stat-label">Average Score</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="bestScore">--</div>
                <div class="stat-label">Best Score</div>
            </div>
        </div>

        <button class="add-round-btn" onclick="openAddRoundModal()">
            + Add New Round
        </button>

        <div class="rounds-list" id="roundsList">
            <div class="empty-state">
                <h3>No rounds recorded yet</h3>
                <p>Add your first round to get started!</p>
            </div>
        </div>
    </div>

    <!-- Add Round Modal -->
    <div class="modal" id="addRoundModal">
        <div class="modal-content">
            <h2>Add New Round</h2>
            <form id="addRoundForm">
                <div class="form-group">
                    <label for="courseName">Course Name</label>
                    <input type="text" id="courseName" required placeholder="e.g., Doral Golf Resort">
                </div>
                
                <div class="form-group">
                    <label for="courseLocation">Location</label>
                    <input type="text" id="courseLocation" required placeholder="e.g., Miami, FL">
                </div>
                
                <div class="form-group">
                    <label for="playDate">Date Played</label>
                    <input type="date" id="playDate" required>
                </div>
                
                <div class="form-group">
                    <label for="score">Score</label>
                    <input type="number" id="score" required placeholder="e.g., 85">
                </div>
                
                <div class="form-group">
                    <label for="par">Course Par</label>
                    <input type="number" id="par" placeholder="e.g., 72">
                </div>
                
                <div class="form-group">
                    <label for="tees">Tees Played</label>
                    <select id="tees">
                        <option value="">Select tees</option>
                        <option value="Black">Black (Championship)</option>
                        <option value="Blue">Blue (Men's)</option>
                        <option value="White">White (Regular)</option>
                        <option value="Gold">Gold (Senior)</option>
                        <option value="Red">Red (Ladies)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea id="notes" rows="3" placeholder="How was the course? Weather conditions? Memorable shots?"></textarea>
                </div>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="closeAddRoundModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Round</button>
                </div>
            </form>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
