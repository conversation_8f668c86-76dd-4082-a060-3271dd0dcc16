/**
 * Simple Backtesting Script
 *
 * This script runs a backtest of your trading strategy on historical data.
 * It uses the same strategy logic as your successful trading bot.
 */

const fs = require('fs');
const path = require('path');

// Define configurations
const mnqConfig = {
    symbol: 'MNQ',
    pointValue: 0.50,
    slFactors: 4.5,
    tpFactors: 3.0,
    trailFactors: 0.11,
    fixedTpPoints: 40,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14
};

const mgcConfig = {
    symbol: 'MGC',
    pointValue: 10.00,
    slFactors: 8.0,
    tpFactors: 7.0,
    trailFactors: 0.02,
    fixedTpPoints: 0,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14
};

const m2kConfig = {
    symbol: 'M2K',
    pointValue: 5.00,
    slFactors: 3.0,
    tpFactors: 3.0,
    trailFactors: 0.01,
    fixedTpPoints: 0,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14
};

// Create MES config based on MNQ config
const mesConfig = {
    symbol: 'MES',
    pointValue: 5.00,
    slFactors: 3.0,
    tpFactors: 3.0,
    trailFactors: 0.01,
    fixedTpPoints: 0,
    rsiPeriod: 14,
    rsiMaPeriod: 8,
    wma50Period: 50,
    atrPeriod: 14
};

// Map of symbols to configs
const symbolConfigs = {
    'MNQ': mnqConfig,
    'MES': mesConfig,
    'MGC': mgcConfig,
    'M2K': m2kConfig
};

// Parse command line arguments
const args = process.argv.slice(2);
const symbolArg = args.find(arg => arg.startsWith('--symbol='));
const startDateArg = args.find(arg => arg.startsWith('--start='));
const endDateArg = args.find(arg => arg.startsWith('--end='));
const positionSizeArg = args.find(arg => arg.startsWith('--size='));

// Default values
const defaultSymbol = 'MNQ';
const defaultStartDate = new Date('2025-01-01T00:00:00Z');
const defaultEndDate = new Date('2025-01-31T23:59:59Z');
const defaultPositionSize = 2;

// Parse symbol
let symbol = defaultSymbol;
if (symbolArg) {
    symbol = symbolArg.split('=')[1];
}

// Parse dates
let startDate = defaultStartDate;
if (startDateArg) {
    startDate = new Date(startDateArg.split('=')[1]);
}

let endDate = defaultEndDate;
if (endDateArg) {
    endDate = new Date(endDateArg.split('=')[1]);
}

// Parse position size
let positionSize = defaultPositionSize;
if (positionSizeArg) {
    positionSize = parseInt(positionSizeArg.split('=')[1], 10);
}

// Print configuration
console.log('Backtest Configuration:');
console.log(`Symbol: ${symbol}`);
console.log(`Start Date: ${startDate.toISOString()}`);
console.log(`End Date: ${endDate.toISOString()}`);
console.log(`Position Size: ${positionSize} contracts`);

// Load the configuration for the symbol
const config = symbolConfigs[symbol] || symbolConfigs.MNQ;
console.log(`Using configuration for ${config.symbol}`);

// Technical indicators
class Indicators {
    constructor() {
        this.cache = new Map();
    }

    // Calculate RSI
    calculateRSI(prices, period = 14) {
        const cacheKey = `rsi_${period}_${prices.length}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        if (prices.length < period + 1) {
            return [];
        }

        const gains = [];
        const losses = [];

        // First value is 0
        gains.push(0);
        losses.push(0);

        // Calculate gains and losses
        for (let i = 1; i < prices.length; i++) {
            const diff = prices[i] - prices[i - 1];
            gains.push(diff > 0 ? diff : 0);
            losses.push(diff < 0 ? Math.abs(diff) : 0);
        }

        // Calculate average gains and losses
        const avgGains = [];
        const avgLosses = [];

        // First average is simple average
        let gainSum = 0;
        let lossSum = 0;

        for (let i = 0; i < period; i++) {
            gainSum += gains[i];
            lossSum += losses[i];
        }

        avgGains.push(gainSum / period);
        avgLosses.push(lossSum / period);

        // Rest of averages are smoothed
        for (let i = period; i < prices.length; i++) {
            avgGains.push((avgGains[avgGains.length - 1] * (period - 1) + gains[i]) / period);
            avgLosses.push((avgLosses[avgLosses.length - 1] * (period - 1) + losses[i]) / period);
        }

        // Calculate RS and RSI
        const rsiValues = [];

        for (let i = 0; i < avgGains.length; i++) {
            const rs = avgLosses[i] === 0 ? 100 : avgGains[i] / avgLosses[i];
            const rsi = avgLosses[i] === 0 ? 100 : 100 - (100 / (1 + rs));
            rsiValues.push(rsi);
        }

        this.cache.set(cacheKey, rsiValues);
        return rsiValues;
    }

    // Calculate SMA
    calculateSMA(values, period = 14) {
        const cacheKey = `sma_${period}_${values.length}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        if (values.length < period) {
            return [];
        }

        const smaValues = [];

        for (let i = period - 1; i < values.length; i++) {
            let sum = 0;
            for (let j = 0; j < period; j++) {
                sum += values[i - j];
            }
            smaValues.push(sum / period);
        }

        this.cache.set(cacheKey, smaValues);
        return smaValues;
    }

    // Calculate WMA
    calculateWMA(values, period = 50) {
        const cacheKey = `wma_${period}_${values.length}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        if (values.length < period) {
            return [];
        }

        const wmaValues = [];

        for (let i = period - 1; i < values.length; i++) {
            let sum = 0;
            let weightSum = 0;

            for (let j = 0; j < period; j++) {
                const weight = period - j;
                sum += values[i - j] * weight;
                weightSum += weight;
            }

            wmaValues.push(sum / weightSum);
        }

        this.cache.set(cacheKey, wmaValues);
        return wmaValues;
    }

    // Calculate ATR
    calculateATR(highs, lows, closes, period = 14) {
        const cacheKey = `atr_${period}_${highs.length}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        if (highs.length < period + 1) {
            return [];
        }

        const trValues = [];

        // Calculate true ranges
        for (let i = 1; i < highs.length; i++) {
            const high = highs[i];
            const low = lows[i];
            const prevClose = closes[i - 1];

            const tr1 = high - low;
            const tr2 = Math.abs(high - prevClose);
            const tr3 = Math.abs(low - prevClose);

            trValues.push(Math.max(tr1, tr2, tr3));
        }

        // Calculate ATR
        const atrValues = [];

        // First ATR is simple average of TR
        let sum = 0;
        for (let i = 0; i < period; i++) {
            sum += trValues[i];
        }
        atrValues.push(sum / period);

        // Rest of ATRs are smoothed
        for (let i = period; i < trValues.length; i++) {
            atrValues.push((atrValues[atrValues.length - 1] * (period - 1) + trValues[i]) / period);
        }

        this.cache.set(cacheKey, atrValues);
        return atrValues;
    }
}

// Load historical data
function loadHistoricalData(symbol, startDate, endDate) {
    const filePath = path.join('C:', 'backtest-bot', 'input', `${symbol}_test.csv`);

    if (!fs.existsSync(filePath)) {
        console.error(`Historical data file not found: ${filePath}`);
        return [];
    }

    console.log(`Loading historical data from ${filePath}...`);

    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n');

    // Parse header
    const header = lines[0].split(',');
    const timestampIndex = header.indexOf('timestamp');
    const openIndex = header.indexOf('open');
    const highIndex = header.indexOf('high');
    const lowIndex = header.indexOf('low');
    const closeIndex = header.indexOf('close');
    const volumeIndex = header.indexOf('volume');

    if (timestampIndex === -1 || openIndex === -1 || highIndex === -1 ||
        lowIndex === -1 || closeIndex === -1 || volumeIndex === -1) {
        console.error(`Invalid header in ${filePath}: ${header.join(',')}`);
        return [];
    }

    // Parse data
    const data = [];

    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',');

        if (values.length <= Math.max(timestampIndex, openIndex, highIndex, lowIndex, closeIndex, volumeIndex)) {
            console.warn(`Invalid line ${i} in ${filePath}: ${line}`);
            continue;
        }

        const timestamp = new Date(values[timestampIndex]);

        // Skip data outside the date range
        if (timestamp < startDate || timestamp > endDate) {
            continue;
        }

        const open = parseFloat(values[openIndex]);
        const high = parseFloat(values[highIndex]);
        const low = parseFloat(values[lowIndex]);
        const close = parseFloat(values[closeIndex]);
        const volume = parseFloat(values[volumeIndex]);

        data.push({
            timestamp,
            open,
            high,
            low,
            close,
            volume
        });
    }

    console.log(`Loaded ${data.length} data points`);

    return data;
}

// Backtest function
function runBacktest(symbol, data, config, positionSize) {
    console.log(`Running backtest for ${symbol}...`);

    // Initialize indicators
    const indicators = new Indicators();

    // Extract price data
    const timestamps = data.map(d => d.timestamp);
    const opens = data.map(d => d.open);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    const closes = data.map(d => d.close);
    const volumes = data.map(d => d.volume);

    // Calculate indicators
    const rsiValues = indicators.calculateRSI(closes, config.rsiPeriod);
    const rsiMaValues = indicators.calculateSMA(rsiValues, config.rsiMaPeriod);
    const wmaValues = indicators.calculateWMA(closes, config.wma50Period);
    const atrValues = indicators.calculateATR(highs, lows, closes, config.atrPeriod);

    // Initialize state
    let position = 'flat'; // 'flat', 'long', 'short'
    let entryPrice = 0;
    let stopLoss = 0;
    let takeProfit = 0;
    let trailPrice = 0;
    let trades = [];
    let equity = 10000;
    let balance = 10000;

    // Process each bar
    for (let i = Math.max(config.rsiPeriod, config.wma50Period, config.atrPeriod); i < data.length; i++) {
        const bar = data[i];
        const rsi = rsiValues[i - config.rsiPeriod];
        const rsiMa = rsiMaValues[i - config.rsiPeriod - config.rsiMaPeriod + 1];
        const wma = wmaValues[i - config.wma50Period];
        const atr = atrValues[i - config.atrPeriod];

        // Skip if we don't have all indicators
        if (rsi === undefined || rsiMa === undefined || wma === undefined || atr === undefined) {
            continue;
        }

        // Check for exit first
        if (position !== 'flat') {
            let exitReason = null;
            let exitPrice = bar.close;

            // Long position
            if (position === 'long') {
                // Update trail price
                const trailAmount = config.trailFactors * atr;
                if (bar.high > entryPrice + trailAmount) {
                    const newTrailPrice = bar.high - trailAmount;
                    if (newTrailPrice > trailPrice) {
                        trailPrice = newTrailPrice;
                    }
                }

                // Check stop loss
                if (bar.low <= stopLoss) {
                    exitReason = 'Stop Loss';
                    exitPrice = stopLoss;
                }
                // Check take profit
                else if (bar.high >= takeProfit) {
                    exitReason = 'Take Profit';
                    exitPrice = takeProfit;
                }
                // Check trailing stop
                else if (bar.low <= trailPrice && bar.high > entryPrice + trailAmount) {
                    exitReason = 'Trailing Stop';
                    exitPrice = trailPrice;
                }
            }
            // Short position
            else if (position === 'short') {
                // Update trail price
                const trailAmount = config.trailFactors * atr;
                if (bar.low < entryPrice - trailAmount) {
                    const newTrailPrice = bar.low + trailAmount;
                    if (newTrailPrice < trailPrice) {
                        trailPrice = newTrailPrice;
                    }
                }

                // Check stop loss
                if (bar.high >= stopLoss) {
                    exitReason = 'Stop Loss';
                    exitPrice = stopLoss;
                }
                // Check take profit
                else if (bar.low <= takeProfit) {
                    exitReason = 'Take Profit';
                    exitPrice = takeProfit;
                }
                // Check trailing stop
                else if (bar.high >= trailPrice && bar.low < entryPrice - trailAmount) {
                    exitReason = 'Trailing Stop';
                    exitPrice = trailPrice;
                }
            }

            // Exit trade if we have a reason
            if (exitReason) {
                // Calculate P&L
                let pnlPoints = 0;
                if (position === 'long') {
                    pnlPoints = exitPrice - entryPrice;
                } else if (position === 'short') {
                    pnlPoints = entryPrice - exitPrice;
                }

                const pnl = pnlPoints * positionSize * config.pointValue;

                // Add to trades list
                trades.push({
                    symbol,
                    direction: position,
                    entryPrice,
                    exitPrice,
                    entryTime: entryTime,
                    exitTime: bar.timestamp,
                    pnl,
                    pnlPoints,
                    reason: exitReason
                });

                // Update balance
                balance += pnl;

                // Reset position
                position = 'flat';
                entryPrice = 0;
                stopLoss = 0;
                takeProfit = 0;
                trailPrice = 0;

                console.log(`${bar.timestamp.toISOString()} - ${symbol} - ${position} Exit at ${exitPrice.toFixed(2)}, Reason: ${exitReason}, PnL: $${pnl.toFixed(2)}, Points: ${pnlPoints.toFixed(2)}`);
            }
        }

        // Check for entry if we're flat
        if (position === 'flat') {
            // Long signal
            if (rsi > rsiMa && bar.close > wma) {
                position = 'long';
                entryPrice = bar.close;
                entryTime = bar.timestamp;

                // Calculate stop loss and take profit
                const slPoints = Math.min(entryPrice * 0.05, config.slFactors * atr); // Limit SL to 5% of price
                const tpPoints = Math.min(entryPrice * 0.03, config.tpFactors * atr); // Limit TP to 3% of price
                const trailAmount = Math.min(entryPrice * 0.01, config.trailFactors * atr); // Limit trail to 1% of price

                stopLoss = Math.max(0.1, entryPrice - slPoints); // Ensure SL is positive
                takeProfit = config.fixedTpPoints > 0 ? entryPrice + config.fixedTpPoints : entryPrice + tpPoints;
                trailPrice = Math.max(0.1, entryPrice - trailAmount); // Ensure trail is positive

                console.log(`${bar.timestamp.toISOString()} - ${symbol} - LONG Entry at ${entryPrice.toFixed(2)}, SL: ${stopLoss.toFixed(2)}, TP: ${takeProfit.toFixed(2)}, Trail: ${trailPrice.toFixed(2)}`);
            }
            // Short signal
            else if (rsi < rsiMa && bar.close < wma) {
                position = 'short';
                entryPrice = bar.close;
                entryTime = bar.timestamp;

                // Calculate stop loss and take profit
                const slPoints = Math.min(entryPrice * 0.05, config.slFactors * atr); // Limit SL to 5% of price
                const tpPoints = Math.min(entryPrice * 0.03, config.tpFactors * atr); // Limit TP to 3% of price
                const trailAmount = Math.min(entryPrice * 0.01, config.trailFactors * atr); // Limit trail to 1% of price

                stopLoss = entryPrice + slPoints; // For short positions, SL is above entry
                takeProfit = config.fixedTpPoints > 0 ? Math.max(0.1, entryPrice - config.fixedTpPoints) : Math.max(0.1, entryPrice - tpPoints);
                trailPrice = entryPrice + trailAmount; // For short positions, trail is above entry

                console.log(`${bar.timestamp.toISOString()} - ${symbol} - SHORT Entry at ${entryPrice.toFixed(2)}, SL: ${stopLoss.toFixed(2)}, TP: ${takeProfit.toFixed(2)}, Trail: ${trailPrice.toFixed(2)}`);
            }
        }
    }

    // Calculate statistics
    const totalTrades = trades.length;
    const winningTrades = trades.filter(t => t.pnl > 0).length;
    const losingTrades = trades.filter(t => t.pnl < 0).length;
    const breakEvenTrades = totalTrades - winningTrades - losingTrades;

    const totalPnL = trades.reduce((sum, t) => sum + t.pnl, 0);
    const winningPnL = trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0);
    const losingPnL = trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0);

    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const avgWin = winningTrades > 0 ? winningPnL / winningTrades : 0;
    const avgLoss = losingTrades > 0 ? losingPnL / losingTrades : 0;
    const profitFactor = losingPnL !== 0 ? Math.abs(winningPnL / losingPnL) : 0;

    return {
        symbol,
        totalTrades,
        winningTrades,
        losingTrades,
        breakEvenTrades,
        winRate,
        totalPnL,
        winningPnL,
        losingPnL,
        avgWin,
        avgLoss,
        profitFactor,
        balance,
        trades
    };
}

// Main function
function main() {
    try {
        // Load historical data
        const data = loadHistoricalData(symbol, startDate, endDate);

        if (data.length === 0) {
            console.error('No historical data found');
            return;
        }

        // Run backtest
        const results = runBacktest(symbol, data, config, positionSize);

        // Print results
        console.log('\nBacktest Results:');
        console.log('================');
        console.log(`Symbol: ${results.symbol}`);
        console.log(`Total Trades: ${results.totalTrades}`);
        console.log(`Winning Trades: ${results.winningTrades} (${results.winRate.toFixed(2)}%)`);
        console.log(`Losing Trades: ${results.losingTrades}`);
        console.log(`Total P&L: $${results.totalPnL.toFixed(2)}`);
        console.log(`Avg Win: $${results.avgWin.toFixed(2)}`);
        console.log(`Avg Loss: $${results.avgLoss.toFixed(2)}`);
        console.log(`Profit Factor: ${results.profitFactor.toFixed(2)}`);
        console.log(`Final Balance: $${results.balance.toFixed(2)}`);

        // Save results to file
        const resultsFile = path.join('C:', 'backtest-bot', 'backtest-results.json');
        fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));

        console.log(`\nResults saved to ${resultsFile}`);
    } catch (error) {
        console.error('Error running backtest:', error);
    }
}

// Run the main function
main();
