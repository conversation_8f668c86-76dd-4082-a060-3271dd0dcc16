#!/usr/bin/env python3
"""
Extract data from Databento ZIP file for recent data.
"""

import os
import sys
import zipfile
import pandas as pd
import csv
import io
from datetime import datetime

# Configure paths
input_file = "C:\\backtest-bot\\input\\GLBX-20250520-CR7H4G5WFU.zip"
output_dir = "C:\\backtest-bot\\input"

def extract_zip(zip_file, output_dir):
    """
    Extract a ZIP file.

    Args:
        zip_file (str): Path to ZIP file
        output_dir (str): Directory to extract to

    Returns:
        list: List of extracted files
    """
    try:
        print(f"Extracting {zip_file}...")

        extracted_files = []
        with zipfile.ZipFile(zip_file, 'r') as z:
            z.extractall(output_dir)
            extracted_files = z.namelist()

        print(f"Extracted {len(extracted_files)} files")
        for file in extracted_files:
            print(f"- {file}")

        return [os.path.join(output_dir, f) for f in extracted_files]

    except Exception as e:
        print(f"Error extracting ZIP file: {e}")
        return []

def analyze_csv_file(file_path):
    """
    Analyze a CSV file.

    Args:
        file_path (str): Path to CSV file

    Returns:
        DataFrame: Pandas DataFrame
    """
    try:
        print(f"Analyzing file: {file_path}")

        # Read the first few lines to check the format
        with open(file_path, 'r') as f:
            lines = f.readlines()[:5]

        print(f"File: {file_path}")
        print(f"Header: {lines[0].strip()}")
        print(f"First data row: {lines[1].strip()}")

        # Try to parse as a DataFrame
        df = pd.read_csv(file_path)
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {df.columns.tolist()}")

        # Check for symbol column
        if 'symbol' in df.columns:
            symbols = df['symbol'].unique()
            print(f"Unique symbols: {symbols}")

            # Count records per symbol
            for symbol in symbols:
                count = len(df[df['symbol'] == symbol])
                print(f"Records for {symbol}: {count}")

        return df
    except Exception as e:
        print(f"Error analyzing CSV file: {e}")
        return None

def extract_symbol_data(df, symbol_prefix, output_path):
    """
    Extract data for a specific symbol and save to CSV.

    Args:
        df (DataFrame): Pandas DataFrame
        symbol_prefix (str): Symbol prefix to extract (e.g., 'MNQ')
        output_path (str): Path to save the CSV file

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if 'symbol' not in df.columns:
            print(f"No 'symbol' column found in DataFrame")
            return False

        # Filter for the symbol
        symbol_mask = df['symbol'].str.startswith(symbol_prefix)
        symbol_df = df[symbol_mask]

        if len(symbol_df) == 0:
            print(f"No data found for symbol prefix: {symbol_prefix}")
            return False

        print(f"Found {len(symbol_df)} records for {symbol_prefix}")

        # Check if we have OHLC columns
        required_columns = ['ts_event', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in symbol_df.columns]

        if missing_columns:
            print(f"Missing required columns: {missing_columns}")
            return False

        # Format the data for the backtest engine
        backtest_df = symbol_df[required_columns].copy()
        
        # Convert timestamp to datetime
        backtest_df['ts_event'] = pd.to_datetime(backtest_df['ts_event'], unit='ns')
        
        # Format for the backtest engine
        backtest_df = backtest_df.rename(columns={
            'ts_event': 'Time',
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # Sort by time
        backtest_df = backtest_df.sort_values('Time')
        
        # Save to CSV
        backtest_df.to_csv(output_path, index=False)
        print(f"Saved {len(backtest_df)} records to {output_path}")
        
        return True
    except Exception as e:
        print(f"Error extracting symbol data: {e}")
        return False

def process_csv_files(csv_files):
    """
    Process CSV files and extract data for each symbol.
    """
    symbols_to_extract = ['MNQ', 'MES', 'MGC', 'M2K']

    for file in csv_files:
        print(f"\nProcessing file: {file}")
        df = analyze_csv_file(file)

        if df is None:
            print(f"Failed to analyze file: {file}")
            continue

        for symbol in symbols_to_extract:
            print(f"\nExtracting data for {symbol} from {file}...")
            output_path = os.path.join(output_dir, f"{symbol}_Databento_2025.csv")
            success = extract_symbol_data(df, symbol, output_path)

            if not success:
                print(f"Failed to extract data for {symbol} from {file}")

def main():
    """
    Main function.
    """
    print(f"Extracting and analyzing Databento ZIP file: {input_file}")

    # Check if file exists
    if not os.path.exists(input_file):
        print(f"File not found: {input_file}")
        return

    # Extract the ZIP file
    extracted_files = extract_zip(input_file, output_dir)
    if not extracted_files:
        print("Failed to extract ZIP file.")
        return

    # Find CSV files
    csv_files = [f for f in extracted_files if f.endswith('.csv')]
    if not csv_files:
        print("No CSV files found in the ZIP file.")
        return

    # Process CSV files
    process_csv_files(csv_files)

    print("\nExtraction complete!")

if __name__ == "__main__":
    main()
