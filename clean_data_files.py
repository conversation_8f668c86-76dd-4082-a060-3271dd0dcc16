#!/usr/bin/env python3
"""
Clean up data files to fix format issues.
"""

import os
import sys
import pandas as pd
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clean_csv_file(file_path):
    """
    Clean up a CSV file to fix format issues.
    
    Args:
        file_path (str): Path to CSV file
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Cleaning {file_path}...")
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        
        logger.info(f"Original columns: {df.columns.tolist()}")
        
        # Keep only the required columns
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        
        # Check if we have all required columns
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False
        
        # Select only the required columns
        df = df[required_columns]
        
        logger.info(f"Cleaned columns: {df.columns.tolist()}")
        
        # Save the cleaned file
        df.to_csv(file_path, index=False)
        
        logger.info(f"Saved cleaned file to {file_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error cleaning {file_path}: {str(e)}")
        return False

def main():
    """Main function."""
    # Configure paths
    input_dir = os.path.join('C:', 'backtest-bot', 'input')
    
    # Find CSV files
    csv_files = [f for f in os.listdir(input_dir) if f.endswith('_1m.csv')]
    
    if not csv_files:
        logger.error("No CSV files found")
        return
    
    logger.info(f"Found {len(csv_files)} CSV files")
    
    # Process each CSV file
    for csv_file in csv_files:
        file_path = os.path.join(input_dir, csv_file)
        success = clean_csv_file(file_path)
        
        if not success:
            logger.error(f"Failed to clean {csv_file}")
    
    logger.info("Data cleaning complete!")

if __name__ == "__main__":
    main()
