/**
 * generate-trade-data.js
 * Generate detailed trade data for performance analysis
 */

const fs = require('fs');
const path = require('path');
const { runBacktest } = require('./verify-backtest');
const { mnqConfig, mesConfig, mgcConfig } = require('../multi_symbol_config');
const exactPM = require('../exact_position_management');

// Function to generate detailed trade data for a symbol
async function generateTradeData(config) {
    console.log(`\n=== GENERATING TRADE DATA FOR ${config.symbol} ===\n`);
    
    // Run backtest with original implementation
    const result = await runBacktest(config);
    
    // Create a directory for the trade data if it doesn't exist
    const tradeDataDir = path.join(__dirname, 'trade_data');
    if (!fs.existsSync(tradeDataDir)) {
        fs.mkdirSync(tradeDataDir);
    }
    
    // Generate daily and weekly performance data
    const dailyPerformance = {};
    const weeklyPerformance = {};
    
    // Group trades by day and week
    const allTrades = result.trades;
    if (typeof allTrades === 'number') {
        console.log(`No detailed trade data available for ${config.symbol}`);
        return;
    }
    
    // Process each trade
    allTrades.forEach(trade => {
        // Parse dates
        const exitDate = new Date(trade.exitTimestamp);
        
        // Format date strings
        const dayKey = exitDate.toISOString().split('T')[0]; // YYYY-MM-DD
        const weekKey = getWeekNumber(exitDate);
        
        // Initialize if not exists
        if (!dailyPerformance[dayKey]) {
            dailyPerformance[dayKey] = {
                trades: 0,
                wins: 0,
                losses: 0,
                pnl: 0
            };
        }
        
        if (!weeklyPerformance[weekKey]) {
            weeklyPerformance[weekKey] = {
                trades: 0,
                wins: 0,
                losses: 0,
                pnl: 0
            };
        }
        
        // Update counts
        dailyPerformance[dayKey].trades++;
        weeklyPerformance[weekKey].trades++;
        
        // Update wins/losses and PnL
        if (trade.pnlNetTotal > 0) {
            dailyPerformance[dayKey].wins++;
            weeklyPerformance[weekKey].wins++;
        } else {
            dailyPerformance[dayKey].losses++;
            weeklyPerformance[weekKey].losses++;
        }
        
        dailyPerformance[dayKey].pnl += trade.pnlNetTotal;
        weeklyPerformance[weekKey].pnl += trade.pnlNetTotal;
    });
    
    // Convert to arrays for sorting
    const dailyArray = Object.keys(dailyPerformance).map(day => ({
        date: day,
        trades: dailyPerformance[day].trades,
        wins: dailyPerformance[day].wins,
        losses: dailyPerformance[day].losses,
        pnl: dailyPerformance[day].pnl,
        winRate: dailyPerformance[day].trades > 0 ? 
            (dailyPerformance[day].wins / dailyPerformance[day].trades * 100).toFixed(2) : '0.00'
    }));
    
    const weeklyArray = Object.keys(weeklyPerformance).map(week => ({
        week,
        trades: weeklyPerformance[week].trades,
        wins: weeklyPerformance[week].wins,
        losses: weeklyPerformance[week].losses,
        pnl: weeklyPerformance[week].pnl,
        winRate: weeklyPerformance[week].trades > 0 ? 
            (weeklyPerformance[week].wins / weeklyPerformance[week].trades * 100).toFixed(2) : '0.00'
    }));
    
    // Sort by date/week
    dailyArray.sort((a, b) => a.date.localeCompare(b.date));
    weeklyArray.sort((a, b) => a.week.localeCompare(b.week));
    
    // Calculate statistics
    const dailyPnLs = dailyArray.map(d => d.pnl);
    const weeklyPnLs = weeklyArray.map(w => w.pnl);
    
    const dailyStats = {
        mean: average(dailyPnLs),
        median: median(dailyPnLs),
        min: Math.min(...dailyPnLs),
        max: Math.max(...dailyPnLs),
        stdDev: standardDeviation(dailyPnLs)
    };
    
    const weeklyStats = {
        mean: average(weeklyPnLs),
        median: median(weeklyPnLs),
        min: Math.min(...weeklyPnLs),
        max: Math.max(...weeklyPnLs),
        stdDev: standardDeviation(weeklyPnLs)
    };
    
    // Count profitable days and weeks
    const profitableDays = dailyArray.filter(d => d.pnl > 0).length;
    const profitableWeeks = weeklyArray.filter(w => w.pnl > 0).length;
    
    const dailyWinRate = (profitableDays / dailyArray.length * 100).toFixed(2);
    const weeklyWinRate = (profitableWeeks / weeklyArray.length * 100).toFixed(2);
    
    // Save data to files
    fs.writeFileSync(
        path.join(tradeDataDir, `${config.symbol}_daily_performance.json`),
        JSON.stringify(dailyArray, null, 2)
    );
    
    fs.writeFileSync(
        path.join(tradeDataDir, `${config.symbol}_weekly_performance.json`),
        JSON.stringify(weeklyArray, null, 2)
    );
    
    fs.writeFileSync(
        path.join(tradeDataDir, `${config.symbol}_performance_summary.json`),
        JSON.stringify({
            symbol: config.symbol,
            daily: {
                totalDays: dailyArray.length,
                profitableDays,
                dailyWinRate,
                stats: dailyStats
            },
            weekly: {
                totalWeeks: weeklyArray.length,
                profitableWeeks,
                weeklyWinRate,
                stats: weeklyStats
            }
        }, null, 2)
    );
    
    // Display summary
    console.log(`\n=== DAILY PERFORMANCE SUMMARY FOR ${config.symbol} ===`);
    console.log(`Total Days: ${dailyArray.length}`);
    console.log(`Profitable Days: ${profitableDays} (${dailyWinRate}%)`);
    console.log(`Average Daily P&L: $${dailyStats.mean.toFixed(2)}`);
    console.log(`Median Daily P&L: $${dailyStats.median.toFixed(2)}`);
    console.log(`Best Day: $${dailyStats.max.toFixed(2)}`);
    console.log(`Worst Day: $${dailyStats.min.toFixed(2)}`);
    
    console.log(`\n=== WEEKLY PERFORMANCE SUMMARY FOR ${config.symbol} ===`);
    console.log(`Total Weeks: ${weeklyArray.length}`);
    console.log(`Profitable Weeks: ${profitableWeeks} (${weeklyWinRate}%)`);
    console.log(`Average Weekly P&L: $${weeklyStats.mean.toFixed(2)}`);
    console.log(`Median Weekly P&L: $${weeklyStats.median.toFixed(2)}`);
    console.log(`Best Week: $${weeklyStats.max.toFixed(2)}`);
    console.log(`Worst Week: $${weeklyStats.min.toFixed(2)}`);
    
    return {
        symbol: config.symbol,
        daily: {
            totalDays: dailyArray.length,
            profitableDays,
            dailyWinRate,
            stats: dailyStats
        },
        weekly: {
            totalWeeks: weeklyArray.length,
            profitableWeeks,
            weeklyWinRate,
            stats: weeklyStats
        }
    };
}

// Helper function to get week number (YYYY-WW format)
function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
    const week = Math.floor((d.getTime() - new Date(d.getFullYear(), 0, 4).getTime()) / 86400000 / 7) + 1;
    return `${d.getFullYear()}-W${week.toString().padStart(2, '0')}`;
}

// Helper functions for statistics
function average(arr) {
    return arr.reduce((a, b) => a + b, 0) / arr.length;
}

function median(arr) {
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
}

function standardDeviation(arr) {
    const mean = average(arr);
    const squaredDiffs = arr.map(val => Math.pow(val - mean, 2));
    const variance = average(squaredDiffs);
    return Math.sqrt(variance);
}

// Main function to generate trade data for all symbols
async function generateAllTradeData() {
    try {
        // Create trade_data directory if it doesn't exist
        const tradeDataDir = path.join(__dirname, 'trade_data');
        if (!fs.existsSync(tradeDataDir)) {
            fs.mkdirSync(tradeDataDir);
        }
        
        // Generate trade data for each symbol
        const mnqResult = await generateTradeData(mnqConfig);
        const mesResult = await generateTradeData(mesConfig);
        const mgcResult = await generateTradeData(mgcConfig);
        
        // Display combined summary
        console.log("\n=== COMBINED PERFORMANCE SUMMARY ===\n");
        console.log("Symbol | Daily Win % | Avg Daily P&L | Weekly Win % | Avg Weekly P&L");
        console.log("-------|-------------|---------------|--------------|---------------");
        
        [mnqResult, mesResult, mgcResult].forEach(result => {
            if (result) {
                console.log(`${result.symbol.padEnd(7)} | ${result.daily.dailyWinRate.padEnd(13)}% | $${result.daily.stats.mean.toFixed(2).padEnd(13)} | ${result.weekly.weeklyWinRate.padEnd(12)}% | $${result.weekly.stats.mean.toFixed(2)}`);
            }
        });
        
        // Save combined results
        fs.writeFileSync(
            path.join(tradeDataDir, 'combined_performance_summary.json'),
            JSON.stringify({
                mnq: mnqResult,
                mes: mesResult,
                mgc: mgcResult
            }, null, 2)
        );
        
        console.log(`\nTrade data saved to ${tradeDataDir}`);
    } catch (error) {
        console.error('Error generating trade data:', error);
    }
}

// Run the main function
generateAllTradeData();
