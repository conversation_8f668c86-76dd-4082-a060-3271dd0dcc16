/**
 * Market Data Service
 * Provides a unified interface for accessing market data from different sources
 */

const { spawn } = require('child_process');
const path = require('path');
const tradovateApi = require('./tradovate_api_optimized');
const localDataProvider = require('./local-data-provider');
const logger = require('./logger');

// Default configuration
const defaultConfig = {
  dataSource: 'local', // 'tradovate', 'databento', or 'local'
  useLocalForHistorical: true, // Use local data for historical data
  useLocalForBacktesting: true, // Use local data for backtesting
  useDabentoForHistorical: false,
  useDabentoForRealtime: false,
  useDabentoForBackup: true,
  symbols: ['MNQ', 'MES', 'MGC', 'M2K'],
  dabentoDataset: 'GLBX.MDP3',
  dabentoSchema: 'trades',  // Using trades schema for high-frequency trading strategy
  dabentoHistoricalSchema: 'ohlcv-1m',  // Use OHLCV for historical data
  dabentoSymbolType: 'native',  // Use native for specific contract months
  dabentoUseWebSocket: true,
  dabentoHeartbeatInterval: 30,
  localDataDir: "C:\\backtest-bot\\input",
  localDataPattern: '{symbol}_1m.csv',
  symbolMap: {
    'MNQ': 'MNQ',
    'MES': 'MES',
    'MGC': 'MGC',
    'M2K': 'MiniRussell2000'
  }
};

class MarketDataService {
  /**
   * Create a new Market Data Service
   * @param {Object} config - Configuration options
   */
  constructor(config = {}) {
    this.config = { ...defaultConfig, ...config };
    this.tradovateApi = tradovateApi;

    // Initialize chart data cache
    this.chartDataCache = {};

    // Symbol mapping between Tradovate and Databento
    this.symbolMap = {
      // Base symbols - continuous front month contracts (Node.js format)
      'MNQ': 'NQ.c.0',  // Micro E-mini NASDAQ-100 continuous front month
      'MES': 'ES.c.0',  // Micro E-mini S&P 500 continuous front month
      'MGC': 'GC.c.0',  // Micro Gold continuous front month
      'M2K': 'RTY.c.0',  // Micro E-mini Russell 2000 continuous front month

      // Contract month symbols (May 2025) - specific contract months
      'MNQM5': 'NQM5',  // Micro E-mini NASDAQ-100 May 2025
      'MESM5': 'ESM5',  // Micro E-mini S&P 500 May 2025
      'MGCM5': 'GCM5',  // Micro Gold May 2025
      'M2KM5': 'RTYM5'  // Micro E-mini Russell 2000 May 2025
    };

    // Log the symbol map for debugging
    logger.logSystem(`Symbol map initialized: ${JSON.stringify(this.symbolMap)}`, 'info');

    // Reverse mapping for looking up Tradovate symbols from Databento symbols
    this.reverseSymbolMap = {};
    for (const [tradovateSymbol, dabentoSymbol] of Object.entries(this.symbolMap)) {
      this.reverseSymbolMap[dabentoSymbol] = tradovateSymbol;
      // Also add mappings without the .FUT suffix
      const basePart = dabentoSymbol.split('.')[0];
      this.reverseSymbolMap[basePart] = tradovateSymbol;
    }

    // Initialize Databento bridge if needed
    if (this.config.dataSource === 'databento' ||
        this.config.useDabentoForHistorical ||
        this.config.useDabentoForRealtime ||
        this.config.useDabentoForBackup) {
      if (!process.env.DATABENTO_API_KEY) {
        logger.logSystem('DATABENTO_API_KEY environment variable not found, using default API key', 'warning');
      }

      this.databentoBridge = null;
      this.databentoBridgeReady = false;
      this.pendingRequests = new Map();
      this.requestIdCounter = 0;
      this.liveDataCallbacks = new Map();
    }

    this.callbacks = new Map();
    this.isInitialized = false;
  }

  /**
   * Generate a unique request ID
   * @returns {string} - Unique request ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${this.requestIdCounter++}`;
  }

  /**
   * Initialize the Databento bridge
   * @returns {Promise<void>}
   */
  initDatabentoBridge() {
    return new Promise((resolve, reject) => {
      if (this.databentoBridge && this.databentoBridgeReady) {
        resolve();
        return;
      }

      logger.logSystem('Initializing Databento bridge...', 'info');

      // Spawn the Python bridge process
      const pythonPath = 'python'; // Use 'python3' on Linux/Mac
      const scriptPath = path.join(__dirname, 'databento_bridge.py');

      this.databentoBridge = spawn(pythonPath, [scriptPath]);

      // Handle process exit
      this.databentoBridge.on('exit', (code, signal) => {
        logger.logSystem(`Databento bridge exited with code ${code} and signal ${signal}`, 'warning');
        this.databentoBridgeReady = false;
        this.databentoBridge = null;
      });

      // Handle process errors
      this.databentoBridge.on('error', (error) => {
        logger.logSystem(`Databento bridge error: ${error.message}`, 'error');
        reject(error);
      });

      // Handle stdout (responses from the bridge)
      this.databentoBridge.stdout.on('data', (data) => {
        const lines = data.toString().trim().split('\n');

        for (const line of lines) {
          try {
            const response = JSON.parse(line);

            // Handle ready message
            if (response.type === 'ready') {
              logger.logSystem('Databento bridge ready', 'info');
              this.databentoBridgeReady = true;

              // Set API key if available
              if (process.env.DATABENTO_API_KEY) {
                this.sendBridgeCommand('set_api_key', { api_key: process.env.DATABENTO_API_KEY });
              }

              resolve();
              continue;
            }

            // Handle request responses
            if (response.request_id && this.pendingRequests.has(response.request_id)) {
              const { resolve: resolveRequest, reject: rejectRequest } = this.pendingRequests.get(response.request_id);

              if (response.type === 'error') {
                rejectRequest(new Error(response.data.message));
              } else {
                resolveRequest(response.data);
              }

              this.pendingRequests.delete(response.request_id);
              continue;
            }

            // Handle live data
            if (response.type === 'live_data') {
              // Find the appropriate callback based on the symbol in the data
              const data = response.data;

              // Log the raw data for debugging
              logger.logSystem(`Received raw data from Databento: ${JSON.stringify(data)}`, 'info');

              let symbol = data.symbol || this.getSymbolFromInstrumentId(data.instrument_id);

              // Clean up symbol if needed (remove .FUT suffix)
              if (symbol && symbol.includes('.')) {
                const basePart = symbol.split('.')[0];
                // Check if we have a reverse mapping for this symbol
                for (const [key, value] of Object.entries(this.symbolMap)) {
                  if (value === symbol) {
                    symbol = key;
                    break;
                  } else if (value.startsWith(basePart)) {
                    symbol = key;
                    break;
                  }
                }
              }

              // Log the data for debugging
              logger.logSystem(`Received live data for symbol: ${symbol}, type: ${data.record_type}, instrument_id: ${data.instrument_id}`, 'info');

              if (symbol && this.liveDataCallbacks.has(symbol)) {
                const callback = this.liveDataCallbacks.get(symbol);
                const transformedData = this.transformDabentoLiveData(data, symbol);

                // Log the transformed data
                if (transformedData) {
                  logger.logSystem(`Transformed data: ${JSON.stringify(transformedData)}`, 'info');
                  callback(transformedData);
                }
              } else if (symbol) {
                // Try to find a callback for a similar symbol
                for (const [callbackSymbol, callback] of this.liveDataCallbacks.entries()) {
                  if (symbol.includes(callbackSymbol) || callbackSymbol.includes(symbol)) {
                    const transformedData = this.transformDabentoLiveData(data, callbackSymbol);
                    if (transformedData) {
                      logger.logSystem(`Transformed data: ${JSON.stringify(transformedData)}`, 'info');
                      callback(transformedData);
                      logger.logSystem(`Mapped ${symbol} to ${callbackSymbol} for callback`, 'info');
                    }
                    break;
                  }
                }
              }

              continue;
            }

            // Handle market data (for candle building)
            if (response.type === 'market_data') {
              // Find the appropriate callback based on the symbol in the data
              const data = response.data;

              // Log the raw market data for debugging
              logger.logSystem(`Received market data from Databento: ${JSON.stringify(data)}`, 'info');

              let symbol = data.symbol || this.getSymbolFromInstrumentId(data.instrument_id);

              // Clean up symbol if needed (remove .FUT suffix)
              if (symbol && symbol.includes('.')) {
                const basePart = symbol.split('.')[0];
                // Check if we have a reverse mapping for this symbol
                for (const [key, value] of Object.entries(this.symbolMap)) {
                  if (value === symbol) {
                    symbol = key;
                    break;
                  } else if (value.startsWith(basePart)) {
                    symbol = key;
                    break;
                  }
                }
              }

              // Log the market data for debugging
              logger.logSystem(`Processing market data for symbol: ${symbol}, price: ${data.price}, size: ${data.size}`, 'info');

              // Debug: Log available callbacks
              logger.logSystem(`Available callbacks: ${Array.from(this.liveDataCallbacks.keys()).join(', ')}`, 'info');

              if (symbol && this.liveDataCallbacks.has(symbol)) {
                const callback = this.liveDataCallbacks.get(symbol);

                // Call the callback directly with the market data
                // This will trigger the bot's handleMarketData function
                callback(data);
              } else if (symbol) {
                // Try to find a callback for a similar symbol
                let callbackFound = false;

                // First try exact base symbol mapping (e.g., NQM5 -> MNQ)
                let baseSymbol = null;
                if (symbol === 'NQM5') {
                  baseSymbol = 'MNQ';
                } else if (symbol === 'ESM5') {
                  baseSymbol = 'MES';
                } else if (symbol === 'GCM5') {
                  baseSymbol = 'MGC';
                } else if (symbol === 'RTYM5') {
                  baseSymbol = 'M2K';
                } else if (symbol.length > 3) {
                  // For other contract symbols, try extracting the base
                  baseSymbol = symbol.substring(0, 3);
                }

                if (baseSymbol && this.liveDataCallbacks.has(baseSymbol)) {
                  const callback = this.liveDataCallbacks.get(baseSymbol);
                  callback(data);
                  logger.logSystem(`Mapped ${symbol} to ${baseSymbol} for market data callback`, 'info');
                  callbackFound = true;
                }

                // If not found, try the original fuzzy matching
                if (!callbackFound) {
                  for (const [callbackSymbol, callback] of this.liveDataCallbacks.entries()) {
                    if (symbol.includes(callbackSymbol) || callbackSymbol.includes(symbol)) {
                      callback(data);
                      logger.logSystem(`Mapped ${symbol} to ${callbackSymbol} for market data callback`, 'info');
                      break;
                    }
                  }
                }
              }

              continue;
            }

            // Handle errors
            if (response.type === 'error') {
              logger.logSystem(`Databento bridge error: ${response.data.message}`, 'error');
              continue;
            }

            logger.logSystem(`Unhandled response from Databento bridge: ${line}`, 'warning');
          } catch (error) {
            logger.logSystem(`Error parsing response from Databento bridge: ${error.message}`, 'error');
          }
        }
      });

      // Handle stderr (logs from the bridge)
      this.databentoBridge.stderr.on('data', (data) => {
        logger.logSystem(`Databento bridge stderr: ${data.toString().trim()}`, 'debug');
      });

      // Set a timeout for initialization (increased to 60 seconds)
      setTimeout(() => {
        if (!this.databentoBridgeReady) {
          reject(new Error('Databento bridge initialization timed out after 60 seconds'));
        }
      }, 60000); // 60 seconds timeout
    });
  }

  /**
   * Send a command to the Databento bridge
   * @param {string} type - Command type
   * @param {Object} params - Command parameters
   * @returns {Promise<any>} - Command response
   */
  sendBridgeCommand(type, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.databentoBridge || !this.databentoBridgeReady) {
        reject(new Error('Databento bridge not ready'));
        return;
      }

      const requestId = this.generateRequestId();

      // Store the promise callbacks
      this.pendingRequests.set(requestId, { resolve, reject });

      // Send the command
      const command = {
        type,
        params,
        request_id: requestId
      };

      this.databentoBridge.stdin.write(JSON.stringify(command) + '\n');
    });
  }

  /**
   * Get symbol from instrument ID
   * @param {number} instrumentId - Instrument ID
   * @returns {string|null} - Symbol
   */
  getSymbolFromInstrumentId(instrumentId) {
    // Maintain a mapping of instrument IDs to symbols
    // This is populated based on SymbolMappingMsg messages from Databento
    if (!this.instrumentIdToSymbolMap) {
      this.instrumentIdToSymbolMap = {
        // Known instrument IDs for May 2025 futures contracts
        // These are based on the data we've received
        42005804: 'NQ',  // MNQ futures (Micro E-mini NASDAQ-100)
        4916: 'ES',      // MES futures (Micro E-mini S&P 500)
        287: 'GC',       // MGC futures (Micro Gold)
        6188: 'RTY',     // M2K futures (Micro E-mini Russell 2000)

        // Add more mappings based on the Databento logs
        158704: 'NQ',    // NQZ5 - NASDAQ-100 Dec 2025
        14160: 'ES',     // ESU5 - S&P 500 Sep 2025
        39231: 'GC',     // GCZ5 - Gold Dec 2025
        42009162: 'RTY'  // RTYM5 - Russell 2000 May 2025
      };

      // Log the instrument ID map for debugging
      logger.logSystem(`Instrument ID map initialized: ${JSON.stringify(this.instrumentIdToSymbolMap)}`, 'info');
    }

    // Hard-coded mappings for known instrument IDs
    // Return the base symbol for continuous contracts
    if (instrumentId === 42005804 || instrumentId === 158704) {
      return 'MNQ';  // Micro E-mini NASDAQ-100 continuous
    } else if (instrumentId === 4916 || instrumentId === 14160) {
      return 'MES';  // Micro E-mini S&P 500 continuous
    } else if (instrumentId === 287 || instrumentId === 39231) {
      return 'MGC';  // Micro Gold continuous
    } else if (instrumentId === 6188 || instrumentId === 42009162) {
      return 'M2K';  // Micro E-mini Russell 2000 continuous
    }

    // Log unknown instrument IDs for debugging
    logger.logSystem(`Looking up symbol for instrument ID: ${instrumentId}`, 'info');

    // Try to get the symbol from our mapping
    const symbol = this.instrumentIdToSymbolMap[instrumentId];
    if (symbol) {
      // Check if we have a reverse mapping for this symbol
      if (this.reverseSymbolMap[symbol]) {
        return this.reverseSymbolMap[symbol];
      }
      // If not, check if we have a reverse mapping for symbol.FUT
      if (this.reverseSymbolMap[`${symbol}.FUT`]) {
        return this.reverseSymbolMap[`${symbol}.FUT`];
      }

      // Try to match with our known contract month symbols
      for (const [tradovateSymbol, dabentoSymbol] of Object.entries(this.symbolMap)) {
        const baseSymbol = dabentoSymbol.split('.')[0]; // Extract ES from ES.FUT
        if (baseSymbol === symbol) {
          // Check if this is a contract month symbol (e.g., MESM5)
          if (tradovateSymbol.length > 3) {
            return tradovateSymbol;
          }
        }
      }
    }

    // If we still don't have a match, try to infer from the instrument ID pattern
    // Databento often uses specific ranges for different products
    if (instrumentId >= 42000000 && instrumentId < 43000000) {
      // This is likely a CME Globex product
      // Try to match with our known symbols
      for (const [tradovateSymbol, dabentoSymbol] of Object.entries(this.symbolMap)) {
        if (tradovateSymbol.startsWith('MNQ')) {
          return tradovateSymbol;
        }
      }
    } else if (instrumentId >= 4000 && instrumentId < 5000) {
      // This might be ES/MES
      for (const [tradovateSymbol, dabentoSymbol] of Object.entries(this.symbolMap)) {
        if (tradovateSymbol.startsWith('MES')) {
          return tradovateSymbol;
        }
      }
    } else if (instrumentId >= 200 && instrumentId < 300) {
      // This might be GC/MGC
      for (const [tradovateSymbol, dabentoSymbol] of Object.entries(this.symbolMap)) {
        if (tradovateSymbol.startsWith('MGC')) {
          return tradovateSymbol;
        }
      }
    } else if (instrumentId >= 6000 && instrumentId < 7000) {
      // This might be RTY/M2K
      for (const [tradovateSymbol, dabentoSymbol] of Object.entries(this.symbolMap)) {
        if (tradovateSymbol.startsWith('M2K')) {
          return tradovateSymbol;
        }
      }
    }

    // Log unknown instrument IDs for future reference
    logger.logSystem(`Unknown instrument ID: ${instrumentId}`, 'debug');
    return null;
  }

  /**
   * Initialize the market data service
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize primary data source
      if (this.config.dataSource === 'tradovate') {
        logger.logSystem('Initializing Tradovate API...', 'info');
        // The tradovate_api_optimized.js doesn't have an initialize method
        // It's initialized elsewhere in the application
      } else if (this.config.dataSource === 'databento') {
        logger.logSystem('Initializing Databento bridge...', 'info');
        await this.initDatabentoBridge();
      }

      // Initialize backup data source if needed
      if (this.config.dataSource === 'tradovate' &&
          this.config.useDabentoForBackup) {
        logger.logSystem('Initializing Databento bridge as backup...', 'info');
        await this.initDatabentoBridge();
        logger.logSystem('Databento bridge available as backup', 'info');
      }

      this.isInitialized = true;
      logger.logSystem('Market Data Service initialized', 'info');
    } catch (error) {
      logger.logSystem(`Error initializing Market Data Service: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Map a symbol to the format required by the current data source
   * @param {string} symbol - Symbol to map
   * @param {string} source - Source to map to ('tradovate' or 'databento')
   * @returns {string} - Mapped symbol
   */
  mapSymbol(symbol, source) {
    if (source === 'databento' && this.symbolMap[symbol]) {
      return this.symbolMap[symbol];
    }
    return symbol;
  }

  /**
   * Transform Databento data to match Tradovate format
   * @param {Array} data - Databento data
   * @param {string} symbol - Original symbol
   * @returns {Array} - Transformed data
   */
  transformDabentoData(data, symbol) {
    // If data is empty, return empty array
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }

    return data.map(item => {
      // Parse timestamp from ISO string
      const timestamp = new Date(item.ts_event);

      // For OHLCV data
      if ('open' in item && 'high' in item && 'low' in item && 'close' in item) {
        return {
          symbol,
          timestamp,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume || 0
        };
      }

      // For trade data
      if ('price' in item && 'size' in item) {
        return {
          symbol,
          timestamp,
          price: item.price,
          size: item.size,
          side: item.side || 'unknown'
        };
      }

      // Default case
      return {
        symbol,
        timestamp,
        ...item
      };
    });
  }

  /**
   * Transform Databento live data to match Tradovate format
   * @param {Object} data - Databento data
   * @param {string} symbol - Original symbol
   * @returns {Object} - Transformed data
   */
  transformDabentoLiveData(data, symbol) {
    try {
      // Parse timestamp from ISO string if it exists
      const timestamp = data.ts_event ? new Date(data.ts_event) : new Date();

      // Check record type
      const recordType = data.record_type || '';

      // For symbol mapping data
      if (recordType === 'SymbolMappingMsg') {
        // Store the mapping for future reference
        if (!this.instrumentIdToSymbolMap) {
          this.instrumentIdToSymbolMap = {
            // Known instrument IDs for May 2025 futures contracts
            // These are based on the data we've received
            42005804: 'NQ',  // MNQ futures (Micro E-mini NASDAQ-100)
            4916: 'ES',      // MES futures (Micro E-mini S&P 500)
            287: 'GC',       // MGC futures (Micro Gold)
            6188: 'RTY'      // M2K futures (Micro E-mini Russell 2000)
          };
        }

        if (data.instrument_id) {
          // Store the instrument ID mapping
          // We'll resolve the symbol later when we receive trade data
          // For known instrument IDs, use our predefined mapping
          if (data.instrument_id === 42005804) {
            this.instrumentIdToSymbolMap[data.instrument_id] = 'NQ';
            logger.logSystem(`Added symbol mapping: NQ -> ${data.instrument_id}`, 'debug');
          } else if (data.instrument_id === 4916) {
            this.instrumentIdToSymbolMap[data.instrument_id] = 'ES';
            logger.logSystem(`Added symbol mapping: ES -> ${data.instrument_id}`, 'debug');
          } else if (data.instrument_id === 287) {
            this.instrumentIdToSymbolMap[data.instrument_id] = 'GC';
            logger.logSystem(`Added symbol mapping: GC -> ${data.instrument_id}`, 'debug');
          } else if (data.instrument_id === 6188) {
            this.instrumentIdToSymbolMap[data.instrument_id] = 'RTY';
            logger.logSystem(`Added symbol mapping: RTY -> ${data.instrument_id}`, 'debug');
          } else {
            this.instrumentIdToSymbolMap[data.instrument_id] = data.symbol || 'unknown';
            logger.logSystem(`Added symbol mapping: ${data.symbol || 'unknown'} -> ${data.instrument_id}`, 'debug');
          }
        }

        // We don't need to process this as market data
        return null;
      }

      // For OHLCV data (OhlcvMsg)
      if (recordType === 'OhlcvMsg' || ('open' in data && 'high' in data && 'low' in data && 'close' in data)) {
        // Make sure we're using the correct Tradovate symbol
        const tradovateSymbol = this.getTradovateSymbol(symbol, data);

        return {
          symbol: tradovateSymbol || symbol,
          timestamp,
          open: parseFloat(data.open) || 0,
          high: parseFloat(data.high) || 0,
          low: parseFloat(data.low) || 0,
          close: parseFloat(data.close) || 0,
          volume: parseFloat(data.volume) || 0
        };
      }

      // For MBO data (MBOMsg) - Market By Order
      if (recordType === 'MBOMsg' || ('order_id' in data && 'price' in data && 'size' in data)) {
        logger.logSystem(`Processing MBO data: ${JSON.stringify(data)}`, 'info');

        // Initialize trade aggregation cache if it doesn't exist
        if (!this.tradeAggregationCache) {
          this.tradeAggregationCache = {};
        }

        // Initialize chart data cache if it doesn't exist
        if (!this.chartDataCache) {
          this.chartDataCache = {};
        }

        // Get the correct Tradovate symbol
        let tradovateSymbol = this.getTradovateSymbol(symbol, data);

        // If we couldn't determine the symbol from the provided data, try to get it from the instrument ID
        if (!tradovateSymbol && data.instrument_id) {
          // Try to find the symbol in our known mappings
          for (const [key, value] of Object.entries(this.symbolMap)) {
            // Check if this is one of our target instruments
            const baseSymbol = value.split('.')[0]; // Extract ES from ES.FUT
            const dabentoSymbol = this.instrumentIdToSymbolMap[data.instrument_id];

            if (dabentoSymbol && dabentoSymbol.includes(baseSymbol)) {
              tradovateSymbol = key;
              logger.logSystem(`Resolved symbol for instrument_id ${data.instrument_id}: ${dabentoSymbol} -> ${tradovateSymbol}`, 'debug');
              break;
            }
          }
        }

        // If we still couldn't determine the symbol, skip this order
        if (!tradovateSymbol) {
          logger.logSystem(`Could not determine symbol for order with instrument_id ${data.instrument_id}`, 'debug');
          return null;
        }

        // Parse price and size
        const price = parseFloat(data.price);
        const size = parseFloat(data.size) || 0;
        const side = data.side || 'unknown';
        const action = data.action || 'unknown';

        // Log the price for debugging
        logger.logSystem(`Parsed MBO price for ${tradovateSymbol}: ${price}, side: ${side}, action: ${action}`, 'info');

        // Only process 'Add' actions as trades for now
        if (action !== 'A') {
          logger.logSystem(`Skipping MBO action ${action} for ${tradovateSymbol}`, 'debug');
          return null;
        }

        // Get the current minute for aggregation
        const currentMinute = Math.floor(timestamp.getTime() / 60000) * 60000;
        const cacheKey = `${tradovateSymbol}-${currentMinute}`;

        // Check if we already have a candle for this minute
        if (!this.tradeAggregationCache[cacheKey]) {
          // Create a new candle
          this.tradeAggregationCache[cacheKey] = {
            symbol: tradovateSymbol,
            timestamp: new Date(currentMinute),
            open: price,
            high: price,
            low: price,
            close: price,
            volume: size,
            trades: 1,
            lastUpdate: timestamp.getTime()
          };
        } else {
          // Update existing candle
          const candle = this.tradeAggregationCache[cacheKey];
          candle.high = Math.max(candle.high, price);
          candle.low = Math.min(candle.low, price);
          candle.close = price;
          candle.volume += size;
          candle.trades += 1;
          candle.lastUpdate = timestamp.getTime();
        }

        // Get the updated candle
        const candle = this.tradeAggregationCache[cacheKey];

        // Store the candle in the chart data cache
        if (!this.chartDataCache[tradovateSymbol]) {
          this.chartDataCache[tradovateSymbol] = {};
        }
        this.chartDataCache[tradovateSymbol][currentMinute] = { ...candle };

        // Clean up old candles (older than 10 minutes)
        const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
        for (const key in this.tradeAggregationCache) {
          if (this.tradeAggregationCache[key].lastUpdate < tenMinutesAgo) {
            delete this.tradeAggregationCache[key];
          }
        }

        // Return the current candle
        return {
          symbol: tradovateSymbol,
          timestamp: candle.timestamp,
          open: candle.open,
          high: candle.high,
          low: candle.low,
          close: candle.close,
          volume: candle.volume,
          trades: candle.trades,
          // Keep original order data for reference
          order: {
            order_id: data.order_id,
            price: price,
            size: size,
            side: side,
            action: action
          }
        };
      }

      // For trade data (TradeMsg) - This is our primary data source
      if (recordType === 'TradeMsg' || ('price' in data && 'size' in data && !('order_id' in data))) {
        // Check if this is simulated data
        const isSimulated = data.simulated === true;

        // Log the trade data
        if (isSimulated) {
          logger.logSystem(`Processing SIMULATED trade: ${symbol} price=${data.price} size=${data.size}`, 'info');
        } else {
          logger.logSystem(`Processing REAL trade: ${symbol} price=${data.price} size=${data.size}`, 'info');
        }

        // Initialize trade aggregation cache if it doesn't exist
        if (!this.tradeAggregationCache) {
          this.tradeAggregationCache = {};
        }

        // Initialize chart data cache if it doesn't exist
        if (!this.chartDataCache) {
          this.chartDataCache = {};
        }

        // Get the correct Tradovate symbol
        let tradovateSymbol = this.getTradovateSymbol(symbol, data);

        // If we couldn't determine the symbol from the provided data, try to get it from the instrument ID
        if (!tradovateSymbol && data.instrument_id) {
          // Try to find the symbol in our known mappings
          for (const [key, value] of Object.entries(this.symbolMap)) {
            // Check if this is one of our target instruments
            const baseSymbol = value.split('.')[0]; // Extract ES from ES.FUT
            const dabentoSymbol = this.instrumentIdToSymbolMap[data.instrument_id];

            if (dabentoSymbol && dabentoSymbol.includes(baseSymbol)) {
              tradovateSymbol = key;
              logger.logSystem(`Resolved symbol for instrument_id ${data.instrument_id}: ${dabentoSymbol} -> ${tradovateSymbol}`, 'debug');
              break;
            }
          }

          // For known instrument IDs, use our predefined mapping
          if (!tradovateSymbol) {
            if (data.instrument_id === 42005804) {
              tradovateSymbol = 'MNQM5';
              logger.logSystem(`Using predefined mapping for instrument_id ${data.instrument_id}: MNQM5`, 'info');
            } else if (data.instrument_id === 4916) {
              tradovateSymbol = 'MESM5';
              logger.logSystem(`Using predefined mapping for instrument_id ${data.instrument_id}: MESM5`, 'info');
            } else if (data.instrument_id === 287) {
              tradovateSymbol = 'MGCM5';
              logger.logSystem(`Using predefined mapping for instrument_id ${data.instrument_id}: MGCM5`, 'info');
            } else if (data.instrument_id === 6188) {
              tradovateSymbol = 'M2KM5';
              logger.logSystem(`Using predefined mapping for instrument_id ${data.instrument_id}: M2KM5`, 'info');
            }
          }
        }

        // If we still couldn't determine the symbol, skip this trade
        if (!tradovateSymbol) {
          logger.logSystem(`Could not determine symbol for trade with instrument_id ${data.instrument_id}`, 'debug');
          return null;
        }

        // Parse price and size
        const price = parseFloat(data.price);
        const size = parseFloat(data.size) || 0;
        const side = data.side || 'unknown';

        // Log the price for debugging
        logger.logSystem(`Parsed trade price for ${tradovateSymbol}: ${price}, side: ${side}, simulated: ${isSimulated}`, 'info');

        // Get the current minute for aggregation
        const currentMinute = Math.floor(timestamp.getTime() / 60000) * 60000;
        const cacheKey = `${tradovateSymbol}-${currentMinute}`;

        // Check if we already have a candle for this minute
        if (!this.tradeAggregationCache[cacheKey]) {
          // Create a new candle
          this.tradeAggregationCache[cacheKey] = {
            symbol: tradovateSymbol,
            timestamp: new Date(currentMinute),
            open: price,
            high: price,
            low: price,
            close: price,
            volume: size,
            trades: 1,
            lastUpdate: timestamp.getTime()
          };

          logger.logSystem(`Created new 1-minute candle for ${tradovateSymbol}: O=${price} H=${price} L=${price} C=${price} V=${size}`, 'info');
        } else {
          // Update existing candle
          const candle = this.tradeAggregationCache[cacheKey];
          candle.high = Math.max(candle.high, price);
          candle.low = Math.min(candle.low, price);
          candle.close = price;
          candle.volume += size;
          candle.trades += 1;
          candle.lastUpdate = timestamp.getTime();

          logger.logSystem(`Updated 1-minute candle for ${tradovateSymbol}: O=${candle.open} H=${candle.high} L=${candle.low} C=${candle.close} V=${candle.volume}`, 'info');
        }

        // Get the updated candle
        const candle = this.tradeAggregationCache[cacheKey];

        // Store the candle in the chart data cache
        if (!this.chartDataCache[tradovateSymbol]) {
          this.chartDataCache[tradovateSymbol] = {};
        }
        this.chartDataCache[tradovateSymbol][currentMinute] = { ...candle };

        // Clean up old candles (older than 10 minutes)
        const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
        for (const key in this.tradeAggregationCache) {
          if (this.tradeAggregationCache[key].lastUpdate < tenMinutesAgo) {
            delete this.tradeAggregationCache[key];
          }
        }

        // Clean up old chart data (older than 60 minutes)
        const sixtyMinutesAgo = Date.now() - 60 * 60 * 1000;
        for (const sym in this.chartDataCache) {
          for (const time in this.chartDataCache[sym]) {
            if (parseInt(time) < sixtyMinutesAgo) {
              delete this.chartDataCache[sym][time];
            }
          }
        }

        // Return the current candle
        return {
          symbol: tradovateSymbol,
          timestamp: candle.timestamp,
          open: candle.open,
          high: candle.high,
          low: candle.low,
          close: candle.close,
          volume: candle.volume,
          trades: candle.trades,
          // Include the original trade data for reference
          trade: {
            price: price,
            size: size,
            side: side,
            timestamp: timestamp,
            simulated: isSimulated
          }
        };
      }

      // Default case - try to extract useful information
      logger.logSystem(`Received unknown data type: ${recordType}`, 'debug');
      return null;
    } catch (error) {
      logger.logSystem(`Error transforming Databento data: ${error.message}`, 'error');
      return null;
    }
  }

  /**
   * Get the Tradovate symbol from Databento data
   * @param {string} symbol - Original symbol
   * @param {Object} data - Databento data
   * @returns {string} - Tradovate symbol
   */
  getTradovateSymbol(symbol, data) {
    // First check if we already have the correct Tradovate symbol
    if (Object.keys(this.symbolMap).includes(symbol)) {
      return symbol;
    }

    // Try to get the symbol from the instrument ID
    if (data.instrument_id) {
      const symbolFromId = this.getSymbolFromInstrumentId(data.instrument_id);
      if (symbolFromId) {
        return symbolFromId;
      }
    }

    // Try to get the symbol from the data.symbol field
    if (data.symbol) {
      // Check if we have a direct mapping
      if (this.reverseSymbolMap[data.symbol]) {
        return this.reverseSymbolMap[data.symbol];
      }

      // Check if we have a mapping for the base symbol
      if (data.symbol.includes('.')) {
        const baseSymbol = data.symbol.split('.')[0];
        if (this.reverseSymbolMap[baseSymbol]) {
          return this.reverseSymbolMap[baseSymbol];
        }
      }
    }

    // If all else fails, return the original symbol
    return symbol;
  }

  /**
   * Get historical data for a symbol
   * @param {string} symbol - Symbol to get data for
   * @param {string|Date} startDate - Start date
   * @param {string|Date} endDate - End date
   * @param {string} interval - Bar interval (e.g., '1m', '1h', '1d')
   * @returns {Promise<Array>} - Historical data
   */
  async getHistoricalData(symbol, startDate, endDate, interval = '1m') {
    try {
      // Determine which source to use
      const useLocal = this.config.dataSource === 'local' ||
                      this.config.useLocalForHistorical;

      const useDatabento = this.config.dataSource === 'databento' ||
                          this.config.useDabentoForHistorical;

      // First try local data if configured
      if (useLocal) {
        logger.logSystem(`Getting historical data for ${symbol} from local data`, 'info');

        // Configure local data provider
        localDataProvider.config = {
          dataDir: this.config.localDataDir,
          filePattern: this.config.localDataPattern
        };

        // Get data from local provider
        return await localDataProvider.getHistoricalData(symbol, startDate, endDate, interval);
      }
      // Then try Databento if configured
      else if (useDatabento) {
        logger.logSystem(`Getting historical data for ${symbol} from Databento`, 'info');

        // Initialize Databento bridge if needed
        if (!this.databentoBridgeReady) {
          await this.initDatabentoBridge();
        }

        const mappedSymbol = this.mapSymbol(symbol, 'databento');

        // Format dates as ISO strings
        const startDateStr = startDate instanceof Date ? startDate.toISOString() : new Date(startDate).toISOString();
        const endDateStr = endDate instanceof Date ? endDate.toISOString() : new Date(endDate).toISOString();

        // Send command to Python bridge
        const response = await this.sendBridgeCommand('get_historical', {
          dataset: this.config.dabentoDataset,
          symbols: mappedSymbol,
          start: startDateStr,
          end: endDateStr,
          schema: 'ohlcv-1m', // Use OHLCV schema for bars
          stype_in: this.config.dabentoSymbolType
        });

        // Transform the data
        return this.transformDabentoData(response, symbol);
      }
      // Finally, try Tradovate
      else {
        logger.logSystem(`Getting historical data for ${symbol} from Tradovate`, 'info');
        return await this.tradovateApi.getHistoricalData(symbol, startDate, endDate, interval);
      }
    } catch (error) {
      logger.logSystem(`Error getting historical data for ${symbol}: ${error.message}`, 'error');

      // Try fallback options

      // First try local data as fallback if not already tried
      if (this.config.dataSource !== 'local' && !this.config.useLocalForHistorical) {
        try {
          logger.logSystem(`Falling back to local data for ${symbol}`, 'info');

          // Configure local data provider
          localDataProvider.config = {
            dataDir: this.config.localDataDir,
            filePattern: this.config.localDataPattern
          };

          // Get data from local provider
          return await localDataProvider.getHistoricalData(symbol, startDate, endDate, interval);
        } catch (localError) {
          logger.logSystem(`Error getting local data for ${symbol}: ${localError.message}`, 'error');
        }
      }

      // Then try Databento as fallback if not already tried
      if (this.config.dataSource !== 'databento' &&
          !this.config.useDabentoForHistorical &&
          this.config.useDabentoForBackup) {
        try {
          logger.logSystem(`Falling back to Databento for ${symbol}`, 'info');

          // Initialize Databento bridge if needed
          if (!this.databentoBridgeReady) {
            await this.initDatabentoBridge();
          }

          const mappedSymbol = this.mapSymbol(symbol, 'databento');

          // Format dates as ISO strings
          const startDateStr = startDate instanceof Date ? startDate.toISOString() : new Date(startDate).toISOString();
          const endDateStr = endDate instanceof Date ? endDate.toISOString() : new Date(endDate).toISOString();

          // Send command to Python bridge
          const response = await this.sendBridgeCommand('get_historical', {
            dataset: this.config.dabentoDataset,
            symbols: mappedSymbol,
            start: startDateStr,
            end: endDateStr,
            schema: 'ohlcv-1m', // Use OHLCV schema for bars
            stype_in: this.config.dabentoSymbolType
          });

          // Transform the data
          return this.transformDabentoData(response, symbol);
        } catch (dabentoError) {
          logger.logSystem(`Error getting Databento data for ${symbol}: ${dabentoError.message}`, 'error');
        }
      }

      // If all fallbacks fail, throw the original error
      throw error;
    }
  }

  /**
   * Subscribe to real-time market data for a symbol
   * @param {string} symbol - Symbol to subscribe to
   * @param {Function} callback - Callback function for data updates
   * @returns {Promise<void>}
   */
  async subscribeToMarketData(symbol, callback) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    this.callbacks.set(symbol, callback);

    try {
      // Determine which source to use
      const useLocal = this.config.dataSource === 'local';
      const useDatabento = this.config.dataSource === 'databento' ||
                          this.config.useDabentoForRealtime;

      // Use local data if configured (for backtesting)
      if (useLocal) {
        logger.logSystem(`Subscribing to ${symbol} from local data`, 'info');

        // Configure local data provider
        localDataProvider.config = {
          dataDir: this.config.localDataDir,
          filePattern: this.config.localDataPattern
        };

        // Subscribe to the symbol
        localDataProvider.subscribeToMarketData(symbol, callback);

        logger.logSystem(`Subscribed to ${symbol} from local data`, 'info');
      }
      // Use Databento if configured
      else if (useDatabento) {
        logger.logSystem(`Subscribing to ${symbol} from Databento`, 'info');

        // Initialize Databento bridge if needed
        if (!this.databentoBridgeReady) {
          await this.initDatabentoBridge();
        }

        const mappedSymbol = this.mapSymbol(symbol, 'databento');

        // Store the callback for both the original and mapped symbols
        this.liveDataCallbacks.set(symbol, callback);

        // Log the symbol mapping for debugging
        logger.logSystem(`Subscribing to ${symbol} from Databento using mapped symbol ${mappedSymbol}`, 'info');

        // Subscribe to the symbol
        await this.sendBridgeCommand('subscribe', {
          dataset: this.config.dabentoDataset,
          symbols: mappedSymbol,
          schema: this.config.dabentoSchema || 'mbo', // Use MBO schema as suggested by Databento
          stype_in: this.config.dabentoSymbolType
        });

        logger.logSystem(`Subscribed to ${mappedSymbol} from Databento`, 'info');
      }
      // Use Tradovate as default
      else {
        logger.logSystem(`Subscribing to ${symbol} from Tradovate`, 'info');
        await this.tradovateApi.subscribeToMarketData(symbol, callback);
      }
    } catch (error) {
      logger.logSystem(`Error subscribing to ${symbol}: ${error.message}`, 'error');

      // Try fallback options

      // First try local data as fallback if not already tried
      if (this.config.dataSource !== 'local') {
        try {
          logger.logSystem(`Falling back to local data for ${symbol}`, 'info');

          // Configure local data provider
          localDataProvider.config = {
            dataDir: this.config.localDataDir,
            filePattern: this.config.localDataPattern
          };

          // Subscribe to the symbol
          localDataProvider.subscribeToMarketData(symbol, callback);

          logger.logSystem(`Subscribed to ${symbol} from local data (backup)`, 'info');
          return;
        } catch (localError) {
          logger.logSystem(`Error subscribing to local data for ${symbol}: ${localError.message}`, 'error');
        }
      }

      // Then try Databento as fallback if not already tried
      if (this.config.dataSource !== 'databento' &&
          !this.config.useDabentoForRealtime &&
          this.config.useDabentoForBackup) {
        try {
          logger.logSystem(`Falling back to Databento for ${symbol}`, 'info');

          // Initialize Databento bridge if needed
          if (!this.databentoBridgeReady) {
            await this.initDatabentoBridge();
          }

          const mappedSymbol = this.mapSymbol(symbol, 'databento');

          // Store the callback for both the original and mapped symbols
          this.liveDataCallbacks.set(symbol, callback);

          // Log the symbol mapping for debugging
          logger.logSystem(`Subscribing to ${symbol} from Databento (backup) using mapped symbol ${mappedSymbol}`, 'info');

          // Subscribe to the symbol
          await this.sendBridgeCommand('subscribe', {
            dataset: this.config.dabentoDataset,
            symbols: mappedSymbol,
            schema: this.config.dabentoSchema || 'mbo', // Use MBO schema as suggested by Databento
            stype_in: this.config.dabentoSymbolType
          });

          logger.logSystem(`Subscribed to ${mappedSymbol} from Databento (backup)`, 'info');
          return;
        } catch (dabentoError) {
          logger.logSystem(`Error subscribing to Databento for ${symbol}: ${dabentoError.message}`, 'error');
        }
      }

      // If all fallbacks fail, throw the original error
      throw error;
    }
  }

  /**
   * Unsubscribe from real-time market data for a symbol
   * @param {string} symbol - Symbol to unsubscribe from
   */
  async unsubscribeFromMarketData(symbol) {
    const callback = this.callbacks.get(symbol);
    if (!callback) {
      return;
    }

    // Determine which source to use
    const useLocal = this.config.dataSource === 'local';
    const useDatabento = this.config.dataSource === 'databento' ||
                        this.config.useDabentoForRealtime;

    // Unsubscribe from local data if configured
    if (useLocal) {
      try {
        localDataProvider.unsubscribeFromMarketData(symbol);
        logger.logSystem(`Unsubscribed from ${symbol} from local data`, 'info');
      } catch (error) {
        logger.logSystem(`Error unsubscribing from ${symbol} from local data: ${error.message}`, 'error');
      }
    }
    // Unsubscribe from Databento if configured
    else if (useDatabento && this.databentoBridgeReady) {
      const mappedSymbol = this.mapSymbol(symbol, 'databento');

      try {
        // Unsubscribe from the symbol
        await this.sendBridgeCommand('unsubscribe', {
          dataset: this.config.dabentoDataset,
          symbols: mappedSymbol,
          schema: this.config.dabentoSchema || 'mbo' // Use MBO schema as suggested by Databento
        });

        // Remove the callback for this symbol
        this.liveDataCallbacks.delete(mappedSymbol);

        logger.logSystem(`Unsubscribed from ${mappedSymbol} from Databento`, 'info');
      } catch (error) {
        logger.logSystem(`Error unsubscribing from ${mappedSymbol}: ${error.message}`, 'error');
      }
    }
    // Unsubscribe from Tradovate as default
    else {
      try {
        this.tradovateApi.unsubscribeFromMarketData(symbol, callback);
        logger.logSystem(`Unsubscribed from ${symbol} from Tradovate`, 'info');
      } catch (error) {
        logger.logSystem(`Error unsubscribing from ${symbol} from Tradovate: ${error.message}`, 'error');
      }
    }

    this.callbacks.delete(symbol);
  }

  /**
   * Disconnect from all data sources
   */
  async disconnect() {
    // Unsubscribe from all symbols
    for (const [symbol, callback] of this.callbacks.entries()) {
      await this.unsubscribeFromMarketData(symbol);
    }

    // Disconnect from Databento bridge
    if (this.databentoBridge) {
      logger.logSystem('Disconnecting from Databento bridge...', 'info');

      try {
        // Kill the Python process
        this.databentoBridge.kill();
        this.databentoBridge = null;
        this.databentoBridgeReady = false;

        logger.logSystem('Disconnected from Databento bridge', 'info');
      } catch (error) {
        logger.logSystem(`Error disconnecting from Databento bridge: ${error.message}`, 'error');
      }
    }

    // Any Tradovate-specific cleanup would go here

    this.callbacks.clear();
    this.liveDataCallbacks.clear();
    this.isInitialized = false;
  }

  /**
   * Simulate real-time data for backtesting
   * @param {Date} currentTime - Current time for simulation
   */
  simulateRealTimeData(currentTime = new Date()) {
    // Only simulate data if using local data source
    if (this.config.dataSource !== 'local') {
      return;
    }

    // Simulate data for all subscribed symbols
    for (const symbol of this.callbacks.keys()) {
      localDataProvider.simulateRealTimeData(symbol, currentTime);
    }
  }

  // Add a method to get chart data for a symbol
  getChartData(symbol, timeframe = '1m', bars = 100) {
    // Check if we have chart data for this symbol
    if (!this.chartDataCache || !this.chartDataCache[symbol]) {
      return [];
    }

    // Get all timestamps for this symbol, sorted in descending order (newest first)
    const timestamps = Object.keys(this.chartDataCache[symbol])
      .map(Number)
      .sort((a, b) => b - a);

    // Return the requested number of bars (or all available if less than requested)
    const result = [];
    const limit = Math.min(bars, timestamps.length);

    for (let i = 0; i < limit; i++) {
      const timestamp = timestamps[i];
      result.push({
        timestamp,
        ...this.chartDataCache[symbol][timestamp]
      });
    }

    return result;
  }
}

module.exports = new MarketDataService();
